package com.fishing.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fishing.admin.dto.moment.AdminMomentQueryDTO;
import com.fishing.admin.dto.moment.BatchMomentAuditRequest;
import com.fishing.admin.service.AdminMomentService;
import com.fishing.admin.vo.moment.AdminMomentListVO;
import com.fishing.admin.vo.moment.AdminMomentStatsResponse;
import com.fishing.admin.vo.moment.AdminMomentVO;
import com.fishing.admin.vo.moment.MomentStructuredDataVO;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import com.fishing.domain.User;
import com.fishing.domain.UserBrowsingHistory;
import com.fishing.domain.moment.*;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.mapper.*;
import com.fishing.vo.spot.SpotBatchInfoVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Admin动态管理服务实现
 * 访问 moments 表，该表在 fishing_biz 数据库中
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
@Slf4j
@Service
@DataSource(DataSourceType.FISHING_BIZ)
@RequiredArgsConstructor
public class AdminMomentServiceImpl extends ServiceImpl<MomentMapper, Moment> implements AdminMomentService {

    private final FishingSpotMapper fishingSpotMapper;
    private final MomentLikeMapper momentLikeMapper;
    private final MomentCommentMapper momentCommentMapper;
    private final MomentImageMapper momentImageMapper;
    private final MomentFishingCatchMapper momentFishingCatchMapper;
    private final MomentEquipmentMapper momentEquipmentMapper;
    private final MomentTechniqueMapper momentTechniqueMapper;
    private final MomentQuestionMapper momentQuestionMapper;
    private final MomentCaughtFishMapper momentCaughtFishMapper;
    private final UserBrowsingHistoryMapper userBrowsingHistoryMapper;
    private final UserMapper userMapper;

    @Override
    public List<Moment> selectMomentList(AdminMomentQueryDTO queryDTO) {
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();

        if (queryDTO != null) {
            wrapper.eq(queryDTO.getUserId() != null, Moment::getUserId, queryDTO.getUserId())
                    .eq(StringUtils.hasText(queryDTO.getMomentType()), Moment::getMomentType, queryDTO.getMomentType())
                    .eq(StringUtils.hasText(queryDTO.getVisibility()), Moment::getVisibility, queryDTO.getVisibility())
                    .eq(queryDTO.getFishingSpotId() != null, Moment::getFishingSpotId, queryDTO.getFishingSpotId())
                    .like(StringUtils.hasText(queryDTO.getContent()), Moment::getContent, queryDTO.getContent());

            // 处理开始时间（字符串转换为LocalDateTime）
            if (StringUtils.hasText(queryDTO.getStartTime())) {
                try {
                    LocalDate startDate = LocalDate.parse(queryDTO.getStartTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    LocalDateTime startDateTime = startDate.atStartOfDay();
                    wrapper.ge(Moment::getCreateTime, startDateTime);
                } catch (Exception e) {
                    log.warn("开始时间格式错误: {}", queryDTO.getStartTime());
                }
            }

            // 处理结束时间（字符串转换为LocalDateTime）
            if (StringUtils.hasText(queryDTO.getEndTime())) {
                try {
                    LocalDate endDate = LocalDate.parse(queryDTO.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                    LocalDateTime endDateTime = endDate.atTime(LocalTime.MAX);
                    wrapper.le(Moment::getCreateTime, endDateTime);
                } catch (Exception e) {
                    log.warn("结束时间格式错误: {}", queryDTO.getEndTime());
                }
            }
        }

        // 注释掉默认30天限制，改为由前端控制查询范围
        // boolean noConditions = wrapper.getExpression() == null || wrapper.getExpression().getNormal().isEmpty();
        // if (noConditions) {
        //     LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
        //     wrapper.ge(Moment::getCreateTime, thirtyDaysAgo);
        //     log.info("应用默认查询限制：查询最近30天的动态数据，避免全表查询");
        // }

        wrapper.orderByDesc(Moment::getCreateTime);
        return list(wrapper);
    }

    @Override
    public AdminMomentVO getMomentDetail(Long id, Long currentUserId) {
        Moment moment = getById(id);
        if (moment == null) {
            return null;
        }

        AdminMomentVO vo = new AdminMomentVO();
        BeanUtils.copyProperties(moment, vo);

        // 设置时间字段映射
        vo.setCreateTime(moment.getCreateTime());
        vo.setUpdateTime(moment.getUpdateTime());

        // 设置用户信息
        vo.setPublisherId(moment.getUserId());
        vo.setUserName("用户" + moment.getUserId());
        vo.setPublisherName("用户" + moment.getUserId());

        // 获取真实的统计数据
        vo.setLikeCount(getLikeCountForMoment(id));
        vo.setCommentCount(getCommentCountForMoment(id));
        vo.setViewCount(getViewCountForMoment(id));

        // 获取图片信息
        List<MomentImage> images = getMomentImages(id);
        vo.setImages(images.stream()
                .map(image -> {
                    AdminMomentVO.MomentImageVO imageVO = new AdminMomentVO.MomentImageVO();
                    imageVO.setId(image.getId());
                    imageVO.setImageUrl(image.getImageUrl());
                    imageVO.setDisplayOrder(image.getDisplayOrder());
                    return imageVO;
                })
                .collect(Collectors.toList()));

        // 获取钓点信息
        if (moment.getFishingSpotId() != null && moment.getFishingSpotId() > 0) {
            FishingSpot spot = fishingSpotMapper.selectById(moment.getFishingSpotId());
            if (spot != null) {
                vo.setFishingSpotName(spot.getName());
                vo.setFishingSpotAddress(spot.getAddress());
            }
        }

        // TODO: 结构化数据现在存储在单独的表中，可以通过专门的API获取
        // Object structuredData = getStructuredDataForMoment(moment);

        // 设置其他字段
        vo.setIsLiked(false);
        vo.setIsBookmarked(false);
        vo.setIsViolation(false);
        vo.setAuditStatus("NORMAL");
        vo.setVisibility(moment.getVisibility() != null ? moment.getVisibility() : "public");

        return vo;
    }

    @Override
    public boolean deleteMomentByIds(Long[] ids) {
        return removeByIds(Arrays.asList(ids));
    }

    @Override
    public boolean batchAuditMoments(BatchMomentAuditRequest request) {
        // 批量审核动态
        for (Long momentId : request.getMomentIds()) {
            Moment moment = new Moment();
            moment.setId(momentId);
            moment.setUpdateTime(LocalDateTime.now());
            updateById(moment);
        }
        return true;
    }

    @Override
    public boolean setMomentVisibility(Long[] ids, String visibility, Long operatorId) {
        for (Long id : ids) {
            Moment moment = new Moment();
            moment.setId(id);
            moment.setVisibility(visibility);
            moment.setUpdateTime(LocalDateTime.now());
            updateById(moment);
        }
        return true;
    }

    @Override
    public AdminMomentStatsResponse getMomentStats() {
        AdminMomentStatsResponse response = new AdminMomentStatsResponse();

        // 基础统计
        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        long totalMoments = count(wrapper);
        response.setTotalCount(totalMoments);

        // 今日动态数量
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = today.atStartOfDay();
        LambdaQueryWrapper<Moment> todayWrapper = new LambdaQueryWrapper<>();
        todayWrapper.ge(Moment::getCreateTime, todayStart);
        long todayCount = count(todayWrapper);
        response.setTodayCount(todayCount);

        // 计算真实的交互数据
        response.setTotalLikes(getTotalLikes());
        response.setTotalComments(getTotalComments());
        response.setTotalBookmarks(0L); // TODO: 实现收藏统计

        // 计算今日新增交互数据
        response.setTodayNewLikes(getTodayNewLikes(todayStart));
        response.setTodayNewComments(getTodayNewComments(todayStart));

        // 活跃动态数(最近7天有交互的动态)
        response.setActiveMoments(getActiveMoments());

        // 审核相关统计(模拟数据)
        response.setPendingAuditCount((long) (totalMoments * 0.05));
        response.setApprovedCount((long) (totalMoments * 0.9));
        response.setRejectedCount((long) (totalMoments * 0.05));
        response.setViolationCount(0L);

        // 动态类型分布统计
        response.setTypeDistribution(getMomentTypeDistribution());

        // 结构化数据统计
        response.setStructuredDataStats(getStructuredDataStats());

        // 最近7天趋势分析
        response.setWeeklyTrend(getWeeklyTrend());

        return response;
    }

    @Override
    public List<AdminMomentListVO> getAdminMomentList(AdminMomentQueryDTO queryDTO) {
        List<Moment> moments = selectMomentList(queryDTO);
        return convertToAdminMomentListVOOptimized(moments);
    }

    @Override
    public List<AdminMomentListVO> convertToAdminMomentListVOOptimized(List<Moment> moments) {
        if (moments == null || moments.isEmpty()) {
            return List.of();
        }

        // 批量获取钓点信息
        Map<Long, SpotBatchInfoVO> spotInfoMap = batchGetSpotsMap(moments);

        // 批量获取用户信息
        Map<Long, User> userInfoMap = batchGetUsersMap(moments);

        // 批量获取点赞和评论统计
        Map<Long, Integer> likeCountMap = batchGetLikeCount(moments);
        Map<Long, Integer> commentCountMap = batchGetCommentCount(moments);

        // 批量获取查看统计
        Map<Long, Integer> viewCountMap = batchGetViewCount(moments);

        // 批量获取图片信息
        Map<Long, List<MomentImage>> imageMap = batchGetMomentImages(moments);

        // 批量获取结构化数据
        Map<Long, Object> structuredDataMap = batchGetStructuredData(moments);

        return moments.stream()
                .map(moment -> buildAdminMomentListVO(moment, spotInfoMap, userInfoMap, likeCountMap, commentCountMap, viewCountMap, imageMap, structuredDataMap))
                .collect(Collectors.toList());
    }

    /**
     * 批量获取用户信息
     */
    private Map<Long, User> batchGetUsersMap(List<Moment> moments) {
        // 提取所有用户ID
        Set<Long> userIds = moments.stream()
                .map(Moment::getUserId)
                .filter(id -> id != null && id > 0)
                .collect(Collectors.toSet());

        if (userIds.isEmpty()) {
            return Map.of();
        }

        try {
            // 批量查询用户信息
            List<User> users = userMapper.selectBatchIds(userIds);
            return users.stream()
                    .collect(Collectors.toMap(
                            User::getId,
                            user -> user,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("批量获取用户信息失败，userIds: {}", userIds, e);
            return Map.of();
        }
    }

    /**
     * 批量获取钓点信息
     */
    private Map<Long, SpotBatchInfoVO> batchGetSpotsMap(List<Moment> moments) {
        // 提取所有钓点ID
        Set<Long> spotIds = moments.stream()
                .map(Moment::getFishingSpotId)
                .filter(id -> id != null && id > 0)
                .collect(Collectors.toSet());

        if (spotIds.isEmpty()) {
            return Map.of();
        }

        // 批量查询钓点信息
        List<FishingSpot> spots = fishingSpotMapper.selectBatchIds(spotIds);

        return spots.stream()
                .collect(Collectors.toMap(
                        FishingSpot::getId,
                        this::convertToSpotBatchInfoVO,
                        (existing, replacement) -> existing
                ));
    }

    /**
     * 构建管理端动态列表VO
     */
    private AdminMomentListVO buildAdminMomentListVO(Moment moment, Map<Long, SpotBatchInfoVO> spotInfoMap,
                                                     Map<Long, User> userInfoMap, Map<Long, Integer> likeCountMap,
                                                     Map<Long, Integer> commentCountMap, Map<Long, Integer> viewCountMap,
                                                     Map<Long, List<MomentImage>> imageMap, Map<Long, Object> structuredDataMap) {
        AdminMomentListVO vo = new AdminMomentListVO();
        BeanUtils.copyProperties(moment, vo);

        // 设置时间字段映射
        vo.setCreateTime(moment.getCreateTime());
        vo.setUpdateTime(moment.getUpdateTime());

        // 设置用户信息字段映射（从批量查询的用户信息中获取）
        vo.setPublisherId(moment.getUserId());
        User user = userInfoMap.get(moment.getUserId());
        if (user != null) {
            vo.setUserName(user.getName() != null ? user.getName() : "用户" + moment.getUserId());
            vo.setPublisherName(user.getName() != null ? user.getName() : "用户" + moment.getUserId());
            vo.setPublisherAvatar(user.getAvatarUrl()); // 设置真实的用户头像
            vo.setPublisherPhone(user.getTelephoneNumber()); // 设置用户手机号
        } else {
            // 用户信息查询失败时的默认值
            vo.setUserName("用户" + moment.getUserId());
            vo.setPublisherName("用户" + moment.getUserId());
            vo.setPublisherAvatar(null); // 头像为空
            vo.setPublisherPhone(null);
        }

        // 设置钓点信息
        if (moment.getFishingSpotId() != null && moment.getFishingSpotId() > 0) {
            SpotBatchInfoVO spotInfo = spotInfoMap.get(moment.getFishingSpotId());
            if (spotInfo != null) {
                vo.setFishingSpotName(spotInfo.getName());
                vo.setFishingSpotAddress(spotInfo.getAddress());
            } else {
                SpotBatchInfoVO defaultInfo = createDefaultSpotInfo();
                vo.setFishingSpotName(defaultInfo.getName());
                vo.setFishingSpotAddress(defaultInfo.getAddress());
            }
        } else {
            SpotBatchInfoVO defaultInfo = createDefaultSpotInfo();
            vo.setFishingSpotName(defaultInfo.getName());
            vo.setFishingSpotAddress(defaultInfo.getAddress());
        }

        // 设置真实的统计数据
        vo.setLikeCount(likeCountMap.getOrDefault(moment.getId(), 0));
        vo.setCommentCount(commentCountMap.getOrDefault(moment.getId(), 0));
        vo.setViewCount(viewCountMap.getOrDefault(moment.getId(), 0));
        vo.setIsViolation(false);
        vo.setAuditStatus("NORMAL");
        vo.setVisibility(moment.getVisibility() != null ? moment.getVisibility() : "public");

        // 设置图片信息
        List<MomentImage> momentImages = imageMap.getOrDefault(moment.getId(), List.of());
        vo.setImages(momentImages.stream()
                .map(image -> {
                    AdminMomentListVO.MomentImageVO imageVO = new AdminMomentListVO.MomentImageVO();
                    imageVO.setId(image.getId());
                    imageVO.setImageUrl(image.getImageUrl());
                    imageVO.setDisplayOrder(image.getDisplayOrder());
                    return imageVO;
                })
                .collect(Collectors.toList()));

        // TODO: 结构化数据现在存储在单独的表中，可以通过专门的API获取
        // Object structuredData = structuredDataMap.get(moment.getId());

        return vo;
    }

    /**
     * 转换钓点为批量信息VO
     */
    private SpotBatchInfoVO convertToSpotBatchInfoVO(FishingSpot spot) {
        return SpotBatchInfoVO.builder()
                .id(spot.getId())
                .name(spot.getName())
                .address(spot.getAddress())
                .latitude(spot.getLatitude() != null ? spot.getLatitude() : BigDecimal.ZERO)
                .longitude(spot.getLongitude() != null ? spot.getLongitude() : BigDecimal.ZERO)
                .build();
    }

    /**
     * 创建默认钓点信息
     */
    private SpotBatchInfoVO createDefaultSpotInfo() {
        return SpotBatchInfoVO.builder()
                .id(-1L)
                .name("未知钓点")
                .address("未知地址")
                .latitude(BigDecimal.ZERO)
                .longitude(BigDecimal.ZERO)
                .build();
    }

    /**
     * 批量获取动态点赞数
     */
    private Map<Long, Integer> batchGetLikeCount(List<Moment> moments) {
        if (moments == null || moments.isEmpty()) {
            return Map.of();
        }

        Set<Long> momentIds = moments.stream()
                .map(Moment::getId)
                .collect(Collectors.toSet());

        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentLike::getMomentId, momentIds);

        List<MomentLike> likes = momentLikeMapper.selectList(wrapper);

        return likes.stream()
                .collect(Collectors.groupingBy(
                        MomentLike::getMomentId,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
    }

    /**
     * 批量获取动态评论数
     */
    private Map<Long, Integer> batchGetCommentCount(List<Moment> moments) {
        if (moments == null || moments.isEmpty()) {
            return Map.of();
        }

        Set<Long> momentIds = moments.stream()
                .map(Moment::getId)
                .collect(Collectors.toSet());

        LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentComment::getMomentId, momentIds);

        List<MomentComment> comments = momentCommentMapper.selectList(wrapper);

        return comments.stream()
                .collect(Collectors.groupingBy(
                        MomentComment::getMomentId,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
    }

    /**
     * 批量获取动态图片
     */
    private Map<Long, List<MomentImage>> batchGetMomentImages(List<Moment> moments) {
        if (moments == null || moments.isEmpty()) {
            return Map.of();
        }

        Set<Long> momentIds = moments.stream()
                .map(Moment::getId)
                .collect(Collectors.toSet());

        LambdaQueryWrapper<MomentImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentImage::getMomentId, momentIds)
                .orderByAsc(MomentImage::getDisplayOrder);

        List<MomentImage> images = momentImageMapper.selectList(wrapper);

        return images.stream()
                .collect(Collectors.groupingBy(MomentImage::getMomentId));
    }

    /**
     * 批量获取动态查看数
     */
    private Map<Long, Integer> batchGetViewCount(List<Moment> moments) {
        if (moments == null || moments.isEmpty()) {
            return Map.of();
        }

        Set<Long> momentIds = moments.stream()
                .map(Moment::getId)
                .collect(Collectors.toSet());

        LambdaQueryWrapper<UserBrowsingHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserBrowsingHistory::getBrowseType, "moment")
                .in(UserBrowsingHistory::getTargetId, momentIds);

        List<UserBrowsingHistory> browsingHistories = userBrowsingHistoryMapper.selectList(wrapper);

        return browsingHistories.stream()
                .collect(Collectors.groupingBy(
                        UserBrowsingHistory::getTargetId,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
    }

    @Override
    public int getUserMomentCount(Long userId) {
        if (userId == null) {
            return 0;
        }

        LambdaQueryWrapper<Moment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Moment::getUserId, userId);

        return Math.toIntExact(count(wrapper));
    }

    /**
     * 获取单个动态的点赞数
     */
    private int getLikeCountForMoment(Long momentId) {
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentLike::getMomentId, momentId);
        return Math.toIntExact(momentLikeMapper.selectCount(wrapper));
    }

    /**
     * 获取单个动态的评论数
     */
    private int getCommentCountForMoment(Long momentId) {
        LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentComment::getMomentId, momentId);
        return Math.toIntExact(momentCommentMapper.selectCount(wrapper));
    }

    /**
     * 获取单个动态的查看数
     */
    private int getViewCountForMoment(Long momentId) {
        LambdaQueryWrapper<UserBrowsingHistory> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserBrowsingHistory::getBrowseType, "moment")
                .eq(UserBrowsingHistory::getTargetId, momentId);
        return Math.toIntExact(userBrowsingHistoryMapper.selectCount(wrapper));
    }

    /**
     * 获取单个动态的图片
     */
    private List<MomentImage> getMomentImages(Long momentId) {
        LambdaQueryWrapper<MomentImage> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentImage::getMomentId, momentId)
                .orderByAsc(MomentImage::getDisplayOrder);
        return momentImageMapper.selectList(wrapper);
    }

    /**
     * 批量获取结构化数据
     */
    private Map<Long, Object> batchGetStructuredData(List<Moment> moments) {
        Map<Long, Object> result = new HashMap<>();

        if (moments == null || moments.isEmpty()) {
            return result;
        }

        // 按动态类型分组
        Map<String, List<Moment>> momentsByType = moments.stream()
                .filter(moment -> moment.getMomentType() != null)
                .collect(Collectors.groupingBy(Moment::getMomentType));

        // 批量获取各类型的结构化数据
        if (momentsByType.containsKey("1")) {
            Map<Long, Object> fishingCatchData = batchGetFishingCatchData(momentsByType.get("1"));
            result.putAll(fishingCatchData);
        }

        if (momentsByType.containsKey("2")) {
            Map<Long, Object> equipmentData = batchGetEquipmentData(momentsByType.get("2"));
            result.putAll(equipmentData);
        }

        if (momentsByType.containsKey("3")) {
            Map<Long, Object> techniqueData = batchGetTechniqueData(momentsByType.get("3"));
            result.putAll(techniqueData);
        }

        if (momentsByType.containsKey("4")) {
            Map<Long, Object> questionData = batchGetQuestionData(momentsByType.get("4"));
            result.putAll(questionData);
        }

        return result;
    }

    /**
     * 批量获取钓获分享数据
     */
    private Map<Long, Object> batchGetFishingCatchData(List<Moment> moments) {
        Map<Long, Object> result = new HashMap<>();

        Set<Long> momentIds = moments.stream().map(Moment::getId).collect(Collectors.toSet());

        // 批量查询钓获分享数据
        LambdaQueryWrapper<MomentFishingCatch> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentFishingCatch::getMomentId, momentIds);
        List<MomentFishingCatch> fishingCatches = momentFishingCatchMapper.selectList(wrapper);

        // 获取所有钓获分享ID
        Set<Long> fishingCatchIds = fishingCatches.stream().map(MomentFishingCatch::getId).collect(Collectors.toSet());

        // 批量查询捕获鱼类数据
        Map<Long, List<MomentCaughtFish>> caughtFishMap = new HashMap<>();
        if (!fishingCatchIds.isEmpty()) {
            LambdaQueryWrapper<MomentCaughtFish> fishWrapper = new LambdaQueryWrapper<>();
            fishWrapper.in(MomentCaughtFish::getFishingCatchId, fishingCatchIds);
            List<MomentCaughtFish> allCaughtFishes = momentCaughtFishMapper.selectList(fishWrapper);

            caughtFishMap = allCaughtFishes.stream()
                    .collect(Collectors.groupingBy(MomentCaughtFish::getFishingCatchId));
        }

        // 组装数据
        for (MomentFishingCatch fishingCatch : fishingCatches) {
            Map<String, Object> data = new HashMap<>();
            data.put("totalWeight", fishingCatch.getTotalWeight());
            data.put("fishingMethod", fishingCatch.getFishingMethod());
            data.put("weatherConditions", fishingCatch.getWeatherConditions());
            data.put("caughtFishes", caughtFishMap.getOrDefault(fishingCatch.getId(), List.of()));
            data.put("attributes", fishingCatch.getAttributes());

            result.put(fishingCatch.getMomentId(), data);
        }

        return result;
    }

    /**
     * 批量获取装备展示数据
     */
    private Map<Long, Object> batchGetEquipmentData(List<Moment> moments) {
        Map<Long, Object> result = new HashMap<>();

        Set<Long> momentIds = moments.stream().map(Moment::getId).collect(Collectors.toSet());

        LambdaQueryWrapper<MomentEquipment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentEquipment::getMomentId, momentIds);
        List<MomentEquipment> equipments = momentEquipmentMapper.selectList(wrapper);

        for (MomentEquipment equipment : equipments) {
            Map<String, Object> data = new HashMap<>();
            data.put("equipmentName", equipment.getEquipmentName());
            data.put("category", equipment.getCategory());
            data.put("brand", equipment.getBrand());
            data.put("model", equipment.getModel());
            data.put("price", equipment.getPrice());
            data.put("rating", equipment.getRating());
            data.put("targetFish", equipment.getTargetFish());
            data.put("attributes", equipment.getAttributes());

            result.put(equipment.getMomentId(), data);
        }

        return result;
    }

    /**
     * 批量获取技巧分享数据
     */
    private Map<Long, Object> batchGetTechniqueData(List<Moment> moments) {
        Map<Long, Object> result = new HashMap<>();

        Set<Long> momentIds = moments.stream().map(Moment::getId).collect(Collectors.toSet());

        LambdaQueryWrapper<MomentTechnique> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentTechnique::getMomentId, momentIds);
        List<MomentTechnique> techniques = momentTechniqueMapper.selectList(wrapper);

        for (MomentTechnique technique : techniques) {
            Map<String, Object> data = new HashMap<>();
            data.put("techniqueName", technique.getTechniqueName());
            data.put("difficulty", technique.getDifficulty());
            data.put("targetFish", technique.getTargetFish());
            data.put("description", technique.getDescription());
            data.put("attributes", technique.getAttributes());

            result.put(technique.getMomentId(), data);
        }

        return result;
    }

    /**
     * 批量获取问答求助数据
     */
    private Map<Long, Object> batchGetQuestionData(List<Moment> moments) {
        Map<Long, Object> result = new HashMap<>();

        Set<Long> momentIds = moments.stream().map(Moment::getId).collect(Collectors.toSet());

        LambdaQueryWrapper<MomentQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentQuestion::getMomentId, momentIds);
        List<MomentQuestion> questions = momentQuestionMapper.selectList(wrapper);

        for (MomentQuestion question : questions) {
            Map<String, Object> data = new HashMap<>();
            data.put("questionTitle", question.getQuestionTitle());
            data.put("detailedProblem", question.getDetailedProblem());
            data.put("isResolved", question.getIsResolved());
            data.put("bestAnswerId", question.getBestAnswerId());
            data.put("resolvedAt", question.getResolvedAt());
            data.put("attributes", question.getAttributes());

            result.put(question.getMomentId(), data);
        }

        return result;
    }

    /**
     * 根据动态类型获取结构化数据
     */
    private Object getStructuredDataForMoment(Moment moment) {
        if (moment == null || moment.getMomentType() == null) {
            return null;
        }

        try {
            int momentType = Integer.parseInt(moment.getMomentType());

            return switch (momentType) {
                case 1 -> // 钓获分享
                        getFishingCatchData(moment.getId());
                case 2 -> // 装备展示
                        getEquipmentData(moment.getId());
                case 3 -> // 技巧分享
                        getTechniqueData(moment.getId());
                case 4 -> // 问答求助
                        getQuestionData(moment.getId());
                default -> null;
            };
        } catch (Exception e) {
            log.warn("获取动态 {} 的结构化数据失败: {}", moment.getId(), e.getMessage());
            return null;
        }
    }

    /**
     * 获取钓获分享数据
     */
    private Object getFishingCatchData(Long momentId) {
        LambdaQueryWrapper<MomentFishingCatch> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentFishingCatch::getMomentId, momentId);
        MomentFishingCatch fishingCatch = momentFishingCatchMapper.selectOne(wrapper);

        if (fishingCatch == null) {
            return null;
        }

        // 获取捕获鱼类列表 - 使用 fishingCatchId 关联
        LambdaQueryWrapper<MomentCaughtFish> fishWrapper = new LambdaQueryWrapper<>();
        fishWrapper.eq(MomentCaughtFish::getFishingCatchId, fishingCatch.getId());
        List<MomentCaughtFish> caughtFishes = momentCaughtFishMapper.selectList(fishWrapper);

        // 构建返回对象
        Map<String, Object> result = new HashMap<>();
        result.put("totalWeight", fishingCatch.getTotalWeight());
        result.put("fishingMethod", fishingCatch.getFishingMethod());
        result.put("weatherConditions", fishingCatch.getWeatherConditions());
        result.put("caughtFishes", caughtFishes);
        result.put("attributes", fishingCatch.getAttributes());

        return result;
    }

    /**
     * 获取装备展示数据
     */
    private Object getEquipmentData(Long momentId) {
        LambdaQueryWrapper<MomentEquipment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentEquipment::getMomentId, momentId);
        MomentEquipment equipment = momentEquipmentMapper.selectOne(wrapper);

        if (equipment == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("equipmentName", equipment.getEquipmentName());
        result.put("category", equipment.getCategory());
        result.put("brand", equipment.getBrand());
        result.put("model", equipment.getModel());
        result.put("price", equipment.getPrice());
        result.put("rating", equipment.getRating());
        result.put("targetFish", equipment.getTargetFish());
        result.put("attributes", equipment.getAttributes());

        return result;
    }

    /**
     * 获取技巧分享数据
     */
    private Object getTechniqueData(Long momentId) {
        LambdaQueryWrapper<MomentTechnique> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentTechnique::getMomentId, momentId);
        MomentTechnique technique = momentTechniqueMapper.selectOne(wrapper);

        if (technique == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("techniqueName", technique.getTechniqueName());
        result.put("difficulty", technique.getDifficulty());
        result.put("targetFish", technique.getTargetFish());
        result.put("description", technique.getDescription());
        result.put("attributes", technique.getAttributes());

        return result;
    }

    /**
     * 获取问答求助数据
     */
    private Object getQuestionData(Long momentId) {
        LambdaQueryWrapper<MomentQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentQuestion::getMomentId, momentId);
        MomentQuestion question = momentQuestionMapper.selectOne(wrapper);

        if (question == null) {
            return null;
        }

        Map<String, Object> result = new HashMap<>();
        result.put("questionTitle", question.getQuestionTitle());
        result.put("detailedProblem", question.getDetailedProblem());
        result.put("isResolved", question.getIsResolved());
        result.put("bestAnswerId", question.getBestAnswerId());
        result.put("resolvedAt", question.getResolvedAt());
        result.put("attributes", question.getAttributes());

        return result;
    }

    /**
     * 获取总点赞数
     */
    private Long getTotalLikes() {
        return momentLikeMapper.selectCount(null);
    }

    /**
     * 获取总评论数
     */
    private Long getTotalComments() {
        return momentCommentMapper.selectCount(null);
    }

    /**
     * 获取今日新增点赞数
     */
    private Long getTodayNewLikes(LocalDateTime todayStart) {
        LambdaQueryWrapper<MomentLike> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(MomentLike::getCreateTime, todayStart);
        return momentLikeMapper.selectCount(wrapper);
    }

    /**
     * 获取今日新增评论数
     */
    private Long getTodayNewComments(LocalDateTime todayStart) {
        LambdaQueryWrapper<MomentComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(MomentComment::getCreateTime, todayStart);
        return momentCommentMapper.selectCount(wrapper);
    }

    /**
     * 获取活跃动态数(最近7天有交互的动态)
     */
    private Long getActiveMoments() {
        LocalDateTime sevenDaysAgo = LocalDateTime.now().minusDays(7);

        // 获取最近7天有点赞的动态ID
        LambdaQueryWrapper<MomentLike> likeWrapper = new LambdaQueryWrapper<>();
        likeWrapper.ge(MomentLike::getCreateTime, sevenDaysAgo);
        Set<Long> likedMomentIds = momentLikeMapper.selectList(likeWrapper).stream()
                .map(MomentLike::getMomentId)
                .collect(Collectors.toSet());

        // 获取最近7天有评论的动态ID
        LambdaQueryWrapper<MomentComment> commentWrapper = new LambdaQueryWrapper<>();
        commentWrapper.ge(MomentComment::getCreateTime, sevenDaysAgo);
        Set<Long> commentedMomentIds = momentCommentMapper.selectList(commentWrapper).stream()
                .map(MomentComment::getMomentId)
                .collect(Collectors.toSet());

        // 合并所有活跃动态ID
        Set<Long> activeMomentIds = new HashSet<>();
        activeMomentIds.addAll(likedMomentIds);
        activeMomentIds.addAll(commentedMomentIds);

        return (long) activeMomentIds.size();
    }

    /**
     * 获取动态类型分布统计
     */
    private Map<String, Long> getMomentTypeDistribution() {
        List<Moment> allMoments = getBaseMapper().selectList(null);

        Map<String, Long> distribution = new HashMap<>();
        distribution.put("fishing_catch", 0L);
        distribution.put("equipment", 0L);
        distribution.put("technique", 0L);
        distribution.put("question", 0L);
        distribution.put("other", 0L);

        for (Moment moment : allMoments) {
            if (moment.getMomentType() == null) {
                distribution.merge("other", 1L, Long::sum);
                continue;
            }

            try {
                int momentType = Integer.parseInt(moment.getMomentType());
                switch (momentType) {
                    case 1:
                        distribution.merge("fishing_catch", 1L, Long::sum);
                        break;
                    case 2:
                        distribution.merge("equipment", 1L, Long::sum);
                        break;
                    case 3:
                        distribution.merge("technique", 1L, Long::sum);
                        break;
                    case 4:
                        distribution.merge("question", 1L, Long::sum);
                        break;
                    default:
                        distribution.merge("other", 1L, Long::sum);
                        break;
                }
            } catch (NumberFormatException e) {
                distribution.merge("other", 1L, Long::sum);
            }
        }

        return distribution;
    }

    /**
     * 获取结构化数据统计
     */
    private Map<String, Object> getStructuredDataStats() {
        Map<String, Object> stats = new HashMap<>();

        // 钓获分享数据统计
        Map<String, Object> fishingCatchStats = new HashMap<>();
        List<MomentFishingCatch> fishingCatches = momentFishingCatchMapper.selectList(null);
        fishingCatchStats.put("total", fishingCatches.size());

        // 计算平均重量
        if (!fishingCatches.isEmpty()) {
            double avgWeight = fishingCatches.stream()
                    .filter(fc -> fc.getTotalWeight() != null)
                    .mapToDouble(fc -> fc.getTotalWeight().doubleValue())
                    .average()
                    .orElse(0.0);
            fishingCatchStats.put("averageWeight", avgWeight);
        } else {
            fishingCatchStats.put("averageWeight", 0.0);
        }

        // 统计捕获鱼类数量
        long totalCaughtFish = momentCaughtFishMapper.selectCount(null);
        fishingCatchStats.put("totalCaughtFish", totalCaughtFish);

        stats.put("fishingCatch", fishingCatchStats);

        // 装备展示数据统计
        Map<String, Object> equipmentStats = new HashMap<>();
        List<MomentEquipment> equipments = momentEquipmentMapper.selectList(null);
        equipmentStats.put("total", equipments.size());

        // 热门品牌统计
        Map<String, Long> brandStats = equipments.stream()
                .filter(eq -> eq.getBrand() != null && !eq.getBrand().trim().isEmpty())
                .collect(Collectors.groupingBy(
                        MomentEquipment::getBrand,
                        Collectors.counting()
                ));

        // 获取前5个热门品牌
        List<Map.Entry<String, Long>> topBrands = brandStats.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .toList();

        equipmentStats.put("topBrands", topBrands.stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                )));

        // 平均评分
        if (!equipments.isEmpty()) {
            double avgRating = equipments.stream()
                    .filter(eq -> eq.getRating() != null)
                    .mapToDouble(MomentEquipment::getRating)
                    .average()
                    .orElse(0.0);
            equipmentStats.put("averageRating", avgRating);
        } else {
            equipmentStats.put("averageRating", 0.0);
        }

        stats.put("equipment", equipmentStats);

        // 技巧分享数据统计
        Map<String, Object> techniqueStats = new HashMap<>();
        List<MomentTechnique> techniques = momentTechniqueMapper.selectList(null);
        techniqueStats.put("total", techniques.size());

        // 难度分布
        Map<Integer, Long> difficultyDistribution = techniques.stream()
                .filter(tech -> tech.getDifficulty() != null)
                .collect(Collectors.groupingBy(
                        MomentTechnique::getDifficulty,
                        Collectors.counting()
                ));
        techniqueStats.put("difficultyDistribution", difficultyDistribution);

        stats.put("technique", techniqueStats);

        // 问答求助数据统计
        Map<String, Object> questionStats = new HashMap<>();
        List<MomentQuestion> questions = momentQuestionMapper.selectList(null);
        questionStats.put("total", questions.size());

        // 解决率
        if (!questions.isEmpty()) {
            long resolvedCount = questions.stream()
                    .filter(q -> q.getIsResolved() != null && q.getIsResolved() == 1)
                    .count();
            double resolveRate = (double) resolvedCount / questions.size() * 100;
            questionStats.put("resolveRate", resolveRate);
        } else {
            questionStats.put("resolveRate", 0.0);
        }

        stats.put("question", questionStats);

        return stats;
    }

    /**
     * 获取最近7天趋势分析
     */
    private Map<String, Object> getWeeklyTrend() {
        Map<String, Object> trend = new HashMap<>();
        LocalDateTime now = LocalDateTime.now();

        // 构建最近7天的日期列表
        List<LocalDate> dates = new ArrayList<>();
        List<Long> momentCounts = new ArrayList<>();
        List<Long> likeCounts = new ArrayList<>();
        List<Long> commentCounts = new ArrayList<>();

        for (int i = 6; i >= 0; i--) {
            LocalDate date = now.minusDays(i).toLocalDate();
            dates.add(date);

            // 当日动态数量
            LocalDateTime dayStart = date.atStartOfDay();
            LocalDateTime dayEnd = date.plusDays(1).atStartOfDay();

            LambdaQueryWrapper<Moment> momentWrapper = new LambdaQueryWrapper<>();
            momentWrapper.ge(Moment::getCreateTime, dayStart)
                    .lt(Moment::getCreateTime, dayEnd);
            long dayMoments = count(momentWrapper);
            momentCounts.add(dayMoments);

            // 当日点赞数量
            LambdaQueryWrapper<MomentLike> likeWrapper = new LambdaQueryWrapper<>();
            likeWrapper.ge(MomentLike::getCreateTime, dayStart)
                    .lt(MomentLike::getCreateTime, dayEnd);
            long dayLikes = momentLikeMapper.selectCount(likeWrapper);
            likeCounts.add(dayLikes);

            // 当日评论数量
            LambdaQueryWrapper<MomentComment> commentWrapper = new LambdaQueryWrapper<>();
            commentWrapper.ge(MomentComment::getCreateTime, dayStart)
                    .lt(MomentComment::getCreateTime, dayEnd);
            long dayComments = momentCommentMapper.selectCount(commentWrapper);
            commentCounts.add(dayComments);
        }

        trend.put("dates", dates.stream().map(LocalDate::toString).collect(Collectors.toList()));
        trend.put("momentCounts", momentCounts);
        trend.put("likeCounts", likeCounts);
        trend.put("commentCounts", commentCounts);

        // 计算增长趋势
        if (momentCounts.size() >= 2) {
            long yesterdayMoments = momentCounts.get(momentCounts.size() - 2);
            long todayMoments = momentCounts.getLast();
            double momentGrowthRate = yesterdayMoments > 0 ?
                    ((double) (todayMoments - yesterdayMoments) / yesterdayMoments) * 100 : 0.0;
            trend.put("momentGrowthRate", momentGrowthRate);
        } else {
            trend.put("momentGrowthRate", 0.0);
        }

        return trend;
    }

    @Override
    public MomentStructuredDataVO getMomentStructuredData(Long momentId) {
        // 首先查询基础动态信息
        Moment moment = baseMapper.selectById(momentId);
        if (moment == null) {
            return null;
        }

        MomentStructuredDataVO vo = new MomentStructuredDataVO();
        vo.setMomentId(momentId);
        vo.setMomentType(moment.getMomentType());

        // 根据动态类型获取对应的结构化数据
        switch (moment.getMomentType()) {
            case "fishing_catch":
                vo.setFishingCatchData(getFishingCatchDataVO(momentId));
                break;
            case "equipment":
                vo.setEquipmentData(getEquipmentDataVO(momentId));
                break;
            case "technique":
                vo.setTechniqueData(getTechniqueDataVO(momentId));
                break;
            case "question":
                vo.setQuestionData(getQuestionDataVO(momentId));
                break;
            default:
                log.warn("未知的动态类型: {}", moment.getMomentType());
        }

        return vo;
    }

    @Override
    public List<MomentStructuredDataVO> batchGetMomentStructuredData(List<Long> momentIds) {
        if (momentIds == null || momentIds.isEmpty()) {
            return List.of();
        }

        List<MomentStructuredDataVO> result = new ArrayList<>();

        // 批量查询基础动态信息
        List<Moment> moments = baseMapper.selectBatchIds(momentIds);

        // 按动态类型分组来批量查询结构化数据
        Map<String, List<Long>> momentsByType = moments.stream()
                .collect(Collectors.groupingBy(
                        Moment::getMomentType,
                        Collectors.mapping(Moment::getId, Collectors.toList())
                ));

        // 批量查询各种类型的结构化数据
        Map<Long, MomentStructuredDataVO.FishingCatchDataVO> fishingCatchDataMap =
                batchGetFishingCatchDataVO(momentsByType.getOrDefault("fishing_catch", List.of()));

        Map<Long, MomentStructuredDataVO.EquipmentDataVO> equipmentDataMap =
                batchGetEquipmentDataVO(momentsByType.getOrDefault("equipment", List.of()));

        Map<Long, MomentStructuredDataVO.TechniqueDataVO> techniqueDataMap =
                batchGetTechniqueDataVO(momentsByType.getOrDefault("technique", List.of()));

        Map<Long, MomentStructuredDataVO.QuestionDataVO> questionDataMap =
                batchGetQuestionDataVO(momentsByType.getOrDefault("question", List.of()));

        // 组装结果
        for (Moment moment : moments) {
            MomentStructuredDataVO vo = new MomentStructuredDataVO();
            vo.setMomentId(moment.getId());
            vo.setMomentType(moment.getMomentType());

            switch (moment.getMomentType()) {
                case "fishing_catch":
                    vo.setFishingCatchData(fishingCatchDataMap.get(moment.getId()));
                    break;
                case "equipment":
                    vo.setEquipmentData(equipmentDataMap.get(moment.getId()));
                    break;
                case "technique":
                    vo.setTechniqueData(techniqueDataMap.get(moment.getId()));
                    break;
                case "question":
                    vo.setQuestionData(questionDataMap.get(moment.getId()));
                    break;
            }

            result.add(vo);
        }

        return result;
    }

    /**
     * 获取钓获分享数据VO
     */
    private MomentStructuredDataVO.FishingCatchDataVO getFishingCatchDataVO(Long momentId) {
        LambdaQueryWrapper<MomentFishingCatch> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentFishingCatch::getMomentId, momentId);
        MomentFishingCatch fishingCatch = momentFishingCatchMapper.selectOne(wrapper);

        if (fishingCatch == null) {
            return null;
        }

        MomentStructuredDataVO.FishingCatchDataVO vo = new MomentStructuredDataVO.FishingCatchDataVO();
        vo.setTotalWeight(fishingCatch.getTotalWeight() != null ? fishingCatch.getTotalWeight().toString() : null);
        vo.setFishingMethod(fishingCatch.getFishingMethod());
        vo.setWeatherConditions(fishingCatch.getWeatherConditions());
        vo.setAttributes(fishingCatch.getAttributes());

        // 获取捕获鱼类列表
        LambdaQueryWrapper<MomentCaughtFish> caughtFishWrapper = new LambdaQueryWrapper<>();
        caughtFishWrapper.eq(MomentCaughtFish::getFishingCatchId, fishingCatch.getId());
        List<MomentCaughtFish> caughtFishList = momentCaughtFishMapper.selectList(caughtFishWrapper);

        List<MomentStructuredDataVO.CaughtFishVO> caughtFishVOs = caughtFishList.stream()
                .map(cf -> {
                    MomentStructuredDataVO.CaughtFishVO cfVO = new MomentStructuredDataVO.CaughtFishVO();
                    cfVO.setFishTypeId(cf.getFishTypeId());
                    cfVO.setFishTypeName(cf.getFishTypeName());
                    cfVO.setCount(cf.getCount());
                    cfVO.setWeight(cf.getWeight() != null ? cf.getWeight().toString() : null);
                    return cfVO;
                })
                .collect(Collectors.toList());

        vo.setCaughtFish(caughtFishVOs);
        return vo;
    }

    /**
     * 获取装备展示数据VO
     */
    private MomentStructuredDataVO.EquipmentDataVO getEquipmentDataVO(Long momentId) {
        LambdaQueryWrapper<MomentEquipment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentEquipment::getMomentId, momentId);
        MomentEquipment equipment = momentEquipmentMapper.selectOne(wrapper);

        if (equipment == null) {
            return null;
        }

        MomentStructuredDataVO.EquipmentDataVO vo = new MomentStructuredDataVO.EquipmentDataVO();
        vo.setEquipmentName(equipment.getEquipmentName());
        vo.setBrand(equipment.getBrand());
        vo.setModel(equipment.getModel());
        vo.setPriceRange(equipment.getPrice() != null ? equipment.getPrice().toString() : null);
        vo.setPurchaseChannel(null); // 该字段在domain中不存在
        vo.setExperience(null); // 该字段在domain中不存在
        vo.setRecommendationIndex(equipment.getRating());
        vo.setAttributes(equipment.getAttributes());

        return vo;
    }

    /**
     * 获取技巧分享数据VO
     */
    private MomentStructuredDataVO.TechniqueDataVO getTechniqueDataVO(Long momentId) {
        LambdaQueryWrapper<MomentTechnique> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentTechnique::getMomentId, momentId);
        MomentTechnique technique = momentTechniqueMapper.selectOne(wrapper);

        if (technique == null) {
            return null;
        }

        MomentStructuredDataVO.TechniqueDataVO vo = new MomentStructuredDataVO.TechniqueDataVO();
        vo.setTechniqueTitle(technique.getTechniqueName());
        vo.setDifficultyLevel(technique.getDifficulty() != null ? technique.getDifficulty().toString() : null);
        vo.setApplicableEnvironment(null); // 该字段在domain中不存在
        vo.setRequiredEquipment(null); // 该字段在domain中不存在
        vo.setDetailedSteps(technique.getDescription());
        vo.setPrecautions(null); // 该字段在domain中不存在
        vo.setAttributes(technique.getAttributes());

        // TODO: 获取适用鱼类和环境条件列表
        // 这需要查询 moment_technique_fish_type 和 moment_technique_environment 表
        vo.setApplicableFishTypes(List.of());
        vo.setEnvironmentConditions(List.of());

        return vo;
    }

    /**
     * 获取问答求助数据VO
     */
    private MomentStructuredDataVO.QuestionDataVO getQuestionDataVO(Long momentId) {
        LambdaQueryWrapper<MomentQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentQuestion::getMomentId, momentId);
        MomentQuestion question = momentQuestionMapper.selectOne(wrapper);

        if (question == null) {
            return null;
        }

        MomentStructuredDataVO.QuestionDataVO vo = new MomentStructuredDataVO.QuestionDataVO();
        vo.setQuestionTitle(question.getQuestionTitle());
        vo.setQuestionType(null); // 该字段在domain中不存在
        vo.setUrgencyLevel(null); // 该字段在domain中不存在
        vo.setAttributes(question.getAttributes());

        // TODO: 获取问题标签列表
        // 这需要查询 moment_question_tag 表
        vo.setQuestionTags(List.of());

        return vo;
    }

    /**
     * 批量获取钓获分享数据VO
     */
    private Map<Long, MomentStructuredDataVO.FishingCatchDataVO> batchGetFishingCatchDataVO(List<Long> momentIds) {
        if (momentIds.isEmpty()) {
            return Map.of();
        }

        // 批量查询钓获分享数据
        LambdaQueryWrapper<MomentFishingCatch> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentFishingCatch::getMomentId, momentIds);
        List<MomentFishingCatch> fishingCatchList = momentFishingCatchMapper.selectList(wrapper);

        // 批量查询捕获鱼类数据
        List<Long> fishingCatchIds = fishingCatchList.stream()
                .map(MomentFishingCatch::getId)
                .collect(Collectors.toList());

        Map<Long, List<MomentCaughtFish>> caughtFishByFishingCatchId = Map.of();
        if (!fishingCatchIds.isEmpty()) {
            LambdaQueryWrapper<MomentCaughtFish> caughtFishWrapper = new LambdaQueryWrapper<>();
            caughtFishWrapper.in(MomentCaughtFish::getFishingCatchId, fishingCatchIds);
            List<MomentCaughtFish> caughtFishList = momentCaughtFishMapper.selectList(caughtFishWrapper);

            caughtFishByFishingCatchId = caughtFishList.stream()
                    .collect(Collectors.groupingBy(MomentCaughtFish::getFishingCatchId));
        }

        // 构建结果映射
        Map<Long, MomentStructuredDataVO.FishingCatchDataVO> result = new HashMap<>();

        for (MomentFishingCatch fishingCatch : fishingCatchList) {
            MomentStructuredDataVO.FishingCatchDataVO vo = new MomentStructuredDataVO.FishingCatchDataVO();
            vo.setTotalWeight(fishingCatch.getTotalWeight() != null ? fishingCatch.getTotalWeight().toString() : null);
            vo.setFishingMethod(fishingCatch.getFishingMethod());
            vo.setWeatherConditions(fishingCatch.getWeatherConditions());
            vo.setAttributes(fishingCatch.getAttributes());

            // 设置捕获鱼类列表
            List<MomentCaughtFish> caughtFishList = caughtFishByFishingCatchId.getOrDefault(fishingCatch.getId(), List.of());
            List<MomentStructuredDataVO.CaughtFishVO> caughtFishVOs = caughtFishList.stream()
                    .map(cf -> {
                        MomentStructuredDataVO.CaughtFishVO cfVO = new MomentStructuredDataVO.CaughtFishVO();
                        cfVO.setFishTypeId(cf.getFishTypeId());
                        cfVO.setFishTypeName(cf.getFishTypeName());
                        cfVO.setCount(cf.getCount());
                        cfVO.setWeight(cf.getWeight() != null ? cf.getWeight().toString() : null);
                        return cfVO;
                    })
                    .collect(Collectors.toList());

            vo.setCaughtFish(caughtFishVOs);
            result.put(fishingCatch.getMomentId(), vo);
        }

        return result;
    }

    /**
     * 批量获取装备展示数据VO
     */
    private Map<Long, MomentStructuredDataVO.EquipmentDataVO> batchGetEquipmentDataVO(List<Long> momentIds) {
        if (momentIds.isEmpty()) {
            return Map.of();
        }

        LambdaQueryWrapper<MomentEquipment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentEquipment::getMomentId, momentIds);
        List<MomentEquipment> equipmentList = momentEquipmentMapper.selectList(wrapper);

        return equipmentList.stream()
                .collect(Collectors.toMap(
                        MomentEquipment::getMomentId,
                        equipment -> {
                            MomentStructuredDataVO.EquipmentDataVO vo = new MomentStructuredDataVO.EquipmentDataVO();
                            vo.setEquipmentName(equipment.getEquipmentName());
                            vo.setBrand(equipment.getBrand());
                            vo.setModel(equipment.getModel());
                            vo.setPriceRange(equipment.getPrice() != null ? equipment.getPrice().toString() : null);
                            vo.setPurchaseChannel(null);
                            vo.setExperience(null);
                            vo.setRecommendationIndex(equipment.getRating());
                            vo.setAttributes(equipment.getAttributes());
                            return vo;
                        }
                ));
    }

    /**
     * 批量获取技巧分享数据VO
     */
    private Map<Long, MomentStructuredDataVO.TechniqueDataVO> batchGetTechniqueDataVO(List<Long> momentIds) {
        if (momentIds.isEmpty()) {
            return Map.of();
        }

        LambdaQueryWrapper<MomentTechnique> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentTechnique::getMomentId, momentIds);
        List<MomentTechnique> techniqueList = momentTechniqueMapper.selectList(wrapper);

        return techniqueList.stream()
                .collect(Collectors.toMap(
                        MomentTechnique::getMomentId,
                        technique -> {
                            MomentStructuredDataVO.TechniqueDataVO vo = new MomentStructuredDataVO.TechniqueDataVO();
                            vo.setTechniqueTitle(technique.getTechniqueName());
                            vo.setDifficultyLevel(technique.getDifficulty() != null ? technique.getDifficulty().toString() : null);
                            vo.setApplicableEnvironment(null);
                            vo.setRequiredEquipment(null);
                            vo.setDetailedSteps(technique.getDescription());
                            vo.setPrecautions(null);
                            vo.setAttributes(technique.getAttributes());
                            vo.setApplicableFishTypes(List.of());
                            vo.setEnvironmentConditions(List.of());
                            return vo;
                        }
                ));
    }

    /**
     * 批量获取问答求助数据VO
     */
    private Map<Long, MomentStructuredDataVO.QuestionDataVO> batchGetQuestionDataVO(List<Long> momentIds) {
        if (momentIds.isEmpty()) {
            return Map.of();
        }

        LambdaQueryWrapper<MomentQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MomentQuestion::getMomentId, momentIds);
        List<MomentQuestion> questionList = momentQuestionMapper.selectList(wrapper);

        return questionList.stream()
                .collect(Collectors.toMap(
                        MomentQuestion::getMomentId,
                        question -> {
                            MomentStructuredDataVO.QuestionDataVO vo = new MomentStructuredDataVO.QuestionDataVO();
                            vo.setQuestionTitle(question.getQuestionTitle());
                            vo.setQuestionType(null);
                            vo.setUrgencyLevel(null);
                            vo.setAttributes(question.getAttributes());
                            vo.setQuestionTags(List.of());
                            return vo;
                        }
                ));
    }
}