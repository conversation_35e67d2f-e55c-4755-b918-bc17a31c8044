package com.fishing.admin.service;

import com.fishing.admin.vo.moment.MomentDataExportVO;
import org.springframework.core.io.Resource;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 动态数据导出服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
public interface MomentDataExportService {

    /**
     * 导出所有结构化数据为Excel
     *
     * @return 导出文件资源
     */
    Resource exportAllStructuredDataToExcel();

    /**
     * 导出指定类型的结构化数据为Excel
     *
     * @param momentType 动态类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出文件资源
     */
    Resource exportStructuredDataByTypeToExcel(String momentType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 导出所有结构化数据为CSV
     *
     * @return 导出文件资源
     */
    Resource exportAllStructuredDataToCsv();

    /**
     * 导出指定类型的结构化数据为CSV
     *
     * @param momentType 动态类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出文件资源
     */
    Resource exportStructuredDataByTypeToCsv(String momentType, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取导出数据预览
     *
     * @param momentType 动态类型
     * @param limit 预览数量限制
     * @return 导出数据预览
     */
    List<MomentDataExportVO> previewExportData(String momentType, Integer limit);

    /**
     * 导出钓获分享数据统计报告
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出文件资源
     */
    Resource exportFishingCatchReport(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 导出装备使用统计报告
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 导出文件资源
     */
    Resource exportEquipmentReport(LocalDateTime startTime, LocalDateTime endTime);
}