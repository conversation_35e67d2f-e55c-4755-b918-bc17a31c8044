package com.fishing.admin.service.fishing.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.admin.dto.fish.FishTypeCreateDTO;
import com.fishing.admin.dto.fish.FishTypeQueryDTO;
import com.fishing.admin.dto.fish.FishTypeUpdateDTO;
import com.fishing.admin.service.cache.FishTypeCacheService;
import com.fishing.admin.service.fishing.biz.AdminFishTypeService;
import com.fishing.admin.vo.spot.AdminFishTypeVO;
import com.fishing.common.result.PageResult;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import com.fishing.domain.moment.MomentCaughtFish;
import com.fishing.domain.spot.FishType;
import com.fishing.domain.spot.SpotFishType;
import com.fishing.mapper.FishTypeMapper;
import com.fishing.mapper.MomentCaughtFishMapper;
import com.fishing.mapper.MomentMapper;
import com.fishing.mapper.SpotFishTypeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Admin鱼类管理服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@DataSource(DataSourceType.FISHING_BIZ)
@RequiredArgsConstructor
public class AdminFishTypeServiceImpl implements AdminFishTypeService {

    private final FishTypeMapper fishTypeMapper;
    private final SpotFishTypeMapper spotFishTypesMapper;
    private final MomentMapper momentMapper;
    private final MomentCaughtFishMapper momentCaughtFishMapper;
    private final FishTypeCacheService fishTypeCacheService;

    @Override
    @Cacheable(value = "fish-types", key = "'single:' + #id", unless = "#result == null")
    public AdminFishTypeVO getFishTypeById(Long id) {
        FishType fishType = fishTypeMapper.selectById(id);
        if (fishType == null) {
            return null;
        }

        // 查询统计信息
        Map<Long, Integer> spotCountMap = getSpotCountByFishTypeIds(List.of(id));
        Map<Long, Integer> momentCountMap = getMomentCountByFishTypeIds(List.of(id));

        return convertToAdminFishTypeVO(fishType, spotCountMap, momentCountMap);
    }

    @Override
    public PageResult<AdminFishTypeVO> getFishTypeList(FishTypeQueryDTO queryDTO) {
        // 设置默认分页参数
        if (queryDTO.getPageNum() == null || queryDTO.getPageNum() <= 0) {
            queryDTO.setPageNum(1);
        }
        if (queryDTO.getPageSize() == null || queryDTO.getPageSize() <= 0) {
            queryDTO.setPageSize(10);
        }

        // 构建查询条件
        LambdaQueryWrapper<FishType> queryWrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(queryDTO.getName())) {
            queryWrapper.like(FishType::getName, queryDTO.getName());
        }

        if (StringUtils.hasText(queryDTO.getKeyword())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(FishType::getName, queryDTO.getKeyword())
                    .or()
                    .like(FishType::getDescription, queryDTO.getKeyword())
            );
        }

        if (StringUtils.hasText(queryDTO.getSeason())) {
            switch (queryDTO.getSeason().toLowerCase()) {
                case "spring":
                    queryWrapper.eq(FishType::getSeasonSpring, true);
                    break;
                case "summer":
                    queryWrapper.eq(FishType::getSeasonSummer, true);
                    break;
                case "autumn":
                    queryWrapper.eq(FishType::getSeasonAutumn, true);
                    break;
                case "winter":
                    queryWrapper.eq(FishType::getSeasonWinter, true);
                    break;
            }
        }

        // 分页查询
        Page<FishType> page = new Page<>(queryDTO.getPageNum(), queryDTO.getPageSize());
        Page<FishType> result = fishTypeMapper.selectPage(page, queryWrapper);

        if (result.getRecords().isEmpty()) {
            return PageResult.empty();
        }

        // 获取鱼类ID列表
        List<Long> fishTypeIds = result.getRecords().stream()
                .map(FishType::getId)
                .collect(Collectors.toList());

        // 批量查询统计信息
        Map<Long, Integer> spotCountMap = getSpotCountByFishTypeIds(fishTypeIds);
        Map<Long, Integer> momentCountMap = getMomentCountByFishTypeIds(fishTypeIds);

        // 转换为VO
        List<AdminFishTypeVO> voList = result.getRecords().stream()
                .map(ft -> convertToAdminFishTypeVO(ft, spotCountMap, momentCountMap))
                .collect(Collectors.toList());

        return PageResult.of(voList, result.getTotal(), queryDTO.getPageNum(), queryDTO.getPageSize());
    }

    @Override
    @Cacheable(value = "fish-types", key = "'all'", unless = "#result.isEmpty()")
    public List<AdminFishTypeVO> getAllFishTypes() {
        List<FishType> fishTypes = fishTypeMapper.selectList(null);

        if (fishTypes.isEmpty()) {
            return List.of();
        }

        // 获取鱼类ID列表
        List<Long> fishTypeIds = fishTypes.stream()
                .map(FishType::getId)
                .collect(Collectors.toList());

        // 批量查询统计信息
        Map<Long, Integer> spotCountMap = getSpotCountByFishTypeIds(fishTypeIds);
        Map<Long, Integer> momentCountMap = getMomentCountByFishTypeIds(fishTypeIds);

        // 转换为VO并设置统计信息
        return fishTypes.stream()
                .map(ft -> convertToAdminFishTypeVO(ft, spotCountMap, momentCountMap))
                .collect(Collectors.toList());
    }

    @Override
    @Cacheable(value = "fish-types", key = "'all-lite'", unless = "#result.isEmpty()")
    public List<AdminFishTypeVO> getAllFishTypesLite() {
        List<FishType> fishTypes = fishTypeMapper.selectList(null);

        if (fishTypes.isEmpty()) {
            return List.of();
        }

        // 轻量级转换，不查询统计信息
        return fishTypes.stream()
                .map(this::convertToAdminFishTypeVOLite)
                .collect(Collectors.toList());
    }

    @Override
    @CacheEvict(value = "fish-types", allEntries = true)
    public boolean createFishType(FishTypeCreateDTO createDTO) {
        FishType fishType = new FishType();
        BeanUtils.copyProperties(createDTO, fishType);
        return fishTypeMapper.insert(fishType) > 0;
    }

    @Override
    @CacheEvict(value = "fish-types", allEntries = true)
    public boolean updateFishType(FishTypeUpdateDTO updateDTO) {
        FishType fishType = new FishType();
        BeanUtils.copyProperties(updateDTO, fishType);
        return fishTypeMapper.updateById(fishType) > 0;
    }

    @Override
    @CacheEvict(value = "fish-types", allEntries = true)
    public boolean deleteFishTypeByIds(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return false;
        }

        List<Long> idList = Arrays.asList(ids);
        return fishTypeMapper.deleteBatchIds(idList) > 0;
    }

    @Override
    @CacheEvict(value = "fish-types", allEntries = true)
    public boolean deleteFishTypeById(Long id) {
        return fishTypeMapper.deleteById(id) > 0;
    }

    @Override
    public boolean checkNameExists(String name, Long excludeId) {
        LambdaQueryWrapper<FishType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FishType::getName, name);

        if (excludeId != null) {
            wrapper.ne(FishType::getId, excludeId);
        }

        return fishTypeMapper.selectCount(wrapper) > 0;
    }

    @Override
    public void warmUpCache() {
        log.info("开始预热鱼类缓存");

        try {
            // 1. 从数据库获取所有鱼类数据（轻量级版本）
            List<AdminFishTypeVO> allFishTypes = getAllFishTypesLite();

            // 2. 通过缓存服务缓存全量数据
            List<AdminFishTypeVO> cachedAllFishTypes = fishTypeCacheService.cacheAllFishTypes(allFishTypes);
            log.info("预热全部鱼类缓存完成，数量: {}", cachedAllFishTypes.size());

            // 3. 预热季节性缓存
            String[] seasons = {"spring", "summer", "autumn", "winter"};
            for (String season : seasons) {
                List<AdminFishTypeVO> seasonFishTypes = filterFishTypesBySeason(cachedAllFishTypes, season);
                // 通过缓存服务存储季节性数据
                fishTypeCacheService.cacheSeasonFishTypes(season, seasonFishTypes);
                log.info("预热{}季节鱼类缓存完成，数量: {}", season, seasonFishTypes.size());
            }

            log.info("鱼类缓存预热完成");
        } catch (Exception e) {
            log.error("预热鱼类缓存失败", e);
            throw new RuntimeException("缓存预热失败", e);
        }
    }

    /**
     * 在内存中过滤季节性鱼类数据
     */
    private List<AdminFishTypeVO> filterFishTypesBySeason(List<AdminFishTypeVO> allFishTypes, String season) {
        return allFishTypes.stream()
                .filter(fishType -> isSeasonalFish(fishType, season))
                .sorted((a, b) -> {
                    if (a.getName() == null && b.getName() == null) return 0;
                    if (a.getName() == null) return 1;
                    if (b.getName() == null) return -1;
                    return a.getName().compareTo(b.getName());
                })
                .collect(Collectors.toList());
    }

    /**
     * 检查鱼类是否属于指定季节
     */
    private boolean isSeasonalFish(AdminFishTypeVO fishType, String season) {
        return switch (season.toLowerCase()) {
            case "spring" -> Boolean.TRUE.equals(fishType.getSeasonSpring());
            case "summer" -> Boolean.TRUE.equals(fishType.getSeasonSummer());
            case "autumn" -> Boolean.TRUE.equals(fishType.getSeasonAutumn());
            case "winter" -> Boolean.TRUE.equals(fishType.getSeasonWinter());
            default -> false;
        };
    }

    /**
     * 批量查询鱼类关联的钓点数量
     */
    private Map<Long, Integer> getSpotCountByFishTypeIds(List<Long> fishTypeIds) {
        if (fishTypeIds.isEmpty()) {
            return Map.of();
        }

        LambdaQueryWrapper<SpotFishType> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(SpotFishType::getFishTypeId, fishTypeIds);

        List<SpotFishType> spotFishTypes = spotFishTypesMapper.selectList(wrapper);

        return spotFishTypes.stream()
                .collect(Collectors.groupingBy(
                        SpotFishType::getFishTypeId,
                        Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)
                ));
    }

    /**
     * 批量查询鱼类相关的动态数量
     * 查询moment_caught_fish表中包含特定鱼类的动态数量
     */
    private Map<Long, Integer> getMomentCountByFishTypeIds(List<Long> fishTypeIds) {
        if (fishTypeIds.isEmpty()) {
            return Map.of();
        }

        Map<Long, Integer> momentCountMap = fishTypeIds.stream()
                .collect(Collectors.toMap(id -> id, id -> 0));

        try {
            // 直接从结构化数据表查询每个鱼类的动态数量
            for (Long fishTypeId : fishTypeIds) {
                LambdaQueryWrapper<MomentCaughtFish> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MomentCaughtFish::getFishTypeId, fishTypeId);

                long count = momentCaughtFishMapper.selectCount(wrapper);
                momentCountMap.put(fishTypeId, Math.toIntExact(count));
            }
        } catch (Exception e) {
            log.warn("查询鱼类相关动态数量失败: {}", e.getMessage());
            // 出现异常时返回默认值
        }

        return momentCountMap;
    }


    /**
     * 转换FishType为AdminFishTypeVO
     */
    private AdminFishTypeVO convertToAdminFishTypeVO(
            FishType fishType,
            Map<Long, Integer> spotCountMap,
            Map<Long, Integer> momentCountMap) {

        AdminFishTypeVO vo = new AdminFishTypeVO();
        BeanUtils.copyProperties(fishType, vo);

        // 设置统计信息
        vo.setSpotCount(spotCountMap.getOrDefault(fishType.getId(), 0));
        vo.setMomentCount(momentCountMap.getOrDefault(fishType.getId(), 0));

        // 设置默认值（这些字段在domain实体中不存在）
        vo.setCategory(null);
        vo.setIsCommon(false);
        vo.setSortOrder(0);

        return vo;
    }

    /**
     * 轻量级转换FishType为AdminFishTypeVO（不包含统计信息）
     */
    private AdminFishTypeVO convertToAdminFishTypeVOLite(FishType fishType) {
        AdminFishTypeVO vo = new AdminFishTypeVO();
        BeanUtils.copyProperties(fishType, vo);

        // 设置默认统计信息（不查询数据库）
        vo.setSpotCount(0);
        vo.setMomentCount(0);

        // 设置默认值（这些字段在domain实体中不存在）
        vo.setCategory(null);
        vo.setIsCommon(false);
        vo.setSortOrder(0);

        return vo;
    }
}