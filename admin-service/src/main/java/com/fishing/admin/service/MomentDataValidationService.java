package com.fishing.admin.service;

import com.fishing.admin.vo.validation.MomentDataValidationResult;

import java.util.List;

/**
 * 动态数据完整性验证服务
 * 
 * <AUTHOR>
 * @since 2025-01-18
 */
public interface MomentDataValidationService {

    /**
     * 验证所有动态数据的完整性
     * 
     * @return 验证结果报告
     */
    MomentDataValidationResult validateAllMomentData();

    /**
     * 验证指定动态的数据完整性
     * 
     * @param momentId 动态ID
     * @return 验证结果
     */
    MomentDataValidationResult validateMomentData(Long momentId);

    /**
     * 修复动态数据不一致问题
     * 
     * @param momentIds 需要修复的动态ID列表
     * @return 修复结果
     */
    MomentDataValidationResult repairMomentData(List<Long> momentIds);

    /**
     * 检查孤立的结构化数据
     * 
     * @return 孤立数据报告
     */
    MomentDataValidationResult checkOrphanedStructuredData();
}