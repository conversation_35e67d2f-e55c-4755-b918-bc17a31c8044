package com.fishing.admin.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fishing.admin.service.MomentDataValidationService;
import com.fishing.admin.vo.validation.MomentDataValidationResult;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import com.fishing.domain.moment.*;
import com.fishing.mapper.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态数据完整性验证服务实现
 * 
 * <AUTHOR>
 * @since 2025-01-18
 */
@Slf4j
@Service
@DataSource(DataSourceType.FISHING_BIZ)
@RequiredArgsConstructor
public class MomentDataValidationServiceImpl implements MomentDataValidationService {

    private final MomentMapper momentMapper;
    private final MomentFishingCatchMapper momentFishingCatchMapper;
    private final MomentEquipmentMapper momentEquipmentMapper;
    private final MomentTechniqueMapper momentTechniqueMapper;
    private final MomentQuestionMapper momentQuestionMapper;
    private final MomentCaughtFishMapper momentCaughtFishMapper;
    private final MomentImageMapper momentImageMapper;

    @Override
    public MomentDataValidationResult validateAllMomentData() {
        log.info("开始验证所有动态数据完整性");
        
        MomentDataValidationResult result = new MomentDataValidationResult();
        result.setValidationTime(LocalDateTime.now());
        result.setIsValid(true);
        
        List<MomentDataValidationResult.DataIntegrityIssue> issues = new ArrayList<>();
        Map<String, Integer> issuesByType = new HashMap<>();

        // 获取所有动态
        List<Moment> allMoments = momentMapper.selectList(null);
        result.setTotalMoments(allMoments.size());
        
        int validCount = 0;
        
        for (Moment moment : allMoments) {
            List<MomentDataValidationResult.DataIntegrityIssue> momentIssues = validateSingleMoment(moment);
            if (momentIssues.isEmpty()) {
                validCount++;
            } else {
                issues.addAll(momentIssues);
                result.setIsValid(false);
                
                // 统计问题类型
                for (MomentDataValidationResult.DataIntegrityIssue issue : momentIssues) {
                    issuesByType.merge(issue.getIssueType(), 1, Integer::sum);
                }
            }
        }
        
        result.setValidMoments(validCount);
        result.setInvalidMoments(allMoments.size() - validCount);
        result.setIssues(issues);
        result.setIssuesByType(issuesByType);
        
        // 检查孤立数据
        result.setOrphanedData(checkOrphanedData());
        
        // 生成修复建议
        result.setRepairSuggestions(generateRepairSuggestions(issues));
        
        log.info("数据验证完成，总计{}条动态，有效{}条，无效{}条", 
                allMoments.size(), validCount, allMoments.size() - validCount);
        
        return result;
    }

    @Override
    public MomentDataValidationResult validateMomentData(Long momentId) {
        MomentDataValidationResult result = new MomentDataValidationResult();
        result.setValidationTime(LocalDateTime.now());
        result.setTotalMoments(1);
        
        Moment moment = momentMapper.selectById(momentId);
        if (moment == null) {
            MomentDataValidationResult.DataIntegrityIssue issue = new MomentDataValidationResult.DataIntegrityIssue();
            issue.setMomentId(momentId);
            issue.setIssueType("MOMENT_NOT_FOUND");
            issue.setDescription("动态不存在");
            issue.setSeverity("CRITICAL");
            issue.setSuggestedAction("检查动态ID是否正确");
            
            result.setIsValid(false);
            result.setValidMoments(0);
            result.setInvalidMoments(1);
            result.setIssues(List.of(issue));
            return result;
        }
        
        List<MomentDataValidationResult.DataIntegrityIssue> issues = validateSingleMoment(moment);
        
        result.setIsValid(issues.isEmpty());
        result.setValidMoments(issues.isEmpty() ? 1 : 0);
        result.setInvalidMoments(issues.isEmpty() ? 0 : 1);
        result.setIssues(issues);
        
        return result;
    }

    @Override
    public MomentDataValidationResult repairMomentData(List<Long> momentIds) {
        MomentDataValidationResult result = new MomentDataValidationResult();
        result.setValidationTime(LocalDateTime.now());
        
        // TODO: 实现数据修复逻辑
        log.info("数据修复功能待实现，影响的动态ID: {}", momentIds);
        
        result.setIsValid(true);
        result.setRepairSuggestions(List.of());
        
        return result;
    }

    @Override
    public MomentDataValidationResult checkOrphanedStructuredData() {
        MomentDataValidationResult result = new MomentDataValidationResult();
        result.setValidationTime(LocalDateTime.now());
        result.setOrphanedData(checkOrphanedData());
        result.setIsValid(true);
        
        return result;
    }

    /**
     * 验证单个动态的数据完整性
     */
    private List<MomentDataValidationResult.DataIntegrityIssue> validateSingleMoment(Moment moment) {
        List<MomentDataValidationResult.DataIntegrityIssue> issues = new ArrayList<>();
        
        if (moment.getMomentType() == null) {
            issues.add(createIssue(moment.getId(), "MISSING_MOMENT_TYPE", 
                    "动态类型为空", "HIGH", List.of("momentType"), "设置正确的动态类型"));
            return issues;
        }

        try {
            Integer momentType = Integer.parseInt(moment.getMomentType().toString());
            
            switch (momentType) {
                case 1: // 钓获分享
                    issues.addAll(validateFishingCatchData(moment));
                    break;
                case 2: // 装备展示
                    issues.addAll(validateEquipmentData(moment));
                    break;
                case 3: // 技巧分享
                    issues.addAll(validateTechniqueData(moment));
                    break;
                case 4: // 问答求助
                    issues.addAll(validateQuestionData(moment));
                    break;
                default:
                    issues.add(createIssue(moment.getId(), "INVALID_MOMENT_TYPE", 
                            "无效的动态类型: " + momentType, "HIGH", 
                            List.of("momentType"), "使用有效的动态类型(1-4)"));
            }
        } catch (NumberFormatException e) {
            issues.add(createIssue(moment.getId(), "INVALID_MOMENT_TYPE_FORMAT", 
                    "动态类型格式错误: " + moment.getMomentType(), "HIGH", 
                    List.of("momentType"), "使用数字格式的动态类型"));
        }
        
        return issues;
    }

    /**
     * 验证钓获分享数据
     */
    private List<MomentDataValidationResult.DataIntegrityIssue> validateFishingCatchData(Moment moment) {
        List<MomentDataValidationResult.DataIntegrityIssue> issues = new ArrayList<>();
        
        LambdaQueryWrapper<MomentFishingCatch> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentFishingCatch::getMomentId, moment.getId());
        MomentFishingCatch fishingCatch = momentFishingCatchMapper.selectOne(wrapper);
        
        if (fishingCatch == null) {
            issues.add(createIssue(moment.getId(), "MISSING_FISHING_CATCH_DATA", 
                    "钓获分享动态缺少结构化数据", "CRITICAL", 
                    List.of("fishingCatchData"), "创建对应的钓获分享数据记录"));
        } else {
            // 验证必要字段
            if (fishingCatch.getTotalWeight() == null || fishingCatch.getTotalWeight().doubleValue() < 0) {
                issues.add(createIssue(moment.getId(), "INVALID_TOTAL_WEIGHT", 
                        "总重量数据无效", "MEDIUM", 
                        List.of("totalWeight"), "设置有效的总重量"));
            }
            
            // 验证捕获鱼类数据
            LambdaQueryWrapper<MomentCaughtFish> fishWrapper = new LambdaQueryWrapper<>();
            fishWrapper.eq(MomentCaughtFish::getFishingCatchId, fishingCatch.getId());
            List<MomentCaughtFish> caughtFishes = momentCaughtFishMapper.selectList(fishWrapper);
            
            if (caughtFishes.isEmpty()) {
                issues.add(createIssue(moment.getId(), "NO_CAUGHT_FISH_DATA", 
                        "钓获分享动态没有捕获鱼类数据", "LOW", 
                        List.of("caughtFishes"), "添加捕获鱼类信息"));
            }
        }
        
        return issues;
    }

    /**
     * 验证装备展示数据
     */
    private List<MomentDataValidationResult.DataIntegrityIssue> validateEquipmentData(Moment moment) {
        List<MomentDataValidationResult.DataIntegrityIssue> issues = new ArrayList<>();
        
        LambdaQueryWrapper<MomentEquipment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentEquipment::getMomentId, moment.getId());
        MomentEquipment equipment = momentEquipmentMapper.selectOne(wrapper);
        
        if (equipment == null) {
            issues.add(createIssue(moment.getId(), "MISSING_EQUIPMENT_DATA", 
                    "装备展示动态缺少结构化数据", "CRITICAL", 
                    List.of("equipmentData"), "创建对应的装备展示数据记录"));
        } else {
            if (equipment.getEquipmentName() == null || equipment.getEquipmentName().trim().isEmpty()) {
                issues.add(createIssue(moment.getId(), "MISSING_EQUIPMENT_NAME", 
                        "装备名称为空", "HIGH", 
                        List.of("equipmentName"), "设置装备名称"));
            }
            
            if (equipment.getRating() != null && (equipment.getRating() < 1 || equipment.getRating() > 5)) {
                issues.add(createIssue(moment.getId(), "INVALID_EQUIPMENT_RATING", 
                        "装备评分超出有效范围(1-5)", "MEDIUM", 
                        List.of("rating"), "设置1-5星的有效评分"));
            }
        }
        
        return issues;
    }

    /**
     * 验证技巧分享数据
     */
    private List<MomentDataValidationResult.DataIntegrityIssue> validateTechniqueData(Moment moment) {
        List<MomentDataValidationResult.DataIntegrityIssue> issues = new ArrayList<>();
        
        LambdaQueryWrapper<MomentTechnique> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentTechnique::getMomentId, moment.getId());
        MomentTechnique technique = momentTechniqueMapper.selectOne(wrapper);
        
        if (technique == null) {
            issues.add(createIssue(moment.getId(), "MISSING_TECHNIQUE_DATA", 
                    "技巧分享动态缺少结构化数据", "CRITICAL", 
                    List.of("techniqueData"), "创建对应的技巧分享数据记录"));
        } else {
            if (technique.getTechniqueName() == null || technique.getTechniqueName().trim().isEmpty()) {
                issues.add(createIssue(moment.getId(), "MISSING_TECHNIQUE_NAME", 
                        "技巧名称为空", "HIGH", 
                        List.of("techniqueName"), "设置技巧名称"));
            }
            
            if (technique.getDifficulty() != null && (technique.getDifficulty() < 1 || technique.getDifficulty() > 3)) {
                issues.add(createIssue(moment.getId(), "INVALID_DIFFICULTY", 
                        "难度等级超出有效范围(1-3)", "MEDIUM", 
                        List.of("difficulty"), "设置1-3级的有效难度"));
            }
        }
        
        return issues;
    }

    /**
     * 验证问答求助数据
     */
    private List<MomentDataValidationResult.DataIntegrityIssue> validateQuestionData(Moment moment) {
        List<MomentDataValidationResult.DataIntegrityIssue> issues = new ArrayList<>();
        
        LambdaQueryWrapper<MomentQuestion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MomentQuestion::getMomentId, moment.getId());
        MomentQuestion question = momentQuestionMapper.selectOne(wrapper);
        
        if (question == null) {
            issues.add(createIssue(moment.getId(), "MISSING_QUESTION_DATA", 
                    "问答求助动态缺少结构化数据", "CRITICAL", 
                    List.of("questionData"), "创建对应的问答求助数据记录"));
        } else {
            if (question.getQuestionTitle() == null || question.getQuestionTitle().trim().isEmpty()) {
                issues.add(createIssue(moment.getId(), "MISSING_QUESTION_TITLE", 
                        "问题标题为空", "HIGH", 
                        List.of("questionTitle"), "设置问题标题"));
            }
            
            if (question.getIsResolved() != null && question.getIsResolved() != 0 && question.getIsResolved() != 1) {
                issues.add(createIssue(moment.getId(), "INVALID_RESOLVED_STATUS", 
                        "解决状态值无效", "LOW", 
                        List.of("isResolved"), "设置0(未解决)或1(已解决)"));
            }
        }
        
        return issues;
    }

    /**
     * 检查孤立数据
     */
    private MomentDataValidationResult.OrphanedDataStats checkOrphanedData() {
        MomentDataValidationResult.OrphanedDataStats stats = new MomentDataValidationResult.OrphanedDataStats();
        
        // 检查孤立的钓获分享数据
        List<MomentFishingCatch> allFishingCatches = momentFishingCatchMapper.selectList(null);
        Set<Long> validMomentIds = momentMapper.selectList(null).stream().map(Moment::getId).collect(Collectors.toSet());
        
        stats.setOrphanedFishingCatch((int) allFishingCatches.stream()
                .filter(fc -> !validMomentIds.contains(fc.getMomentId()))
                .count());
        
        // 检查孤立的装备展示数据
        List<MomentEquipment> allEquipments = momentEquipmentMapper.selectList(null);
        stats.setOrphanedEquipment((int) allEquipments.stream()
                .filter(eq -> !validMomentIds.contains(eq.getMomentId()))
                .count());
        
        // 检查孤立的技巧分享数据
        List<MomentTechnique> allTechniques = momentTechniqueMapper.selectList(null);
        stats.setOrphanedTechnique((int) allTechniques.stream()
                .filter(tech -> !validMomentIds.contains(tech.getMomentId()))
                .count());
        
        // 检查孤立的问答求助数据
        List<MomentQuestion> allQuestions = momentQuestionMapper.selectList(null);
        stats.setOrphanedQuestion((int) allQuestions.stream()
                .filter(q -> !validMomentIds.contains(q.getMomentId()))
                .count());
        
        // 检查孤立的捕获鱼类数据
        List<MomentCaughtFish> allCaughtFishes = momentCaughtFishMapper.selectList(null);
        Set<Long> validFishingCatchIds = allFishingCatches.stream().map(MomentFishingCatch::getId).collect(Collectors.toSet());
        stats.setOrphanedCaughtFish((int) allCaughtFishes.stream()
                .filter(cf -> !validFishingCatchIds.contains(cf.getFishingCatchId()))
                .count());
        
        // 检查孤立的图片数据
        List<MomentImage> allImages = momentImageMapper.selectList(null);
        stats.setOrphanedImages((int) allImages.stream()
                .filter(img -> !validMomentIds.contains(img.getMomentId()))
                .count());
        
        return stats;
    }

    /**
     * 生成修复建议
     */
    private List<MomentDataValidationResult.RepairSuggestion> generateRepairSuggestions(
            List<MomentDataValidationResult.DataIntegrityIssue> issues) {
        
        Map<String, List<Long>> issueGroups = issues.stream()
                .collect(Collectors.groupingBy(
                        MomentDataValidationResult.DataIntegrityIssue::getIssueType,
                        Collectors.mapping(MomentDataValidationResult.DataIntegrityIssue::getMomentId, Collectors.toList())
                ));
        
        List<MomentDataValidationResult.RepairSuggestion> suggestions = new ArrayList<>();
        
        for (Map.Entry<String, List<Long>> entry : issueGroups.entrySet()) {
            MomentDataValidationResult.RepairSuggestion suggestion = new MomentDataValidationResult.RepairSuggestion();
            suggestion.setSuggestionType(entry.getKey());
            suggestion.setAffectedMomentIds(entry.getValue());
            
            switch (entry.getKey()) {
                case "MISSING_FISHING_CATCH_DATA":
                    suggestion.setDescription("为钓获分享动态创建缺失的结构化数据");
                    suggestion.setCanAutoRepair(false);
                    suggestion.setRepairAction("手动为这些动态创建moment_fishing_catch记录");
                    break;
                case "MISSING_EQUIPMENT_DATA":
                    suggestion.setDescription("为装备展示动态创建缺失的结构化数据");
                    suggestion.setCanAutoRepair(false);
                    suggestion.setRepairAction("手动为这些动态创建moment_equipment记录");
                    break;
                case "MISSING_TECHNIQUE_DATA":
                    suggestion.setDescription("为技巧分享动态创建缺失的结构化数据");
                    suggestion.setCanAutoRepair(false);
                    suggestion.setRepairAction("手动为这些动态创建moment_technique记录");
                    break;
                case "MISSING_QUESTION_DATA":
                    suggestion.setDescription("为问答求助动态创建缺失的结构化数据");
                    suggestion.setCanAutoRepair(false);
                    suggestion.setRepairAction("手动为这些动态创建moment_question记录");
                    break;
                default:
                    suggestion.setDescription("修复" + entry.getKey() + "类型的问题");
                    suggestion.setCanAutoRepair(false);
                    suggestion.setRepairAction("需要根据具体问题手动修复");
            }
            
            suggestions.add(suggestion);
        }
        
        return suggestions;
    }

    /**
     * 创建数据完整性问题对象
     */
    private MomentDataValidationResult.DataIntegrityIssue createIssue(Long momentId, String issueType, 
                                                                      String description, String severity, 
                                                                      List<String> affectedFields, String suggestedAction) {
        MomentDataValidationResult.DataIntegrityIssue issue = new MomentDataValidationResult.DataIntegrityIssue();
        issue.setMomentId(momentId);
        issue.setIssueType(issueType);
        issue.setDescription(description);
        issue.setSeverity(severity);
        issue.setAffectedFields(affectedFields);
        issue.setSuggestedAction(suggestedAction);
        return issue;
    }
}