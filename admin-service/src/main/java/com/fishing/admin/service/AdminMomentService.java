package com.fishing.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fishing.admin.dto.moment.AdminMomentQueryDTO;
import com.fishing.admin.dto.moment.BatchMomentAuditRequest;
import com.fishing.admin.vo.moment.AdminMomentListVO;
import com.fishing.admin.vo.moment.AdminMomentStatsResponse;
import com.fishing.admin.vo.moment.AdminMomentVO;
import com.fishing.admin.vo.moment.MomentStructuredDataVO;
import com.fishing.domain.moment.Moment;

import java.util.List;

/**
 * Admin动态管理服务接口
 *
 * <AUTHOR>
 * @since 2025-07-01
 */
public interface AdminMomentService extends IService<Moment> {

    /**
     * 查询动态基础列表（新版本，支持完整查询条件）
     */
    List<Moment> selectMomentList(AdminMomentQueryDTO queryDTO);

    /**
     * 获取管理端动态列表（查询+转换一体化）
     */
    List<AdminMomentListVO> getAdminMomentList(AdminMomentQueryDTO queryDTO);

    /**
     * 获取动态详情
     */
    AdminMomentVO getMomentDetail(Long id, Long currentUserId);

    /**
     * 批量删除动态
     */
    boolean deleteMomentByIds(Long[] ids);

    /**
     * 批量审核动态
     */
    boolean batchAuditMoments(BatchMomentAuditRequest request);

    /**
     * 批量设置动态可见性
     */
    boolean setMomentVisibility(Long[] ids, String visibility, Long operatorId);

    /**
     * 获取动态统计信息
     */
    AdminMomentStatsResponse getMomentStats();

    /**
     * 转换动态列表为管理端VO（优化版）
     */
    List<AdminMomentListVO> convertToAdminMomentListVOOptimized(List<Moment> moments);

    /**
     * 获取用户动态数量
     *
     * @param userId 用户ID
     * @return 动态数量
     */
    int getUserMomentCount(Long userId);

    /**
     * 获取动态的结构化数据
     *
     * @param momentId 动态ID
     * @return 结构化数据响应对象
     */
    MomentStructuredDataVO getMomentStructuredData(Long momentId);

    /**
     * 批量获取动态的结构化数据
     *
     * @param momentIds 动态ID列表
     * @return 结构化数据响应列表
     */
    List<MomentStructuredDataVO> batchGetMomentStructuredData(List<Long> momentIds);
}