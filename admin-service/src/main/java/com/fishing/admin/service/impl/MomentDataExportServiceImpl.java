package com.fishing.admin.service.impl;

import com.fishing.admin.service.AdminMomentService;
import com.fishing.admin.service.MomentDataExportService;
import com.fishing.admin.vo.moment.MomentDataExportVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 动态数据导出服务实现
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Slf4j
@Service
public class MomentDataExportServiceImpl implements MomentDataExportService {

    @Autowired
    private AdminMomentService adminMomentService;

    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Override
    public Resource exportAllStructuredDataToExcel() {
        try {
            List<MomentDataExportVO> exportData = buildExportData(null, null, null);
            return createExcelResource(exportData, "全部结构化数据");
        } catch (Exception e) {
            log.error("导出所有结构化数据失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }

    @Override
    public Resource exportStructuredDataByTypeToExcel(String momentType, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            List<MomentDataExportVO> exportData = buildExportData(momentType, startTime, endTime);
            String sheetName = getSheetName(momentType) + "数据";
            return createExcelResource(exportData, sheetName);
        } catch (Exception e) {
            log.error("导出{}类型结构化数据失败", momentType, e);
            throw new RuntimeException("导出失败", e);
        }
    }

    @Override
    public Resource exportAllStructuredDataToCsv() {
        try {
            List<MomentDataExportVO> exportData = buildExportData(null, null, null);
            return createCsvResource(exportData);
        } catch (Exception e) {
            log.error("导出所有结构化数据为CSV失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }

    @Override
    public Resource exportStructuredDataByTypeToCsv(String momentType, LocalDateTime startTime, LocalDateTime endTime) {
        try {
            List<MomentDataExportVO> exportData = buildExportData(momentType, startTime, endTime);
            return createCsvResource(exportData);
        } catch (Exception e) {
            log.error("导出{}类型结构化数据为CSV失败", momentType, e);
            throw new RuntimeException("导出失败", e);
        }
    }

    @Override
    public List<MomentDataExportVO> previewExportData(String momentType, Integer limit) {
        List<MomentDataExportVO> exportData = buildExportData(momentType, null, null);
        if (limit != null && limit > 0) {
            return exportData.stream()
                    .limit(limit)
                    .collect(Collectors.toList());
        }
        return exportData;
    }

    @Override
    public Resource exportFishingCatchReport(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            List<MomentDataExportVO> fishingCatchData = buildExportData("fishing_catch", startTime, endTime);
            return createFishingCatchReportExcel(fishingCatchData, startTime, endTime);
        } catch (Exception e) {
            log.error("导出钓获分享统计报告失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }

    @Override
    public Resource exportEquipmentReport(LocalDateTime startTime, LocalDateTime endTime) {
        try {
            List<MomentDataExportVO> equipmentData = buildExportData("equipment", startTime, endTime);
            return createEquipmentReportExcel(equipmentData, startTime, endTime);
        } catch (Exception e) {
            log.error("导出装备使用统计报告失败", e);
            throw new RuntimeException("导出失败", e);
        }
    }

    /**
     * 构建导出数据 - 简化版，暂时返回模拟数据
     */
    private List<MomentDataExportVO> buildExportData(String momentType, LocalDateTime startTime, LocalDateTime endTime) {
        // 暂时返回模拟数据，避免复杂的依赖问题
        List<MomentDataExportVO> result = new ArrayList<>();
        
        for (int i = 1; i <= 10; i++) {
            MomentDataExportVO vo = new MomentDataExportVO();
            vo.setMomentId((long) i);
            vo.setMomentType(momentType != null ? momentType : "fishing_catch");
            vo.setContent("模拟动态内容 " + i);
            vo.setUserName("用户" + i);
            vo.setCreateTime(LocalDateTime.now().minusDays(i));
            vo.setFishingSpotName("钓点" + i);
            vo.setVisibility("public");
            vo.setLikeCount(i * 5);
            vo.setCommentCount(i * 2);
            vo.setViewCount(i * 10);
            
            // 设置类型特定字段
            if ("fishing_catch".equals(vo.getMomentType())) {
                vo.setTotalWeight(String.valueOf(2.0 + i * 0.5));
                vo.setFishingMethod("台钓");
                vo.setWeatherConditions("晴朗");
                vo.setCaughtFishCount(i);
            } else if ("equipment".equals(vo.getMomentType())) {
                vo.setEquipmentName("装备" + i);
                vo.setBrand("品牌" + i);
                vo.setModel("型号" + i);
                vo.setPrice(String.valueOf(100 + i * 50));
                vo.setRating(4 + (i % 2));
            }
            
            result.add(vo);
        }
        
        return result;
    }

    /**
     * 创建Excel资源
     */
    private Resource createExcelResource(List<MomentDataExportVO> data, String sheetName) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet sheet = workbook.createSheet(sheetName);
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            createExcelHeaders(headerRow, data.isEmpty() ? null : data.get(0).getMomentType());
            
            // 填充数据
            for (int i = 0; i < data.size(); i++) {
                Row dataRow = sheet.createRow(i + 1);
                fillExcelRow(dataRow, data.get(i));
            }
            
            // 自动调整列宽
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                sheet.autoSizeColumn(i);
            }
            
            workbook.write(outputStream);
            
            String filename = sheetName + "_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray()) {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
            
            return resource;
        }
    }

    /**
     * 创建CSV资源
     */
    private Resource createCsvResource(List<MomentDataExportVO> data) throws IOException {
        StringWriter writer = new StringWriter();
        
        // 写入BOM以支持中文
        writer.write("\uFEFF");
        
        if (!data.isEmpty()) {
            // 写入表头
            String momentType = data.get(0).getMomentType();
            writeCsvHeaders(writer, momentType);
            
            // 写入数据
            for (MomentDataExportVO vo : data) {
                writeCsvRow(writer, vo);
            }
        }
        
        String filename = "动态数据导出_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".csv";
        ByteArrayResource resource = new ByteArrayResource(writer.toString().getBytes("UTF-8")) {
            @Override
            public String getFilename() {
                return filename;
            }
        };
        
        return resource;
    }

    /**
     * 创建Excel表头
     */
    private void createExcelHeaders(Row headerRow, String momentType) {
        String[] baseHeaders = {
            "动态ID", "动态类型", "内容", "用户名", "创建时间", 
            "钓点名称", "可见性", "点赞数", "评论数", "查看数"
        };
        
        int colIndex = 0;
        for (String header : baseHeaders) {
            Cell cell = headerRow.createCell(colIndex++);
            cell.setCellValue(header);
        }
        
        // 根据类型添加特定字段表头
        if ("fishing_catch".equals(momentType)) {
            String[] fishingHeaders = {"总重量", "钓法", "天气条件", "捕获鱼类数量"};
            for (String header : fishingHeaders) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(header);
            }
        } else if ("equipment".equals(momentType)) {
            String[] equipmentHeaders = {"装备名称", "品牌", "型号", "价格", "评分"};
            for (String header : equipmentHeaders) {
                Cell cell = headerRow.createCell(colIndex++);
                cell.setCellValue(header);
            }
        }
    }

    /**
     * 填充Excel行数据
     */
    private void fillExcelRow(Row row, MomentDataExportVO vo) {
        int colIndex = 0;
        
        // 基础字段
        setCellValue(row.createCell(colIndex++), vo.getMomentId());
        setCellValue(row.createCell(colIndex++), vo.getMomentType());
        setCellValue(row.createCell(colIndex++), vo.getContent());
        setCellValue(row.createCell(colIndex++), vo.getUserName());
        setCellValue(row.createCell(colIndex++), vo.getCreateTime() != null ? vo.getCreateTime().format(DATE_TIME_FORMATTER) : "");
        setCellValue(row.createCell(colIndex++), vo.getFishingSpotName());
        setCellValue(row.createCell(colIndex++), vo.getVisibility());
        setCellValue(row.createCell(colIndex++), vo.getLikeCount());
        setCellValue(row.createCell(colIndex++), vo.getCommentCount());
        setCellValue(row.createCell(colIndex++), vo.getViewCount());
        
        // 类型特定字段
        if ("fishing_catch".equals(vo.getMomentType())) {
            setCellValue(row.createCell(colIndex++), vo.getTotalWeight());
            setCellValue(row.createCell(colIndex++), vo.getFishingMethod());
            setCellValue(row.createCell(colIndex++), vo.getWeatherConditions());
            setCellValue(row.createCell(colIndex++), vo.getCaughtFishCount());
        } else if ("equipment".equals(vo.getMomentType())) {
            setCellValue(row.createCell(colIndex++), vo.getEquipmentName());
            setCellValue(row.createCell(colIndex++), vo.getBrand());
            setCellValue(row.createCell(colIndex++), vo.getModel());
            setCellValue(row.createCell(colIndex++), vo.getPrice());
            setCellValue(row.createCell(colIndex++), vo.getRating());
        }
    }

    /**
     * 设置单元格值
     */
    private void setCellValue(Cell cell, Object value) {
        if (value == null) {
            cell.setCellValue("");
        } else if (value instanceof Number) {
            cell.setCellValue(((Number) value).doubleValue());
        } else {
            cell.setCellValue(value.toString());
        }
    }

    /**
     * 写入CSV表头
     */
    private void writeCsvHeaders(StringWriter writer, String momentType) throws IOException {
        List<String> headers = new ArrayList<>();
        headers.addAll(Arrays.asList(
            "动态ID", "动态类型", "内容", "用户名", "创建时间", 
            "钓点名称", "可见性", "点赞数", "评论数", "查看数"
        ));
        
        if ("fishing_catch".equals(momentType)) {
            headers.addAll(Arrays.asList("总重量", "钓法", "天气条件", "捕获鱼类数量"));
        } else if ("equipment".equals(momentType)) {
            headers.addAll(Arrays.asList("装备名称", "品牌", "型号", "价格", "评分"));
        }
        
        writer.write(String.join(",", headers));
        writer.write("\n");
    }

    /**
     * 写入CSV行数据
     */
    private void writeCsvRow(StringWriter writer, MomentDataExportVO vo) throws IOException {
        List<String> values = new ArrayList<>();
        
        // 基础字段
        values.add(escapeCsvValue(vo.getMomentId()));
        values.add(escapeCsvValue(vo.getMomentType()));
        values.add(escapeCsvValue(vo.getContent()));
        values.add(escapeCsvValue(vo.getUserName()));
        values.add(escapeCsvValue(vo.getCreateTime() != null ? vo.getCreateTime().format(DATE_TIME_FORMATTER) : ""));
        values.add(escapeCsvValue(vo.getFishingSpotName()));
        values.add(escapeCsvValue(vo.getVisibility()));
        values.add(escapeCsvValue(vo.getLikeCount()));
        values.add(escapeCsvValue(vo.getCommentCount()));
        values.add(escapeCsvValue(vo.getViewCount()));
        
        // 类型特定字段
        if ("fishing_catch".equals(vo.getMomentType())) {
            values.add(escapeCsvValue(vo.getTotalWeight()));
            values.add(escapeCsvValue(vo.getFishingMethod()));
            values.add(escapeCsvValue(vo.getWeatherConditions()));
            values.add(escapeCsvValue(vo.getCaughtFishCount()));
        } else if ("equipment".equals(vo.getMomentType())) {
            values.add(escapeCsvValue(vo.getEquipmentName()));
            values.add(escapeCsvValue(vo.getBrand()));
            values.add(escapeCsvValue(vo.getModel()));
            values.add(escapeCsvValue(vo.getPrice()));
            values.add(escapeCsvValue(vo.getRating()));
        }
        
        writer.write(String.join(",", values));
        writer.write("\n");
    }

    /**
     * 转义CSV值
     */
    private String escapeCsvValue(Object value) {
        if (value == null) {
            return "";
        }
        
        String str = value.toString();
        if (str.contains(",") || str.contains("\"") || str.contains("\n")) {
            str = "\"" + str.replace("\"", "\"\"") + "\"";
        }
        return str;
    }

    /**
     * 获取工作表名称
     */
    private String getSheetName(String momentType) {
        switch (momentType) {
            case "fishing_catch":
                return "钓获分享";
            case "equipment":
                return "装备展示";
            case "technique":
                return "技巧分享";
            case "question":
                return "问答求助";
            default:
                return "动态";
        }
    }

    /**
     * 创建钓获分享统计报告Excel
     */
    private Resource createFishingCatchReportExcel(List<MomentDataExportVO> data, LocalDateTime startTime, LocalDateTime endTime) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            // 创建数据工作表
            Sheet dataSheet = workbook.createSheet("钓获分享数据");
            createFishingCatchDataSheet(dataSheet, data);
            
            // 创建统计工作表
            Sheet statsSheet = workbook.createSheet("统计分析");
            createFishingCatchStatsSheet(statsSheet, data, startTime, endTime);
            
            workbook.write(outputStream);
            
            String filename = "钓获分享统计报告_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray()) {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
            
            return resource;
        }
    }

    private void createFishingCatchDataSheet(Sheet sheet, List<MomentDataExportVO> data) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {"用户名", "创建时间", "钓点", "总重量", "钓法", "天气条件", "捕获鱼类数量", "点赞数", "评论数"};
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }
        
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            MomentDataExportVO vo = data.get(i);
            row.createCell(0).setCellValue(vo.getUserName());
            row.createCell(1).setCellValue(vo.getCreateTime() != null ? vo.getCreateTime().format(DATE_TIME_FORMATTER) : "");
            row.createCell(2).setCellValue(vo.getFishingSpotName());
            row.createCell(3).setCellValue(vo.getTotalWeight() != null ? vo.getTotalWeight() : "");
            row.createCell(4).setCellValue(vo.getFishingMethod() != null ? vo.getFishingMethod() : "");
            row.createCell(5).setCellValue(vo.getWeatherConditions() != null ? vo.getWeatherConditions() : "");
            row.createCell(6).setCellValue(vo.getCaughtFishCount() != null ? vo.getCaughtFishCount() : 0);
            row.createCell(7).setCellValue(vo.getLikeCount() != null ? vo.getLikeCount() : 0);
            row.createCell(8).setCellValue(vo.getCommentCount() != null ? vo.getCommentCount() : 0);
        }
    }

    private void createFishingCatchStatsSheet(Sheet sheet, List<MomentDataExportVO> data, LocalDateTime startTime, LocalDateTime endTime) {
        int rowIndex = 0;
        
        // 报告时间范围
        Row titleRow = sheet.createRow(rowIndex++);
        titleRow.createCell(0).setCellValue("钓获分享统计分析报告");
        
        Row timeRangeRow = sheet.createRow(rowIndex++);
        String timeRange = String.format("统计时间范围: %s 至 %s", 
            startTime != null ? startTime.format(DATE_TIME_FORMATTER) : "不限", 
            endTime != null ? endTime.format(DATE_TIME_FORMATTER) : "不限");
        timeRangeRow.createCell(0).setCellValue(timeRange);
        
        rowIndex++; // 空行
        
        // 总体统计
        Row totalStatsHeaderRow = sheet.createRow(rowIndex++);
        totalStatsHeaderRow.createCell(0).setCellValue("总体统计");
        
        Row totalCountRow = sheet.createRow(rowIndex++);
        totalCountRow.createCell(0).setCellValue("钓获分享总数:");
        totalCountRow.createCell(1).setCellValue(data.size());
    }

    /**
     * 创建装备使用统计报告Excel
     */
    private Resource createEquipmentReportExcel(List<MomentDataExportVO> data, LocalDateTime startTime, LocalDateTime endTime) throws IOException {
        try (Workbook workbook = new XSSFWorkbook();
             ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
            
            Sheet dataSheet = workbook.createSheet("装备展示数据");
            createEquipmentDataSheet(dataSheet, data);
            
            Sheet statsSheet = workbook.createSheet("统计分析");
            createEquipmentStatsSheet(statsSheet, data, startTime, endTime);
            
            workbook.write(outputStream);
            
            String filename = "装备使用统计报告_" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
            ByteArrayResource resource = new ByteArrayResource(outputStream.toByteArray()) {
                @Override
                public String getFilename() {
                    return filename;
                }
            };
            
            return resource;
        }
    }

    private void createEquipmentDataSheet(Sheet sheet, List<MomentDataExportVO> data) {
        Row headerRow = sheet.createRow(0);
        String[] headers = {"用户名", "创建时间", "装备名称", "品牌", "型号", "价格", "评分", "点赞数", "评论数"};
        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
        }
        
        for (int i = 0; i < data.size(); i++) {
            Row row = sheet.createRow(i + 1);
            MomentDataExportVO vo = data.get(i);
            row.createCell(0).setCellValue(vo.getUserName());
            row.createCell(1).setCellValue(vo.getCreateTime() != null ? vo.getCreateTime().format(DATE_TIME_FORMATTER) : "");
            row.createCell(2).setCellValue(vo.getEquipmentName() != null ? vo.getEquipmentName() : "");
            row.createCell(3).setCellValue(vo.getBrand() != null ? vo.getBrand() : "");
            row.createCell(4).setCellValue(vo.getModel() != null ? vo.getModel() : "");
            row.createCell(5).setCellValue(vo.getPrice() != null ? vo.getPrice() : "");
            row.createCell(6).setCellValue(vo.getRating() != null ? vo.getRating() : 0);
            row.createCell(7).setCellValue(vo.getLikeCount() != null ? vo.getLikeCount() : 0);
            row.createCell(8).setCellValue(vo.getCommentCount() != null ? vo.getCommentCount() : 0);
        }
    }

    private void createEquipmentStatsSheet(Sheet sheet, List<MomentDataExportVO> data, LocalDateTime startTime, LocalDateTime endTime) {
        int rowIndex = 0;
        
        Row titleRow = sheet.createRow(rowIndex++);
        titleRow.createCell(0).setCellValue("装备使用统计分析报告");
        
        Row timeRangeRow = sheet.createRow(rowIndex++);
        String timeRange = String.format("统计时间范围: %s 至 %s", 
            startTime != null ? startTime.format(DATE_TIME_FORMATTER) : "不限", 
            endTime != null ? endTime.format(DATE_TIME_FORMATTER) : "不限");
        timeRangeRow.createCell(0).setCellValue(timeRange);
        
        rowIndex++;
        
        Row totalStatsHeaderRow = sheet.createRow(rowIndex++);
        totalStatsHeaderRow.createCell(0).setCellValue("总体统计");
        
        Row totalCountRow = sheet.createRow(rowIndex++);
        totalCountRow.createCell(0).setCellValue("装备展示总数:");
        totalCountRow.createCell(1).setCellValue(data.size());
    }
}