-- =====================================================
-- 动态数据迁移脚本
-- 从 JSON 结构迁移到结构化表
-- =====================================================

-- 设置安全模式和编码
SET SQL_SAFE_UPDATES = 0;
SET NAMES utf8mb4;

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 移除 moment 表的 type_specific_data 字段
-- =====================================================
ALTER TABLE moment DROP COLUMN IF EXISTS type_specific_data;

-- =====================================================
-- 2. 创建临时迁移日志表
-- =====================================================
CREATE TEMPORARY TABLE IF NOT EXISTS migration_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    moment_id BIGINT,
    old_type VARCHAR(50),
    new_type TINYINT,
    status VARCHAR(20),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =====================================================
-- 3. 迁移钓获分享 (fishing_catch) 数据
-- =====================================================
DELIMITER $$

CREATE PROCEDURE migrate_fishing_catch_data()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_moment_id BIGINT;
    DECLARE v_json_data JSON;
    DECLARE v_error_msg TEXT;
    
    -- 声明游标
    DECLARE moment_cursor CURSOR FOR 
        SELECT id, JSON_EXTRACT(type_specific_data, '$') 
        FROM moment 
        WHERE moment_type = 1;  -- fishing_catch
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 v_error_msg = MESSAGE_TEXT;
        INSERT INTO migration_log (moment_id, old_type, new_type, status, error_message)
        VALUES (v_moment_id, 'fishing_catch', 1, 'ERROR', v_error_msg);
    END;
    
    OPEN moment_cursor;
    
    read_loop: LOOP
        FETCH moment_cursor INTO v_moment_id, v_json_data;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 插入主记录到 moment_fishing_catch
        INSERT INTO moment_fishing_catch (
            moment_id,
            total_weight,
            fishing_method,
            bait_used,
            weather_conditions,
            water_temperature,
            water_depth,
            fishing_duration,
            create_time
        )
        SELECT 
            v_moment_id,
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.totalWeight')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.fishingMethod')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.baitUsed')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.weatherConditions')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.waterTemperature')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.waterDepth')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.fishingDuration')),
            NOW();
        
        -- 迁移捕获的鱼类数据
        IF JSON_CONTAINS_PATH(v_json_data, 'one', '$.caughtFishes') THEN
            INSERT INTO moment_caught_fish (
                fishing_catch_id,
                fish_type_id,
                count,
                weight,
                size,
                remark,
                create_time
            )
            SELECT 
                (SELECT id FROM moment_fishing_catch WHERE moment_id = v_moment_id),
                JSON_UNQUOTE(JSON_EXTRACT(fish, '$.fishTypeId')),
                JSON_UNQUOTE(JSON_EXTRACT(fish, '$.count')),
                JSON_UNQUOTE(JSON_EXTRACT(fish, '$.weight')),
                JSON_UNQUOTE(JSON_EXTRACT(fish, '$.size')),
                JSON_UNQUOTE(JSON_EXTRACT(fish, '$.remark')),
                NOW()
            FROM JSON_TABLE(
                JSON_EXTRACT(v_json_data, '$.caughtFishes'),
                '$[*]' COLUMNS (
                    fish JSON PATH '$'
                )
            ) AS jt;
        END IF;
        
        -- 迁移图片数据
        IF JSON_CONTAINS_PATH(v_json_data, 'one', '$.images') THEN
            INSERT INTO moment_catch_image (
                fishing_catch_id,
                image_url,
                display_order,
                create_time
            )
            SELECT 
                (SELECT id FROM moment_fishing_catch WHERE moment_id = v_moment_id),
                JSON_UNQUOTE(JSON_EXTRACT(image, '$.url')),
                ROW_NUMBER() OVER (ORDER BY (SELECT NULL)),
                NOW()
            FROM JSON_TABLE(
                JSON_EXTRACT(v_json_data, '$.images'),
                '$[*]' COLUMNS (
                    image JSON PATH '$'
                )
            ) AS jt;
        END IF;
        
        -- 记录成功
        INSERT INTO migration_log (moment_id, old_type, new_type, status)
        VALUES (v_moment_id, 'fishing_catch', 1, 'SUCCESS');
        
    END LOOP;
    
    CLOSE moment_cursor;
END$$

-- =====================================================
-- 4. 迁移装备展示 (equipment) 数据
-- =====================================================
CREATE PROCEDURE migrate_equipment_data()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_moment_id BIGINT;
    DECLARE v_json_data JSON;
    DECLARE v_error_msg TEXT;
    
    DECLARE moment_cursor CURSOR FOR 
        SELECT id, JSON_EXTRACT(type_specific_data, '$') 
        FROM moment 
        WHERE moment_type = 2;  -- equipment
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 v_error_msg = MESSAGE_TEXT;
        INSERT INTO migration_log (moment_id, old_type, new_type, status, error_message)
        VALUES (v_moment_id, 'equipment', 2, 'ERROR', v_error_msg);
    END;
    
    OPEN moment_cursor;
    
    read_loop: LOOP
        FETCH moment_cursor INTO v_moment_id, v_json_data;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 插入装备记录
        INSERT INTO moment_equipment (
            moment_id,
            equipment_name,
            equipment_type,
            brand,
            model,
            price,
            purchase_link,
            rating,
            pros,
            cons,
            usage_tips,
            create_time
        )
        SELECT 
            v_moment_id,
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.equipmentName')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.equipmentType')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.brand')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.model')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.price')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.purchaseLink')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.rating')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.pros')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.cons')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.usageTips')),
            NOW();
        
        -- 迁移目标鱼种
        IF JSON_CONTAINS_PATH(v_json_data, 'one', '$.targetFishTypes') THEN
            INSERT INTO moment_equipment_fish_type (
                equipment_id,
                fish_type_id,
                create_time
            )
            SELECT 
                (SELECT id FROM moment_equipment WHERE moment_id = v_moment_id),
                JSON_UNQUOTE(JSON_EXTRACT(fish_type, '$')),
                NOW()
            FROM JSON_TABLE(
                JSON_EXTRACT(v_json_data, '$.targetFishTypes'),
                '$[*]' COLUMNS (
                    fish_type JSON PATH '$'
                )
            ) AS jt;
        END IF;
        
        -- 记录成功
        INSERT INTO migration_log (moment_id, old_type, new_type, status)
        VALUES (v_moment_id, 'equipment', 2, 'SUCCESS');
        
    END LOOP;
    
    CLOSE moment_cursor;
END$$

-- =====================================================
-- 5. 迁移技巧分享 (technique) 数据
-- =====================================================
CREATE PROCEDURE migrate_technique_data()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_moment_id BIGINT;
    DECLARE v_json_data JSON;
    DECLARE v_error_msg TEXT;
    
    DECLARE moment_cursor CURSOR FOR 
        SELECT id, JSON_EXTRACT(type_specific_data, '$') 
        FROM moment 
        WHERE moment_type = 3;  -- technique
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 v_error_msg = MESSAGE_TEXT;
        INSERT INTO migration_log (moment_id, old_type, new_type, status, error_message)
        VALUES (v_moment_id, 'technique', 3, 'ERROR', v_error_msg);
    END;
    
    OPEN moment_cursor;
    
    read_loop: LOOP
        FETCH moment_cursor INTO v_moment_id, v_json_data;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 插入技巧记录
        INSERT INTO moment_technique (
            moment_id,
            technique_name,
            difficulty,
            tools_required,
            best_time,
            success_rate,
            detailed_steps,
            common_mistakes,
            video_url,
            create_time
        )
        SELECT 
            v_moment_id,
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.techniqueName')),
            CASE JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.difficulty'))
                WHEN 'beginner' THEN 1
                WHEN 'intermediate' THEN 2
                WHEN 'advanced' THEN 3
                ELSE 1
            END,
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.toolsRequired')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.bestTime')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.successRate')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.detailedSteps')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.commonMistakes')),
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.videoUrl')),
            NOW();
        
        -- 迁移适用环境
        IF JSON_CONTAINS_PATH(v_json_data, 'one', '$.suitableEnvironments') THEN
            INSERT INTO moment_technique_environment (
                technique_id,
                environment,
                create_time
            )
            SELECT 
                (SELECT id FROM moment_technique WHERE moment_id = v_moment_id),
                JSON_UNQUOTE(JSON_EXTRACT(env, '$')),
                NOW()
            FROM JSON_TABLE(
                JSON_EXTRACT(v_json_data, '$.suitableEnvironments'),
                '$[*]' COLUMNS (
                    env JSON PATH '$'
                )
            ) AS jt;
        END IF;
        
        -- 迁移目标鱼种
        IF JSON_CONTAINS_PATH(v_json_data, 'one', '$.targetFishTypes') THEN
            INSERT INTO moment_technique_fish_type (
                technique_id,
                fish_type_id,
                create_time
            )
            SELECT 
                (SELECT id FROM moment_technique WHERE moment_id = v_moment_id),
                JSON_UNQUOTE(JSON_EXTRACT(fish_type, '$')),
                NOW()
            FROM JSON_TABLE(
                JSON_EXTRACT(v_json_data, '$.targetFishTypes'),
                '$[*]' COLUMNS (
                    fish_type JSON PATH '$'
                )
            ) AS jt;
        END IF;
        
        -- 记录成功
        INSERT INTO migration_log (moment_id, old_type, new_type, status)
        VALUES (v_moment_id, 'technique', 3, 'SUCCESS');
        
    END LOOP;
    
    CLOSE moment_cursor;
END$$

-- =====================================================
-- 6. 迁移求助问答 (question) 数据
-- =====================================================
CREATE PROCEDURE migrate_question_data()
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE v_moment_id BIGINT;
    DECLARE v_json_data JSON;
    DECLARE v_error_msg TEXT;
    DECLARE v_question_id BIGINT;
    
    DECLARE moment_cursor CURSOR FOR 
        SELECT id, JSON_EXTRACT(type_specific_data, '$') 
        FROM moment 
        WHERE moment_type = 4;  -- question
    
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = TRUE;
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        GET DIAGNOSTICS CONDITION 1 v_error_msg = MESSAGE_TEXT;
        INSERT INTO migration_log (moment_id, old_type, new_type, status, error_message)
        VALUES (v_moment_id, 'question', 4, 'ERROR', v_error_msg);
    END;
    
    OPEN moment_cursor;
    
    read_loop: LOOP
        FETCH moment_cursor INTO v_moment_id, v_json_data;
        IF done THEN
            LEAVE read_loop;
        END IF;
        
        -- 插入问题记录
        INSERT INTO moment_question (
            moment_id,
            question_title,
            urgency,
            status,
            best_answer_id,
            create_time
        )
        SELECT 
            v_moment_id,
            JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.questionTitle')),
            CASE JSON_UNQUOTE(JSON_EXTRACT(v_json_data, '$.urgency'))
                WHEN 'low' THEN 1
                WHEN 'medium' THEN 2
                WHEN 'high' THEN 3
                ELSE 2
            END,
            1,  -- 默认为未解决
            NULL,
            NOW();
        
        SET v_question_id = LAST_INSERT_ID();
        
        -- 迁移标签
        IF JSON_CONTAINS_PATH(v_json_data, 'one', '$.tags') THEN
            -- 首先确保标签存在
            INSERT INTO tag (name, category, create_time)
            SELECT DISTINCT
                JSON_UNQUOTE(JSON_EXTRACT(tag, '$')),
                'question',
                NOW()
            FROM JSON_TABLE(
                JSON_EXTRACT(v_json_data, '$.tags'),
                '$[*]' COLUMNS (
                    tag JSON PATH '$'
                )
            ) AS jt
            ON DUPLICATE KEY UPDATE id = id;
            
            -- 创建标签关联
            INSERT INTO moment_question_tag (
                question_id,
                tag_id,
                create_time
            )
            SELECT 
                v_question_id,
                t.id,
                NOW()
            FROM JSON_TABLE(
                JSON_EXTRACT(v_json_data, '$.tags'),
                '$[*]' COLUMNS (
                    tag_name VARCHAR(100) PATH '$'
                )
            ) AS jt
            INNER JOIN tag t ON t.name = jt.tag_name;
        END IF;
        
        -- 记录成功
        INSERT INTO migration_log (moment_id, old_type, new_type, status)
        VALUES (v_moment_id, 'question', 4, 'SUCCESS');
        
    END LOOP;
    
    CLOSE moment_cursor;
END$$

DELIMITER ;

-- =====================================================
-- 7. 执行迁移
-- =====================================================
-- 调用各个迁移存储过程
CALL migrate_fishing_catch_data();
CALL migrate_equipment_data();
CALL migrate_technique_data();
CALL migrate_question_data();

-- =====================================================
-- 8. 生成迁移报告
-- =====================================================
SELECT 
    old_type as '原类型',
    new_type as '新类型代码',
    COUNT(*) as '记录数',
    SUM(CASE WHEN status = 'SUCCESS' THEN 1 ELSE 0 END) as '成功数',
    SUM(CASE WHEN status = 'ERROR' THEN 1 ELSE 0 END) as '失败数',
    GROUP_CONCAT(DISTINCT error_message SEPARATOR '; ') as '错误信息'
FROM migration_log
GROUP BY old_type, new_type;

-- =====================================================
-- 9. 清理
-- =====================================================
DROP PROCEDURE IF EXISTS migrate_fishing_catch_data;
DROP PROCEDURE IF EXISTS migrate_equipment_data;
DROP PROCEDURE IF EXISTS migrate_technique_data;
DROP PROCEDURE IF EXISTS migrate_question_data;

-- 提交事务
COMMIT;

-- =====================================================
-- 10. 数据验证查询
-- =====================================================
-- 验证钓获数据迁移
SELECT 
    'fishing_catch' as type,
    COUNT(DISTINCT mfc.moment_id) as moment_count,
    COUNT(DISTINCT mcf.id) as caught_fish_count,
    COUNT(DISTINCT mci.id) as image_count
FROM moment_fishing_catch mfc
LEFT JOIN moment_caught_fish mcf ON mcf.fishing_catch_id = mfc.id
LEFT JOIN moment_catch_image mci ON mci.fishing_catch_id = mfc.id

UNION ALL

-- 验证装备数据迁移
SELECT 
    'equipment' as type,
    COUNT(DISTINCT me.moment_id) as moment_count,
    COUNT(DISTINCT meft.id) as fish_type_count,
    0 as image_count
FROM moment_equipment me
LEFT JOIN moment_equipment_fish_type meft ON meft.equipment_id = me.id

UNION ALL

-- 验证技巧数据迁移
SELECT 
    'technique' as type,
    COUNT(DISTINCT mt.moment_id) as moment_count,
    COUNT(DISTINCT mtft.id) as fish_type_count,
    COUNT(DISTINCT mte.id) as environment_count
FROM moment_technique mt
LEFT JOIN moment_technique_fish_type mtft ON mtft.technique_id = mt.id
LEFT JOIN moment_technique_environment mte ON mte.technique_id = mt.id

UNION ALL

-- 验证问答数据迁移
SELECT 
    'question' as type,
    COUNT(DISTINCT mq.moment_id) as moment_count,
    COUNT(DISTINCT mqt.id) as tag_count,
    0 as image_count
FROM moment_question mq
LEFT JOIN moment_question_tag mqt ON mqt.question_id = mq.id;

-- 恢复安全模式
SET SQL_SAFE_UPDATES = 1;