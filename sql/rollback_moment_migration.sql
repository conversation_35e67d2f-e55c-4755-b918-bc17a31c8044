-- =====================================================
-- 动态数据迁移回滚脚本
-- 用于撤销结构化表迁移，恢复到 JSON 结构
-- =====================================================

-- 设置安全模式和编码
SET SQL_SAFE_UPDATES = 0;
SET NAMES utf8mb4;

-- 开始事务
START TRANSACTION;

-- =====================================================
-- 1. 重新添加 type_specific_data 字段到 moment 表
-- =====================================================
ALTER TABLE moment 
ADD COLUMN IF NOT EXISTS type_specific_data JSON 
COMMENT '类型特定数据（JSON格式）' 
AFTER visibility;

-- =====================================================
-- 2. 恢复钓获分享数据到 JSON
-- =====================================================
UPDATE moment m
INNER JOIN moment_fishing_catch mfc ON m.id = mfc.moment_id
SET m.type_specific_data = JSON_OBJECT(
    'totalWeight', mfc.total_weight,
    'fishingMethod', mfc.fishing_method,
    'baitUsed', mfc.bait_used,
    'weatherConditions', mfc.weather_conditions,
    'waterTemperature', mfc.water_temperature,
    'waterDepth', mfc.water_depth,
    'fishingDuration', mfc.fishing_duration,
    'caughtFishes', (
        SELECT JSON_ARRAYAGG(
            JSON_OBJECT(
                'fishTypeId', mcf.fish_type_id,
                'count', mcf.count,
                'weight', mcf.weight,
                'size', mcf.size,
                'remark', mcf.remark
            )
        )
        FROM moment_caught_fish mcf
        WHERE mcf.fishing_catch_id = mfc.id
    ),
    'images', (
        SELECT JSON_ARRAYAGG(
            JSON_OBJECT(
                'url', mci.image_url,
                'order', mci.display_order
            )
        )
        FROM moment_catch_image mci
        WHERE mci.fishing_catch_id = mfc.id
    )
)
WHERE m.moment_type = 1;

-- =====================================================
-- 3. 恢复装备展示数据到 JSON
-- =====================================================
UPDATE moment m
INNER JOIN moment_equipment me ON m.id = me.moment_id
SET m.type_specific_data = JSON_OBJECT(
    'equipmentName', me.equipment_name,
    'equipmentType', me.equipment_type,
    'brand', me.brand,
    'model', me.model,
    'price', me.price,
    'purchaseLink', me.purchase_link,
    'rating', me.rating,
    'pros', me.pros,
    'cons', me.cons,
    'usageTips', me.usage_tips,
    'targetFishTypes', (
        SELECT JSON_ARRAYAGG(meft.fish_type_id)
        FROM moment_equipment_fish_type meft
        WHERE meft.equipment_id = me.id
    )
)
WHERE m.moment_type = 2;

-- =====================================================
-- 4. 恢复技巧分享数据到 JSON
-- =====================================================
UPDATE moment m
INNER JOIN moment_technique mt ON m.id = mt.moment_id
SET m.type_specific_data = JSON_OBJECT(
    'techniqueName', mt.technique_name,
    'difficulty', CASE mt.difficulty
        WHEN 1 THEN 'beginner'
        WHEN 2 THEN 'intermediate'
        WHEN 3 THEN 'advanced'
        ELSE 'beginner'
    END,
    'toolsRequired', mt.tools_required,
    'bestTime', mt.best_time,
    'successRate', mt.success_rate,
    'detailedSteps', mt.detailed_steps,
    'commonMistakes', mt.common_mistakes,
    'videoUrl', mt.video_url,
    'suitableEnvironments', (
        SELECT JSON_ARRAYAGG(mte.environment)
        FROM moment_technique_environment mte
        WHERE mte.technique_id = mt.id
    ),
    'targetFishTypes', (
        SELECT JSON_ARRAYAGG(mtft.fish_type_id)
        FROM moment_technique_fish_type mtft
        WHERE mtft.technique_id = mt.id
    )
)
WHERE m.moment_type = 3;

-- =====================================================
-- 5. 恢复求助问答数据到 JSON
-- =====================================================
UPDATE moment m
INNER JOIN moment_question mq ON m.id = mq.moment_id
SET m.type_specific_data = JSON_OBJECT(
    'questionTitle', mq.question_title,
    'urgency', CASE mq.urgency
        WHEN 1 THEN 'low'
        WHEN 2 THEN 'medium'
        WHEN 3 THEN 'high'
        ELSE 'medium'
    END,
    'tags', (
        SELECT JSON_ARRAYAGG(t.name)
        FROM moment_question_tag mqt
        INNER JOIN tag t ON mqt.tag_id = t.id
        WHERE mqt.question_id = mq.id
    )
)
WHERE m.moment_type = 4;

-- =====================================================
-- 6. 删除结构化表中的数据（可选）
-- 注意：这将永久删除数据，请谨慎操作
-- =====================================================
-- 如果确定要删除结构化表中的数据，取消下面的注释

/*
-- 删除钓获相关数据
DELETE mci FROM moment_catch_image mci
INNER JOIN moment_fishing_catch mfc ON mci.fishing_catch_id = mfc.id;

DELETE mcf FROM moment_caught_fish mcf
INNER JOIN moment_fishing_catch mfc ON mcf.fishing_catch_id = mfc.id;

DELETE FROM moment_fishing_catch;

-- 删除装备相关数据
DELETE FROM moment_equipment_fish_type;
DELETE FROM moment_equipment;

-- 删除技巧相关数据
DELETE FROM moment_technique_fish_type;
DELETE FROM moment_technique_environment;
DELETE FROM moment_technique;

-- 删除问答相关数据
DELETE FROM moment_question_tag;
DELETE FROM moment_question;
*/

-- =====================================================
-- 7. 验证回滚结果
-- =====================================================
SELECT 
    moment_type,
    CASE moment_type
        WHEN 1 THEN '钓获分享'
        WHEN 2 THEN '装备展示'
        WHEN 3 THEN '技巧分享'
        WHEN 4 THEN '求助问答'
    END as type_name,
    COUNT(*) as total_count,
    SUM(CASE WHEN type_specific_data IS NOT NULL THEN 1 ELSE 0 END) as restored_count,
    SUM(CASE WHEN type_specific_data IS NULL THEN 1 ELSE 0 END) as failed_count
FROM moment
WHERE moment_type IN (1, 2, 3, 4)
GROUP BY moment_type;

-- 提交事务
COMMIT;

-- 恢复安全模式
SET SQL_SAFE_UPDATES = 1;

-- =====================================================
-- 8. 回滚后的清理建议
-- =====================================================
/*
如果回滚成功，可以考虑：
1. 删除新创建的结构化表（如果不再需要）
2. 删除相关的索引和外键约束
3. 更新应用程序代码回到使用 JSON 的版本

删除表的示例命令（谨慎使用）：
DROP TABLE IF EXISTS moment_catch_image;
DROP TABLE IF EXISTS moment_caught_fish;
DROP TABLE IF EXISTS moment_fishing_catch;
DROP TABLE IF EXISTS moment_equipment_fish_type;
DROP TABLE IF EXISTS moment_equipment;
DROP TABLE IF EXISTS moment_technique_fish_type;
DROP TABLE IF EXISTS moment_technique_environment;
DROP TABLE IF EXISTS moment_technique;
DROP TABLE IF EXISTS moment_question_tag;
DROP TABLE IF EXISTS moment_question;
*/