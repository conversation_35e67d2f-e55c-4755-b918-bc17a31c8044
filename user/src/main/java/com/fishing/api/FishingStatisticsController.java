package com.fishing.api;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.config.CurrentUser;
import com.fishing.domain.moment.Moment;
import com.fishing.dto.statistics.FishingStatisticsQueryDTO;
import com.fishing.user.service.MomentService;
import com.fishing.user.service.SpotCheckinService;
import com.fishing.user.service.IUserInfoService;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.statistics.*;
import com.fishing.vo.moment.MomentVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * C端钓鱼统计Controller
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
@Tag(name = "C端-钓鱼统计")
@RestController
@RequestMapping("/api/user/fishing")
@RequiredArgsConstructor
@Validated
public class FishingStatisticsController {

    private final MomentService momentService;
    private final SpotCheckinService spotCheckinService;
    private final IUserInfoService userInfoService;

    /**
     * 获取钓鱼统计概览
     * GET /api/user/fishing/statistics
     */
    @Operation(summary = "获取钓鱼统计概览")
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<FishingStatisticsVO>> getStatistics(
            @Parameter(description = "时间范围", example = "week")
            @RequestParam(defaultValue = "week") String timeRange,
            @CurrentUser Long userId) {
        
        try {
            FishingStatisticsVO statistics = buildFishingStatistics(timeRange, userId);
            return ResponseEntity.ok(ApiResponse.success(statistics));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "获取钓鱼统计失败: " + e.getMessage()));
        }
    }

    /**
     * 获取出钓记录
     * GET /api/user/fishing/trip-records
     */
    @Operation(summary = "获取出钓记录")
    @GetMapping("/trip-records")
    public ResponseEntity<ApiResponse<List<TripRecordVO>>> getTripRecords(
            @Parameter(description = "时间范围") @RequestParam(required = false) String timeRange,
            @Parameter(description = "数量限制") @RequestParam(defaultValue = "20") @Min(1) @Max(100) Integer limit,
            @Parameter(description = "偏移量") @RequestParam(defaultValue = "0") @Min(0) Integer offset,
            @CurrentUser Long userId) {
        
        try {
            FishingStatisticsQueryDTO queryDTO = new FishingStatisticsQueryDTO();
            queryDTO.setTimeRange(timeRange != null ? timeRange : "week");
            queryDTO.setLimit(limit);
            queryDTO.setOffset(offset);
            
            List<TripRecordVO> tripRecords = buildTripRecords(queryDTO, userId);
            return ResponseEntity.ok(ApiResponse.success(tripRecords));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "获取出钓记录失败: " + e.getMessage()));
        }
    }

    /**
     * 获取装备使用统计
     * GET /api/user/fishing/equipment-stats
     */
    @Operation(summary = "获取装备使用统计")
    @GetMapping("/equipment-stats")
    public ResponseEntity<ApiResponse<List<EquipmentUsageVO>>> getEquipmentStats(
            @Parameter(description = "装备分类") @RequestParam(required = false) String category,
            @CurrentUser Long userId) {
        
        try {
            List<EquipmentUsageVO> equipmentStats = buildEquipmentStats(category, userId);
            return ResponseEntity.ok(ApiResponse.success(equipmentStats));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "获取装备统计失败: " + e.getMessage()));
        }
    }

    /**
     * 获取钓获趋势数据
     * GET /api/user/fishing/catch-trend
     */
    @Operation(summary = "获取钓获趋势数据")
    @GetMapping("/catch-trend")
    public ResponseEntity<ApiResponse<List<CatchTrendVO>>> getCatchTrend(
            @Parameter(description = "时间范围") @RequestParam(defaultValue = "week") String timeRange,
            @CurrentUser Long userId) {
        
        try {
            List<CatchTrendVO> catchTrend = buildCatchTrend(timeRange, userId);
            return ResponseEntity.ok(ApiResponse.success(catchTrend));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "获取钓获趋势失败: " + e.getMessage()));
        }
    }

    /**
     * 获取鱼种分布数据
     * GET /api/user/fishing/fish-species
     */
    @Operation(summary = "获取鱼种分布数据")
    @GetMapping("/fish-species")
    public ResponseEntity<ApiResponse<List<FishSpeciesVO>>> getFishSpeciesDistribution(
            @Parameter(description = "时间范围") @RequestParam(required = false) String timeRange,
            @CurrentUser Long userId) {
        
        try {
            List<FishSpeciesVO> fishSpecies = buildFishSpeciesDistribution(timeRange, userId);
            return ResponseEntity.ok(ApiResponse.success(fishSpecies));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "获取鱼种分布失败: " + e.getMessage()));
        }
    }

    /**
     * 获取钓点使用统计
     * GET /api/user/fishing/spot-stats
     */
    @Operation(summary = "获取钓点使用统计")
    @GetMapping("/spot-stats")
    public ResponseEntity<ApiResponse<List<SpotStatisticsVO>>> getSpotStatistics(
            @Parameter(description = "时间范围") @RequestParam(required = false) String timeRange,
            @Parameter(description = "返回数量") @RequestParam(defaultValue = "10") @Min(1) @Max(50) Integer limit,
            @CurrentUser Long userId) {
        
        try {
            List<SpotStatisticsVO> spotStats = buildSpotStatistics(timeRange, limit, userId);
            return ResponseEntity.ok(ApiResponse.success(spotStats));
        } catch (Exception e) {
            return ResponseEntity.ok(ApiResponse.error(500, "获取钓点统计失败: " + e.getMessage()));
        }
    }

    // ========== 私有方法：业务逻辑实现 ==========

    private FishingStatisticsVO buildFishingStatistics(String timeRange, Long userId) {
        // 计算时间范围
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = calculateStartTime(endTime, timeRange);
        
        // 获取用户钓鱼相关动态（钓获分享类型）
        Page<Moment> momentPage = new Page<>(1, 1000); // 获取足够多的数据用于统计
        Page<MomentVO> userMomentsPage = momentService.getUserMoments(userId, momentPage, userId);
        
        // 过滤时间范围内的钓鱼相关动态
        List<MomentVO> fishingMoments = userMomentsPage.getRecords().stream()
                .filter(moment -> {
                    LocalDateTime createdAt = moment.getCreateTime();
                    return createdAt != null && 
                           !createdAt.isBefore(startTime) && 
                           !createdAt.isAfter(endTime) &&
                           ("catch_share".equals(moment.getMomentType()) || 
                            "fishing_trip".equals(moment.getMomentType()));
                })
                .collect(Collectors.toList());
        
        // 统计数据
        int totalTrips = fishingMoments.size();
        int totalCatch = 0;
        double totalWeight = 0.0;
        int successfulTrips = 0;
        
        for (MomentVO moment : fishingMoments) {
            // 从动态的内容中提取钓获信息（这里可以根据实际的MomentVO结构调整）
            Map<String, Object> fishingData = extractFishingDataFromVO(moment);
            if (fishingData != null) {
                Integer catchCount = (Integer) fishingData.get("catchCount");
                Double weight = (Double) fishingData.get("weight");
                
                if (catchCount != null && catchCount > 0) {
                    totalCatch += catchCount;
                    successfulTrips++;
                    
                    if (weight != null) {
                        totalWeight += weight;
                    }
                }
            }
        }
        
        // 计算成功率
        double successRate = totalTrips > 0 ? ((double) successfulTrips / totalTrips) * 100 : 0.0;
        
        // 计算平均每次钓获
        double averageCatchPerTrip = totalTrips > 0 ? (double) totalCatch / totalTrips : 0.0;
        
        return FishingStatisticsVO.builder()
                .totalCatch(totalCatch)
                .totalTrips(totalTrips)
                .totalWeight(Math.round(totalWeight * 10.0) / 10.0)
                .timeRange(timeRange)
                .lastUpdated(LocalDateTime.now())
                .averageCatchPerTrip(Math.round(averageCatchPerTrip * 10.0) / 10.0)
                .successRate(Math.round(successRate * 10.0) / 10.0)
                .build();
    }

    private List<TripRecordVO> buildTripRecords(FishingStatisticsQueryDTO queryDTO, Long userId) {
        // 计算时间范围
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = calculateStartTime(endTime, queryDTO.getTimeRange());
        
        // 获取用户钓鱼动态，支持分页
        Page<Moment> momentPage = new Page<>(
            queryDTO.getOffset() / queryDTO.getLimit() + 1, 
            queryDTO.getLimit()
        );
        Page<MomentVO> userMomentsPage = momentService.getUserMoments(userId, momentPage, userId);
        
        // 过滤时间范围内的钓鱼相关动态
        List<MomentVO> fishingMoments = userMomentsPage.getRecords().stream()
                .filter(moment -> {
                    LocalDateTime createdAt = moment.getCreateTime();
                    return createdAt != null && 
                           !createdAt.isBefore(startTime) && 
                           !createdAt.isAfter(endTime) &&
                           ("catch_share".equals(moment.getMomentType()) || 
                            "fishing_trip".equals(moment.getMomentType()));
                })
                .collect(Collectors.toList());
        
        List<TripRecordVO> records = new ArrayList<>();
        
        for (MomentVO moment : fishingMoments) {
            Map<String, Object> fishingData = extractFishingDataFromVO(moment);
            
            TripRecordVO record = TripRecordVO.builder()
                    .id(moment.getId())
                    .date(moment.getCreateTime().toLocalDate())
                    .location(getLocationFromMoment(moment))
                    .duration(getDurationFromMoment(moment))
                    .catchCount(getCatchCountFromData(fishingData))
                    .result(evaluateTripResult(fishingData))
                    .weight(getWeightFromData(fishingData))
                    .fishTypes(getFishTypesFromData(fishingData))
                    .notes(moment.getContent())
                    .weather(getWeatherFromData(fishingData))
                    .temperature(getTemperatureFromData(fishingData))
                    .build();
                    
            records.add(record);
        }
        
        return records;
    }

    private List<EquipmentUsageVO> buildEquipmentStats(String category, Long userId) {
        // 获取用户装备展示类型的动态
        Page<Moment> momentPage = new Page<>(1, 1000);
        Page<MomentVO> userMomentsPage = momentService.getUserMoments(userId, momentPage, userId);
        
        // 过滤装备相关动态
        List<MomentVO> equipmentMoments = userMomentsPage.getRecords().stream()
                .filter(moment -> "equipment_show".equals(moment.getMomentType()))
                .collect(Collectors.toList());
        
        // 统计装备使用情况
        Map<String, EquipmentStats> equipmentStatsMap = new HashMap<>();
        
        for (MomentVO moment : equipmentMoments) {
            Map<String, Object> equipmentData = extractFishingDataFromVO(moment);
            if (equipmentData != null) {
                processEquipmentData(equipmentStatsMap, equipmentData, moment.getCreateTime());
            }
        }
        
        // 转换为VO列表
        List<EquipmentUsageVO> result = new ArrayList<>();
        for (Map.Entry<String, EquipmentStats> entry : equipmentStatsMap.entrySet()) {
            EquipmentStats stats = entry.getValue();
            
            // 如果指定了分类，只返回该分类的装备
            if (category != null && !category.equals(stats.category)) {
                continue;
            }
            
            EquipmentUsageVO vo = EquipmentUsageVO.builder()
                    .id(stats.id)
                    .name(stats.name)
                    .category(stats.category)
                    .usagePercentage(calculateUsagePercentage(stats.usageCount, equipmentMoments.size()))
                    .usageCount(stats.usageCount)
                    .lastUsed(stats.lastUsed)
                    .brand(stats.brand)
                    .model(stats.model)
                    .build();
                    
            result.add(vo);
        }
        
        return result;
    }

    private List<CatchTrendVO> buildCatchTrend(String timeRange, Long userId) {
        // 计算时间范围
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = calculateStartTime(endTime, timeRange);
        
        List<CatchTrendVO> trendData = new ArrayList<>();
        
        if ("week".equals(timeRange)) {
            // 按天统计
            for (int i = 6; i >= 0; i--) {
                LocalDate date = LocalDate.now().minusDays(i);
                // 使用现有方法统计当天数据
                Page<Moment> dayPage = new Page<>(1, 1000);
                Page<MomentVO> dayMomentsPage = momentService.getUserMoments(userId, dayPage, userId);
                
                // 过滤当天的钓鱼动态
                List<MomentVO> dayMoments = dayMomentsPage.getRecords().stream()
                        .filter(moment -> moment.getCreateTime() != null &&
                                        moment.getCreateTime().toLocalDate().equals(date) &&
                                        ("catch_share".equals(moment.getMomentType()) || 
                                         "fishing_trip".equals(moment.getMomentType())))
                        .collect(Collectors.toList());
                
                int catchCount = 0;
                double weight = 0.0;
                int tripCount = dayMoments.size();
                
                for (MomentVO moment : dayMoments) {
                    Map<String, Object> fishingData = extractFishingDataFromVO(moment);
                    Integer count = (Integer) fishingData.get("catchCount");
                    Double w = (Double) fishingData.get("weight");
                    if (count != null) catchCount += count;
                    if (w != null) weight += w;
                }
                
                trendData.add(CatchTrendVO.builder()
                        .date(formatDateLabel(date, "week"))
                        .count(catchCount)
                        .weight(weight)
                        .tripCount(tripCount)
                        .build());
            }
        } else if ("month".equals(timeRange)) {
            // 按周统计
            for (int i = 3; i >= 0; i--) {
                LocalDate weekStart = LocalDate.now().minusWeeks(i);
                // 使用现有方法统计一周数据
                LocalDate weekEnd = weekStart.plusDays(6);
                Page<Moment> weekPage = new Page<>(1, 1000);
                Page<MomentVO> weekMomentsPage = momentService.getUserMoments(userId, weekPage, userId);
                
                // 过滤一周内的钓鱼动态
                List<MomentVO> weekMoments = weekMomentsPage.getRecords().stream()
                        .filter(moment -> {
                            LocalDate momentDate = moment.getCreateTime().toLocalDate();
                            return moment.getCreateTime() != null &&
                                   !momentDate.isBefore(weekStart) && 
                                   !momentDate.isAfter(weekEnd) &&
                                   ("catch_share".equals(moment.getMomentType()) || 
                                    "fishing_trip".equals(moment.getMomentType()));
                        })
                        .collect(Collectors.toList());
                
                int catchCount = 0;
                double weight = 0.0;
                int tripCount = weekMoments.size();
                
                for (MomentVO moment : weekMoments) {
                    Map<String, Object> fishingData = extractFishingDataFromVO(moment);
                    Integer count = (Integer) fishingData.get("catchCount");
                    Double w = (Double) fishingData.get("weight");
                    if (count != null) catchCount += count;
                    if (w != null) weight += w;
                }
                
                trendData.add(CatchTrendVO.builder()
                        .date(formatDateLabel(weekStart, "month"))
                        .count(catchCount)
                        .weight(weight)
                        .tripCount(tripCount)
                        .build());
            }
        } else {
            // 按月统计
            for (int i = 11; i >= 0; i--) {
                LocalDate monthStart = LocalDate.now().minusMonths(i);
                // 使用现有方法统计一个月数据
                LocalDate monthEnd = monthStart.plusMonths(1).minusDays(1);
                Page<Moment> monthPage = new Page<>(1, 1000);
                Page<MomentVO> monthMomentsPage = momentService.getUserMoments(userId, monthPage, userId);
                
                // 过滤一个月内的钓鱼动态
                List<MomentVO> monthMoments = monthMomentsPage.getRecords().stream()
                        .filter(moment -> {
                            LocalDate momentDate = moment.getCreateTime().toLocalDate();
                            return moment.getCreateTime() != null &&
                                   !momentDate.isBefore(monthStart) && 
                                   !momentDate.isAfter(monthEnd) &&
                                   ("catch_share".equals(moment.getMomentType()) || 
                                    "fishing_trip".equals(moment.getMomentType()));
                        })
                        .collect(Collectors.toList());
                
                int catchCount = 0;
                double weight = 0.0;
                int tripCount = monthMoments.size();
                
                for (MomentVO moment : monthMoments) {
                    Map<String, Object> fishingData = extractFishingDataFromVO(moment);
                    Integer count = (Integer) fishingData.get("catchCount");
                    Double w = (Double) fishingData.get("weight");
                    if (count != null) catchCount += count;
                    if (w != null) weight += w;
                }
                
                trendData.add(CatchTrendVO.builder()
                        .date(formatDateLabel(monthStart, "year"))
                        .count(catchCount)
                        .weight(weight)
                        .tripCount(tripCount)
                        .build());
            }
        }
        
        return trendData;
    }

    private List<FishSpeciesVO> buildFishSpeciesDistribution(String timeRange, Long userId) {
        // 计算时间范围
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = calculateStartTime(endTime, timeRange);
        
        // 获取用户的鱼种统计数据
        Page<Moment> momentPage = new Page<>(1, 1000);
        Page<MomentVO> userMomentsPage = momentService.getUserMoments(userId, momentPage, userId);
        
        // 过滤时间范围内的钓鱼动态
        List<MomentVO> fishingMoments = userMomentsPage.getRecords().stream()
                .filter(moment -> {
                    LocalDateTime createdAt = moment.getCreateTime();
                    return createdAt != null && 
                           !createdAt.isBefore(startTime) && 
                           !createdAt.isAfter(endTime) &&
                           ("catch_share".equals(moment.getMomentType()) || 
                            "fishing_trip".equals(moment.getMomentType()));
                })
                .collect(Collectors.toList());
        
        // 统计各鱼种数据
        Map<String, FishSpeciesStats> fishSpeciesMap = new HashMap<>();
        
        for (MomentVO moment : fishingMoments) {
            Map<String, Object> fishingData = extractFishingDataFromVO(moment);
            List<String> fishTypes = getFishTypesFromData(fishingData);
            Double weight = getWeightFromData(fishingData);
            
            for (String fishType : fishTypes) {
                fishSpeciesMap.computeIfAbsent(fishType, k -> new FishSpeciesStats())
                        .addCatch(weight != null ? weight : 0.0);
            }
        }
        
        // 计算总数用于百分比计算
        int totalCount = fishSpeciesMap.values().stream().mapToInt(stats -> stats.count).sum();
        
        List<FishSpeciesVO> result = new ArrayList<>();
        String[] colors = {"#2196F3", "#4CAF50", "#FF9800", "#F44336", "#9C27B0", "#607D8B", "#795548"};
        int colorIndex = 0;
        
        for (Map.Entry<String, FishSpeciesStats> entry : fishSpeciesMap.entrySet()) {
            FishSpeciesStats stats = entry.getValue();
            double percentage = totalCount > 0 ? ((double) stats.count / totalCount) * 100 : 0.0;
            
            result.add(FishSpeciesVO.builder()
                    .species(entry.getKey())
                    .count(stats.count)
                    .percentage(Math.round(percentage * 10.0) / 10.0)
                    .color(colors[colorIndex % colors.length])
                    .averageWeight(Math.round(stats.averageWeight * 10.0) / 10.0)
                    .totalWeight(Math.round(stats.totalWeight * 10.0) / 10.0)
                    .build());
                    
            colorIndex++;
        }
        
        return result;
    }

    private List<SpotStatisticsVO> buildSpotStatistics(String timeRange, Integer limit, Long userId) {
        // 计算时间范围
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = calculateStartTime(endTime, timeRange);
        
        // 获取用户在各钓点的统计数据
        // 由于SpotCheckinService没有getUserSpotStatistics方法，我们使用moment数据来统计
        Page<Moment> momentPage = new Page<>(1, 1000);
        Page<MomentVO> userMomentsPage = momentService.getUserMoments(userId, momentPage, userId);
        
        // 过滤时间范围内的钓鱼动态
        List<MomentVO> fishingMoments = userMomentsPage.getRecords().stream()
                .filter(moment -> {
                    LocalDateTime createdAt = moment.getCreateTime();
                    return createdAt != null && 
                           !createdAt.isBefore(startTime) && 
                           !createdAt.isAfter(endTime) &&
                           ("catch_share".equals(moment.getMomentType()) || 
                            "fishing_trip".equals(moment.getMomentType()));
                })
                .collect(Collectors.toList());
        
        // 按钓点统计数据
        Map<Long, SpotStats> spotStatsMap = new HashMap<>();
        
        for (MomentVO moment : fishingMoments) {
            Long spotId = moment.getFishingSpotId();
            if (spotId != null) {
                SpotStats stats = spotStatsMap.computeIfAbsent(spotId, k -> new SpotStats());
                stats.tripCount++;
                stats.lastVisit = moment.getCreateTime();
                
                Map<String, Object> fishingData = extractFishingDataFromVO(moment);
                Integer catchCount = getCatchCountFromData(fishingData);
                if (catchCount != null && catchCount > 0) {
                    stats.catchCount += catchCount;
                    stats.successfulTrips++;
                }
            }
        }
        
        // 转换为结果列表
        List<SpotStatisticsVO> result = new ArrayList<>();
        int count = 0;
        
        for (Map.Entry<Long, SpotStats> entry : spotStatsMap.entrySet()) {
            if (count >= limit) break;
            
            Long spotId = entry.getKey();
            SpotStats stats = entry.getValue();
            
            double successRate = stats.tripCount > 0 ? 
                    ((double) stats.successfulTrips / stats.tripCount) * 100 : 0.0;
            double averageCatchPerTrip = stats.tripCount > 0 ? 
                    (double) stats.catchCount / stats.tripCount : 0.0;
            
            result.add(SpotStatisticsVO.builder()
                    .id(spotId)
                    .name("钓点" + spotId) // 占位名称
                    .tripCount(stats.tripCount)
                    .catchCount(stats.catchCount)
                    .successRate(Math.round(successRate * 10.0) / 10.0)
                    .lastVisit(stats.lastVisit.toLocalDate())
                    .averageCatchPerTrip(Math.round(averageCatchPerTrip * 10.0) / 10.0)
                    .address("未知地址") // 占位地址
                    .build());
            
            count++;
        }
        
        return result;
    }

    // ========== 辅助方法 ==========

    private LocalDateTime calculateStartTime(LocalDateTime endTime, String timeRange) {
        return switch (timeRange) {
            case "week" -> endTime.minus(7, ChronoUnit.DAYS);
            case "month" -> endTime.minus(1, ChronoUnit.MONTHS);
            case "year" -> endTime.minus(1, ChronoUnit.YEARS);
            default -> endTime.minus(1, ChronoUnit.MONTHS);
        };
    }

    private Map<String, Object> extractFishingDataFromVO(MomentVO moment) {
        // 从MomentVO中提取钓鱼数据
        // 根据实际的MomentVO结构调整这个方法
        Map<String, Object> data = new HashMap<>();
        
        // 这里可以根据MomentVO的实际字段来提取数据
        // 例如：moment.getExtendedData() 或其他字段
        data.put("catchCount", 0); // 默认值，实际应从MomentVO中提取
        data.put("weight", 0.0);   // 默认值，实际应从MomentVO中提取
        
        return data;
    }

    private Map<String, Object> extractFishingData(Moment moment) {
        // TODO: 从结构化数据表 moment_fishing_catch 中提取钓鱼数据
        // 实际实现需要查询 MomentFishingCatch 表
        return new HashMap<>(); // 占位实现
    }

    private Map<String, Object> extractEquipmentData(Moment moment) {
        // TODO: 从结构化数据表 moment_equipment 中提取装备数据
        return new HashMap<>(); // 占位实现
    }

    private String getLocationFromMoment(MomentVO moment) {
        // 从MomentVO中提取位置信息
        return moment.getFishingSpotName() != null ? moment.getFishingSpotName() : "未知位置";
    }

    private String getDurationFromMoment(MomentVO moment) {
        // 从MomentVO数据中提取钓鱼时长
        return "未知"; // 占位实现，需要根据实际MomentVO结构调整
    }

    private String getLocationFromMoment(Moment moment) {
        // 从关联的钓点或动态内容中提取位置信息
        return "未知位置"; // 占位实现
    }

    private String getDurationFromMoment(Moment moment) {
        // 从动态数据中提取钓鱼时长
        return "未知"; // 占位实现
    }

    private Integer getCatchCountFromData(Map<String, Object> data) {
        return data != null ? (Integer) data.getOrDefault("catchCount", 0) : 0;
    }

    private String evaluateTripResult(Map<String, Object> data) {
        Integer count = getCatchCountFromData(data);
        if (count >= 10) return "优秀";
        if (count >= 5) return "良好";
        if (count > 0) return "一般";
        return "空军";
    }

    private Double getWeightFromData(Map<String, Object> data) {
        return data != null ? (Double) data.getOrDefault("weight", 0.0) : 0.0;
    }

    private List<String> getFishTypesFromData(Map<String, Object> data) {
        return new ArrayList<>(); // 占位实现
    }

    private String getWeatherFromData(Map<String, Object> data) {
        return data != null ? (String) data.getOrDefault("weather", "未知") : "未知";
    }

    private String getTemperatureFromData(Map<String, Object> data) {
        return data != null ? (String) data.getOrDefault("temperature", "未知") : "未知";
    }

    private void processEquipmentData(Map<String, EquipmentStats> statsMap, 
                                    Map<String, Object> equipmentData, LocalDateTime usedAt) {
        // 处理装备使用数据
        // 占位实现
    }

    private Double calculateUsagePercentage(Integer usageCount, int totalMoments) {
        return totalMoments > 0 ? ((double) usageCount / totalMoments) * 100 : 0.0;
    }

    private String formatDateLabel(LocalDate date, String timeRange) {
        return switch (timeRange) {
            case "week" -> date.getDayOfWeek().toString();
            case "month" -> "第" + date.getDayOfMonth() / 7 + 1 + "周";
            case "year" -> date.getMonthValue() + "月";
            default -> date.toString();
        };
    }

    // 内部统计类
    private static class EquipmentStats {
        Long id = System.currentTimeMillis();
        String name;
        String category;
        String brand;
        String model;
        Integer usageCount = 0;
        LocalDateTime lastUsed;
    }

    private static class FishSpeciesStats {
        int count = 0;
        double totalWeight = 0.0;
        double averageWeight = 0.0;
        
        void addCatch(double weight) {
            count++;
            totalWeight += weight;
            averageWeight = count > 0 ? totalWeight / count : 0.0;
        }
    }
    
    private static class SpotStats {
        int tripCount = 0;
        int catchCount = 0;
        int successfulTrips = 0;
        LocalDateTime lastVisit;
    }
}