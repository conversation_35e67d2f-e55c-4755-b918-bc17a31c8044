package com.fishing.api;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.config.CurrentUser;
import com.fishing.domain.User;
import com.fishing.dto.moment.structured.CreateStructuredMomentRequest;
import com.fishing.user.service.StructuredMomentService;
import com.fishing.vo.ApiResponse;
import com.fishing.vo.moment.structured.StructuredMomentResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 结构化动态接口
 */
@Tag(name = "结构化动态管理", description = "结构化动态的创建、查询、更新和删除")
@RestController
@RequestMapping("/api/v1/moments/structured")
public class StructuredMomentController {

    private final StructuredMomentService structuredMomentService;

    public StructuredMomentController(StructuredMomentService structuredMomentService) {
        this.structuredMomentService = structuredMomentService;
    }

    @Operation(summary = "创建结构化动态", description = "根据动态类型创建相应的结构化数据")
    @PostMapping
    public ResponseEntity<ApiResponse<StructuredMomentResponse>> createStructuredMoment(
            @CurrentUser User currentUser,
            @Valid @RequestBody CreateStructuredMomentRequest request) {

        StructuredMomentResponse response = structuredMomentService.createStructuredMoment(currentUser.getId(), request);
        
        return ResponseEntity.ok(ApiResponse.success("动态创建成功", response));
    }

    @Operation(summary = "获取结构化动态详情", description = "根据动态ID获取完整的结构化动态信息")
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<StructuredMomentResponse>> getStructuredMoment(
            @Parameter(description = "动态ID") @PathVariable Long id) {

        StructuredMomentResponse response = structuredMomentService.getStructuredMoment(id);
        
        if (response == null) {
            return ResponseEntity.ok(ApiResponse.error(404, "动态不存在"));
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取成功", response));
    }

    @Operation(summary = "更新结构化动态", description = "更新指定动态的内容和结构化数据")
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<StructuredMomentResponse>> updateStructuredMoment(
            @CurrentUser User currentUser,
            @Parameter(description = "动态ID") @PathVariable Long id,
            @Valid @RequestBody CreateStructuredMomentRequest request) {

        try {
            StructuredMomentResponse response = structuredMomentService.updateStructuredMoment(currentUser.getId(), id, request);
            return ResponseEntity.ok(ApiResponse.success("更新成功", response));
        } catch (RuntimeException e) {
            return ResponseEntity.ok(ApiResponse.error(400, e.getMessage()));
        }
    }

    @Operation(summary = "删除结构化动态", description = "删除指定的动态及其所有关联数据")
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteStructuredMoment(
            @CurrentUser User currentUser,
            @Parameter(description = "动态ID") @PathVariable Long id) {

        try {
            structuredMomentService.deleteStructuredMoment(currentUser.getId(), id);
            return ResponseEntity.ok(ApiResponse.success("删除成功", (Void) null));
        } catch (RuntimeException e) {
            return ResponseEntity.ok(ApiResponse.error(400, e.getMessage()));
        }
    }

    @Operation(summary = "获取结构化动态列表", description = "分页获取结构化动态列表，支持按类型、用户等条件筛选")
    @GetMapping
    public ResponseEntity<ApiResponse<IPage<StructuredMomentResponse>>> getStructuredMomentList(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "20") Integer limit,
            @Parameter(description = "动态类型: 1-钓获分享, 2-装备展示, 3-技巧分享, 4-问答求助") @RequestParam(required = false) Integer type,
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "钓点ID") @RequestParam(required = false) Long fishingSpotId,
            @Parameter(description = "可见性: 1-公开, 2-关注者可见, 3-仅自己可见") @RequestParam(required = false) Integer visibility) {

        Page<StructuredMomentResponse> pageParam = new Page<>(page, Math.min(limit, 100));
        
        IPage<StructuredMomentResponse> result = structuredMomentService.getStructuredMomentList(
                pageParam, type, userId, fishingSpotId, visibility);
        
        return ResponseEntity.ok(ApiResponse.success("获取成功", result));
    }

    @Operation(summary = "搜索结构化动态", description = "根据关键词搜索动态")
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<IPage<StructuredMomentResponse>>> searchStructuredMoments(
            @Parameter(description = "搜索关键词") @RequestParam String keyword,
            @Parameter(description = "动态类型") @RequestParam(required = false) Integer type,
            @Parameter(description = "标签，多个用逗号分隔") @RequestParam(required = false) String tags,
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "20") Integer limit) {

        // TODO: 实现基于关键词和标签的搜索功能
        Page<StructuredMomentResponse> pageParam = new Page<>(page, Math.min(limit, 100));
        IPage<StructuredMomentResponse> result = structuredMomentService.getStructuredMomentList(
                pageParam, type, null, null, 1); // 临时实现，只返回公开动态

        return ResponseEntity.ok(ApiResponse.success("搜索成功", result));
    }

    @Operation(summary = "获取动态统计信息", description = "获取动态的统计数据")
    @GetMapping("/statistics")
    public ResponseEntity<ApiResponse<Object>> getMomentStatistics(
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "动态类型") @RequestParam(required = false) Integer type,
            @Parameter(description = "开始日期 (ISO 8601格式)") @RequestParam(required = false) String startDate,
            @Parameter(description = "结束日期 (ISO 8601格式)") @RequestParam(required = false) String endDate) {

        // TODO: 实现动态统计功能
        // 统计信息包括：总动态数、各类型动态数、按月统计、热门鱼类等
        Object statistics = new Object(); // 临时实现

        return ResponseEntity.ok(ApiResponse.success("获取统计信息成功", statistics));
    }
}