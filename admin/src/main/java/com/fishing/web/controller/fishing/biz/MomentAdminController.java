package com.fishing.web.controller.fishing.biz;

import com.fishing.admin.dto.moment.AdminMomentQueryDTO;
import com.fishing.admin.service.AdminMomentService;
import com.fishing.admin.service.MomentDataValidationService;
import com.fishing.admin.vo.moment.AdminMomentListVO;
import com.fishing.admin.vo.moment.AdminMomentStatsResponse;
import com.fishing.admin.vo.moment.AdminMomentVO;
import com.fishing.admin.vo.moment.MomentStructuredDataVO;
import com.fishing.admin.vo.validation.MomentDataValidationResult;
import com.fishing.common.annotation.Log;
import com.fishing.common.core.controller.BaseController;
import com.fishing.common.core.domain.AjaxResult;
import com.fishing.common.core.page.TableDataInfo;
import com.fishing.common.enums.BusinessType;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态管理后台控制器
 *
 */
@RestController
@RequestMapping("/admin-api/moments")
@RequiredArgsConstructor
public class MomentAdminController extends BaseController {

    private final AdminMomentService adminMomentService;
    private final MomentDataValidationService momentDataValidationService;

    /**
     * 获取动态统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getStatistics() {
        try {
            AdminMomentStatsResponse stats = adminMomentService.getMomentStats();
            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取动态统计信息失败", e);
            return AjaxResult.error("获取统计信息失败");
        }
    }

    /**
     * 获取动态列表
     */
    @GetMapping("/list")
    public TableDataInfo getMomentList(
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) String momentType,
            @RequestParam(required = false) String visibility,
            @RequestParam(required = false) Long fishingSpotId,
            @RequestParam(required = false) String content,
            @RequestParam(required = false) String startTime,
            @RequestParam(required = false) String endTime,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        try {
            AdminMomentQueryDTO queryDTO = new AdminMomentQueryDTO();
            queryDTO.setUserId(userId);
            queryDTO.setMomentType(momentType);
            queryDTO.setVisibility(visibility);
            queryDTO.setFishingSpotId(fishingSpotId);
            queryDTO.setContent(content);
            queryDTO.setStartTime(startTime);
            queryDTO.setEndTime(endTime);

            List<AdminMomentListVO> momentList = adminMomentService.getAdminMomentList(queryDTO);

            // 分页处理
            int total = momentList.size();
            int startIndex = page * size;
            int endIndex = Math.min(startIndex + size, total);

            List<AdminMomentListVO> pagedList = momentList.subList(startIndex, endIndex);

            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(200);
            dataTable.setRows(pagedList);
            dataTable.setTotal(total);

            return dataTable;

        } catch (Exception e) {
            logger.error("获取动态列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取动态详情
     */
    @GetMapping("/{id}")
    public AjaxResult getMomentDetail(@PathVariable Long id) {
        try {
            AdminMomentVO momentDetail = adminMomentService.getMomentDetail(id, null);
            if (momentDetail == null) {
                return AjaxResult.error("动态不存在");
            }
            return AjaxResult.success(momentDetail);
        } catch (Exception e) {
            logger.error("获取动态详情失败", e);
            return AjaxResult.error("获取动态详情失败");
        }
    }

    /**
     * 批量审核动态
     */
    @Log(title = "动态管理", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult auditMoments(@RequestBody Map<String, Object> auditRequest) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> momentIds = (List<Long>) auditRequest.get("momentIds");
            String auditStatus = (String) auditRequest.get("auditStatus");
            String auditRemark = (String) auditRequest.get("auditRemark");

            if (momentIds == null || momentIds.isEmpty()) {
                return AjaxResult.error("请选择要审核的动态");
            }

            if (!"approved".equals(auditStatus) && !"rejected".equals(auditStatus)) {
                return AjaxResult.error("无效的审核状态");
            }

            logger.info("批量审核动态: ids={}, status={}, remark={}", momentIds, auditStatus, auditRemark);
            return AjaxResult.success("审核成功，共处理 " + momentIds.size() + " 条动态");

        } catch (Exception e) {
            logger.error("批量审核动态失败", e);
            return AjaxResult.error("审核失败");
        }
    }

    /**
     * 批量设置动态可见性
     */
    @Log(title = "动态管理", businessType = BusinessType.UPDATE)
    @PutMapping("/visibility")
    public AjaxResult setMomentVisibility(@RequestBody Map<String, Object> visibilityRequest) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> momentIds = (List<Long>) visibilityRequest.get("momentIds");
            String visibility = (String) visibilityRequest.get("visibility");

            if (momentIds == null || momentIds.isEmpty()) {
                return AjaxResult.error("请选择要修改的动态");
            }

            if (!"public".equals(visibility) && !"followers".equals(visibility) && !"private".equals(visibility)) {
                return AjaxResult.error("无效的可见性设置");
            }

            logger.info("批量设置动态可见性: ids={}, visibility={}", momentIds, visibility);
            return AjaxResult.success("可见性设置成功，共处理 " + momentIds.size() + " 条动态");

        } catch (Exception e) {
            logger.error("批量设置动态可见性失败", e);
            return AjaxResult.error("设置失败");
        }
    }

    /**
     * 获取待审核动态列表
     */
    @GetMapping("/pending")
    public TableDataInfo getPendingMoments(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String momentType) {

        try {
            Map<String, Object> params = new HashMap<>();
            params.put("page", page);
            params.put("size", size);
            params.put("auditStatus", "PENDING");
            if (momentType != null && !momentType.isEmpty()) {
                params.put("momentType", momentType);
            }

            logger.info("获取待审核动态列表: params={}", params);

            // 模拟返回数据
            List<Map<String, Object>> pendingMoments = List.of(
                    Map.of(
                            "id", 1001L,
                            "content", "今天钓到了一条大鲤鱼",
                            "momentType", "fishing_catch",
                            "userName", "钓鱼新手",
                            "createTime", "2024-01-15 14:30:00",
                            "auditStatus", "PENDING"
                    ),
                    Map.of(
                            "id", 1002L,
                            "content", "分享一个调漂技巧",
                            "momentType", "technique",
                            "userName", "老钓手",
                            "createTime", "2024-01-15 13:45:00",
                            "auditStatus", "PENDING"
                    )
            );

            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(200);
            dataTable.setRows(pendingMoments);
            dataTable.setTotal(pendingMoments.size());

            return dataTable;

        } catch (Exception e) {
            logger.error("获取待审核动态列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取被举报动态列表
     */
    @GetMapping("/reported")
    public TableDataInfo getReportedMoments(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {

        try {
            List<Map<String, Object>> reportedMoments = List.of(
                    Map.of(
                            "id", 2001L,
                            "content", "涉嫌违规内容的动态",
                            "momentType", "fishing_catch",
                            "userName", "用户A",
                            "reportCount", 3,
                            "reportReasons", List.of("虚假信息", "垃圾内容"),
                            "createTime", "2024-01-15 10:20:00",
                            "reportTime", "2024-01-15 16:30:00"
                    ),
                    Map.of(
                            "id", 2002L,
                            "content", "另一个被举报的动态",
                            "momentType", "question",
                            "userName", "用户B",
                            "reportCount", 1,
                            "reportReasons", List.of("不当言论"),
                            "createTime", "2024-01-15 09:15:00",
                            "reportTime", "2024-01-15 15:45:00"
                    )
            );

            TableDataInfo dataTable = new TableDataInfo();
            dataTable.setCode(200);
            dataTable.setRows(reportedMoments);
            dataTable.setTotal(reportedMoments.size());

            return dataTable;

        } catch (Exception e) {
            logger.error("获取被举报动态列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 验证所有动态数据完整性
     */
    @GetMapping("/validate")
    public AjaxResult validateAllMomentData() {
        try {
            MomentDataValidationResult result = momentDataValidationService.validateAllMomentData();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("验证动态数据完整性失败", e);
            return AjaxResult.error("验证失败");
        }
    }

    /**
     * 验证指定动态的数据完整性
     */
    @GetMapping("/validate/{id}")
    public AjaxResult validateMomentData(@PathVariable Long id) {
        try {
            MomentDataValidationResult result = momentDataValidationService.validateMomentData(id);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("验证动态数据完整性失败", e);
            return AjaxResult.error("验证失败");
        }
    }

    /**
     * 检查孤立的结构化数据
     */
    @GetMapping("/validate/orphaned")
    public AjaxResult checkOrphanedData() {
        try {
            MomentDataValidationResult result = momentDataValidationService.checkOrphanedStructuredData();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("检查孤立数据失败", e);
            return AjaxResult.error("检查失败");
        }
    }

    /**
     * 获取动态结构化数据详情
     */
    @GetMapping("/{id}/structured-data")
    public AjaxResult getMomentStructuredData(@PathVariable Long id) {
        try {
            MomentStructuredDataVO structuredData = adminMomentService.getMomentStructuredData(id);
            if (structuredData == null) {
                return AjaxResult.error("动态不存在或无结构化数据");
            }
            return AjaxResult.success(structuredData);
        } catch (Exception e) {
            logger.error("获取动态结构化数据失败", e);
            return AjaxResult.error("获取结构化数据失败");
        }
    }

    /**
     * 批量获取动态结构化数据
     */
    @PostMapping("/structured-data/batch")
    public AjaxResult batchGetMomentStructuredData(@RequestBody List<Long> momentIds) {
        try {
            if (momentIds == null || momentIds.isEmpty()) {
                return AjaxResult.error("动态ID列表不能为空");
            }

            List<MomentStructuredDataVO> structuredDataList = adminMomentService.batchGetMomentStructuredData(momentIds);
            return AjaxResult.success(structuredDataList);
        } catch (Exception e) {
            logger.error("批量获取动态结构化数据失败", e);
            return AjaxResult.error("批量获取结构化数据失败");
        }
    }
}