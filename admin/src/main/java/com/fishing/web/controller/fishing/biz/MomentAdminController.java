package com.fishing.web.controller.fishing.biz;

import com.fishing.admin.dto.moment.AdminMomentQueryDTO;
import com.fishing.admin.dto.moment.BatchMomentAuditRequest;
import com.fishing.admin.service.AdminMomentService;
import com.fishing.admin.service.MomentDataValidationService;
import com.fishing.admin.vo.moment.AdminMomentListVO;
import com.fishing.admin.vo.moment.AdminMomentStatsResponse;
import com.fishing.admin.vo.moment.AdminMomentVO;
import com.fishing.admin.vo.moment.MomentStructuredDataVO;
import com.fishing.admin.vo.validation.MomentDataValidationResult;
import com.fishing.common.annotation.Log;
import com.fishing.common.core.controller.BaseController;
import com.fishing.common.core.domain.AjaxResult;
import com.fishing.common.core.page.TableDataInfo;
import com.fishing.common.enums.BusinessType;
import com.fishing.datasource.annotation.DataSource;
import com.fishing.datasource.enums.DataSourceType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 动态管理后台控制器
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Tag(name = "动态管理", description = "admin-ui 动态管理相关接口")
@RestController
@RequestMapping("/fishing-biz/moment")
@RequiredArgsConstructor
@DataSource(DataSourceType.FISHING_BIZ)
@Validated
public class MomentAdminController extends BaseController {

    private final AdminMomentService adminMomentService;
    private final MomentDataValidationService momentDataValidationService;

    /**
     * 获取动态统计信息
     */
    @Operation(summary = "获取动态统计信息", description = "获取动态总数、今日新增、审核状态分布等统计信息")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:stats')")
    @GetMapping("/stats")
    public AjaxResult getStatistics() {
        try {
            AdminMomentStatsResponse stats = adminMomentService.getMomentStats();
            return success(stats);
        } catch (Exception e) {
            logger.error("获取动态统计信息失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取动态列表
     */
    @Operation(summary = "获取动态列表", description = "分页查询动态列表，支持多条件筛选")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:list')")
    @GetMapping("/list")
    public TableDataInfo getMomentList(
            @Parameter(description = "用户ID") @RequestParam(required = false) Long userId,
            @Parameter(description = "动态类型") @RequestParam(required = false) String momentType,
            @Parameter(description = "可见性") @RequestParam(required = false) String visibility,
            @Parameter(description = "钓点ID") @RequestParam(required = false) Long fishingSpotId,
            @Parameter(description = "内容关键词") @RequestParam(required = false) String content,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime,
            @Parameter(description = "审核状态") @RequestParam(required = false) String auditStatus,
            @Parameter(description = "是否违规") @RequestParam(required = false) Boolean isViolation,
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {

        try {
            startPage();
            AdminMomentQueryDTO queryDTO = new AdminMomentQueryDTO();
            queryDTO.setUserId(userId);
            queryDTO.setMomentType(momentType);
            queryDTO.setVisibility(visibility);
            queryDTO.setFishingSpotId(fishingSpotId);
            queryDTO.setContent(content);
            queryDTO.setStartTime(startTime);
            queryDTO.setEndTime(endTime);
            queryDTO.setAuditStatus(auditStatus);
            queryDTO.setIsViolation(isViolation);

            List<AdminMomentListVO> momentList = adminMomentService.getAdminMomentList(queryDTO);
            return getDataTable(momentList);

        } catch (Exception e) {
            logger.error("获取动态列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取动态详情
     */
    @Operation(summary = "获取动态详情", description = "根据动态ID获取详细信息")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:query')")
    @GetMapping("/{id}")
    public AjaxResult getMomentDetail(
            @Parameter(description = "动态ID", required = true) @PathVariable Long id) {
        try {
            AdminMomentVO momentDetail = adminMomentService.getMomentDetail(id, getUserId());
            if (momentDetail == null) {
                return error("动态不存在");
            }
            return success(momentDetail);
        } catch (Exception e) {
            logger.error("获取动态详情失败", e);
            return error("获取动态详情失败");
        }
    }

    /**
     * 批量审核动态
     */
    @Operation(summary = "批量审核动态", description = "批量审核动态，支持通过或拒绝")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:audit')")
    @Log(title = "动态管理", businessType = BusinessType.UPDATE)
    @PutMapping("/audit")
    public AjaxResult auditMoments(@Valid @RequestBody BatchMomentAuditRequest request) {
        try {
            // 设置审核人信息
            request.setAuditorId(getUserId().toString());
            request.setAuditorName(getUsername());

            boolean result = adminMomentService.batchAuditMoments(request);
            if (result) {
                return success("审核成功，共处理 " + request.getMomentIds().size() + " 条动态");
            } else {
                return error("审核失败");
            }

        } catch (Exception e) {
            logger.error("批量审核动态失败", e);
            return error("审核失败: " + e.getMessage());
        }
    }

    /**
     * 批量设置动态可见性
     */
    @Operation(summary = "批量设置动态可见性", description = "批量设置动态的可见性")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:edit')")
    @Log(title = "动态管理", businessType = BusinessType.UPDATE)
    @PutMapping("/visibility")
    public AjaxResult setMomentVisibility(@Valid @RequestBody Map<String, Object> visibilityRequest) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> momentIds = (List<Long>) visibilityRequest.get("momentIds");
            String visibility = (String) visibilityRequest.get("visibility");

            if (momentIds == null || momentIds.isEmpty()) {
                return error("请选择要修改的动态");
            }

            if (!"public".equals(visibility) && !"followers".equals(visibility) && !"private".equals(visibility)) {
                return error("无效的可见性设置");
            }

            Long[] ids = momentIds.toArray(new Long[0]);
            boolean result = adminMomentService.setMomentVisibility(ids, visibility, getUserId());

            if (result) {
                return success("可见性设置成功，共处理 " + momentIds.size() + " 条动态");
            } else {
                return error("设置失败");
            }

        } catch (Exception e) {
            logger.error("批量设置动态可见性失败", e);
            return error("设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取待审核动态列表
     */
    @Operation(summary = "获取待审核动态列表", description = "分页查询待审核的动态列表")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:audit')")
    @GetMapping("/pending")
    public TableDataInfo getPendingMoments(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "动态类型") @RequestParam(required = false) String momentType) {

        try {
            startPage();
            AdminMomentQueryDTO queryDTO = new AdminMomentQueryDTO();
            queryDTO.setAuditStatus("PENDING");
            if (momentType != null && !momentType.isEmpty()) {
                queryDTO.setMomentType(momentType);
            }

            List<AdminMomentListVO> pendingMoments = adminMomentService.getAdminMomentList(queryDTO);
            return getDataTable(pendingMoments);

        } catch (Exception e) {
            logger.error("获取待审核动态列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 获取被举报动态列表
     */
    @Operation(summary = "获取被举报动态列表", description = "分页查询被举报的动态列表")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:audit')")
    @GetMapping("/reported")
    public TableDataInfo getReportedMoments(
            @Parameter(description = "页码") @RequestParam(defaultValue = "0") Integer page,
            @Parameter(description = "每页数量") @RequestParam(defaultValue = "10") Integer size) {

        try {
            startPage();
            AdminMomentQueryDTO queryDTO = new AdminMomentQueryDTO();
            queryDTO.setIsViolation(true);

            List<AdminMomentListVO> reportedMoments = adminMomentService.getAdminMomentList(queryDTO);
            return getDataTable(reportedMoments);

        } catch (Exception e) {
            logger.error("获取被举报动态列表失败", e);
            return new TableDataInfo();
        }
    }

    /**
     * 验证所有动态数据完整性
     */
    @GetMapping("/validate")
    public AjaxResult validateAllMomentData() {
        try {
            MomentDataValidationResult result = momentDataValidationService.validateAllMomentData();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("验证动态数据完整性失败", e);
            return AjaxResult.error("验证失败");
        }
    }

    /**
     * 验证指定动态的数据完整性
     */
    @GetMapping("/validate/{id}")
    public AjaxResult validateMomentData(@PathVariable Long id) {
        try {
            MomentDataValidationResult result = momentDataValidationService.validateMomentData(id);
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("验证动态数据完整性失败", e);
            return AjaxResult.error("验证失败");
        }
    }

    /**
     * 检查孤立的结构化数据
     */
    @GetMapping("/validate/orphaned")
    public AjaxResult checkOrphanedData() {
        try {
            MomentDataValidationResult result = momentDataValidationService.checkOrphanedStructuredData();
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("检查孤立数据失败", e);
            return AjaxResult.error("检查失败");
        }
    }

    /**
     * 获取动态结构化数据详情
     */
    @GetMapping("/{id}/structured-data")
    public AjaxResult getMomentStructuredData(@PathVariable Long id) {
        try {
            MomentStructuredDataVO structuredData = adminMomentService.getMomentStructuredData(id);
            if (structuredData == null) {
                return AjaxResult.error("动态不存在或无结构化数据");
            }
            return AjaxResult.success(structuredData);
        } catch (Exception e) {
            logger.error("获取动态结构化数据失败", e);
            return AjaxResult.error("获取结构化数据失败");
        }
    }

    /**
     * 批量获取动态结构化数据
     */
    @Operation(summary = "批量获取动态结构化数据", description = "批量获取多个动态的结构化数据")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:query')")
    @PostMapping("/structured-data/batch")
    public AjaxResult batchGetMomentStructuredData(@Valid @RequestBody List<Long> momentIds) {
        try {
            if (momentIds == null || momentIds.isEmpty()) {
                return error("动态ID列表不能为空");
            }

            List<MomentStructuredDataVO> structuredDataList = adminMomentService.batchGetMomentStructuredData(momentIds);
            return success(structuredDataList);
        } catch (Exception e) {
            logger.error("批量获取动态结构化数据失败", e);
            return error("批量获取结构化数据失败");
        }
    }

    /**
     * 删除动态
     */
    @Operation(summary = "删除动态", description = "根据动态ID删除动态")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:remove')")
    @Log(title = "动态管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(
            @Parameter(description = "动态ID，多个用逗号分隔", required = true) @PathVariable Long[] ids) {
        try {
            boolean result = adminMomentService.deleteMomentByIds(ids);
            if (result) {
                return success("删除成功");
            } else {
                return error("删除失败");
            }
        } catch (Exception e) {
            logger.error("删除动态失败", e);
            return error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除动态
     */
    @Operation(summary = "批量删除动态", description = "批量删除多个动态")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:remove')")
    @Log(title = "动态管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/batch")
    public AjaxResult batchRemove(@Valid @RequestBody List<Long> momentIds) {
        try {
            if (momentIds == null || momentIds.isEmpty()) {
                return error("请选择要删除的动态");
            }

            Long[] ids = momentIds.toArray(new Long[0]);
            boolean result = adminMomentService.deleteMomentByIds(ids);

            if (result) {
                return success("批量删除成功，共删除 " + momentIds.size() + " 条动态");
            } else {
                return error("批量删除失败");
            }
        } catch (Exception e) {
            logger.error("批量删除动态失败", e);
            return error("批量删除失败: " + e.getMessage());
        }
    }

    /**
     * 标记动态为违规
     */
    @Operation(summary = "标记动态为违规", description = "将动态标记为违规内容")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:violation')")
    @Log(title = "动态管理", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/violation")
    public AjaxResult markAsViolation(
            @Parameter(description = "动态ID", required = true) @PathVariable Long id,
            @Parameter(description = "违规原因") @RequestParam(required = false) String reason) {
        try {
            boolean result = adminMomentService.markMomentAsViolation(id, reason, getUserId());
            if (result) {
                return success("标记违规成功");
            } else {
                return error("标记违规失败");
            }
        } catch (Exception e) {
            logger.error("标记动态违规失败", e);
            return error("标记违规失败: " + e.getMessage());
        }
    }

    /**
     * 取消动态违规标记
     */
    @Operation(summary = "取消动态违规标记", description = "取消动态的违规标记")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:violation')")
    @Log(title = "动态管理", businessType = BusinessType.UPDATE)
    @DeleteMapping("/{id}/violation")
    public AjaxResult unmarkViolation(
            @Parameter(description = "动态ID", required = true) @PathVariable Long id) {
        try {
            boolean result = adminMomentService.unmarkMomentViolation(id, getUserId());
            if (result) {
                return success("取消违规标记成功");
            } else {
                return error("取消违规标记失败");
            }
        } catch (Exception e) {
            logger.error("取消动态违规标记失败", e);
            return error("取消违规标记失败: " + e.getMessage());
        }
    }

    /**
     * 获取动态举报信息
     */
    @Operation(summary = "获取动态举报信息", description = "获取指定动态的举报信息列表")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:query')")
    @GetMapping("/{id}/reports")
    public AjaxResult getMomentReports(
            @Parameter(description = "动态ID", required = true) @PathVariable Long id) {
        try {
            List<Object> reports = adminMomentService.getMomentReports(id);
            return success(reports);
        } catch (Exception e) {
            logger.error("获取动态举报信息失败", e);
            return error("获取举报信息失败: " + e.getMessage());
        }
    }

    /**
     * 获取动态统计详情
     */
    @Operation(summary = "获取动态统计详情", description = "获取指定动态的详细统计信息")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:query')")
    @GetMapping("/{id}/statistics")
    public AjaxResult getMomentStatistics(
            @Parameter(description = "动态ID", required = true) @PathVariable Long id) {
        try {
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("commentCount", adminMomentService.getMomentCommentCount(id));
            statistics.put("likeCount", adminMomentService.getMomentLikeCount(id));
            // 可以添加更多统计信息

            return success(statistics);
        } catch (Exception e) {
            logger.error("获取动态统计详情失败", e);
            return error("获取统计详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取用户动态数量
     */
    @Operation(summary = "获取用户动态数量", description = "获取指定用户的动态总数")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:query')")
    @GetMapping("/user/{userId}/count")
    public AjaxResult getUserMomentCount(
            @Parameter(description = "用户ID", required = true) @PathVariable Long userId) {
        try {
            Integer count = adminMomentService.getUserMomentCount(userId);
            return success(Map.of("count", count));
        } catch (Exception e) {
            logger.error("获取用户动态数量失败", e);
            return error("获取用户动态数量失败: " + e.getMessage());
        }
    }

    /**
     * 获取钓点动态数量
     */
    @Operation(summary = "获取钓点动态数量", description = "获取指定钓点的动态总数")
    @PreAuthorize("@ss.hasPermi('fishing-biz:moment:query')")
    @GetMapping("/spot/{spotId}/count")
    public AjaxResult getSpotMomentCount(
            @Parameter(description = "钓点ID", required = true) @PathVariable Long spotId) {
        try {
            Integer count = adminMomentService.getSpotMomentCount(spotId);
            return success(Map.of("count", count));
        } catch (Exception e) {
            logger.error("获取钓点动态数量失败", e);
            return error("获取钓点动态数量失败: " + e.getMessage());
        }
    }
}