# MomentAdminController 功能测试指南

## 概述

本文档提供了 MomentAdminController 完善后的功能测试指南，包括所有新增和改进的接口测试方法。

## 测试环境准备

### 1. 权限配置

确保在系统中配置了以下权限：

```sql
-- 动态管理权限
INSERT INTO sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark) VALUES
('动态列表', 2000, 1, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:list', '', 'admin', NOW(), 'admin', NOW(), ''),
('动态查询', 2000, 2, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:query', '', 'admin', NOW(), 'admin', NOW(), ''),
('动态审核', 2000, 3, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:audit', '', 'admin', NOW(), 'admin', NOW(), ''),
('动态编辑', 2000, 4, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:edit', '', 'admin', NOW(), 'admin', NOW(), ''),
('动态删除', 2000, 5, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:remove', '', 'admin', NOW(), 'admin', NOW(), ''),
('违规管理', 2000, 6, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:violation', '', 'admin', NOW(), 'admin', NOW(), ''),
('统计查看', 2000, 7, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:stats', '', 'admin', NOW(), 'admin', NOW(), ''),
('数据验证', 2000, 8, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:validate', '', 'admin', NOW(), 'admin', NOW(), ''),
('数据修复', 2000, 9, '', '', 1, 0, 'F', '0', '0', 'fishing-biz:moment:repair', '', 'admin', NOW(), 'admin', NOW(), '');
```

### 2. 数据库配置

确保 `fishing_biz` 数据源配置正确，包含所有 moment 相关表。

## 核心功能测试

### 1. 动态列表查询

**接口**: `GET /fishing-biz/moment/list`

**测试用例**:
```bash
# 基础查询
curl -X GET "http://localhost:8080/fishing-biz/moment/list?page=0&size=10" \
  -H "Authorization: Bearer {token}"

# 条件筛选
curl -X GET "http://localhost:8080/fishing-biz/moment/list?momentType=fishing_catch&auditStatus=PENDING" \
  -H "Authorization: Bearer {token}"

# 时间范围查询
curl -X GET "http://localhost:8080/fishing-biz/moment/list?startTime=2024-01-01&endTime=2024-12-31" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 返回分页的动态列表，包含完整的动态信息和结构化数据。

### 2. 动态详情查询

**接口**: `GET /fishing-biz/moment/{id}`

**测试用例**:
```bash
curl -X GET "http://localhost:8080/fishing-biz/moment/123" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 返回指定动态的详细信息。

### 3. 批量审核功能

**接口**: `PUT /fishing-biz/moment/audit`

**测试用例**:
```bash
curl -X PUT "http://localhost:8080/fishing-biz/moment/audit" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '{
    "momentIds": [1, 2, 3],
    "auditStatus": "APPROVED",
    "auditRemark": "内容符合规范",
    "visibility": "public"
  }'
```

**预期结果**: 批量审核成功，返回处理结果。

### 4. 批量删除功能

**接口**: `DELETE /fishing-biz/moment/batch`

**测试用例**:
```bash
curl -X DELETE "http://localhost:8080/fishing-biz/moment/batch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer {token}" \
  -d '[1, 2, 3]'
```

**预期结果**: 批量删除成功。

## 统计功能测试

### 1. 动态统计信息

**接口**: `GET /fishing-biz/moment/stats`

**测试用例**:
```bash
curl -X GET "http://localhost:8080/fishing-biz/moment/stats" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 返回动态总数、今日新增、审核状态分布等统计信息。

### 2. 用户动态数量

**接口**: `GET /fishing-biz/moment/user/{userId}/count`

**测试用例**:
```bash
curl -X GET "http://localhost:8080/fishing-biz/moment/user/123/count" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 返回指定用户的动态总数。

## 违规管理测试

### 1. 标记违规

**接口**: `PUT /fishing-biz/moment/{id}/violation`

**测试用例**:
```bash
curl -X PUT "http://localhost:8080/fishing-biz/moment/123/violation?reason=虚假信息" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 成功标记动态为违规。

### 2. 取消违规标记

**接口**: `DELETE /fishing-biz/moment/{id}/violation`

**测试用例**:
```bash
curl -X DELETE "http://localhost:8080/fishing-biz/moment/123/violation" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 成功取消违规标记。

## 数据验证测试

### 1. 验证所有动态数据

**接口**: `GET /fishing-biz/moment/validate`

**测试用例**:
```bash
curl -X GET "http://localhost:8080/fishing-biz/moment/validate" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 返回完整的数据验证报告。

### 2. 验证指定动态

**接口**: `GET /fishing-biz/moment/validate/{id}`

**测试用例**:
```bash
curl -X GET "http://localhost:8080/fishing-biz/moment/validate/123" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 返回指定动态的验证结果。

### 3. 检查孤立数据

**接口**: `GET /fishing-biz/moment/validate/orphaned`

**测试用例**:
```bash
curl -X GET "http://localhost:8080/fishing-biz/moment/validate/orphaned" \
  -H "Authorization: Bearer {token}"
```

**预期结果**: 返回孤立数据统计。

## 前端集成测试

### 1. 页面加载测试

访问动态管理页面，确保：
- 页面正常加载
- 数据列表正常显示
- 搜索过滤功能正常
- 分页功能正常

### 2. 批量操作测试

在前端页面测试：
- 选择多个动态
- 执行批量审核
- 执行批量删除
- 执行批量设置可见性

### 3. 详情查看测试

测试：
- 点击查看动态详情
- 查看结构化数据
- 查看统计信息

## 性能测试

### 1. 大数据量测试

- 测试 10,000+ 动态的列表查询性能
- 测试批量操作的性能
- 测试数据验证的性能

### 2. 并发测试

- 模拟多用户同时访问
- 测试批量操作的并发安全性

## 错误处理测试

### 1. 权限测试

- 测试无权限用户访问
- 测试权限不足的操作

### 2. 参数验证测试

- 测试无效的动态ID
- 测试空的批量操作列表
- 测试无效的查询参数

### 3. 异常情况测试

- 测试数据库连接异常
- 测试服务异常情况

## 测试检查清单

- [ ] 所有接口返回正确的HTTP状态码
- [ ] 所有接口返回正确的数据格式
- [ ] 权限控制正常工作
- [ ] 日志记录正常
- [ ] 错误处理正确
- [ ] 前端页面功能正常
- [ ] 性能满足要求
- [ ] 数据一致性保证

## 常见问题排查

### 1. 权限问题

如果遇到权限错误，检查：
- 用户是否有对应权限
- 权限配置是否正确
- Token是否有效

### 2. 数据源问题

如果遇到数据库错误，检查：
- 数据源配置是否正确
- 数据库连接是否正常
- 表结构是否完整

### 3. 前端问题

如果前端功能异常，检查：
- API路径是否正确
- 请求参数是否正确
- 响应数据格式是否匹配
