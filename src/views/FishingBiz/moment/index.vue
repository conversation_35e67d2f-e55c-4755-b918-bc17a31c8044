<template>
  <div class="app-container">
    <!-- 搜索过滤组件 -->
    <SearchFilter
      :search-fields="searchFields"
      :advanced-fields="advancedFields"
      :initial-values="queryParams"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 批量操作组件 -->
    <BatchActions
      :selected-count="ids.length"
      :actions="batchActions"
      @action="handleBatchAction"
      @clear-selection="clearSelection"
    />

    <el-row :gutter="10" class="mb8">
      <right-toolbar
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <!-- 数据表格 -->
    <LoadingState
      :loading="loading"
      :empty="momentList.length === 0 && !loading"
      :error="loadError"
      empty-text="暂无动态数据"
      @refresh="getList"
      @retry="getList"
    >
      <el-table :data="momentList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="动态ID" align="center" prop="id" />
        <el-table-column label="用户" align="center" prop="userName" />
        <el-table-column label="动态类型" align="center" prop="momentType">
          <template slot-scope="scope">
            <el-tag
              v-if="scope.row.momentType === 'fishing_catch'"
              type="success"
              >钓获分享</el-tag
            >
            <el-tag
              v-else-if="scope.row.momentType === 'equipment'"
              type="primary"
              >装备展示</el-tag
            >
            <el-tag v-else-if="scope.row.momentType === 'technique'" type="info"
              >技巧分享</el-tag
            >
            <el-tag
              v-else-if="scope.row.momentType === 'question'"
              type="warning"
              >问答求助</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="内容"
          align="center"
          prop="content"
          :show-overflow-tooltip="true"
        />
        <el-table-column label="图片" align="center" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.images && scope.row.images.length > 0" class="moment-images">
              <el-image
                v-for="(image, index) in scope.row.images.slice(0, 3)"
                :key="index"
                :src="image.imageUrl"
                :preview-src-list="scope.row.images.map(img => img.imageUrl)"
                fit="cover"
                style="width: 30px; height: 30px; margin-right: 2px; border-radius: 4px;"
              />
              <span v-if="scope.row.images.length > 3" class="more-images">
                +{{ scope.row.images.length - 3 }}
              </span>
            </div>
            <span v-else class="no-image">-</span>
          </template>
        </el-table-column>
        <el-table-column label="可见性" align="center" prop="visibility">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.visibility === 'public'" type="success"
              >公开</el-tag
            >
            <el-tag
              v-else-if="scope.row.visibility === 'followers'"
              type="warning"
              >仅关注者</el-tag
            >
            <el-tag v-else-if="scope.row.visibility === 'private'" type="danger"
              >私密</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="点赞数" align="center" prop="likeCount" />
        <el-table-column label="评论数" align="center" prop="commentCount" />
        <el-table-column label="查看数" align="center" prop="viewCount" />
        <el-table-column label="结构化数据" align="center" width="150">
          <template slot-scope="scope">
            <div v-if="hasStructuredData(scope.row)" class="specific-data">
              <el-popover
                placement="top"
                width="400"
                trigger="hover"
                :content="formatStructuredData(scope.row)"
              >
                <el-tag
                  slot="reference"
                  size="mini"
                  :type="getSpecificDataTagType(scope.row.momentType)"
                >
                  {{ getStructuredDataSummary(scope.row) }}
                </el-tag>
              </el-popover>
            </div>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          width="180"
        >
          <template slot-scope="scope">
            <span>{{
              parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}')
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          class-name="small-padding fixed-width"
          width="200"
        >
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleView(scope.row)"
              v-hasPermi="['fishing-biz:moment:query']"
              >查看</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-s-comment"
              @click="viewMomentComments(scope.row)"
              v-if="scope.row.commentCount > 0"
              v-hasPermi="['fishing-biz:comment:list']"
              >评论({{ scope.row.commentCount }})</el-button
            >
            <el-button
              size="mini"
              type="text"
              icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['fishing-biz:moment:remove']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        :page.sync="queryParams.page"
        :limit.sync="queryParams.size"
        @pagination="getList"
      />
    </LoadingState>

    <!-- 动态详情对话框 -->
    <el-dialog
      title="动态详情"
      :visible.sync="open"
      width="800px"
      append-to-body
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="动态ID">{{
          form.id
        }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{
          form.userName || form.publisherName
        }}</el-descriptions-item>
        <el-descriptions-item label="动态类型">
          <el-tag
            v-if="form.momentType === 'fishing_catch'"
            type="success"
            >钓获分享</el-tag
          >
          <el-tag
            v-else-if="form.momentType === 'equipment'"
            type="primary"
            >装备展示</el-tag
          >
          <el-tag v-else-if="form.momentType === 'technique'" type="info"
            >技巧分享</el-tag
          >
          <el-tag
            v-else-if="form.momentType === 'question'"
            type="warning"
            >问答求助</el-tag
          >
          <span v-else>{{ form.momentType }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="可见性">
          <el-tag v-if="form.visibility === 'public'" type="success"
            >公开</el-tag
          >
          <el-tag
            v-else-if="form.visibility === 'followers'"
            type="warning"
            >仅关注者</el-tag
          >
          <el-tag v-else-if="form.visibility === 'private'" type="danger"
            >私密</el-tag
          >
          <span v-else>{{ form.visibility }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="内容" :span="2">{{
          form.content
        }}</el-descriptions-item>
        <el-descriptions-item label="钓点信息" :span="2" v-if="form.fishingSpotName">
          {{ form.fishingSpotName }} - {{ form.fishingSpotAddress }}
        </el-descriptions-item>
        <el-descriptions-item label="点赞数">{{
          form.likeCount || 0
        }}</el-descriptions-item>
        <el-descriptions-item label="评论数">{{
          form.commentCount || 0
        }}</el-descriptions-item>
        <el-descriptions-item label="查看数">{{
          form.viewCount || 0
        }}</el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag v-if="form.auditStatus === 'NORMAL'" type="success">正常</el-tag>
          <el-tag v-else-if="form.auditStatus === 'PENDING'" type="warning">待审核</el-tag>
          <el-tag v-else-if="form.auditStatus === 'REJECTED'" type="danger">已拒绝</el-tag>
          <span v-else>{{ form.auditStatus }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{
          parseTime(form.createTime)
        }}</el-descriptions-item>
        <el-descriptions-item label="更新时间" v-if="form.updateTime">{{
          parseTime(form.updateTime)
        }}</el-descriptions-item>
      </el-descriptions>

      <div
        v-if="form.images && form.images.length > 0"
        style="margin-top: 20px"
      >
        <h4>图片</h4>
        <el-row :gutter="10">
          <el-col :span="6" v-for="(image, index) in form.images" :key="index">
            <el-image
              :src="image.imageUrl"
              :preview-src-list="form.images.map((img) => img.imageUrl)"
              fit="cover"
              style="width: 100%; height: 120px"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 结构化数据详情 -->
      <div v-if="hasStructuredData(form)" style="margin-top: 20px">
        <h4>{{ getStructuredDataTitle(form.momentType) }}</h4>
        <el-card shadow="never" body-style="padding: 15px;">
          <component :is="getStructuredDataComponent(form.momentType)" :data="form" />
        </el-card>
      </div>

      <!-- 评论列表 -->
      <div v-if="form.commentCount > 0" style="margin-top: 20px">
        <h4>评论列表 ({{ form.commentCount }}条)</h4>
        <el-table
          v-loading="commentLoading"
          :data="momentComments"
          size="small"
          max-height="300"
          style="margin-top: 10px"
        >
          <el-table-column label="用户" prop="userName" width="100">
            <template slot-scope="scope">
              <div style="display: flex; align-items: center">
                <el-avatar
                  v-if="scope.row.userAvatar"
                  :src="scope.row.userAvatar"
                  size="small"
                  style="margin-right: 8px"
                ></el-avatar>
                <span>{{ scope.row.userName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="评论内容"
            prop="content"
            :show-overflow-tooltip="true"
          >
            <template slot-scope="scope">
              <div class="comment-content">
                <p v-if="scope.row.parentId" class="parent-comment">
                  回复
                  <el-tag size="mini">{{ scope.row.parentUserName }}</el-tag
                  >: {{ scope.row.parentContent }}
                </p>
                <p class="current-comment">{{ scope.row.content }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="时间" prop="createTime" width="140">
            <template slot-scope="scope">
              {{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80">
            <template slot-scope="scope">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="deleteComment(scope.row)"
                v-hasPermi="['fishing-biz:comment:remove']"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listMoment,
  getMoment,
  delMoment,
  auditMoments,
  setMomentVisibility,
} from '@/api/fishing-biz/moment'
import { getMomentComments, delComment } from '@/api/fishing-biz/comment'
import SearchFilter from '@/components/SearchFilter'
import BatchActions from '@/components/BatchActions'
import LoadingState from '@/components/LoadingState'

export default {
  name: 'Moment',
  components: {
    SearchFilter,
    BatchActions,
    LoadingState,
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      loadError: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 动态表格数据
      momentList: [],
      // 弹出层标题
      title: '',
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        page: 0,
        size: 10,
        userId: null,
        momentType: null,
        visibility: null,
        sortBy: 'createTime',
        sortDirection: 'desc'
      },
      // 表单参数
      form: {},
      // 评论相关
      commentLoading: false,
      momentComments: [],
      // 搜索字段配置
      searchFields: [
        {
          prop: 'userId',
          label: '用户ID',
          type: 'input',
          placeholder: '请输入用户ID',
        },
        {
          prop: 'momentType',
          label: '动态类型',
          type: 'select',
          options: [
            { label: '钓获分享', value: 'fishing_catch' },
            { label: '装备展示', value: 'equipment' },
            { label: '技巧分享', value: 'technique' },
            { label: '问答求助', value: 'question' },
          ],
        },
        {
          prop: 'visibility',
          label: '可见性',
          type: 'select',
          options: [
            { label: '公开', value: 'public' },
            { label: '仅关注者', value: 'followers' },
            { label: '私密', value: 'private' },
          ],
        },
        {
          prop: 'auditStatus',
          label: '审核状态',
          type: 'select',
          options: [
            { label: '正常', value: 'NORMAL' },
            { label: '待审核', value: 'PENDING' },
            { label: '已拒绝', value: 'REJECTED' },
          ],
        },
        {
          prop: 'isViolation',
          label: '违规状态',
          type: 'select',
          options: [
            { label: '正常', value: 'false' },
            { label: '违规举报', value: 'true' },
          ],
        },
        {
          prop: 'content',
          label: '内容关键词',
          type: 'input',
          placeholder: '请输入内容关键词',
        },
        {
          prop: 'dateRange',
          label: '创建时间',
          type: 'daterange',
          placeholder: '选择时间范围',
          format: 'yyyy-MM-dd',
          valueFormat: 'yyyy-MM-dd HH:mm:ss',
        },
      ],
      // 高级搜索字段
      advancedFields: [],
      // 批量操作配置
      batchActions: [
        {
          key: 'delete',
          label: '批量删除',
          type: 'danger',
          icon: 'el-icon-delete',
          confirm: true,
          confirmMessage: '确定要删除选中的动态吗？删除后不可恢复！',
        },
        {
          key: 'approve',
          label: '批量通过',
          type: 'success',
          icon: 'el-icon-check',
          requireRemark: true,
          remarkMessage: '请输入审核通过的原因',
        },
        {
          key: 'reject',
          label: '批量拒绝',
          type: 'warning',
          icon: 'el-icon-close',
          requireRemark: true,
          remarkMessage: '请输入审核拒绝的原因',
        },
      ],
    }
  },
  created() {
    this.initializeFromRoute()
    this.getList()
  },
  methods: {
    /** 查询动态列表 */
    getList() {
      this.loading = true
      listMoment(this.queryParams).then((response) => {
        // B端管理接口返回格式: {rows: [], total: number}
        this.momentList = response.rows || []
        this.total = response.total || 0
        this.loading = false
      }).catch((error) => {
        console.error('获取动态列表失败:', error)
        this.loading = false
        this.loadError = true
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {}
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.page = 0
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm('queryForm')
      this.handleQuery()
    },
    /** 新的搜索处理方法 */
    handleSearch(searchParams) {
      // 处理日期范围
      if (searchParams.dateRange && searchParams.dateRange.length === 2) {
        searchParams.startTime = searchParams.dateRange[0]
        searchParams.endTime = searchParams.dateRange[1]
        delete searchParams.dateRange
      }

      // 更新查询参数
      this.queryParams = {
        ...this.queryParams,
        ...searchParams,
        page: 0,
      }
      this.getList()
    },
    /** 重置搜索 */
    handleReset() {
      this.queryParams = {
        page: 0,
        size: 10,
        userId: null,
        momentType: null,
        visibility: null,
        sortBy: 'createTime',
        sortDirection: 'desc'
      }
      this.getList()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 批量操作处理 */
    handleBatchAction(action) {
      const selectedIds = this.ids

      switch (action.key) {
        case 'delete':
          this.batchDelete(selectedIds)
          break
        case 'approve':
          this.batchAudit(selectedIds, 'approved', action.remark)
          break
        case 'reject':
          this.batchAudit(selectedIds, 'rejected', action.remark)
          break
      }
    },
    /** 批量删除 */
    async batchDelete(ids) {
      try {
        // 使用Promise.all并发删除多个动态
        await Promise.all(ids.map(id => delMoment(id)))
        this.$modal.msgSuccess('删除成功')
        this.getList()
        this.clearSelection()
      } catch (error) {
        console.error('批量删除失败:', error)
        this.$modal.msgError('删除失败')
      }
    },
    /** 批量审核 */
    async batchAudit(ids, status, remark) {
      try {
        const auditData = {
          momentIds: ids,
          auditStatus: status,
          auditRemark: remark,
        }
        await auditMoments(auditData)
        this.$modal.msgSuccess('审核成功')
        this.getList()
        this.clearSelection()
      } catch (error) {
        this.$modal.msgError('审核失败')
      }
    },
    /** 清空选择 */
    clearSelection() {
      this.$refs.table && this.$refs.table.clearSelection()
      this.ids = []
      this.single = true
      this.multiple = true
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset()
      const id = row.id || this.ids
      getMoment(id).then((response) => {
        this.form = response.data
        this.open = true
        this.title = '动态详情'

        // 如果有评论，加载评论列表
        if (this.form.commentCount > 0) {
          this.loadMomentComments(id)
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids
      this.$modal
        .confirm('是否确认删除动态编号为"' + ids + '"的数据项？')
        .then(function () {
          return delMoment(ids)
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('删除成功')
        })
        .catch(() => {})
    },
    /** 批量审核操作 */
    handleAudit(status) {
      const statusText = status === 'approved' ? '通过' : '拒绝'
      this.$modal
        .confirm('是否确认' + statusText + '选中的动态？')
        .then(() => {
          return auditMoments({ ids: this.ids, status: status })
        })
        .then(() => {
          this.getList()
          this.$modal.msgSuccess('审核成功')
        })
        .catch(() => {})
    },

    /** 加载动态评论 */
    loadMomentComments(momentId) {
      this.commentLoading = true
      getMomentComments(momentId, 1, 5)
        .then((response) => {
          this.momentComments = response.rows || []
          this.commentLoading = false
        })
        .catch(() => {
          this.commentLoading = false
        })
    },

    /** 删除评论 */
    deleteComment(comment) {
      this.$modal
        .confirm('是否确认删除该评论？')
        .then(() => {
          return delComment(comment.id)
        })
        .then(() => {
          this.loadMomentComments(this.form.id)
          this.$modal.msgSuccess('删除成功')
          // 更新评论数量
          this.form.commentCount = Math.max(0, this.form.commentCount - 1)
        })
        .catch(() => {})
    },

    /** 从动态列表跳转到评论管理 */
    viewMomentComments(moment) {
      this.$router.push({
        path: '/fishing-biz/comment',
        query: {
          momentId: moment.id,
          momentContent: moment.content,
        },
      })
    },

    /** 从路由参数初始化页面 */
    initializeFromRoute() {
      const query = this.$route.query
      const { momentId, action, autoFilter, taskType } = query
      
      // 处理来自Dashboard的自动过滤
      if (autoFilter === 'true') {
        if (taskType === 'report') {
          // 举报处理任务：过滤违规内容
          this.queryParams.isViolation = 'true'
          this.queryParams.auditStatus = 'PENDING' // 假设举报的内容需要待审核
          this.$message.success('已自动筛选出违规举报的内容')
        }
        
        // 处理其他查询参数
        if (query.auditStatus) {
          this.queryParams.auditStatus = query.auditStatus
        }
        if (query.isViolation) {
          this.queryParams.isViolation = query.isViolation
        }
      }
      
      if (momentId && action === 'view') {
        // 自动打开动态详情
        this.$nextTick(() => {
          const moment = this.momentList.find(
            (m) => m.id.toString() === momentId.toString(),
          )
          if (moment) {
            this.handleView(moment)
          } else {
            // 如果当前列表中没有该动态，尝试直接加载
            getMoment(momentId)
              .then((response) => {
                this.form = response.data
                this.open = true
                this.title = '动态详情'

                // 如果有评论，加载评论列表
                if (this.form.commentCount > 0) {
                  this.loadMomentComments(momentId)
                }
              })
              .catch(() => {
                this.$message.error('动态不存在或已被删除')
              })
          }
        })
      }
    },

    /** 检查是否有结构化数据 */
    hasStructuredData(moment) {
      if (!moment.momentType) return false
      
      switch (moment.momentType) {
        case 'fishing_catch':
          return moment.fishingCatch && Object.keys(moment.fishingCatch).length > 0
        case 'equipment':
          return moment.equipment && Object.keys(moment.equipment).length > 0
        case 'technique':
          return moment.technique && Object.keys(moment.technique).length > 0
        case 'question':
          return moment.question && Object.keys(moment.question).length > 0
        default:
          return false
      }
    },

    /** 格式化结构化数据为可读文本 */
    formatStructuredData(moment) {
      if (!this.hasStructuredData(moment)) return '无结构化数据'
      
      const data = this.getStructuredDataObject(moment)
      if (!data) return '无数据'
      
      let result = []
      
      switch (moment.momentType) {
        case 'fishing_catch':
          result.push(`总重量: ${data.totalWeight || 0}kg`)
          result.push(`钓法: ${data.fishingMethod || '-'}`)
          result.push(`天气: ${data.weatherConditions || '-'}`)
          if (data.caughtFishes && data.caughtFishes.length > 0) {
            result.push(`捕获鱼类: ${data.caughtFishes.length}种`)
            data.caughtFishes.forEach((fish, index) => {
              result.push(`  ${index + 1}. ${fish.fishTypeName} ${fish.count}条 ${fish.weight || 0}kg`)
            })
          }
          break
          
        case 'equipment':
          result.push(`装备名称: ${data.equipmentName || '-'}`)
          result.push(`品牌: ${data.brand || '-'}`)
          result.push(`型号: ${data.model || '-'}`)
          result.push(`价格: ${data.price || '-'}`)
          result.push(`评分: ${data.rating ? data.rating + '星' : '-'}`)
          break
          
        case 'technique':
          result.push(`技巧名称: ${data.techniqueName || '-'}`)
          result.push(`难度: ${this.translateDifficulty(data.difficulty)}`)
          result.push(`成功率: ${data.successRate || 0}%`)
          result.push(`所需工具: ${data.toolsRequired || '-'}`)
          break
          
        case 'question':
          result.push(`问题标题: ${data.questionTitle || '-'}`)
          result.push(`紧急程度: ${this.translateUrgency(data.urgency)}`)
          result.push(`状态: ${this.translateQuestionStatus(data.status)}`)
          if (data.tags && data.tags.length > 0) {
            result.push(`标签: ${data.tags.map(tag => tag.name).join(', ')}`)
          }
          break
      }
      
      return result.join('\n')
    },

    /** 获取结构化数据摘要信息 */
    getStructuredDataSummary(moment) {
      if (!this.hasStructuredData(moment)) return '无数据'
      
      const data = this.getStructuredDataObject(moment)
      if (!data) return '无数据'
      
      switch (moment.momentType) {
        case 'fishing_catch':
          const fishCount = data.caughtFishes ? data.caughtFishes.length : 0
          return `${fishCount}种鱼, ${data.totalWeight || 0}kg`
          
        case 'equipment':
          return data.equipmentName || '装备信息'
          
        case 'technique':
          return data.techniqueName || '技巧信息'
          
        case 'question':
          return data.questionTitle || '问题信息'
          
        default:
          return '结构化数据'
      }
    },

    /** 获取结构化数据对象 */
    getStructuredDataObject(moment) {
      switch (moment.momentType) {
        case 'fishing_catch':
          return moment.fishingCatch
        case 'equipment':
          return moment.equipment
        case 'technique':
          return moment.technique
        case 'question':
          return moment.question
        default:
          return null
      }
    },

    /** 获取结构化数据标题 */
    getStructuredDataTitle(momentType) {
      switch (momentType) {
        case 'fishing_catch': return '钓获详情'
        case 'equipment': return '装备信息'
        case 'technique': return '技巧详情'
        case 'question': return '问题详情'
        default: return '结构化数据'
      }
    },

    /** 获取结构化数据展示组件 */
    getStructuredDataComponent(momentType) {
      // 返回内联组件模板
      switch (momentType) {
        case 'fishing_catch':
          return {
            props: ['data'],
            template: `
              <div>
                <el-descriptions :column="2" border size="small">
                  <el-descriptions-item label="总重量">{{ data.fishingCatch?.totalWeight || 0 }}kg</el-descriptions-item>
                  <el-descriptions-item label="钓法">{{ data.fishingCatch?.fishingMethod || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="天气条件">{{ data.fishingCatch?.weatherConditions || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="钓鱼时长">{{ data.fishingCatch?.fishingDuration || 0 }}分钟</el-descriptions-item>
                </el-descriptions>
                <div v-if="data.fishingCatch?.caughtFishes?.length > 0" style="margin-top: 15px;">
                  <h5>捕获鱼类 ({{ data.fishingCatch.caughtFishes.length }}种)</h5>
                  <el-table :data="data.fishingCatch.caughtFishes" size="small" style="margin-top: 8px;">
                    <el-table-column label="鱼种" prop="fishTypeName" />
                    <el-table-column label="数量" prop="count" width="80" />
                    <el-table-column label="重量(kg)" prop="weight" width="100" />
                    <el-table-column label="尺寸(cm)" prop="size" width="100" />
                    <el-table-column label="备注" prop="remark" />
                  </el-table>
                </div>
              </div>
            `
          }
        case 'equipment':
          return {
            props: ['data'],
            template: `
              <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="装备名称">{{ data.equipment?.equipmentName || '-' }}</el-descriptions-item>
                <el-descriptions-item label="装备类型">{{ data.equipment?.equipmentType || '-' }}</el-descriptions-item>
                <el-descriptions-item label="品牌">{{ data.equipment?.brand || '-' }}</el-descriptions-item>
                <el-descriptions-item label="型号">{{ data.equipment?.model || '-' }}</el-descriptions-item>
                <el-descriptions-item label="价格">{{ data.equipment?.price || '-' }}</el-descriptions-item>
                <el-descriptions-item label="评分">{{ data.equipment?.rating ? data.equipment.rating + '星' : '-' }}</el-descriptions-item>
                <el-descriptions-item label="优点" :span="2">{{ data.equipment?.pros || '-' }}</el-descriptions-item>
                <el-descriptions-item label="缺点" :span="2">{{ data.equipment?.cons || '-' }}</el-descriptions-item>
                <el-descriptions-item label="使用技巧" :span="2">{{ data.equipment?.usageTips || '-' }}</el-descriptions-item>
              </el-descriptions>
            `
          }
        case 'technique':
          return {
            props: ['data'],
            template: `
              <el-descriptions :column="2" border size="small">
                <el-descriptions-item label="技巧名称">{{ data.technique?.techniqueName || '-' }}</el-descriptions-item>
                <el-descriptions-item label="难度等级">{{ translateDifficulty(data.technique?.difficulty) }}</el-descriptions-item>
                <el-descriptions-item label="成功率">{{ data.technique?.successRate || 0 }}%</el-descriptions-item>
                <el-descriptions-item label="最佳时间">{{ data.technique?.bestTime || '-' }}</el-descriptions-item>
                <el-descriptions-item label="所需工具" :span="2">{{ data.technique?.toolsRequired || '-' }}</el-descriptions-item>
                <el-descriptions-item label="详细步骤" :span="2">
                  <pre style="white-space: pre-wrap; margin: 0;">{{ data.technique?.detailedSteps || '-' }}</pre>
                </el-descriptions-item>
                <el-descriptions-item label="常见错误" :span="2">{{ data.technique?.commonMistakes || '-' }}</el-descriptions-item>
              </el-descriptions>
            `,
            methods: {
              translateDifficulty: this.translateDifficulty
            }
          }
        case 'question':
          return {
            props: ['data'],
            template: `
              <div>
                <el-descriptions :column="2" border size="small">
                  <el-descriptions-item label="问题标题" :span="2">{{ data.question?.questionTitle || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="紧急程度">{{ translateUrgency(data.question?.urgency) }}</el-descriptions-item>
                  <el-descriptions-item label="问题状态">{{ translateQuestionStatus(data.question?.status) }}</el-descriptions-item>
                </el-descriptions>
                <div v-if="data.question?.tags?.length > 0" style="margin-top: 15px;">
                  <h5>相关标签</h5>
                  <el-tag 
                    v-for="tag in data.question.tags" 
                    :key="tag.id" 
                    size="small" 
                    style="margin-right: 8px; margin-top: 4px;"
                  >
                    {{ tag.name }}
                  </el-tag>
                </div>
              </div>
            `,
            methods: {
              translateUrgency: this.translateUrgency,
              translateQuestionStatus: this.translateQuestionStatus
            }
          }
        default:
          return {
            props: ['data'],
            template: '<div>暂无结构化数据</div>'
          }
      }
    },

    /** 根据动态类型获取标签颜色 */
    getSpecificDataTagType(momentType) {
      switch (momentType) {
        case 'fishing_catch': return 'success'
        case 'equipment': return 'primary'
        case 'technique': return 'info'
        case 'question': return 'warning'
        default: return ''
      }
    },

    /** 翻译字段名为中文 */
    translateFieldName(fieldName) {
      const fieldMap = {
        // 钓获相关
        fishSpecies: '鱼种',
        fishWeight: '重量',
        fishLength: '长度',
        catchTime: '钓获时间',
        bait: '饵料',
        weather: '天气',
        
        // 装备相关
        equipmentName: '装备名称',
        equipmentType: '装备类型',
        brand: '品牌',
        model: '型号',
        price: '价格',
        
        // 技巧相关
        techniqueName: '技巧名称',
        techniqueType: '技巧类型',
        difficulty: '难度',
        steps: '步骤',
        
        // 问题相关
        questionType: '问题类型',
        questionCategory: '问题分类',
        urgency: '紧急程度',
        
        // 通用字段
        description: '描述',
        notes: '备注',
        tags: '标签',
        location: '位置',
        coordinates: '坐标'
      }
      
      return fieldMap[fieldName] || fieldName
    },

    /** 翻译难度等级 */
    translateDifficulty(difficulty) {
      switch (difficulty) {
        case 'beginner': return '初级'
        case 'intermediate': return '中级'
        case 'advanced': return '高级'
        default: return difficulty || '-'
      }
    },

    /** 翻译紧急程度 */
    translateUrgency(urgency) {
      switch (urgency) {
        case 'low': return '低'
        case 'medium': return '中'
        case 'high': return '高'
        default: return urgency || '-'
      }
    },

    /** 翻译问题状态 */
    translateQuestionStatus(status) {
      switch (status) {
        case 'unsolved': return '未解决'
        case 'solved': return '已解决'
        case 'closed': return '已关闭'
        default: return status || '-'
      }
    },
  },
}
</script>

<style scoped>
.comment-content {
  text-align: left;
}

.parent-comment {
  font-size: 12px;
  color: #999;
  margin: 0 0 4px 0;
  background: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

.current-comment {
  margin: 0;
  font-size: 14px;
}

.moment-images {
  display: flex;
  align-items: center;
  justify-content: center;
}

.more-images {
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

.no-image {
  color: #ccc;
  font-size: 12px;
}

.specific-data {
  display: flex;
  justify-content: center;
}

.no-data {
  color: #ccc;
  font-size: 12px;
}
</style>
