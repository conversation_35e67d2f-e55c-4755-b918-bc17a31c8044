<template>
  <div class="app-container">
    <el-card class="overview-card" shadow="never">
      <div slot="header" class="clearfix">
        <span class="card-title">动态数据概览</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          style="float: right"
          @change="handleDateChange"
        />
      </div>

      <!-- 数据概览卡片 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6" v-for="(stat, index) in overviewStats" :key="index">
          <div class="stat-card" :class="`stat-${stat.type}`">
            <div class="stat-icon">
              <i :class="stat.icon"></i>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ stat.value }}</div>
              <div class="stat-label">{{ stat.label }}</div>
            </div>
            <div class="stat-trend" :class="stat.trend > 0 ? 'up' : 'down'">
              <i :class="stat.trend > 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(stat.trend) }}%
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 动态类型分布 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header">
            <span class="card-title">动态类型分布</span>
          </div>
          <div ref="typeChart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>

      <!-- 每日发布趋势 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header">
            <span class="card-title">每日发布趋势</span>
          </div>
          <div ref="trendChart" style="width: 100%; height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px">
      <!-- 用户活跃度排行 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header">
            <span class="card-title">用户活跃度排行 TOP 10</span>
          </div>
          <el-table :data="topUsers" size="small" :show-header="false" style="margin-top: 10px;">
            <el-table-column width="50">
              <template slot-scope="scope">
                <div class="rank-badge" :class="`rank-${scope.$index + 1 <= 3 ? scope.$index + 1 : 'other'}`">
                  {{ scope.$index + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="user-info">
                  <el-avatar :src="scope.row.userAvatar" size="small"></el-avatar>
                  <span class="username">{{ scope.row.username }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="100">
              <template slot-scope="scope">
                <div class="moment-count">{{ scope.row.momentCount }}条动态</div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>

      <!-- 热门钓点统计 -->
      <el-col :span="12">
        <el-card shadow="never">
          <div slot="header">
            <span class="card-title">热门钓点 TOP 10</span>
          </div>
          <el-table :data="topFishingSpots" size="small" :show-header="false" style="margin-top: 10px;">
            <el-table-column width="50">
              <template slot-scope="scope">
                <div class="rank-badge" :class="`rank-${scope.$index + 1 <= 3 ? scope.$index + 1 : 'other'}`">
                  {{ scope.$index + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column>
              <template slot-scope="scope">
                <div class="spot-info">
                  <span class="spot-name">{{ scope.row.spotName }}</span>
                  <span class="spot-address">{{ scope.row.address }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="100">
              <template slot-scope="scope">
                <div class="moment-count">{{ scope.row.momentCount }}条动态</div>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 结构化数据统计 -->
    <el-row style="margin-top: 20px">
      <el-col :span="24">
        <el-card shadow="never">
          <div slot="header">
            <span class="card-title">结构化数据统计</span>
          </div>
          
          <el-row :gutter="20">
            <el-col :span="6" v-for="(stat, key) in structuredStats" :key="key">
              <div class="structured-stat-card">
                <div class="stat-header">
                  <el-tag :type="getStatTagType(key)">{{ getStatTitle(key) }}</el-tag>
                </div>
                <div class="stat-body">
                  <el-descriptions :column="1" size="small" border>
                    <el-descriptions-item 
                      v-for="(value, field) in stat" 
                      :key="field"
                      :label="translateStatField(key, field)"
                    >
                      {{ formatStatValue(value) }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getMomentStats } from '@/api/fishing-biz/moment'

export default {
  name: 'MomentStatistics',
  data() {
    return {
      dateRange: null,
      loading: false,
      overviewStats: [
        {
          label: '总动态数',
          value: 0,
          trend: 12.5,
          icon: 'el-icon-document',
          type: 'primary'
        },
        {
          label: '今日新增',
          value: 0,
          trend: 8.2,
          icon: 'el-icon-plus',
          type: 'success'
        },
        {
          label: '活跃用户',
          value: 0,
          trend: -2.1,
          icon: 'el-icon-user',
          type: 'warning'
        },
        {
          label: '互动总数',
          value: 0,
          trend: 15.8,
          icon: 'el-icon-star-on',
          type: 'danger'
        }
      ],
      topUsers: [],
      topFishingSpots: [],
      structuredStats: {
        fishing_catch: {},
        equipment: {},
        technique: {},
        question: {}
      },
      typeChart: null,
      trendChart: null
    }
  },
  mounted() {
    this.loadStatistics()
    this.$nextTick(() => {
      this.initCharts()
    })
  },
  beforeDestroy() {
    if (this.typeChart) {
      this.typeChart.dispose()
    }
    if (this.trendChart) {
      this.trendChart.dispose()
    }
  },
  methods: {
    async loadStatistics() {
      this.loading = true
      try {
        // 模拟数据，实际应该调用后端API
        await this.loadMockData()
      } catch (error) {
        console.error('加载统计数据失败:', error)
        this.$message.error('加载统计数据失败')
      } finally {
        this.loading = false
      }
    },

    async loadMockData() {
      // 模拟统计数据
      this.overviewStats = [
        {
          label: '总动态数',
          value: '12,468',
          trend: 12.5,
          icon: 'el-icon-document',
          type: 'primary'
        },
        {
          label: '今日新增',
          value: '156',
          trend: 8.2,
          icon: 'el-icon-plus',
          type: 'success'
        },
        {
          label: '活跃用户',
          value: '3,247',
          trend: -2.1,
          icon: 'el-icon-user',
          type: 'warning'
        },
        {
          label: '互动总数',
          value: '38,542',
          trend: 15.8,
          icon: 'el-icon-star-on',
          type: 'danger'
        }
      ]

      this.topUsers = [
        { username: '钓鱼达人01', userAvatar: '', momentCount: 156 },
        { username: '湖边小王', userAvatar: '', momentCount: 134 },
        { username: '钓友老李', userAvatar: '', momentCount: 128 },
        { username: '野钓高手', userAvatar: '', momentCount: 115 },
        { username: '鲫鱼杀手', userAvatar: '', momentCount: 98 }
      ]

      this.topFishingSpots = [
        { spotName: '西湖', address: '杭州市', momentCount: 245 },
        { spotName: '太湖', address: '无锡市', momentCount: 198 },
        { spotName: '东湖', address: '武汉市', momentCount: 167 },
        { spotName: '北海公园', address: '北京市', momentCount: 134 },
        { spotName: '玄武湖', address: '南京市', momentCount: 112 }
      ]

      this.structuredStats = {
        fishing_catch: {
          totalMoments: 5234,
          avgFishCount: 3.2,
          avgWeight: 2.8,
          popularFish: '鲫鱼',
          totalFishCaught: 16748
        },
        equipment: {
          totalMoments: 2156,
          avgRating: 4.2,
          popularBrand: '达亿瓦',
          avgPrice: 299,
          totalReviews: 8934
        },
        technique: {
          totalMoments: 1876,
          avgSuccessRate: 72,
          popularDifficulty: '中级',
          totalViews: 45231,
          avgRating: 4.5
        },
        question: {
          totalMoments: 3202,
          solvedRate: 68,
          avgResponseTime: 2.4,
          popularTag: '新手求助',
          totalAnswers: 12456
        }
      }

      // 更新图表数据
      setTimeout(() => {
        this.updateCharts()
      }, 100)
    },

    initCharts() {
      // 初始化动态类型分布饼图
      this.typeChart = echarts.init(this.$refs.typeChart)
      
      // 初始化趋势图
      this.trendChart = echarts.init(this.$refs.trendChart)
    },

    updateCharts() {
      if (!this.typeChart || !this.trendChart) return

      // 动态类型分布饼图
      const typeOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          data: ['钓获分享', '装备展示', '技巧分享', '问答求助']
        },
        series: [
          {
            name: '动态类型',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            data: [
              { value: 5234, name: '钓获分享' },
              { value: 2156, name: '装备展示' },
              { value: 1876, name: '技巧分享' },
              { value: 3202, name: '问答求助' }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      // 每日发布趋势图
      const trendOption = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['发布数量']
        },
        xAxis: {
          type: 'category',
          data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '发布数量',
            type: 'line',
            data: [156, 143, 178, 134, 189, 234, 198],
            smooth: true,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.8)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
                ]
              }
            }
          }
        ]
      }

      this.typeChart.setOption(typeOption)
      this.trendChart.setOption(trendOption)
    },

    handleDateChange(dateRange) {
      // 处理日期范围变化
      this.loadStatistics()
    },

    getStatTagType(type) {
      const typeMap = {
        fishing_catch: 'success',
        equipment: 'primary',
        technique: 'info',
        question: 'warning'
      }
      return typeMap[type] || ''
    },

    getStatTitle(type) {
      const titleMap = {
        fishing_catch: '钓获分享',
        equipment: '装备展示',
        technique: '技巧分享',
        question: '问答求助'
      }
      return titleMap[type] || type
    },

    translateStatField(type, field) {
      const fieldMaps = {
        fishing_catch: {
          totalMoments: '总动态数',
          avgFishCount: '平均鱼获数',
          avgWeight: '平均重量(kg)',
          popularFish: '热门鱼种',
          totalFishCaught: '总鱼获数'
        },
        equipment: {
          totalMoments: '总动态数',
          avgRating: '平均评分',
          popularBrand: '热门品牌',
          avgPrice: '平均价格(元)',
          totalReviews: '总评价数'
        },
        technique: {
          totalMoments: '总动态数',
          avgSuccessRate: '平均成功率(%)',
          popularDifficulty: '热门难度',
          totalViews: '总浏览数',
          avgRating: '平均评分'
        },
        question: {
          totalMoments: '总动态数',
          solvedRate: '解决率(%)',
          avgResponseTime: '平均响应时间(小时)',
          popularTag: '热门标签',
          totalAnswers: '总回答数'
        }
      }
      
      return fieldMaps[type]?.[field] || field
    },

    formatStatValue(value) {
      if (typeof value === 'number') {
        return value.toLocaleString()
      }
      return value
    }
  }
}
</script>

<style scoped>
.overview-card {
  margin-bottom: 20px;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stats-row {
  margin-top: 20px;
}

.stat-card {
  position: relative;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
}

.stat-card.stat-success {
  border-left-color: #67C23A;
}

.stat-card.stat-warning {
  border-left-color: #E6A23C;
}

.stat-card.stat-danger {
  border-left-color: #F56C6C;
}

.stat-icon {
  font-size: 32px;
  color: #409EFF;
  margin-right: 15px;
}

.stat-success .stat-icon {
  color: #67C23A;
}

.stat-warning .stat-icon {
  color: #E6A23C;
}

.stat-danger .stat-icon {
  color: #F56C6C;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-top: 5px;
}

.stat-trend {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 12px;
}

.stat-trend.up {
  color: #67C23A;
}

.stat-trend.down {
  color: #F56C6C;
}

.rank-badge {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
}

.rank-1 {
  background: linear-gradient(45deg, #FFD700, #FFA500);
}

.rank-2 {
  background: linear-gradient(45deg, #C0C0C0, #808080);
}

.rank-3 {
  background: linear-gradient(45deg, #CD7F32, #8B4513);
}

.rank-other {
  background: #909399;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  margin-left: 10px;
  font-weight: 500;
}

.spot-info {
  display: flex;
  flex-direction: column;
}

.spot-name {
  font-weight: 500;
  color: #303133;
}

.spot-address {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.moment-count {
  color: #409EFF;
  font-weight: 500;
}

.structured-stat-card {
  height: 100%;
}

.stat-header {
  margin-bottom: 10px;
}

.stat-body {
  height: calc(100% - 40px);
}
</style>