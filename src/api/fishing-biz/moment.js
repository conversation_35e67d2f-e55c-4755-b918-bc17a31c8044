import request from '@/utils/request'

// 查询结构化动态列表 (管理后台专用)
export function listMoment(query) {
  return request({
    url: '/admin-api/moments/list',
    method: 'get',
    params: query,
    baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:8080'
  })
}

// 查询结构化动态详细信息 (管理后台专用)
export function getMoment(id) {
  return request({
    url: `/admin-api/moments/${id}`,
    method: 'get',
    baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:8080'
  })
}

// 删除结构化动态
export function delMoment(id) {
  return request({
    url: `/user-api/structured-moments/${id}`,
    method: 'delete',
    baseURL: process.env.VUE_APP_USER_API_BASE_URL || 'http://localhost:9010/api/v1'
  })
}

// 获取用户的结构化动态列表
export function getUserMoments(userId, query) {
  return request({
    url: `/user-api/structured-moments/user/${userId}`,
    method: 'get',
    params: query,
    baseURL: process.env.VUE_APP_USER_API_BASE_URL || 'http://localhost:9010/api/v1'
  })
}

// 获取钓点的结构化动态列表
export function getFishingSpotMoments(spotId, query) {
  return request({
    url: `/user-api/structured-moments/fishing-spot/${spotId}`,
    method: 'get',
    params: query,
    baseURL: process.env.VUE_APP_USER_API_BASE_URL || 'http://localhost:9010/api/v1'
  })
}

// 批量删除动态
export function batchDelMoment(ids) {
  const promises = ids.map(id => delMoment(id))
  return Promise.all(promises)
}

// 批量审核动态 (管理后台专用功能，需要后端B端模块支持)
export function auditMoments(data) {
  return request({
    url: '/admin-api/moments/audit',
    method: 'put',
    data: data,
    baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:8080'
  })
}

// 批量设置动态可见性 (管理后台专用功能)
export function setMomentVisibility(data) {
  return request({
    url: '/admin-api/moments/visibility',
    method: 'put',
    data: data,
    baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:8080'
  })
}

// 获取动态统计信息 (管理后台专用功能)
export function getMomentStats() {
  return request({
    url: '/admin-api/moments/stats',
    method: 'get',
    baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:8080'
  })
}
