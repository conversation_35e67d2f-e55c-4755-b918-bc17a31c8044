import request from '@/utils/request'

// 查询结构化动态列表 (管理后台专用)
export function listMoment(query) {
  return request({
    url: '/fishing-biz/moment/list',
    method: 'get',
    params: query,
  })
}

// 查询结构化动态详细信息 (管理后台专用)
export function getMoment(id) {
  return request({
    url: `/fishing-biz/moment/${id}`,
    method: 'get',
  })
}

// 删除结构化动态
export function delMoment(id) {
  return request({
    url: `/fishing-biz/moment/${id}`,
    method: 'delete',
  })
}

// 获取用户的结构化动态列表
export function getUserMoments(userId, query) {
  return request({
    url: `/user-api/structured-moments/user/${userId}`,
    method: 'get',
    params: query,
    baseURL:
      process.env.VUE_APP_USER_API_BASE_URL || 'http://localhost:9010/api/v1',
  })
}

// 获取钓点的结构化动态列表
export function getFishingSpotMoments(spotId, query) {
  return request({
    url: `/user-api/structured-moments/fishing-spot/${spotId}`,
    method: 'get',
    params: query,
    baseURL:
      process.env.VUE_APP_USER_API_BASE_URL || 'http://localhost:9010/api/v1',
  })
}

// 批量删除动态 (管理后台专用功能)
export function batchDelMoment(ids) {
  return request({
    url: '/fishing-biz/moment/batch',
    method: 'delete',
    data: ids,
  })
}

// 批量审核动态 (管理后台专用功能)
export function auditMoments(data) {
  return request({
    url: '/fishing-biz/moment/audit',
    method: 'put',
    data: data,
  })
}

// 批量设置动态可见性 (管理后台专用功能)
export function setMomentVisibility(data) {
  return request({
    url: '/fishing-biz/moment/visibility',
    method: 'put',
    data: data,
  })
}

// 获取动态统计信息 (管理后台专用功能)
export function getMomentStats() {
  return request({
    url: '/fishing-biz/moment/stats',
    method: 'get',
  })
}

// 获取待审核动态列表
export function getPendingMoments(query) {
  return request({
    url: '/fishing-biz/moment/pending',
    method: 'get',
    params: query,
  })
}

// 获取被举报动态列表
export function getReportedMoments(query) {
  return request({
    url: '/fishing-biz/moment/reported',
    method: 'get',
    params: query,
  })
}

// 获取动态结构化数据
export function getMomentStructuredData(id) {
  return request({
    url: `/fishing-biz/moment/${id}/structured-data`,
    method: 'get',
  })
}

// 批量获取动态结构化数据
export function batchGetMomentStructuredData(momentIds) {
  return request({
    url: '/fishing-biz/moment/structured-data/batch',
    method: 'post',
    data: momentIds,
  })
}

// 标记动态为违规
export function markMomentAsViolation(id, reason) {
  return request({
    url: `/fishing-biz/moment/${id}/violation`,
    method: 'put',
    params: { reason },
  })
}

// 取消动态违规标记
export function unmarkMomentViolation(id) {
  return request({
    url: `/fishing-biz/moment/${id}/violation`,
    method: 'delete',
  })
}

// 获取动态举报信息
export function getMomentReports(id) {
  return request({
    url: `/fishing-biz/moment/${id}/reports`,
    method: 'get',
  })
}

// 获取动态统计详情
export function getMomentStatistics(id) {
  return request({
    url: `/fishing-biz/moment/${id}/statistics`,
    method: 'get',
  })
}

// 获取用户动态数量
export function getUserMomentCount(userId) {
  return request({
    url: `/fishing-biz/moment/user/${userId}/count`,
    method: 'get',
  })
}

// 获取钓点动态数量
export function getSpotMomentCount(spotId) {
  return request({
    url: `/fishing-biz/moment/spot/${spotId}/count`,
    method: 'get',
  })
}

// 验证所有动态数据完整性
export function validateAllMomentData() {
  return request({
    url: '/fishing-biz/moment/validate',
    method: 'get',
  })
}

// 验证指定动态的数据完整性
export function validateMomentData(id) {
  return request({
    url: `/fishing-biz/moment/validate/${id}`,
    method: 'get',
  })
}

// 检查孤立的结构化数据
export function checkOrphanedData() {
  return request({
    url: '/fishing-biz/moment/validate/orphaned',
    method: 'get',
  })
}

// 修复动态数据不一致问题
export function repairMomentData(momentIds) {
  return request({
    url: '/fishing-biz/moment/repair',
    method: 'post',
    data: momentIds,
  })
}
