#!/bin/bash
# Design Tokens 违规检查脚本
# 用于检查和验证 Flutter 应用是否符合 Material Design 3 规范

set -e

PROJECT_ROOT="/Users/<USER>/Documents/yunmiaokeji/user-app-flutter/user_app"
LIB_PATH="$PROJECT_ROOT/lib"

echo "🔍 开始检查 Flutter 应用的 Design Tokens 违规情况..."
echo "项目路径: $PROJECT_ROOT"
echo ""

# 颜色总数统计
total_violations=0
color_violations=0
spacing_violations=0
typography_violations=0
import_violations=0

echo "=== 1. 检查硬编码颜色违规 ==="

# 检查 Color(0xFF...) 硬编码
color_hex_files=$(find "$LIB_PATH" -name "*.dart" -exec grep -l "Color(0x[A-Fa-f0-9]*)" {} \; 2>/dev/null | wc -l || echo "0")
if [ "$color_hex_files" -gt 0 ]; then
    echo "❌ 发现 $color_hex_files 个文件使用硬编码颜色 Color(0xFF...)"
    color_violations=$((color_violations + color_hex_files))
else
    echo "✅ 未发现 Color(0xFF...) 硬编码违规"
fi

# 检查 Colors.* 静态颜色
colors_static_files=$(find "$LIB_PATH" -name "*.dart" -exec grep -l "Colors\.[a-zA-Z]" {} \; 2>/dev/null | wc -l || echo "0")
if [ "$colors_static_files" -gt 0 ]; then
    echo "❌ 发现 $colors_static_files 个文件使用 Colors.* 静态颜色"
    color_violations=$((color_violations + colors_static_files))
else
    echo "✅ 未发现 Colors.* 静态颜色违规"
fi

echo ""
echo "=== 2. 检查硬编码间距违规 ==="

# 检查硬编码 EdgeInsets
spacing_files=$(find "$LIB_PATH" -name "*.dart" -exec grep -l "EdgeInsets\.(all|symmetric|only)([0-9]" {} \; 2>/dev/null | wc -l || echo "0")
if [ "$spacing_files" -gt 0 ]; then
    echo "❌ 发现 $spacing_files 个文件使用硬编码间距 EdgeInsets"
    spacing_violations=$((spacing_violations + spacing_files))
else
    echo "✅ 未发现硬编码间距违规"
fi

echo ""
echo "=== 3. 检查硬编码字体违规 ==="

# 检查硬编码字体大小
typography_files=$(find "$LIB_PATH" -name "*.dart" -exec grep -l "fontSize:[[:space:]]*[0-9]" {} \; 2>/dev/null | wc -l || echo "0")
if [ "$typography_files" -gt 0 ]; then
    echo "❌ 发现 $typography_files 个文件使用硬编码字体大小"
    typography_violations=$((typography_violations + typography_files))
else
    echo "✅ 未发现硬编码字体大小违规"
fi

echo ""
echo "=== 4. 检查相对路径导入违规 ==="

# 检查相对路径导入
relative_import_files=$(find "$LIB_PATH" -name "*.dart" -exec grep -l "import '\.\./\.\." {} \; 2>/dev/null | wc -l || echo "0")
if [ "$relative_import_files" -gt 0 ]; then
    echo "❌ 发现 $relative_import_files 个文件使用相对路径导入"
    import_violations=$((import_violations + relative_import_files))
else
    echo "✅ 未发现相对路径导入违规"
fi

echo ""
echo "=== 5. 检查桶文件完整性 ==="

# 检查关键桶文件是否存在
barrel_files_missing=0
required_barrels=(
    "core/design_tokens/design_tokens.dart"
    "shared/widgets/design_system/design_system.dart"
)

for barrel in "${required_barrels[@]}"; do
    if [ ! -f "$LIB_PATH/$barrel" ]; then
        echo "❌ 缺少桶文件: $barrel"
        barrel_files_missing=$((barrel_files_missing + 1))
    else
        echo "✅ 桶文件存在: $barrel"
    fi
done

echo ""
echo "=== 总结报告 ==="
total_violations=$((color_violations + spacing_violations + typography_violations + import_violations + barrel_files_missing))

echo "📊 违规统计:"
echo "   - 颜色违规: $color_violations 个文件"
echo "   - 间距违规: $spacing_violations 个文件"
echo "   - 字体违规: $typography_violations 个文件"
echo "   - 导入违规: $import_violations 个文件"
echo "   - 桶文件缺失: $barrel_files_missing 个"
echo "   - 总计违规: $total_violations 项"
echo ""

if [ "$total_violations" -eq 0 ]; then
    echo "🎉 恭喜！您的应用完全符合 Material Design 3 规范！"
    exit 0
else
    echo "⚠️  发现 $total_violations 项违规，需要修复"
    echo ""
    echo "📋 修复建议:"
    echo "1. 使用 Theme.of(context).colorScheme 替代硬编码颜色"
    echo "2. 使用 SpacingTokens 替代硬编码间距"
    echo "3. 使用 Theme.of(context).textTheme 替代硬编码字体"
    echo "4. 使用 package: 导入替代相对路径导入"
    echo "5. 创建缺失的桶文件"
    exit 1
fi