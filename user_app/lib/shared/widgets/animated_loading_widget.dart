import 'dart:async';
import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 动画加载组件，用于更好的用户体验
class AnimatedLoadingWidget extends StatefulWidget {
  final String message;
  final String? subMessage;
  final Color? primaryColor;
  final Color? backgroundColor;

 const AnimatedLoadingWidget({
    super.key,
    required this.message,
    this.subMessage,
    this.primaryColor,
    this.backgroundColor,
  });

  @override
  State<AnimatedLoadingWidget> createState() => _AnimatedLoadingWidgetState();
}

class _AnimatedLoadingWidgetState extends State<AnimatedLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _rotationController;
  late AnimationController _fadeController;
  
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    
    // 脉冲动画控制器
    _pulseController = AnimationController(
      duration: MotionTokens.durationLong,
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    
    // 旋转动画控制器
    _rotationController = AnimationController(
      duration: MotionTokens.durationExtraLong,
      vsync: this,
    );
    _rotationAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );
    
    // 淡入动画控制器
    _fadeController = AnimationController(
      duration: MotionTokens.durationMedium,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeOut),
    );
    
    // 启动动画
    _pulseController.repeat(reverse: true);
    _rotationController.repeat();
    _fadeController.forward();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _rotationController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            widget.backgroundColor ?? ColorTokens.surface,
            (widget.backgroundColor ?? ColorTokens.surface).withValues(alpha: 0.95),
          ],
        ),
      ),
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 动画加载图标
            _buildAnimatedIcon(),
            
            SpacingTokens.verticalSpaceLg,
            
            // 主要消息
            AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Text(
                    widget.message,
                    style: TypographyTokens.titleMedium.copyWith(
                      fontWeight: FontWeight.w600,
                      color: widget.primaryColor ?? ColorTokens.onSurface,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
            
            if (widget.subMessage != null) ...[
              SpacingTokens.verticalSpaceMd,
              Text(
                widget.subMessage!,
                style: TypographyTokens.bodyMedium.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            SpacingTokens.verticalSpaceXl,
            
            // 底部装饰动画
            _buildBottomDecoration(),
          ],
        ),
      ),
    );
  }

  Widget _buildAnimatedIcon() {
    return Stack(
      alignment: Alignment.center,
      children: [
        // 外圈脉冲效果
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Container(
              width: SpacingTokens.space24 * _pulseAnimation.value,
              height: SpacingTokens.space24 * _pulseAnimation.value,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: (widget.primaryColor ?? ColorTokens.primary)
                    .withValues(alpha: 0.1 / _pulseAnimation.value),
              ),
            );
          },
        ),
        
        // 中圈旋转效果
        AnimatedBuilder(
          animation: _rotationController,
          builder: (context, child) {
            return Transform.rotate(
              angle: _rotationAnimation.value * 2 * 3.14159,
              child: Container(
                width: SpacingTokens.space20,
                height: SpacingTokens.space20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: (widget.primaryColor ?? ColorTokens.primary)
                        .withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: Icon(
                  Icons.refresh,
                  size: SpacingTokens.space8,
                  color: (widget.primaryColor ?? ColorTokens.primary)
                      .withValues(alpha: 0.5),
                ),
              ),
            );
          },
        ),
        
        // 内圈图标
        Container(
          width: SpacingTokens.space16,
          height: SpacingTokens.space16,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: widget.primaryColor ?? ColorTokens.error,
            boxShadow: [
              BoxShadow(
                color: (widget.primaryColor ?? ColorTokens.primary)
                    .withValues(alpha: 0.3),
                blurRadius: 12,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.phishing,
            size: SpacingTokens.space7,
            color: ColorTokens.onPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomDecoration() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        return AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            final delay = index * 0.3;
            final animationValue = (_pulseController.value + delay) % 1.0;
            final scale = 0.5 + (0.5 * (1 - (animationValue - 0.5).abs() * 2));
            
            return Container(
              margin: EdgeInsets.symmetric(horizontal: SpacingTokens.space1),
              child: Transform.scale(
                scale: scale,
                child: Container(
                  width: SpacingTokens.space2,
                  height: SpacingTokens.space2,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: (widget.primaryColor ?? ColorTokens.primary)
                        .withValues(alpha: scale),
                  ),
                ),
              ),
            );
          },
        );
      }),
    );
  }
}

/// 活动创建专用的动画加载组件
class ActivityCreationAnimatedLoading extends StatefulWidget {
  final String? customMessage;

 const ActivityCreationAnimatedLoading({
    super.key,
    this.customMessage,
  });

  @override
  State<ActivityCreationAnimatedLoading> createState() => _ActivityCreationAnimatedLoadingState();
}

class _ActivityCreationAnimatedLoadingState extends State<ActivityCreationAnimatedLoading> {
  late PageController _messageController;
  late Timer _messageTimer;
  int _currentMessageIndex = 0;
  
  final List<String> _loadingMessages = [
    '正在应用模板并生成活动内容...',
    '正在为您配置钓法和鱼种...',
    '正在生成精彩的活动描述...',
    '正在设置活动时间和地点...',
    '正在完善活动详情...',
  ];

  @override
  void initState() {
    super.initState();
    _messageController = PageController();
    
    // 每2秒切换一次消息
    _messageTimer = Timer.periodic(MotionTokens.durationExtraLong, (timer) {
      if (mounted) {
        _currentMessageIndex = (_currentMessageIndex + 1) % _loadingMessages.length;
        _messageController.animateToPage(
          _currentMessageIndex,
          duration: MotionTokens.durationShort,
          curve: Curves.easeInOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _messageTimer.cancel();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            ColorTokens.surface,
            ColorTokens.surfaceContainer,
          ],
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 使用基础动画组件的图标部分
          _buildAnimatedIcon(),
          
         const SizedBox(height: 32),
          
          // 主标题
         const Text(
            '正在创建钓鱼活动',
            style: TypographyTokens.titleMedium.copyWith(
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          
          SpacingTokens.verticalSpaceMd,
          
          // 动态切换的消息
          SizedBox(
            height: 40,
            child: PageView.builder(
              controller: _messageController,
              itemCount: _loadingMessages.length,
              itemBuilder: (context, index) {
                return Center(
                  child: Text(
                    widget.customMessage ?? _loadingMessages[index],
                    style: TypographyTokens.bodyMedium.copyWith(
                      color: ColorTokens.onSurfaceVariant,
                    ),
                    textAlign: TextAlign.center,
                  ),
                );
              },
            ),
          ),
          
         const SizedBox(height: 40),
          
          // 底部提示和动画点
          _buildBottomSection(),
        ],
      ),
    );
  }

  Widget _buildAnimatedIcon() {
    return TweenAnimationBuilder<double>(
      duration: MotionTokens.durationLong,
      tween: Tween<double>(begin: 0, end: 1),
      builder: (context, value, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // 外圈脉冲效果
            Container(
              width: 120 * (0.8 + 0.4 * value),
              height: 120 * (0.8 + 0.4 * value),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorTokens.primary.withValues(alpha: 0.1 / (0.8 + 0.4 * value)),
              ),
            ),
            
            // 中圈旋转效果
            TweenAnimationBuilder<double>(
              duration: MotionTokens.durationExtraLong,
              tween: Tween<double>(begin: 0, end: 1),
              builder: (context, rotationValue, child) {
                return Transform.rotate(
                  angle: rotationValue * 2 * 3.14159,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: ColorTokens.primary.withValues(alpha: 0.3),
                        width: 2,
                      ),
                    ),
                    child: Icon(
                      Icons.refresh,
                      size: 32,
                      color: ColorTokens.primary.withValues(alpha: 0.5),
                    ),
                  ),
                );
              },
            ),
            
            // 内圈图标
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ColorTokens.primary,
                boxShadow: [
                  BoxShadow(
                    color: ColorTokens.primary.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: const Icon(
                Icons.phishing,
                size: 28,
                color: ColorTokens.onError,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBottomSection() {
    return Column(
      children: [
        // 进度点动画
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: List.generate(3, (index) {
            return TweenAnimationBuilder<double>(
              duration: MotionTokens.durationLong + Duration(milliseconds: index * 200),
              tween: Tween<double>(begin: 0, end: 1),
              builder: (context, value, child) {
                final scale = 0.5 + (0.5 * value);
                return Container(
                  margin: EdgeInsets.symmetric(horizontal: SpacingTokens.space1),
                  child: Transform.scale(
                    scale: scale,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: ColorTokens.primary.withValues(alpha: scale),
                      ),
                    ),
                  ),
                );
              },
            );
          }),
        ),
        
        SpacingTokens.verticalSpaceLg,
        
        // 温馨提示
        Container(
          padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space3),
          decoration: BoxDecoration(
            color: ColorTokens.surfaceContainer,
            borderRadius: ShapeTokens.borderRadiusMd,
            border: Border.all(
              color: ColorTokens.outline,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.tips_and_updates_outlined,
                size: 16,
                color: ColorTokens.onSurfaceVariant,
              ),
              SpacingTokens.horizontalSpaceSm,
              Text(
                '正在为您打造精彩的钓鱼体验...',
                style: TypographyTokens.bodySmall.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}