# 图片上传组件设计规范

## 概述

为了确保应用程序中所有图片上传功能的视觉和交互一致性，我们创建了基于 Material Design 3 规范的 DSImageUploadGrid 组件。

## 组件特性

### DSImageUploadGrid - 图片上传网格组件

- **完整功能**: 支持图片选择、上传、删除和状态管理
- **跨平台兼容**: 自动适配 Web 和移动平台的图片显示
- **状态处理**: 支持加载、上传进度、错误状态的可视化
- **Material 3**: 完全符合 Material Design 3 设计规范
- **Design Tokens**: 严格使用 Design Tokens 系统

## 核心功能

1. **图片网格显示**: 3列网格布局，支持自定义列数和宽高比
2. **添加图片按钮**: 虚线边框的添加按钮，支持禁用状态
3. **图片预览**: 支持本地文件和远程URL图片
4. **删除功能**: 每张图片右上角的删除按钮
5. **主图标记**: 第一张图片显示"主图"标记
6. **上传状态**: 进度条和状态覆盖层
7. **计数器**: 显示当前图片数量和上限

## 使用方法

### 基础用法

```dart
import 'package:user_app/shared/widgets/design_system/design_system.dart';
import 'package:user_app/models/image/uploaded_image.dart';

DSImageUploadGrid(
  images: spotImages,
  onAddImages: () async {
    // 处理添加图片逻辑
    final picker = ImagePicker();
    final files = await picker.pickMultiImage();
    // 转换为 UploadedImage 并添加到列表
  },
  onRemoveImage: (index) {
    // 处理删除图片逻辑
    setState(() {
      spotImages.removeAt(index);
    });
  },
  title: '钓点图片',
  subtitle: '上传真实的钓点照片',
  icon: Icons.photo_library_rounded,
)
```

### 自定义配置

```dart
DSImageUploadGrid(
  images: productImages,
  onAddImages: _handleAddImages,
  onRemoveImage: _handleRemoveImage,
  maxImageCount: 6,                    // 最大6张图片
  crossAxisCount: 2,                   // 2列网格
  childAspectRatio: 1.2,              // 图片宽高比
  crossAxisSpacing: 16.0,             // 水平间距
  mainAxisSpacing: 16.0,              // 垂直间距
  title: '产品图片',
  subtitle: '最多上传6张产品照片',
  icon: Icons.inventory_rounded,
  showPrimaryLabel: false,             // 不显示主图标记
  showCounter: true,                   // 显示计数器
  addButtonText: '选择图片',
  limitReachedText: '已满',
)
```

## 参数说明

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `images` | `List<UploadedImage>` | ✓ | - | 图片列表 |
| `onAddImages` | `Future<void> Function()` | ✓ | - | 添加图片回调 |
| `onRemoveImage` | `void Function(int)` | ✓ | - | 删除图片回调 |
| `maxImageCount` | `int` | - | `9` | 最大图片数量 |
| `crossAxisCount` | `int` | - | `3` | 网格列数 |
| `childAspectRatio` | `double` | - | `1.0` | 图片宽高比 |
| `crossAxisSpacing` | `double` | - | `12.0` | 水平间距 |
| `mainAxisSpacing` | `double` | - | `12.0` | 垂直间距 |
| `title` | `String` | - | `'图片上传'` | 标题 |
| `subtitle` | `String` | - | `'上传相关图片'` | 副标题 |
| `icon` | `IconData` | - | `Icons.photo_library_rounded` | 标题图标 |
| `showCounter` | `bool` | - | `true` | 是否显示计数器 |
| `showPrimaryLabel` | `bool` | - | `true` | 是否显示主图标记 |
| `primaryLabelText` | `String` | - | `'主图'` | 主图标记文本 |
| `addButtonText` | `String` | - | `'添加图片'` | 添加按钮文本 |
| `limitReachedText` | `String` | - | `'已达上限'` | 达到限制时的文本 |

## 图片状态处理

组件支持 `UploadedImage` 模型的所有状态：

1. **正常状态**: 图片正常显示
2. **上传中状态**: 显示进度覆盖层和百分比
3. **错误状态**: 显示错误覆盖层和重试提示
4. **加载状态**: 显示加载指示器

## 迁移指南

从现有图片上传代码迁移到 DSImageUploadGrid：

### 步骤 1: 导入组件

```dart
import 'package:user_app/shared/widgets/design_system/design_system.dart';
```

### 步骤 2: 替换现有实现

```dart
// 原有代码
Column(
  children: [
    _buildImageSection(context),
    // ... 其他内容
  ],
)

// 新代码
Column(
  children: [
    DSImageUploadGrid(
      images: spotImages,
      onAddImages: onAddImages,
      onRemoveImage: onRemoveImage,
      title: '钓点图片',
      subtitle: '上传真实的钓点照片',
      icon: Icons.photo_library_rounded,
    ),
    // ... 其他内容
  ],
)
```

### 步骤 3: 移除旧代码

删除以下方法（已集成到 DSImageUploadGrid 中）：
- `_buildImageSection`
- `_buildAddImageButton`
- `_buildImageItem`
- `_buildImageContent`
- `_buildLoadingPlaceholder`
- `_buildErrorPlaceholder`

## 设计原则

1. **一致的视觉样式**: 使用 Design Tokens 确保颜色、间距、形状的一致性
2. **清晰的状态反馈**: 明确的加载、错误和成功状态
3. **良好的用户体验**: 触觉反馈、动画过渡、无障碍支持
4. **跨平台兼容**: 自动处理不同平台的图片显示差异
5. **Material Design 3**: 完全符合最新的 Material Design 规范

## 注意事项

1. **图片模型依赖**: 必须使用 `UploadedImage` 模型
2. **权限处理**: 确保应用有相册访问权限
3. **错误处理**: 在回调函数中处理可能的异常
4. **性能优化**: 对于大量图片，考虑使用分页或懒加载
5. **存储管理**: 及时清理本地临时文件