import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

/// 迁移示例：展示如何从原始实现迁移到 DSImageUploadGrid
/// 
/// 原始文件: lib/features/fishing_spots/widgets/create_spot/modern_create_spot_step_1.dart
/// 
/// 迁移前后对比示例
class MigrationExample extends StatelessWidget {
  final List<UploadedImage> spotImages;
  final Future<void> Function() onAddImages;
  final void Function(int) onRemoveImage;

  const MigrationExample({
    super.key,
    required this.spotImages,
    required this.onAddImages,
    required this.onRemoveImage,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // === 迁移前的代码结构 ===
        // _buildImageSection(context),  // 包含约180行代码
        // _buildAddImageButton(context), // 约50行代码
        // _buildImageItem(context, index), // 约80行代码
        // _buildImageContent(context, uploadedImage, uniqueKey), // 约55行代码
        // _buildLoadingPlaceholder(context), // 约12行代码
        // _buildErrorPlaceholder(context), // 约10行代码
        // DashedBorderPainter class // 约45行代码
        // 总计: ~430行代码

        // === 迁移后的代码结构 ===
        // 替换为单个组件调用，仅需约15行代码
        DSImageUploadGrid(
          images: spotImages,
          onAddImages: onAddImages,
          onRemoveImage: onRemoveImage,
          maxImageCount: 9,
          title: '钓点图片',
          subtitle: '上传真实的钓点照片',
          icon: Icons.photo_library_rounded,
          showPrimaryLabel: true,
          showCounter: true,
        ),

        const SizedBox(height: SpacingTokens.space8),

        // 其他内容保持不变...
      ],
    );
  }
}

/// 使用新组件的完整示例
class RefactoredModernImagesFishTypesStep extends StatelessWidget {
  final List<UploadedImage> spotImages;
  final Future<void> Function() onAddImages;
  final void Function(int) onRemoveImage;
  // 其他鱼种相关的参数...

  const RefactoredModernImagesFishTypesStep({
    super.key,
    required this.spotImages,
    required this.onAddImages,
    required this.onRemoveImage,
    // 其他参数...
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 使用新的 DSImageUploadGrid 组件
        DSImageUploadGrid(
          images: spotImages,
          onAddImages: onAddImages,
          onRemoveImage: onRemoveImage,
          title: '钓点图片',
          subtitle: '上传真实的钓点照片',
          icon: Icons.photo_library_rounded,
        ),

        const SizedBox(height: SpacingTokens.space8),

        // 鱼种选择部分保持原有逻辑
        // _buildFishTypeSection(context),
      ],
    );
  }
}

/// 迁移检查清单
/// 
/// 1. ✅ 导入新组件: import 'package:user_app/shared/widgets/design_system/design_system.dart';
/// 2. ✅ 替换 _buildImageSection 调用为 DSImageUploadGrid
/// 3. ✅ 传递所有必需的回调函数
/// 4. ✅ 删除原有的图片相关私有方法：
///    - _buildImageSection
///    - _buildAddImageButton
///    - _buildImageItem
///    - _buildImageContent
///    - _buildLoadingPlaceholder
///    - _buildErrorPlaceholder
///    - DashedBorderPainter class (如果只在这里使用)
/// 5. ✅ 测试所有图片上传功能仍正常工作
/// 6. ✅ 验证 Material 3 主题支持
/// 7. ✅ 确认跨平台兼容性 (iOS/Android/Web)