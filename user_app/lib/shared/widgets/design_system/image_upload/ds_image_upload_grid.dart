import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/shared/widgets/design_system/painters/painters.dart';

/// Design System 图片上传网格组件
/// 
/// 符合 Material Design 3 规范的图片上传和管理组件
/// 支持本地文件和远程URL图片显示，包含加载和错误状态处理
class DSImageUploadGrid extends StatelessWidget {
  /// 图片列表
  final List<UploadedImage> images;
  
  /// 添加图片回调
  final Future<void> Function() onAddImages;
  
  /// 删除图片回调
  final void Function(int) onRemoveImage;
  
  /// 最大图片数量限制
  final int maxImageCount;
  
  /// 网格列数
  final int crossAxisCount;
  
  /// 网格项宽高比
  final double childAspectRatio;
  
  /// 网格间距
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  
  /// 标题
  final String title;
  
  /// 副标题/描述
  final String subtitle;
  
  /// 图标
  final IconData icon;
  
  /// 是否显示计数器
  final bool showCounter;
  
  /// 是否显示主图标记
  final bool showPrimaryLabel;
  
  /// 主图标记文本
  final String primaryLabelText;
  
  /// 添加按钮文本
  final String addButtonText;
  
  /// 达到上限时的按钮文本
  final String limitReachedText;

  const DSImageUploadGrid({
    super.key,
    required this.images,
    required this.onAddImages,
    required this.onRemoveImage,
    this.maxImageCount = 9,
    this.crossAxisCount = 3,
    this.childAspectRatio = 1.0,
    this.crossAxisSpacing = 12.0,
    this.mainAxisSpacing = 12.0,
    this.title = '图片上传',
    this.subtitle = '上传相关图片',
    this.icon = Icons.photo_library_rounded,
    this.showCounter = true,
    this.showPrimaryLabel = true,
    this.primaryLabelText = '主图',
    this.addButtonText = '添加图片',
    this.limitReachedText = '已达上限',
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(context),
        SpacingTokens.verticalSpaceMd,
        _buildImageGrid(context),
        if (showCounter && images.isNotEmpty) ...[
          SpacingTokens.verticalSpaceSm,
          _buildImageCounter(context),
        ],
      ],
    );
  }

  /// 构建区域标题
  Widget _buildSectionHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(SpacingTokens.space2 + SpacingTokens.space1),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: ShapeTokens.borderRadiusMd,
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 24,
          ),
        ),
        SpacingTokens.horizontalSpaceMd,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        crossAxisSpacing: crossAxisSpacing,
        mainAxisSpacing: mainAxisSpacing,
        childAspectRatio: childAspectRatio,
      ),
      itemCount: images.length + 1,
      itemBuilder: (context, index) {
        if (index == images.length) {
          return _buildAddImageButton(context);
        }
        return _buildImageItem(context, index);
      },
    );
  }

  /// 构建添加图片按钮
  Widget _buildAddImageButton(BuildContext context) {
    final isEnabled = images.length < maxImageCount;
    
    return GestureDetector(
      onTap: isEnabled
          ? () {
              HapticFeedback.lightImpact();
              onAddImages();
            }
          : null,
      child: DSDashedContainer(
        borderColor: isEnabled
            ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.5)
            : Theme.of(context).colorScheme.outline,
        borderRadius: ShapeTokens.borderRadiusLg,
        backgroundColor: isEnabled
            ? Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3)
            : Theme.of(context).colorScheme.surfaceContainerHighest,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_rounded,
              size: 32,
              color: isEnabled
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SpacingTokens.verticalSpaceSm,
            Text(
              isEnabled ? addButtonText : limitReachedText,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: isEnabled
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建图片项
  Widget _buildImageItem(BuildContext context, int index) {
    final uploadedImage = images[index];
    final uniqueKey = 'image_${index}_${uploadedImage.file.path.hashCode}';

    return Stack(
      key: ValueKey(uniqueKey),
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: ShapeTokens.borderRadiusLg,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: SpacingTokens.space2,
                offset: const Offset(0, SpacingTokens.space1),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: ShapeTokens.borderRadiusLg,
            child: _buildImageContent(context, uploadedImage, uniqueKey),
          ),
        ),

        // 删除按钮
        Positioned(
          top: SpacingTokens.space2,
          right: SpacingTokens.space2,
          child: _buildDeleteButton(context, index),
        ),

        // 主图标记
        if (showPrimaryLabel && index == 0)
          Positioned(
            bottom: SpacingTokens.space2,
            left: SpacingTokens.space2,
            child: _buildPrimaryLabel(context),
          ),

        // 上传进度覆盖层
        if (uploadedImage.isUploading)
          Positioned.fill(
            child: _buildUploadingOverlay(context, uploadedImage.uploadProgress),
          ),

        // 错误状态覆盖层
        if (uploadedImage.hasError)
          Positioned.fill(
            child: _buildErrorOverlay(context),
          ),
      ],
    );
  }

  /// 构建删除按钮
  Widget _buildDeleteButton(BuildContext context, int index) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onRemoveImage(index);
      },
      child: Container(
        padding: SpacingTokens.paddingXs,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.error.withValues(alpha: 0.9),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.2),
              blurRadius: SpacingTokens.space1,
            ),
          ],
        ),
        child: Icon(
          Icons.close_rounded,
          size: 16,
          color: Theme.of(context).colorScheme.onError,
        ),
      ),
    );
  }

  /// 构建主图标记
  Widget _buildPrimaryLabel(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space2,
        vertical: SpacingTokens.space1,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary,
        borderRadius: ShapeTokens.borderRadiusSm,
      ),
      child: Text(
        primaryLabelText,
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
      ),
    );
  }

  /// 构建上传中覆盖层
  Widget _buildUploadingOverlay(BuildContext context, double progress) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: ShapeTokens.borderRadiusLg,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              value: progress,
              strokeWidth: 3,
              color: Theme.of(context).colorScheme.primary,
              backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
            ),
            SpacingTokens.verticalSpaceXs,
            Text(
              '${(progress * 100).toInt()}%',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误状态覆盖层
  Widget _buildErrorOverlay(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.8),
        borderRadius: ShapeTokens.borderRadiusLg,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline_rounded,
              size: 24,
              color: Theme.of(context).colorScheme.onErrorContainer,
            ),
            SpacingTokens.verticalSpaceXs,
            Text(
              '上传失败',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onErrorContainer,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建图片计数器
  Widget _buildImageCounter(BuildContext context) {
    return Center(
      child: Text(
        '${images.length}/$maxImageCount 张图片',
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
      ),
    );
  }

  /// 构建图片内容显示
  Widget _buildImageContent(BuildContext context, UploadedImage uploadedImage, String keyPrefix) {
    // 如果有远程URL，使用CachedNetworkImage
    if (uploadedImage.url != null && uploadedImage.url!.startsWith('http')) {
      return CachedNetworkImage(
        key: ValueKey('${keyPrefix}_remote'),
        imageUrl: uploadedImage.url!,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildLoadingPlaceholder(context),
        errorWidget: (context, url, error) => _buildErrorPlaceholder(context),
      );
    }

    // 对于本地文件，根据平台选择显示方式
    if (kIsWeb) {
      // Web平台：使用Image.memory从bytes加载
      return FutureBuilder<Uint8List>(
        key: ValueKey('${keyPrefix}_web'),
        future: uploadedImage.file.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingPlaceholder(context);
          } else if (snapshot.hasError) {
            return _buildErrorPlaceholder(context);
          } else if (snapshot.hasData) {
            return Image.memory(
              key: ValueKey('${keyPrefix}_memory'),
              snapshot.data!,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) => _buildErrorPlaceholder(context),
            );
          } else {
            return _buildErrorPlaceholder(context);
          }
        },
      );
    } else {
      // 移动平台：使用Image.file
      return Image.file(
        key: ValueKey('${keyPrefix}_file'),
        File(uploadedImage.file.path),
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => _buildErrorPlaceholder(context),
      );
    }
  }

  /// 构建加载占位符
  Widget _buildLoadingPlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  /// 构建错误占位符
  Widget _buildErrorPlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.broken_image_rounded,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }
}