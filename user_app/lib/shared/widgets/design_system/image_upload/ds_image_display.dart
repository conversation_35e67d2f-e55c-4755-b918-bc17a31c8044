import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/models/image/uploaded_image.dart';

/// Design System 图片显示组件
/// 
/// 跨平台图片显示组件，支持本地文件、网络图片和Uint8List
class DSImageDisplay extends StatelessWidget {
  final UploadedImage image;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? errorWidget;
  final Widget? loadingWidget;

  const DSImageDisplay({
    super.key,
    required this.image,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.errorWidget,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;

    // 根据不同平台和图片类型选择显示方式
    if (kIsWeb && image.bytes != null) {
      // Web平台使用Uint8List
      imageWidget = Image.memory(
        image.bytes!,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) => 
            errorWidget ?? _buildErrorPlaceholder(context),
      );
    } else if (!kIsWeb && image.file != null) {
      // 移动平台使用File
      imageWidget = Image.file(
        image.file!,
        width: width,
        height: height,
        fit: fit,
        errorBuilder: (context, error, stackTrace) =>
            errorWidget ?? _buildErrorPlaceholder(context),
      );
    } else if (image.url != null && image.url!.isNotEmpty) {
      // 网络图片
      imageWidget = CachedNetworkImage(
        imageUrl: image.url!,
        width: width,
        height: height,
        fit: fit,
        placeholder: (context, url) => 
            loadingWidget ?? _buildLoadingPlaceholder(context),
        errorWidget: (context, url, error) =>
            errorWidget ?? _buildErrorPlaceholder(context),
      );
    } else {
      // 无有效图片数据
      imageWidget = errorWidget ?? _buildErrorPlaceholder(context);
    }

    // 如果需要圆角，使用ClipRRect包装
    if (borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }

  /// 构建加载占位符
  Widget _buildLoadingPlaceholder(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: borderRadius,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: SpacingTokens.space6,
              height: SpacingTokens.space6,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: SpacingTokens.space2),
            Text(
              '加载中...',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建错误占位符
  Widget _buildErrorPlaceholder(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.errorContainer.withValues(alpha: 0.3),
        borderRadius: borderRadius,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.broken_image_rounded,
              color: Theme.of(context).colorScheme.onErrorContainer,
              size: SpacingTokens.space6,
            ),
            const SizedBox(height: SpacingTokens.space2),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 图片上传状态枚举
enum ImageUploadStatus {
  idle,      // 空闲状态
  uploading, // 上传中
  success,   // 上传成功
  error,     // 上传失败
}

/// 带状态的图片显示组件
class DSImageDisplayWithStatus extends StatelessWidget {
  final UploadedImage image;
  final ImageUploadStatus status;
  final double? progress; // 上传进度 0.0 - 1.0
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final VoidCallback? onRetry;

  const DSImageDisplayWithStatus({
    super.key,
    required this.image,
    this.status = ImageUploadStatus.idle,
    this.progress,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // 基础图片显示
        DSImageDisplay(
          image: image,
          width: width,
          height: height,
          fit: fit,
          borderRadius: borderRadius,
        ),

        // 状态覆盖层
        if (status != ImageUploadStatus.idle)
          _buildStatusOverlay(context),
      ],
    );
  }

  Widget _buildStatusOverlay(BuildContext context) {
    final theme = Theme.of(context);
    
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          borderRadius: borderRadius,
        ),
        child: Center(
          child: _buildStatusContent(theme),
        ),
      ),
    );
  }

  Widget _buildStatusContent(ThemeData theme) {
    switch (status) {
      case ImageUploadStatus.uploading:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (progress != null)
              SizedBox(
                width: SpacingTokens.space8,
                height: SpacingTokens.space8,
                child: CircularProgressIndicator(
                  value: progress,
                  strokeWidth: 3,
                  backgroundColor: Colors.white.withValues(alpha: 0.3),
                  valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            else
              const SizedBox(
                width: SpacingTokens.space8,
                height: SpacingTokens.space8,
                child: CircularProgressIndicator(
                  strokeWidth: 3,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            const SizedBox(height: SpacingTokens.space2),
            Text(
              progress != null 
                  ? '${(progress! * 100).toInt()}%'
                  : '上传中...',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        );

      case ImageUploadStatus.success:
        return Icon(
          Icons.check_circle_rounded,
          color: theme.colorScheme.tertiary,
          size: SpacingTokens.space8,
        );

      case ImageUploadStatus.error:
        return Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_rounded,
              color: theme.colorScheme.error,
              size: SpacingTokens.space8,
            ),
            const SizedBox(height: SpacingTokens.space2),
            if (onRetry != null)
              TextButton(
                onPressed: onRetry,
                style: TextButton.styleFrom(
                  foregroundColor: Colors.white,
                  backgroundColor: Colors.black.withValues(alpha: 0.3),
                ),
                child: const Text('重试'),
              ),
          ],
        );

      case ImageUploadStatus.idle:
        return const SizedBox.shrink();
    }
  }
}