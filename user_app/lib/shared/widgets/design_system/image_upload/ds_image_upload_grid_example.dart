import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

/// DSImageUploadGrid 组件使用示例
/// 
/// 展示如何正确使用 DSImageUploadGrid 组件的示例页面
class DSImageUploadGridExample extends StatefulWidget {
  const DSImageUploadGridExample({super.key});

  @override
  State<DSImageUploadGridExample> createState() => _DSImageUploadGridExampleState();
}

class _DSImageUploadGridExampleState extends State<DSImageUploadGridExample> {
  List<UploadedImage> images = [];
  final ImagePicker _picker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('图片上传组件示例'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // 基础用法
            DSImageUploadGrid(
              images: images,
              onAddImages: _handleAddImages,
              onRemoveImage: _handleRemoveImage,
              title: '钓点图片',
              subtitle: '上传真实的钓点照片',
              icon: Icons.photo_library_rounded,
            ),
            
            const SizedBox(height: 32),
            
            // 自定义配置示例
            DSImageUploadGrid(
              images: images,
              onAddImages: _handleAddImages,
              onRemoveImage: _handleRemoveImage,
              maxImageCount: 6,
              crossAxisCount: 2,
              title: '产品图片',
              subtitle: '最多上传6张产品照片',
              icon: Icons.inventory_rounded,
              showPrimaryLabel: false,
              addButtonText: '选择图片',
              limitReachedText: '已满',
            ),
          ],
        ),
      ),
    );
  }

  /// 处理添加图片
  Future<void> _handleAddImages() async {
    try {
      final List<XFile> pickedFiles = await _picker.pickMultiImage();
      
      if (pickedFiles.isNotEmpty) {
        setState(() {
          for (final file in pickedFiles) {
            if (images.length < 9) {
              images.add(UploadedImage(
                file: file,
                isUploading: true,
              ));
            }
          }
        });

        // 模拟上传过程
        _simulateUpload();
      }
    } catch (e) {
      // 处理错误
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('选择图片失败: $e')),
      );
    }
  }

  /// 处理删除图片
  void _handleRemoveImage(int index) {
    setState(() {
      images.removeAt(index);
    });
  }

  /// 模拟上传过程
  void _simulateUpload() async {
    for (int i = 0; i < images.length; i++) {
      if (images[i].isUploading) {
        // 模拟上传进度
        for (double progress = 0.0; progress <= 1.0; progress += 0.1) {
          await Future.delayed(const Duration(milliseconds: 100));
          if (mounted) {
            setState(() {
              images[i] = UploadedImage(
                file: images[i].file,
                isUploading: true,
                uploadProgress: progress,
              );
            });
          }
        }

        // 上传完成
        if (mounted) {
          setState(() {
            images[i] = UploadedImage(
              file: images[i].file,
              url: 'https://example.com/uploaded_image_$i.jpg',
              isUploading: false,
              uploadProgress: 1.0,
            );
          });
        }
      }
    }
  }
}