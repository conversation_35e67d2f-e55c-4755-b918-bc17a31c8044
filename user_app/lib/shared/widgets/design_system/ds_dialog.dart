import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'ds_button.dart';

/// Design System - Dialog组件
/// 严格遵循Material Design 3规范的对话框组件
/// 
/// 使用方式：
/// ```dart
/// DSDialog.show(
///   context: context,
///   title: '标题',
///   content: '内容',
///   actions: [
///     DSButton.text(text: '取消', onPressed: () {}),
///     DSButton.primary(text: '确定', onPressed: () {}),
///   ],
/// );
/// ```
class DSDialog extends StatelessWidget {
  final String? title;
  final Widget? titleWidget;
  final String? content;
  final Widget? contentWidget;
  final List<Widget>? actions;
  final DSDialogType type;
  final DSDialogSize size;
  final bool barrierDismissible;
  final EdgeInsetsGeometry? titlePadding;
  final EdgeInsetsGeometry? contentPadding;
  final EdgeInsetsGeometry? actionsPadding;
  final MainAxisAlignment? actionsAlignment;

 const DSDialog._({
    super.key,
    required this.type,
    this.title,
    this.titleWidget,
    this.content,
    this.contentWidget,
    this.actions,
    this.size = DSDialogSize.medium,
    this.barrierDismissible = true,
    this.titlePadding,
    this.contentPadding,
    this.actionsPadding,
    this.actionsAlignment,
  });

  /// 基础对话框
  factory DSDialog.basic({
    Key? key,
    String? title,
    Widget? titleWidget,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
    EdgeInsetsGeometry? titlePadding,
    EdgeInsetsGeometry? contentPadding,
    EdgeInsetsGeometry? actionsPadding,
    MainAxisAlignment? actionsAlignment,
  }) {
    return DSDialog._(
      key: key,
      type: DSDialogType.basic,
      title: title,
      titleWidget: titleWidget,
      content: content,
      contentWidget: contentWidget,
      actions: actions,
      size: size,
      barrierDismissible: barrierDismissible,
      titlePadding: titlePadding,
      contentPadding: contentPadding,
      actionsPadding: actionsPadding,
      actionsAlignment: actionsAlignment,
    );
  }

  /// 确认对话框
  factory DSDialog.confirmation({
    Key? key,
    required String title,
    required String content,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    final String confirmText = '确认',
    final String cancelText = '取消',
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
  }) {
    return DSDialog._(
      key: key,
      type: DSDialogType.confirmation,
      title: title,
      content: content,
      actions: [
        DSButton.text(
          text: cancelText,
          onPressed: onCancel,
        ),
        DSButton.primary(
          text: confirmText,
          onPressed: onConfirm,
        ),
      ],
      size: size,
      barrierDismissible: barrierDismissible,
    );
  }

  /// 警告对话框
  factory DSDialog.warning({
    Key? key,
    required String title,
    required String content,
    required VoidCallback onConfirm,
    VoidCallback? onCancel,
    final String confirmText = '确认',
    final String cancelText = '取消',
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
  }) {
    return DSDialog._(
      key: key,
      type: DSDialogType.warning,
      title: title,
      content: content,
      actions: [
        DSButton.text(
          text: cancelText,
          onPressed: onCancel,
        ),
        DSButton.destructive(
          text: confirmText,
          onPressed: onConfirm,
        ),
      ],
      size: size,
      barrierDismissible: barrierDismissible,
    );
  }

  /// 信息对话框
  factory DSDialog.info({
    Key? key,
    required String title,
    required String content,
    VoidCallback? onConfirm,
    final String confirmText = '知道了',
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
  }) {
    return DSDialog._(
      key: key,
      type: DSDialogType.info,
      title: title,
      content: content,
      actions: [
        DSButton.primary(
          text: confirmText,
          onPressed: onConfirm,
        ),
      ],
      size: size,
      barrierDismissible: barrierDismissible,
    );
  }

  /// 显示对话框
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget dialog,
    final bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) {
    return showDialog<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? ColorTokens.shadow.withValues(alpha: 0.5),
      barrierLabel: barrierLabel,
      builder: (context) => dialog,
    );
  }

  /// 显示基础对话框
  static Future<T?> showBasic<T>({
    required BuildContext context,
    String? title,
    Widget? titleWidget,
    String? content,
    Widget? contentWidget,
    List<Widget>? actions,
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
  }) {
    return show<T>(
      context: context,
      barrierDismissible: barrierDismissible,
      dialog: DSDialog.basic(
        title: title,
        titleWidget: titleWidget,
        content: content,
        contentWidget: contentWidget,
        actions: actions,
        size: size,
        barrierDismissible: barrierDismissible,
      ),
    );
  }

  /// 显示确认对话框
  static Future<bool?> showConfirmation({
    required BuildContext context,
    required String title,
    required String content,
    final String confirmText = '确认',
    final String cancelText = '取消',
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
  }) {
    return show<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      dialog: DSDialog.confirmation(
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        size: size,
        barrierDismissible: barrierDismissible,
        onConfirm: () => Navigator.of(context).pop(true),
        onCancel: () => Navigator.of(context).pop(false),
      ),
    );
  }

  /// 显示警告对话框
  static Future<bool?> showWarning({
    required BuildContext context,
    required String title,
    required String content,
    final String confirmText = '确认',
    final String cancelText = '取消',
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
  }) {
    return show<bool>(
      context: context,
      barrierDismissible: barrierDismissible,
      dialog: DSDialog.warning(
        title: title,
        content: content,
        confirmText: confirmText,
        cancelText: cancelText,
        size: size,
        barrierDismissible: barrierDismissible,
        onConfirm: () => Navigator.of(context).pop(true),
        onCancel: () => Navigator.of(context).pop(false),
      ),
    );
  }

  /// 显示信息对话框
  static Future<void> showInfo({
    required BuildContext context,
    required String title,
    required String content,
    final String confirmText = '知道了',
    DSDialogSize size = DSDialogSize.medium,
    final bool barrierDismissible = true,
  }) {
    return show(
      context: context,
      barrierDismissible: barrierDismissible,
      dialog: DSDialog.info(
        title: title,
        content: content,
        confirmText: confirmText,
        size: size,
        barrierDismissible: barrierDismissible,
        onConfirm: () => Navigator.of(context).pop(),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final config = _getDialogConfig(colorScheme);

    return Dialog(
      elevation: config.elevation,
      backgroundColor: config.backgroundColor,
      shadowColor: config.shadowColor,
      surfaceTintColor: config.surfaceTintColor,
      shape: config.shape,
      insetPadding: _getInsetPadding(),
      child: AnimatedContainer(
        duration: MotionTokens.dialogEnterDuration,
        curve: MotionTokens.dialogEnterCurve,
       constraints: _getConstraints(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Title
            if (title != null || titleWidget != null) ...[
              Padding(
                padding: titlePadding ?? _getTitlePadding(),
                child: titleWidget ?? Text(
                  title!,
                  style: config.titleStyle,
                  textAlign: TextAlign.start,
                ),
              ),
            ],
            
            // Content
            if (content != null || contentWidget != null) ...[
              Flexible(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: contentPadding ?? _getContentPadding(),
                    child: contentWidget ?? Text(
                      content!,
                      style: config.contentStyle,
                      textAlign: TextAlign.start,
                    ),
                  ),
                ),
              ),
            ],
            
            // Actions
            if (actions != null && actions!.isNotEmpty) ...[
              Padding(
                padding: actionsPadding ?? _getActionsPadding(),
                child: Row(
                  mainAxisAlignment: actionsAlignment ?? MainAxisAlignment.end,
                  children: _buildActions(),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  List<Widget> _buildActions() {
    if (actions == null || actions!.isEmpty) return [];
    
    final children = <Widget>[];
    for (int i = 0; i < actions!.length; i++) {
      if (i > 0) {
        children.add(SpacingTokens.horizontalSpaceSm);
      }
      children.add(actions![i]);
    }
    
    return children;
  }

  _DialogConfig _getDialogConfig(ColorScheme colorScheme) {
    return _DialogConfig(
      backgroundColor: colorScheme.surface,
      shadowColor: colorScheme.shadow,
      surfaceTintColor: colorScheme.surfaceTint,
      elevation: ElevationTokens.level3,
      shape: ShapeTokens.dialogShape,
      titleStyle: TypographyTokens.headlineSmall.copyWith(
        color: colorScheme.onSurface,
      ),
      contentStyle: TypographyTokens.bodyMedium.copyWith(
        color: colorScheme.onSurfaceVariant,
      ),
    );
  }

  EdgeInsets _getInsetPadding() {
    switch (size) {
      case DSDialogSize.small:
        return const EdgeInsets.symmetric(horizontal: 40.0, vertical: 24.0);
      case DSDialogSize.medium:
        return const EdgeInsets.symmetric(horizontal: 40.0, vertical: 24.0);
      case DSDialogSize.large:
        return const EdgeInsets.symmetric(horizontal: 20.0, vertical: 24.0);
    }
  }

  BoxConstraints _getConstraints() {
    switch (size) {
      case DSDialogSize.small:
        return const BoxConstraints(
          minWidth: 280.0,
          maxWidth: 360.0,
          maxHeight: 400.0,
        );
      case DSDialogSize.medium:
        return const BoxConstraints(
          minWidth: 280.0,
          maxWidth: 400.0,
          maxHeight: 560.0,
        );
      case DSDialogSize.large:
        return const BoxConstraints(
          minWidth: 280.0,
          maxWidth: 600.0,
          maxHeight: 720.0,
        );
    }
  }

  EdgeInsetsGeometry _getTitlePadding() {
    return EdgeInsets.fromLTRB(SpacingTokens.space6, SpacingTokens.space6, SpacingTokens.space6, 0.0);
  }

  EdgeInsetsGeometry _getContentPadding() {
    final hasTitle = title != null || titleWidget != null;
    return EdgeInsets.fromLTRB(
      SpacingTokens.space6,
      hasTitle ? SpacingTokens.space4 : SpacingTokens.space6,
      SpacingTokens.space6,
      0.0,
    );
  }

  EdgeInsetsGeometry _getActionsPadding() {
    return EdgeInsets.fromLTRB(SpacingTokens.space6, SpacingTokens.space4, SpacingTokens.space6, SpacingTokens.space6);
  }
}

/// 对话框配置类
class _DialogConfig {
  final Color backgroundColor;
  final Color shadowColor;
  final Color surfaceTintColor;
  final double elevation;
  final ShapeBorder shape;
  final TextStyle titleStyle;
  final TextStyle contentStyle;

 const _DialogConfig({
    required this.backgroundColor,
    required this.shadowColor,
    required this.surfaceTintColor,
    required this.elevation,
    required this.shape,
    required this.titleStyle,
    required this.contentStyle,
  });
}

/// 对话框类型枚举
enum DSDialogType {
  basic,        // 基础对话框
  confirmation, // 确认对话框
  warning,      // 警告对话框
  info,         // 信息对话框
}

/// 对话框尺寸枚举
enum DSDialogSize {
  small,   // 小尺寸 (最大宽度 360px)
  medium,  // 中等尺寸 (最大宽度 400px)
  large,   // 大尺寸 (最大宽度 600px)
}