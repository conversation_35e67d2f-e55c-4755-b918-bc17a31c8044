import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System - Button组件
/// 严格遵循Material Design 3规范的按钮组件
/// 
/// 使用方式：
/// ```dart
/// DSButton.primary(
///   text: '主要按钮',
///   onPressed: () {},
/// )
/// ```
class DSButton extends StatefulWidget {
  final String text;
  final VoidCallback? onPressed;
  final Widget? icon;
  final DSButtonType type;
  final DSButtonSize size;
  final bool isLoading;
  final bool isFullWidth;
  
 const DSButton._({
    super.key,
    required this.text,
    required this.type,
    this.onPressed,
    this.icon,
    this.size = DSButtonSize.medium,
    this.isLoading = false,
    this.isFullWidth = false,
  });

  /// 主要按钮 - 最重要的操作
  factory DSButton.primary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    Widget? icon,
    DSButtonSize size = DSButtonSize.medium,
    final bool isLoading = false,
    final bool isFullWidth = false,
  }) {
    return DSButton._(
      key: key,
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: DSButtonType.primary,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 次要按钮 - 重要但非主要的操作
  factory DSButton.secondary({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    Widget? icon,
    DSButtonSize size = DSButtonSize.medium,
    final bool isLoading = false,
    final bool isFullWidth = false,
  }) {
    return DSButton._(
      key: key,
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: DSButtonType.secondary,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 轮廓按钮 - 中等重要的操作
  factory DSButton.outlined({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    Widget? icon,
    DSButtonSize size = DSButtonSize.medium,
    final bool isLoading = false,
    final bool isFullWidth = false,
  }) {
    return DSButton._(
      key: key,
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: DSButtonType.outlined,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 文本按钮 - 最低优先级的操作
  factory DSButton.text({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    Widget? icon,
    DSButtonSize size = DSButtonSize.medium,
    final bool isLoading = false,
    final bool isFullWidth = false,
  }) {
    return DSButton._(
      key: key,
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: DSButtonType.text,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  /// 危险操作按钮
  factory DSButton.destructive({
    Key? key,
    required String text,
    VoidCallback? onPressed,
    Widget? icon,
    DSButtonSize size = DSButtonSize.medium,
    final bool isLoading = false,
    final bool isFullWidth = false,
  }) {
    return DSButton._(
      key: key,
      text: text,
      onPressed: onPressed,
      icon: icon,
      type: DSButtonType.destructive,
      size: size,
      isLoading: isLoading,
      isFullWidth: isFullWidth,
    );
  }

  @override
  State<DSButton> createState() => _DSButtonState();
}

class _DSButtonState extends State<DSButton> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: MotionTokens.buttonPressDuration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.96,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: MotionTokens.buttonCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (!widget.isLoading && widget.onPressed != null) {
      setState(() => _isPressed = true);
      _animationController.forward();
      HapticFeedback.lightImpact();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (!widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (!widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final config = _getButtonConfig(colorScheme);
    
    return GestureDetector(
      onTapDown: _onTapDown,
      onTapUp: _onTapUp,
      onTapCancel: _onTapCancel,
      onTap: widget.isLoading ? null : widget.onPressed,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.isFullWidth ? double.infinity : null,
              height: _getButtonHeight(),
              decoration: BoxDecoration(
                color: config.backgroundColor,
                borderRadius: ShapeTokens.borderRadiusMd,
                border: config.border,
                boxShadow: config.boxShadow,
              ),
              child: Material(
                color: ColorTokens.transparent,
                borderRadius: ShapeTokens.borderRadiusMd,
                child: InkWell(
                  borderRadius: ShapeTokens.borderRadiusMd,
                  onTap: widget.isLoading ? null : widget.onPressed,
                  child: Padding(
                    padding: _getButtonPadding(),
                    child: Row(
                      mainAxisSize: widget.isFullWidth ? MainAxisSize.max : MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (widget.isLoading) ...[
                          SizedBox(
                            width: _getIconSize(),
                            height: _getIconSize(),
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(config.foregroundColor),
                            ),
                          ),
                          SpacingTokens.horizontalSpaceSm,
                        ] else if (widget.icon != null) ...[
                          SizedBox(
                            width: _getIconSize(),
                            height: _getIconSize(),
                            child: IconTheme(
                              data: IconThemeData(
                                color: config.foregroundColor,
                                size: _getIconSize(),
                              ),
                              child: widget.icon!,
                            ),
                          ),
                          SpacingTokens.horizontalSpaceSm,
                        ],
                        Flexible(
                          child: Text(
                            widget.text,
                            style: _getTextStyle(config.foregroundColor),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  _ButtonConfig _getButtonConfig(ColorScheme colorScheme) {
    switch (widget.type) {
      case DSButtonType.primary:
        return _ButtonConfig(
          backgroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.12) : colorScheme.primary,
          foregroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.38) : colorScheme.onPrimary,
          boxShadow: widget.onPressed == null ? null : ElevationTokens.buttonShadow(context, colorScheme.primary),
        );
      case DSButtonType.secondary:
        return _ButtonConfig(
          backgroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.12) : colorScheme.secondary,
          foregroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.38) : colorScheme.onSecondary,
          boxShadow: widget.onPressed == null ? null : ElevationTokens.buttonShadow(context, colorScheme.secondary),
        );
      case DSButtonType.outlined:
        return _ButtonConfig(
          backgroundColor: ColorTokens.transparent,
          foregroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.38) : colorScheme.primary,
          border: Border.all(
            color: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.12) : colorScheme.outline,
          ),
        );
      case DSButtonType.text:
        return _ButtonConfig(
          backgroundColor: ColorTokens.transparent,
          foregroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.38) : colorScheme.primary,
        );
      case DSButtonType.destructive:
        return _ButtonConfig(
          backgroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.12) : colorScheme.error,
          foregroundColor: widget.onPressed == null ? colorScheme.onSurface.withValues(alpha: 0.38) : colorScheme.onError,
          boxShadow: widget.onPressed == null ? null : ElevationTokens.buttonShadow(context, colorScheme.error),
        );
    }
  }

  double _getButtonHeight() {
    switch (widget.size) {
      case DSButtonSize.small:
        return SpacingTokens.buttonHeightSmall;
      case DSButtonSize.medium:
        return SpacingTokens.buttonHeightMedium;
      case DSButtonSize.large:
        return SpacingTokens.buttonHeightLarge;
    }
  }

  EdgeInsetsGeometry _getButtonPadding() {
    switch (widget.size) {
      case DSButtonSize.small:
        return SpacingTokens.buttonPaddingSmall;
      case DSButtonSize.medium:
        return SpacingTokens.buttonPaddingMedium;
      case DSButtonSize.large:
        return SpacingTokens.buttonPaddingLarge;
    }
  }

  TextStyle _getTextStyle(Color color) {
    final baseStyle = widget.size == DSButtonSize.small 
        ? TypographyTokens.labelMedium 
        : TypographyTokens.labelLarge;
    
    return baseStyle.copyWith(color: color);
  }

  double _getIconSize() {
    switch (widget.size) {
      case DSButtonSize.small:
        return SpacingTokens.iconSizeSmall;
      case DSButtonSize.medium:
        return SpacingTokens.iconSizeMedium;
      case DSButtonSize.large:
        return SpacingTokens.iconSizeLarge;
    }
  }
}

/// 按钮配置类
class _ButtonConfig {
  final Color backgroundColor;
  final Color foregroundColor;
  final Border? border;
  final List<BoxShadow>? boxShadow;

 const _ButtonConfig({
    required this.backgroundColor,
    required this.foregroundColor,
    this.border,
    this.boxShadow,
  });
}

/// 按钮类型枚举
enum DSButtonType {
  primary,    // 主要按钮
  secondary,  // 次要按钮
  outlined,   // 轮廓按钮
  text,       // 文本按钮
  destructive, // 危险操作按钮
}

/// 按钮尺寸枚举
enum DSButtonSize {
  small,   // 小尺寸 36px
  medium,  // 中等尺寸 44px
  large,   // 大尺寸 52px
}