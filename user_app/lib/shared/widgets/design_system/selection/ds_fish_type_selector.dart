import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/shared/widgets/design_system/common/common.dart';
import 'package:user_app/shared/widgets/design_system/painters/painters.dart';

/// Design System 鱼种选择器组件
/// 
/// 支持多选鱼种，包含加载状态、空状态和自定义鱼种添加功能
class DSFishTypeSelector extends StatelessWidget {
  final List<FishType> availableFishTypes;
  final List<int> selectedFishTypeIds;
  final void Function(int) onToggleFishType;
  final void Function(String) onAddCustomFishType;
  final bool isLoading;
  final String title;
  final String subtitle;
  final IconData icon;
  final int crossAxisCount;
  final double childAspectRatio;
  final double spacing;
  final double runSpacing;
  final EdgeInsetsGeometry? padding;

  const DSFishTypeSelector({
    super.key,
    required this.availableFishTypes,
    required this.selectedFishTypeIds,
    required this.onToggleFishType,
    required this.onAddCustomFishType,
    this.isLoading = false,
    this.title = '可钓鱼种',
    this.subtitle = '选择在此钓点可以钓到的鱼种',
    this.icon = Icons.set_meal_rounded,
    this.crossAxisCount = 4,
    this.childAspectRatio = 2.5,
    this.spacing = SpacingTokens.space3,
    this.runSpacing = SpacingTokens.space3,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 章节标题
        DSSectionHeader(
          title: title,
          subtitle: subtitle,
          icon: icon,
          padding: padding,
        ),

        const SizedBox(height: SpacingTokens.space4),

        // 鱼种选择内容
        _buildFishTypeContent(context),
      ],
    );
  }

  Widget _buildFishTypeContent(BuildContext context) {
    if (isLoading) {
      return _buildLoadingState(context);
    }

    if (availableFishTypes.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildFishTypeGrid(context);
  }

  /// 构建加载状态
  Widget _buildLoadingState(BuildContext context) {
    return Container(
      height: 120,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: SpacingTokens.space8,
              height: SpacingTokens.space8,
              child: CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
            const SizedBox(height: SpacingTokens.space3),
            Text(
              '正在加载鱼种信息...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.info_outline_rounded,
              size: SpacingTokens.space10,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: SpacingTokens.space4),
            Text(
              '暂无可选鱼种',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: SpacingTokens.space2),
            Text(
              '您可以添加自定义鱼种',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: SpacingTokens.space4),
            DSFishTypeAddButton(
              onPressed: () => _showAddCustomFishTypeDialog(context),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建鱼种网格
  Widget _buildFishTypeGrid(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: crossAxisCount,
        childAspectRatio: childAspectRatio,
        crossAxisSpacing: spacing,
        mainAxisSpacing: runSpacing,
      ),
      itemCount: availableFishTypes.length + 1, // +1 for add button
      itemBuilder: (context, index) {
        if (index == availableFishTypes.length) {
          return DSFishTypeAddButton(
            onPressed: () => _showAddCustomFishTypeDialog(context),
          );
        }

        final fishType = availableFishTypes[index];
        final isSelected = selectedFishTypeIds.contains(fishType.id);

        return DSFishTypeItem(
          fishType: fishType,
          isSelected: isSelected,
          onTap: () {
            HapticFeedback.lightImpact();
            onToggleFishType(fishType.id);
          },
        );
      },
    );
  }

  /// 显示添加自定义鱼种对话框
  void _showAddCustomFishTypeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => DSCustomFishTypeDialog(
        onAddFishType: onAddCustomFishType,
      ),
    );
  }
}

/// 鱼种选择项组件
class DSFishTypeItem extends StatelessWidget {
  final FishType fishType;
  final bool isSelected;
  final VoidCallback onTap;

  const DSFishTypeItem({
    super.key,
    required this.fishType,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          color: isSelected
              ? theme.colorScheme.primaryContainer
              : theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          border: Border.all(
            color: isSelected
                ? theme.colorScheme.primary
                : theme.colorScheme.outline.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: isSelected
                  ? theme.colorScheme.primary.withValues(alpha: 0.3)
                  : theme.colorScheme.shadow.withValues(alpha: 0.05),
              blurRadius: isSelected ? SpacingTokens.space3 : SpacingTokens.space2,
              offset: const Offset(0, SpacingTokens.space1),
            ),
          ],
        ),
        child: Center(
          child: Text(
            fishType.name,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isSelected
                  ? theme.colorScheme.onPrimaryContainer
                  : theme.colorScheme.onSurface,
              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}

/// 添加自定义鱼种按钮组件
class DSFishTypeAddButton extends StatelessWidget {
  final VoidCallback onPressed;

  const DSFishTypeAddButton({
    super.key,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onPressed();
      },
      child: DSDashedContainer(
        borderColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
        backgroundColor: Theme.of(context)
            .colorScheme
            .primaryContainer
            .withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_rounded,
                size: SpacingTokens.space5,
                color: Theme.of(context).colorScheme.primary,
              ),
              const SizedBox(height: SpacingTokens.space1),
              Text(
                '自定义',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 自定义鱼种添加对话框
class DSCustomFishTypeDialog extends StatefulWidget {
  final void Function(String) onAddFishType;

  const DSCustomFishTypeDialog({
    super.key,
    required this.onAddFishType,
  });

  @override
  State<DSCustomFishTypeDialog> createState() => _DSCustomFishTypeDialogState();
}

class _DSCustomFishTypeDialogState extends State<DSCustomFishTypeDialog> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 延迟聚焦，确保对话框完全显示后再聚焦
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _focusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(SpacingTokens.space5),
      ),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(SpacingTokens.space2),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(SpacingTokens.space2),
            ),
            child: Icon(
              Icons.set_meal_rounded,
              color: theme.colorScheme.onPrimary,
              size: SpacingTokens.space5,
            ),
          ),
          const SizedBox(width: SpacingTokens.space3),
          Text(
            '添加自定义鱼种',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '请输入鱼种名称：',
            style: theme.textTheme.bodyMedium,
          ),
          const SizedBox(height: SpacingTokens.space3),
          TextField(
            controller: _controller,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: '例如：鲤鱼、草鱼等',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(SpacingTokens.space3),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: SpacingTokens.space4,
                vertical: SpacingTokens.space3,
              ),
            ),
            onSubmitted: (_) => _handleAdd(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            '取消',
            style: TextStyle(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
        ),
        FilledButton(
          onPressed: _handleAdd,
          child: const Text('添加'),
        ),
      ],
    );
  }

  void _handleAdd() {
    final fishTypeName = _controller.text.trim();
    if (fishTypeName.isNotEmpty) {
      widget.onAddFishType(fishTypeName);
      Navigator.of(context).pop();
    }
  }
}