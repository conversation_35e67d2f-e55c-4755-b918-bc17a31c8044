import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System - TextField组件
/// 严格遵循Material Design 3规范的文本输入框组件
/// 
/// 使用方式：
/// ```dart
/// DSTextField.outlined(
///   labelText: '用户名',
///   onChanged: (value) => print(value),
/// )
/// ```
class DSTextField extends StatefulWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final String? helperText;
  final String? errorText;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final VoidCallback? onTap;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final VoidCallback? onEditingComplete;
  final DSTextFieldType type;
  final DSTextFieldSize size;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final bool enabled;
  final bool readOnly;
  final bool obscureText;
  final bool autofocus;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final FocusNode? focusNode;

 const DSTextField._({
    super.key,
    required this.type,
    this.controller,
    this.labelText,
    this.hintText,
    this.helperText,
    this.errorText,
    this.prefixIcon,
    this.suffixIcon,
    this.onTap,
    this.onChanged,
    this.onSubmitted,
    this.onEditingComplete,
    this.size = DSTextFieldSize.medium,
    this.keyboardType,
    this.textInputAction,
    this.enabled = true,
    this.readOnly = false,
    this.obscureText = false,
    this.autofocus = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.focusNode,
  });

  /// 填充文本框
  factory DSTextField.filled({
    Key? key,
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    String? helperText,
    String? errorText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    VoidCallback? onTap,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
    VoidCallback? onEditingComplete,
    DSTextFieldSize size = DSTextFieldSize.medium,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    final bool enabled = true,
    final bool readOnly = false,
    final bool obscureText = false,
    final bool autofocus = false,
    int? maxLines = 1,
    int? minLines,
    int? maxLength,
    List<TextInputFormatter>? inputFormatters,
    FocusNode? focusNode,
  }) {
    return DSTextField._(
      key: key,
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      onTap: onTap,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      onEditingComplete: onEditingComplete,
      type: DSTextFieldType.filled,
      size: size,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      enabled: enabled,
      readOnly: readOnly,
      obscureText: obscureText,
      autofocus: autofocus,
      maxLines: maxLines,
      minLines: minLines,
      maxLength: maxLength,
      inputFormatters: inputFormatters,
      focusNode: focusNode,
    );
  }

  /// 轮廓文本框
  factory DSTextField.outlined({
    Key? key,
    TextEditingController? controller,
    String? labelText,
    String? hintText,
    String? helperText,
    String? errorText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    VoidCallback? onTap,
    ValueChanged<String>? onChanged,
    ValueChanged<String>? onSubmitted,
    VoidCallback? onEditingComplete,
    DSTextFieldSize size = DSTextFieldSize.medium,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    final bool enabled = true,
    final bool readOnly = false,
    final bool obscureText = false,
    final bool autofocus = false,
    int? maxLines = 1,
    int? minLines,
    int? maxLength,
    List<TextInputFormatter>? inputFormatters,
    FocusNode? focusNode,
  }) {
    return DSTextField._(
      key: key,
      controller: controller,
      labelText: labelText,
      hintText: hintText,
      helperText: helperText,
      errorText: errorText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      onTap: onTap,
      onChanged: onChanged,
      onSubmitted: onSubmitted,
      onEditingComplete: onEditingComplete,
      type: DSTextFieldType.outlined,
      size: size,
      keyboardType: keyboardType,
      textInputAction: textInputAction,
      enabled: enabled,
      readOnly: readOnly,
      obscureText: obscureText,
      autofocus: autofocus,
      maxLines: maxLines,
      minLines: minLines,
      maxLength: maxLength,
      inputFormatters: inputFormatters,
      focusNode: focusNode,
    );
  }

  @override
  State<DSTextField> createState() => _DSTextFieldState();
}

class _DSTextFieldState extends State<DSTextField> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _focusAnimation;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    
    _animationController = AnimationController(
      duration: MotionTokens.textFieldFocusDuration,
      vsync: this,
    );
    
    _focusAnimation = CurvedAnimation(
      parent: _animationController,
      curve: MotionTokens.textFieldFocusCurve,
    );

    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _animationController.dispose();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
      if (_isFocused) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final config = _getTextFieldConfig(colorScheme);

    return AnimatedBuilder(
      animation: _focusAnimation,
      builder: (context, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              height: _getTextFieldHeight(),
              child: TextField(
                controller: widget.controller,
                focusNode: _focusNode,
                onTap: widget.onTap,
                onChanged: widget.onChanged,
                onSubmitted: widget.onSubmitted,
                onEditingComplete: widget.onEditingComplete,
                keyboardType: widget.keyboardType,
                textInputAction: widget.textInputAction,
                enabled: widget.enabled,
                readOnly: widget.readOnly,
                obscureText: widget.obscureText,
                autofocus: widget.autofocus,
                maxLines: widget.maxLines,
                minLines: widget.minLines,
                maxLength: widget.maxLength,
                inputFormatters: widget.inputFormatters,
                style: _getTextStyle(colorScheme),
                decoration: InputDecoration(
                  labelText: widget.labelText,
                  hintText: widget.hintText,
                  helperText: widget.helperText,
                  errorText: widget.errorText,
                  prefixIcon: widget.prefixIcon != null 
                    ? IconTheme(
                        data: IconThemeData(
                          color: _getIconColor(colorScheme),
                          size: _getIconSize(),
                        ),
                        child: widget.prefixIcon!,
                      )
                    : null,
                  suffixIcon: widget.suffixIcon != null
                    ? IconTheme(
                        data: IconThemeData(
                          color: _getIconColor(colorScheme),
                          size: _getIconSize(),
                        ),
                        child: widget.suffixIcon!,
                      )
                    : null,
                  filled: widget.type == DSTextFieldType.filled,
                  fillColor: config.fillColor,
                  border: config.border,
                  enabledBorder: config.enabledBorder,
                  focusedBorder: config.focusedBorder.copyWith(
                    borderSide: BorderSide(
                      color: Color.lerp(
                        colorScheme.primary.withValues(alpha: 0.5),
                        colorScheme.primary,
                        _focusAnimation.value,
                      )!,
                      width: 2,
                    ),
                  ),
                  errorBorder: config.errorBorder,
                  focusedErrorBorder: config.focusedErrorBorder,
                  disabledBorder: config.disabledBorder,
                  labelStyle: _getLabelStyle(colorScheme),
                  hintStyle: _getHintStyle(colorScheme),
                  helperStyle: _getHelperStyle(colorScheme),
                  errorStyle: _getErrorStyle(colorScheme),
                  contentPadding: _getContentPadding(),
                  counterStyle: _getCounterStyle(colorScheme),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  _TextFieldConfig _getTextFieldConfig(ColorScheme colorScheme) {
    final bool hasError = widget.errorText != null && widget.errorText!.isNotEmpty;
    
    switch (widget.type) {
      case DSTextFieldType.filled:
        return _TextFieldConfig(
          fillColor: widget.enabled 
            ? colorScheme.surfaceContainerHighest 
            : colorScheme.onSurface.withValues(alpha: 0.04),
          border: const UnderlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusTopMd,
            borderSide: BorderSide.none,
          ),
          enabledBorder: UnderlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusTopMd,
            borderSide: BorderSide(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
          focusedBorder: UnderlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusTopMd,
            borderSide: BorderSide(
              color: colorScheme.primary,
              width: 2,
            ),
          ),
          errorBorder: UnderlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusTopMd,
            borderSide: BorderSide(
              color: colorScheme.error,
            ),
          ),
          focusedErrorBorder: UnderlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusTopMd,
            borderSide: BorderSide(
              color: colorScheme.error,
              width: 2,
            ),
          ),
          disabledBorder: UnderlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusTopMd,
            borderSide: BorderSide(
              color: colorScheme.onSurface.withValues(alpha: 0.38),
            ),
          ),
        );
      case DSTextFieldType.outlined:
        return _TextFieldConfig(
          fillColor: ColorTokens.transparent,
          border: const OutlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusMd,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusMd,
            borderSide: BorderSide(
              color: hasError ? colorScheme.error : colorScheme.outline,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusMd,
            borderSide: BorderSide(
              color: hasError ? colorScheme.error : colorScheme.primary,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusMd,
            borderSide: BorderSide(
              color: colorScheme.error,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusMd,
            borderSide: BorderSide(
              color: colorScheme.error,
              width: 2,
            ),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: ShapeTokens.borderRadiusMd,
            borderSide: BorderSide(
              color: colorScheme.onSurface.withValues(alpha: 0.12),
            ),
          ),
        );
    }
  }

  double? _getTextFieldHeight() {
    switch (widget.size) {
      case DSTextFieldSize.small:
        return widget.maxLines == 1 ? 40.0 : null;
      case DSTextFieldSize.medium:
        return widget.maxLines == 1 ? 56.0 : null;
      case DSTextFieldSize.large:
        return widget.maxLines == 1 ? 64.0 : null;
    }
  }

  EdgeInsetsGeometry _getContentPadding() {
    switch (widget.size) {
      case DSTextFieldSize.small:
        return SpacingTokens.inputPaddingSmall;
      case DSTextFieldSize.medium:
        return SpacingTokens.inputPadding;
      case DSTextFieldSize.large:
        return SpacingTokens.inputPaddingLarge;
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case DSTextFieldSize.small:
        return 16.0;
      case DSTextFieldSize.medium:
        return 20.0;
      case DSTextFieldSize.large:
        return 24.0;
    }
  }

  TextStyle _getTextStyle(ColorScheme colorScheme) {
    final baseStyle = widget.size == DSTextFieldSize.small 
        ? TypographyTokens.bodySmall
        : TypographyTokens.bodyLarge;
    
    return baseStyle.copyWith(
      color: widget.enabled 
        ? colorScheme.onSurface 
        : colorScheme.onSurface.withValues(alpha: 0.38),
    );
  }

  TextStyle _getLabelStyle(ColorScheme colorScheme) {
    return TypographyTokens.bodyMedium.copyWith(
      color: widget.enabled 
        ? (_isFocused 
          ? colorScheme.primary 
          : colorScheme.onSurfaceVariant)
        : colorScheme.onSurface.withValues(alpha: 0.38),
    );
  }

  TextStyle _getHintStyle(ColorScheme colorScheme) {
    return TypographyTokens.bodyMedium.copyWith(
      color: widget.enabled 
        ? colorScheme.onSurfaceVariant.withValues(alpha: 0.6)
        : colorScheme.onSurface.withValues(alpha: 0.38),
    );
  }

  TextStyle _getHelperStyle(ColorScheme colorScheme) {
    return TypographyTokens.bodySmall.copyWith(
      color: colorScheme.onSurfaceVariant,
    );
  }

  TextStyle _getErrorStyle(ColorScheme colorScheme) {
    return TypographyTokens.bodySmall.copyWith(
      color: colorScheme.error,
    );
  }

  TextStyle _getCounterStyle(ColorScheme colorScheme) {
    return TypographyTokens.bodySmall.copyWith(
      color: colorScheme.onSurfaceVariant,
    );
  }

  Color _getIconColor(ColorScheme colorScheme) {
    if (!widget.enabled) {
      return colorScheme.onSurface.withValues(alpha: 0.38);
    }
    if (widget.errorText != null && widget.errorText!.isNotEmpty) {
      return colorScheme.error;
    }
    return _isFocused ? colorScheme.primary : colorScheme.onSurfaceVariant;
  }
}

/// 文本框配置类
class _TextFieldConfig {
  final Color fillColor;
  final InputBorder border;
  final InputBorder enabledBorder;
  final InputBorder focusedBorder;
  final InputBorder errorBorder;
  final InputBorder focusedErrorBorder;
  final InputBorder disabledBorder;

 const _TextFieldConfig({
    required this.fillColor,
    required this.border,
    required this.enabledBorder,
    required this.focusedBorder,
    required this.errorBorder,
    required this.focusedErrorBorder,
    required this.disabledBorder,
  });
}

/// 文本框类型枚举
enum DSTextFieldType {
  filled,    // 填充文本框
  outlined,  // 轮廓文本框
}

/// 文本框尺寸枚举
enum DSTextFieldSize {
  small,   // 小尺寸 40px
  medium,  // 中等尺寸 56px
  large,   // 大尺寸 64px
}