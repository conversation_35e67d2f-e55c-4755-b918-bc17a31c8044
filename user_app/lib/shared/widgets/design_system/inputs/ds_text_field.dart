import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 文本输入框组件
/// 
/// 符合 Material Design 3 规范的统一文本输入框
/// 提供一致的视觉样式和交互体验
class DSTextField extends StatefulWidget {
  final TextEditingController controller;
  final String label;
  final String? hintText;
  final IconData? prefixIcon;
  final Widget? suffixIcon;
  final String? Function(String?)? validator;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final int maxLines;
  final int? maxLength;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final FocusNode? focusNode;
  final void Function(String)? onChanged;
  final void Function(String?)? onSaved;
  final void Function(String)? onFieldSubmitted;
  final EdgeInsetsGeometry? contentPadding;
  final String? errorText;
  final String? helperText;
  final bool showCounter;
  final List<TextInputFormatter>? inputFormatters;

  const DSTextField({
    super.key,
    required this.controller,
    required this.label,
    this.hintText,
    this.prefixIcon,
    this.suffixIcon,
    this.validator,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.next,
    this.maxLines = 1,
    this.maxLength,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.focusNode,
    this.onChanged,
    this.onSaved,
    this.onFieldSubmitted,
    this.contentPadding,
    this.errorText,
    this.helperText,
    this.showCounter = false,
    this.inputFormatters,
  });

  @override
  State<DSTextField> createState() => _DSTextFieldState();
}

class _DSTextFieldState extends State<DSTextField> {
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    if (widget.focusNode == null) {
      _focusNode.dispose();
    } else {
      _focusNode.removeListener(_onFocusChange);
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        boxShadow: _isFocused
            ? [
                BoxShadow(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  blurRadius: SpacingTokens.space2,
                  offset: const Offset(0, SpacingTokens.space1),
                ),
              ]
            : null,
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        validator: widget.validator,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        maxLines: widget.maxLines,
        maxLength: widget.maxLength,
        obscureText: widget.obscureText,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        onChanged: widget.onChanged,
        onSaved: widget.onSaved,
        onFieldSubmitted: widget.onFieldSubmitted,
        inputFormatters: widget.inputFormatters,
        style: theme.textTheme.bodyLarge?.copyWith(
          color: theme.colorScheme.onSurface,
        ),
        decoration: InputDecoration(
          labelText: widget.label,
          hintText: widget.hintText,
          errorText: widget.errorText,
          helperText: widget.helperText,
          prefixIcon: widget.prefixIcon != null
              ? Icon(
                  widget.prefixIcon,
                  color: _isFocused
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                )
              : null,
          suffixIcon: widget.suffixIcon,
          
          // 边框样式
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
            borderSide: BorderSide(
              color: theme.colorScheme.outline,
              width: 1,
            ),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
            borderSide: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.6),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
            borderSide: BorderSide(
              color: theme.colorScheme.primary,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
            borderSide: BorderSide(
              color: theme.colorScheme.error,
              width: 1,
            ),
          ),
          focusedErrorBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
            borderSide: BorderSide(
              color: theme.colorScheme.error,
              width: 2,
            ),
          ),
          disabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
            borderSide: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          
          // 填充样式
          filled: true,
          fillColor: _isFocused
              ? theme.colorScheme.surface
              : theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
          
          // 内边距
          contentPadding: widget.contentPadding ??
              EdgeInsets.symmetric(
                horizontal: SpacingTokens.space4,
                vertical: widget.maxLines > 1 
                    ? SpacingTokens.space4 
                    : SpacingTokens.space3,
              ),
          
          // 标签样式
          labelStyle: theme.textTheme.bodyMedium?.copyWith(
            color: _isFocused
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurfaceVariant,
          ),
          floatingLabelStyle: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.w600,
          ),
          
          // 提示文本样式
          hintStyle: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
          ),
          
          // 帮助文本样式
          helperStyle: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurfaceVariant,
          ),
          
          // 错误文本样式
          errorStyle: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.error,
          ),
          
          // 计数器
          counterStyle: widget.showCounter
              ? theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                )
              : const TextStyle(fontSize: 0), // 隐藏计数器
        ),
      ),
    );
  }
}

/// 多行文本输入框的便捷构造器
class DSTextArea extends DSTextField {
  const DSTextArea({
    super.key,
    required super.controller,
    required super.label,
    super.hintText,
    super.validator,
    super.onChanged,
    super.onSaved,
    super.enabled = true,
    super.maxLength,
    super.showCounter = true,
    int minLines = 3,
    int maxLines = 5,
  }) : super(
          maxLines: maxLines,
          keyboardType: TextInputType.multiline,
          textInputAction: TextInputAction.newline,
          contentPadding: const EdgeInsets.all(SpacingTokens.space4),
        );
}

/// 搜索输入框的便捷构造器
class DSSearchField extends DSTextField {
  const DSSearchField({
    super.key,
    required super.controller,
    super.hintText = '搜索...',
    super.onChanged,
    super.onFieldSubmitted,
    VoidCallback? onClear,
  }) : super(
          label: '',
          prefixIcon: Icons.search_rounded,
          suffixIcon: onClear != null
              ? IconButton(
                  onPressed: onClear,
                  icon: const Icon(Icons.clear_rounded),
                )
              : null,
          keyboardType: TextInputType.text,
          textInputAction: TextInputAction.search,
        );
}