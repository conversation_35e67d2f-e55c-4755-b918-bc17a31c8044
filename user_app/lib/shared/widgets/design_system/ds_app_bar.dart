import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 统一的应用栏组件，确保所有页面头部设计一致
class DSAppBar extends StatelessWidget implements PreferredSizeWidget {
  /// 标题文本
  final String title;

  /// 标题组件（如果提供，将覆盖 title 参数）
  final Widget? titleWidget;

  /// 是否显示返回按钮
  final bool showBackButton;

  /// 右侧操作按钮
  final List<Widget>? actions;

  /// 是否居中标题
  final bool centerTitle;

  /// 背景颜色
  final Color? backgroundColor;

  /// 前景色（文字和图标颜色）
  final Color? foregroundColor;

  /// 底部组件（如 TabBar）
  final PreferredSizeWidget? bottom;

  /// 高度（阴影）
  final double? elevation;

  /// 是否自动设置 leading
  final bool automaticallyImplyLeading;

  /// 自定义 leading 组件
  final Widget? leading;

  /// 系统 UI 覆盖样式
  final SystemUiOverlayStyle? systemOverlayStyle;

  const DSAppBar({
    Key? key,
    required this.title,
    this.titleWidget,
    this.showBackButton = true,
    this.actions,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.bottom,
    this.elevation,
    this.automaticallyImplyLeading = true,
    this.leading,
    this.systemOverlayStyle,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultBackgroundColor = theme.colorScheme.surface;
    final defaultForegroundColor = theme.colorScheme.onSurface;

    return AppBar(
      title: titleWidget ??
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: foregroundColor ?? defaultForegroundColor,
              fontWeight: FontWeight.w600,
            ),
          ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? defaultBackgroundColor,
      foregroundColor: foregroundColor ?? defaultForegroundColor,
      elevation: elevation ?? 0,
      scrolledUnderElevation: elevation ?? 1,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      actions: actions,
      bottom: bottom,
      systemOverlayStyle: systemOverlayStyle ??
          SystemUiOverlayStyle(
            statusBarColor: ColorTokens.transparent,
            statusBarIconBrightness: theme.brightness == Brightness.light
                ? Brightness.dark
                : Brightness.light,
            statusBarBrightness: theme.brightness == Brightness.light
                ? Brightness.light
                : Brightness.dark,
          ),
      surfaceTintColor: ColorTokens.transparent,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
        kToolbarHeight + (bottom?.preferredSize.height ?? 0),
      );
}

/// 可滚动的应用栏，适用于需要滚动效果的页面
class DSSliverAppBar extends StatelessWidget {
  /// 标题文本
  final String title;

  /// 标题组件（如果提供，将覆盖 title 参数）
  final Widget? titleWidget;

  /// 右侧操作按钮
  final List<Widget>? actions;

  /// 是否浮动（滚动时显示/隐藏）
  final bool floating;

  /// 是否固定在顶部
  final bool pinned;

  /// 是否快速返回
  final bool snap;

  /// 展开高度
  final double? expandedHeight;

  /// 灵活空间组件
  final Widget? flexibleSpace;

  /// 底部组件
  final PreferredSizeWidget? bottom;

  /// 是否居中标题
  final bool centerTitle;

  /// 背景颜色
  final Color? backgroundColor;

  /// 前景色
  final Color? foregroundColor;

  /// 是否自动设置 leading
  final bool automaticallyImplyLeading;

  /// 自定义 leading 组件
  final Widget? leading;

  const DSSliverAppBar({
    Key? key,
    required this.title,
    this.titleWidget,
    this.actions,
    this.floating = true,
    this.pinned = false,
    this.snap = false,
    this.expandedHeight,
    this.flexibleSpace,
    this.bottom,
    this.centerTitle = true,
    this.backgroundColor,
    this.foregroundColor,
    this.automaticallyImplyLeading = true,
    this.leading,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final defaultBackgroundColor = theme.colorScheme.surface;
    final defaultForegroundColor = theme.colorScheme.onSurface;

    return SliverAppBar(
      title: titleWidget ??
          Text(
            title,
            style: theme.textTheme.titleLarge?.copyWith(
              color: foregroundColor ?? defaultForegroundColor,
              fontWeight: FontWeight.w600,
            ),
          ),
      centerTitle: centerTitle,
      backgroundColor: backgroundColor ?? defaultBackgroundColor,
      foregroundColor: foregroundColor ?? defaultForegroundColor,
      elevation: 0,
      scrolledUnderElevation: 1,
      floating: floating,
      pinned: pinned,
      snap: snap,
      expandedHeight: expandedHeight,
      flexibleSpace: flexibleSpace,
      bottom: bottom,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      actions: actions,
      surfaceTintColor: ColorTokens.transparent,
    );
  }
}

/// 简单的自定义头部组件，用于不需要 AppBar 功能的页面
class DSCustomHeader extends StatelessWidget {
  /// 标题文本
  final String title;

  /// 标题组件（如果提供，将覆盖 title 参数）
  final Widget? titleWidget;

  /// 右侧操作按钮
  final List<Widget>? actions;

  /// 高度
  final double height;

  /// 内边距
  final EdgeInsetsGeometry? padding;

  /// 背景装饰
  final BoxDecoration? decoration;

  /// 是否添加安全区域
  final bool useSafeArea;

  const DSCustomHeader({
    Key? key,
    required this.title,
    this.titleWidget,
    this.actions,
    this.height = kToolbarHeight,
    this.padding,
    this.decoration,
    this.useSafeArea = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final topPadding = useSafeArea ? MediaQuery.of(context).padding.top : 0.0;

    Widget content = Container(
      height: height + topPadding,
      padding: padding ??
          EdgeInsets.only(
            left: SpacingTokens.space4,
            right: SpacingTokens.space4,
            top: topPadding,
          ),
      decoration: decoration ??
          BoxDecoration(
            color: theme.colorScheme.surface,
            boxShadow: [
              BoxShadow(
                color: ColorTokens.shadow.withOpacity(0.05),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
      child: Row(
        children: [
          Expanded(
            child: Align(
              alignment: Alignment.centerLeft,
              child: titleWidget ??
                  Text(
                    title,
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
            ),
          ),
          if (actions != null) ...actions!,
        ],
      ),
    );

    return content;
  }
}

/// AppBar 操作按钮的统一样式
class DSAppBarAction extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String? tooltip;
  final Widget? badge;

  const DSAppBarAction({
    Key? key,
    required this.icon,
    this.onPressed,
    this.tooltip,
    this.badge,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget iconButton = IconButton(
      icon: Icon(icon),
      onPressed: onPressed,
      tooltip: tooltip,
      iconSize: SpacingTokens.space6,
    );

    if (badge != null) {
      iconButton = Stack(
        clipBehavior: Clip.none,
        children: [
          iconButton,
          Positioned(
            right: SpacingTokens.space2,
            top: SpacingTokens.space2,
            child: badge!,
          ),
        ],
      );
    }

    return iconButton;
  }
}

/// 标准的 AppBar 高度常量
class AppBarHeights {
  static const double standard = kToolbarHeight; // 56.0
  static const double extended = SpacingTokens.space24 * 5; // 120.0
  static const double withSearch = SpacingTokens.space24 * 4; // 96.0 (接近 100.0)
  static const double withTabs = kToolbarHeight + SpacingTokens.space12; // 104.0
}