import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 位置信息卡片组件
/// 
/// 用于显示当前选择的位置信息，支持不同的显示样式
class DSLocationInfoCard extends StatelessWidget {
  final String address;
  final String? subtitle;
  final IconData? leadingIcon;
  final Color? iconColor;
  final Widget? trailing;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final VoidCallback? onTap;
  final bool showBorder;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const DSLocationInfoCard({
    super.key,
    required this.address,
    this.subtitle,
    this.leadingIcon,
    this.iconColor,
    this.trailing,
    this.padding,
    this.margin,
    this.onTap,
    this.showBorder = true,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = iconColor ?? theme.colorScheme.primary;
    final effectiveBackgroundColor = backgroundColor ?? theme.colorScheme.surfaceVariant.withValues(alpha: 0.3);
    final effectivePadding = padding ?? const EdgeInsets.all(SpacingTokens.space4);
    final effectiveMargin = margin ?? const EdgeInsets.symmetric(horizontal: SpacingTokens.space5);
    final effectiveBorderRadius = borderRadius ?? BorderRadius.circular(SpacingTokens.space3);
    
    final content = Container(
      margin: effectiveMargin,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: effectiveBorderRadius,
        border: showBorder 
            ? Border.all(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
                width: 1,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: SpacingTokens.space2,
            offset: const Offset(0, SpacingTokens.space1),
          ),
        ],
      ),
      child: Padding(
        padding: effectivePadding,
        child: Row(
          children: [
            // 前置图标
            if (leadingIcon != null)
              Icon(
                leadingIcon,
                color: effectiveIconColor,
                size: SpacingTokens.space5,
              ),
            
            if (leadingIcon != null)
              const SizedBox(width: SpacingTokens.space3),
            
            // 地址信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (subtitle != null) ...[
                    Text(
                      subtitle!,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: SpacingTokens.space1),
                  ],
                  Text(
                    address,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onSurface,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            
            // 尾随组件
            if (trailing != null) ...[
              const SizedBox(width: SpacingTokens.space3),
              trailing!,
            ],
          ],
        ),
      ),
    );

    if (onTap != null) {
      return Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: effectiveBorderRadius,
          child: content,
        ),
      );
    }

    return content;
  }
}

/// 当前位置信息卡片变体
class DSCurrentLocationCard extends DSLocationInfoCard {
  const DSCurrentLocationCard({
    super.key,
    required super.address,
    super.onTap,
    super.padding,
    super.margin,
  }) : super(
          subtitle: '当前选择',
          leadingIcon: Icons.location_on_rounded,
        );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return DSLocationInfoCard(
      address: address,
      subtitle: subtitle,
      leadingIcon: leadingIcon,
      iconColor: theme.colorScheme.primary,
      backgroundColor: theme.colorScheme.primaryContainer.withValues(alpha: 0.2),
      onTap: onTap,
      padding: padding,
      margin: margin,
    );
  }
}

/// 建议位置信息卡片变体
class DSSuggestedLocationCard extends DSLocationInfoCard {
  const DSSuggestedLocationCard({
    super.key,
    required super.address,
    super.onTap,
    super.padding,
    super.margin,
  }) : super(
          subtitle: '建议位置',
          leadingIcon: Icons.place_rounded,
        );

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return DSLocationInfoCard(
      address: address,
      subtitle: subtitle,
      leadingIcon: leadingIcon,
      iconColor: theme.colorScheme.secondary,
      backgroundColor: theme.colorScheme.secondaryContainer.withValues(alpha: 0.2),
      onTap: onTap,
      padding: padding,
      margin: margin,
    );
  }
}

/// 紧凑型位置信息卡片
class DSCompactLocationCard extends StatelessWidget {
  final String address;
  final IconData icon;
  final Color? iconColor;
  final VoidCallback? onTap;

  const DSCompactLocationCard({
    super.key,
    required this.address,
    this.icon = Icons.location_on_rounded,
    this.iconColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = iconColor ?? theme.colorScheme.onSurfaceVariant;
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space3,
        vertical: SpacingTokens.space2,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(SpacingTokens.space2),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: SpacingTokens.space4,
            color: effectiveIconColor,
          ),
          const SizedBox(width: SpacingTokens.space2),
          Flexible(
            child: Text(
              address,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

/// 位置状态卡片组件
class DSLocationStatusCard extends StatelessWidget {
  final String status;
  final String description;
  final IconData icon;
  final Color statusColor;
  final VoidCallback? onRetry;

  const DSLocationStatusCard({
    super.key,
    required this.status,
    required this.description,
    required this.icon,
    required this.statusColor,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: SpacingTokens.space5),
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(SpacingTokens.space2),
            decoration: BoxDecoration(
              color: statusColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(SpacingTokens.space2),
            ),
            child: Icon(
              icon,
              color: statusColor,
              size: SpacingTokens.space5,
            ),
          ),
          
          const SizedBox(width: SpacingTokens.space3),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: SpacingTokens.space1),
                Text(
                  description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: statusColor.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          
          if (onRetry != null) ...[
            const SizedBox(width: SpacingTokens.space3),
            TextButton(
              onPressed: onRetry,
              child: Text(
                '重试',
                style: TextStyle(color: statusColor),
              ),
            ),
          ],
        ],
      ),
    );
  }
}