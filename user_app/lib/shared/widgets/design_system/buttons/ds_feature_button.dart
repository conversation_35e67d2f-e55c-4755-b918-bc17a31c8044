import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 功能按钮组件
/// 
/// 用于显示带图标、标题和副标题的功能选择按钮
class DSFeatureButton extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;
  final VoidCallback onTap;
  final Color? iconColor;
  final Color? backgroundColor;
  final Color? borderColor;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;
  final bool enabled;
  final Widget? trailing;
  final DSHapticFeedback? hapticFeedback;

  const DSFeatureButton({
    super.key,
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.onTap,
    this.iconColor,
    this.backgroundColor,
    this.borderColor,
    this.padding,
    this.borderRadius,
    this.enabled = true,
    this.trailing,
    this.hapticFeedback,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = iconColor ?? theme.colorScheme.primary;
    final effectiveBackgroundColor = backgroundColor ?? theme.colorScheme.surface;
    final effectiveBorderColor = borderColor ?? theme.colorScheme.primary.withValues(alpha: 0.2);
    final effectivePadding = padding ?? const EdgeInsets.all(SpacingTokens.space4);
    final effectiveBorderRadius = borderRadius ?? SpacingTokens.space4;
    
    return Container(
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        borderRadius: BorderRadius.circular(effectiveBorderRadius),
        border: Border.all(
          color: enabled ? effectiveBorderColor : theme.colorScheme.outline.withValues(alpha: 0.1),
          width: 1.5,
        ),
        boxShadow: enabled ? [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: SpacingTokens.space3,
            offset: const Offset(0, SpacingTokens.space1),
          ),
        ] : null,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: enabled ? () {
            // 触觉反馈
            switch (hapticFeedback) {
              case DSHapticFeedback.light:
                HapticFeedback.lightImpact();
                break;
              case DSHapticFeedback.medium:
                HapticFeedback.mediumImpact();
                break;
              case DSHapticFeedback.heavy:
                HapticFeedback.heavyImpact();
                break;
              case DSHapticFeedback.selection:
                HapticFeedback.selectionClick();
                break;
              default:
                HapticFeedback.lightImpact();
            }
            onTap();
          } : null,
          borderRadius: BorderRadius.circular(effectiveBorderRadius),
          child: Padding(
            padding: effectivePadding,
            child: Row(
              children: [
                // 图标容器
                _buildIconContainer(theme, effectiveIconColor),
                
                const SizedBox(width: SpacingTokens.space4),
                
                // 标题和副标题
                Expanded(
                  child: _buildTitleSection(theme),
                ),
                
                // 尾随组件
                if (trailing != null) ...[
                  const SizedBox(width: SpacingTokens.space3),
                  trailing!,
                ] else
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: SpacingTokens.space4,
                    color: enabled 
                        ? theme.colorScheme.onSurfaceVariant
                        : theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildIconContainer(ThemeData theme, Color iconColor) {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space3),
      decoration: BoxDecoration(
        color: enabled 
            ? iconColor.withValues(alpha: 0.1)
            : theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
      ),
      child: Icon(
        icon,
        color: enabled ? iconColor : theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
        size: SpacingTokens.space6,
      ),
    );
  }

  Widget _buildTitleSection(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: enabled 
                ? theme.colorScheme.onSurface
                : theme.colorScheme.onSurface.withValues(alpha: 0.5),
          ),
        ),
        const SizedBox(height: SpacingTokens.space1),
        Text(
          subtitle,
          style: theme.textTheme.bodySmall?.copyWith(
            color: enabled 
                ? theme.colorScheme.onSurfaceVariant
                : theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
        ),
      ],
    );
  }
}

/// 触觉反馈类型枚举
enum DSHapticFeedback {
  light,
  medium,
  heavy,
  selection,
}

/// 主要功能按钮变体
class DSPrimaryFeatureButton extends DSFeatureButton {
  const DSPrimaryFeatureButton({
    super.key,
    required super.icon,
    required super.title,
    required super.subtitle,
    required super.onTap,
    super.enabled = true,
    super.trailing,
    super.hapticFeedback = DSHapticFeedback.medium,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return DSFeatureButton(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      iconColor: theme.colorScheme.primary,
      backgroundColor: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
      borderColor: theme.colorScheme.primary.withValues(alpha: 0.4),
      enabled: enabled,
      trailing: trailing,
      hapticFeedback: hapticFeedback,
    );
  }
}

/// 次要功能按钮变体
class DSSecondaryFeatureButton extends DSFeatureButton {
  const DSSecondaryFeatureButton({
    super.key,
    required super.icon,
    required super.title,
    required super.subtitle,
    required super.onTap,
    super.enabled = true,
    super.trailing,
    super.hapticFeedback = DSHapticFeedback.light,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return DSFeatureButton(
      icon: icon,
      title: title,
      subtitle: subtitle,
      onTap: onTap,
      iconColor: theme.colorScheme.secondary,
      backgroundColor: theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
      borderColor: theme.colorScheme.outline.withValues(alpha: 0.2),
      enabled: enabled,
      trailing: trailing,
      hapticFeedback: hapticFeedback,
    );
  }
}

/// 紧凑型功能按钮
class DSCompactFeatureButton extends StatelessWidget {
  final IconData icon;
  final String title;
  final VoidCallback onTap;
  final Color? iconColor;
  final bool enabled;

  const DSCompactFeatureButton({
    super.key,
    required this.icon,
    required this.title,
    required this.onTap,
    this.iconColor,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = iconColor ?? theme.colorScheme.primary;
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: enabled ? () {
            HapticFeedback.lightImpact();
            onTap();
          } : null,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: SpacingTokens.space3,
              vertical: SpacingTokens.space2,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  icon,
                  size: SpacingTokens.space4,
                  color: enabled ? effectiveIconColor : theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
                ),
                const SizedBox(width: SpacingTokens.space2),
                Text(
                  title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: enabled 
                        ? theme.colorScheme.onSurface
                        : theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}