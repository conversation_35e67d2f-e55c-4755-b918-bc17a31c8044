import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System - ListTile组件
/// 严格遵循Material Design 3规范的列表项组件
/// 
/// 使用方式：
/// ```dart
/// DSListTile.standard(
///   title: '标题',
///   subtitle: '副标题',
///   leading: Icon(Icons.person),
///   onTap: () {},
/// )
/// ```
class DSListTile extends StatefulWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final DSListTileType type;
  final DSListTileSize size;
  final bool enabled;
  final bool selected;
  final EdgeInsetsGeometry? contentPadding;

 const DSListTile._({
    super.key,
    required this.type,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.size = DSListTileSize.medium,
    this.enabled = true,
    this.selected = false,
    this.contentPadding,
  });

  /// 标准列表项
  factory DSListTile.standard({
    Key? key,
    Widget? leading,
    Widget? title,
    Widget? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    DSListTileSize size = DSListTileSize.medium,
    final bool enabled = true,
    final bool selected = false,
    EdgeInsetsGeometry? contentPadding,
  }) {
    return DSListTile._(
      key: key,
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      onLongPress: onLongPress,
      type: DSListTileType.standard,
      size: size,
      enabled: enabled,
      selected: selected,
      contentPadding: contentPadding,
    );
  }

  /// 头像列表项
  factory DSListTile.avatar({
    Key? key,
    Widget? leading,
    Widget? title,
    Widget? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    DSListTileSize size = DSListTileSize.medium,
    final bool enabled = true,
    final bool selected = false,
    EdgeInsetsGeometry? contentPadding,
  }) {
    return DSListTile._(
      key: key,
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      onLongPress: onLongPress,
      type: DSListTileType.avatar,
      size: size,
      enabled: enabled,
      selected: selected,
      contentPadding: contentPadding,
    );
  }

  /// 图片列表项
  factory DSListTile.image({
    Key? key,
    Widget? leading,
    Widget? title,
    Widget? subtitle,
    Widget? trailing,
    VoidCallback? onTap,
    VoidCallback? onLongPress,
    DSListTileSize size = DSListTileSize.large,
    final bool enabled = true,
    final bool selected = false,
    EdgeInsetsGeometry? contentPadding,
  }) {
    return DSListTile._(
      key: key,
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: onTap,
      onLongPress: onLongPress,
      type: DSListTileType.image,
      size: size,
      enabled: enabled,
      selected: selected,
      contentPadding: contentPadding,
    );
  }

  @override
  State<DSListTile> createState() => _DSListTileState();
}

class _DSListTileState extends State<DSListTile> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _backgroundAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: MotionTokens.listItemEnterDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: MotionTokens.listItemCurve,
    ));

    _backgroundAnimation = ColorTween(
      begin: ColorTokens.transparent,
      end: Theme.of(context).colorScheme.primary.withValues(alpha: 0.08),
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: MotionTokens.listItemCurve,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    if (widget.enabled && (widget.onTap != null || widget.onLongPress != null)) {
      _animationController.forward();
    }
  }

  void _onTapUp(TapUpDetails details) {
    if (widget.enabled) {
      _animationController.reverse();
    }
  }

  void _onTapCancel() {
    if (widget.enabled) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final config = _getListTileConfig(colorScheme);
    final isInteractive = widget.enabled && (widget.onTap != null || widget.onLongPress != null);

    Widget listTile = Container(
      decoration: BoxDecoration(
        color: widget.selected ? config.selectedBackgroundColor : config.backgroundColor,
        borderRadius: ShapeTokens.borderRadiusSm,
      ),
      child: Material(
        color: ColorTokens.transparent,
        child: AnimatedBuilder(
          animation: _backgroundAnimation,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                color: _backgroundAnimation.value,
                borderRadius: ShapeTokens.borderRadiusSm,
              ),
              child: ListTile(
                leading: widget.leading != null 
                  ? _buildLeading(config) 
                  : null,
                title: widget.title != null 
                  ? _buildTitle(config) 
                  : null,
                subtitle: widget.subtitle != null 
                  ? _buildSubtitle(config) 
                  : null,
                trailing: widget.trailing != null 
                  ? _buildTrailing(config) 
                  : null,
                onTap: widget.enabled ? widget.onTap : null,
                onLongPress: widget.enabled ? widget.onLongPress : null,
                enabled: widget.enabled,
                selected: widget.selected,
                contentPadding: widget.contentPadding ?? _getContentPadding(),
                minVerticalPadding: _getMinVerticalPadding(),
                horizontalTitleGap: _getHorizontalTitleGap(),
                minLeadingWidth: _getMinLeadingWidth(),
                shape: ShapeTokens.buttonShapeMedium,
                tileColor: ColorTokens.transparent,
                selectedTileColor: ColorTokens.transparent,
              ),
            );
          },
        ),
      ),
    );

    if (isInteractive) {
      listTile = GestureDetector(
        onTapDown: _onTapDown,
        onTapUp: _onTapUp,
        onTapCancel: _onTapCancel,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: listTile,
            );
          },
        ),
      );
    }

    return listTile;
  }

  Widget _buildLeading(_ListTileConfig config) {
    return SizedBox(
      width: _getLeadingSize(),
      height: _getLeadingSize(),
      child: IconTheme(
        data: IconThemeData(
          color: config.leadingColor,
          size: _getLeadingIconSize(),
        ),
        child: widget.leading!,
      ),
    );
  }

  Widget _buildTitle(_ListTileConfig config) {
    return DefaultTextStyle(
      style: config.titleStyle,
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
      child: widget.title!,
    );
  }

  Widget _buildSubtitle(_ListTileConfig config) {
    return DefaultTextStyle(
      style: config.subtitleStyle,
      maxLines: _getSubtitleMaxLines(),
      overflow: TextOverflow.ellipsis,
      child: widget.subtitle!,
    );
  }

  Widget _buildTrailing(_ListTileConfig config) {
    return IconTheme(
      data: IconThemeData(
        color: config.trailingColor,
        size: _getTrailingIconSize(),
      ),
      child: widget.trailing!,
    );
  }

  _ListTileConfig _getListTileConfig(ColorScheme colorScheme) {
    final titleStyle = _getTitleStyle();
    final subtitleStyle = _getSubtitleStyle();

    return _ListTileConfig(
      backgroundColor: ColorTokens.transparent,
      selectedBackgroundColor: colorScheme.secondaryContainer.withValues(alpha: 0.12),
      titleStyle: titleStyle.copyWith(
        color: widget.enabled 
          ? (widget.selected ? colorScheme.onSecondaryContainer : colorScheme.onSurface)
          : colorScheme.onSurface.withValues(alpha: 0.38),
      ),
      subtitleStyle: subtitleStyle.copyWith(
        color: widget.enabled
          ? (widget.selected ? colorScheme.onSecondaryContainer : colorScheme.onSurfaceVariant)
          : colorScheme.onSurface.withValues(alpha: 0.38),
      ),
      leadingColor: widget.enabled
        ? (widget.selected ? colorScheme.onSecondaryContainer : colorScheme.onSurfaceVariant)
        : colorScheme.onSurface.withValues(alpha: 0.38),
      trailingColor: widget.enabled
        ? (widget.selected ? colorScheme.onSecondaryContainer : colorScheme.onSurfaceVariant)
        : colorScheme.onSurface.withValues(alpha: 0.38),
    );
  }

  TextStyle _getTitleStyle() {
    switch (widget.size) {
      case DSListTileSize.small:
        return TypographyTokens.bodyMedium;
      case DSListTileSize.medium:
        return TypographyTokens.bodyLarge;
      case DSListTileSize.large:
        return TypographyTokens.headlineSmall;
    }
  }

  TextStyle _getSubtitleStyle() {
    switch (widget.size) {
      case DSListTileSize.small:
        return TypographyTokens.bodySmall;
      case DSListTileSize.medium:
        return TypographyTokens.bodyMedium;
      case DSListTileSize.large:
        return TypographyTokens.bodyLarge;
    }
  }

  EdgeInsetsGeometry _getContentPadding() {
    switch (widget.size) {
      case DSListTileSize.small:
        return const EdgeInsets.symmetric(horizontal: SpacingTokens.space3, vertical: SpacingTokens.space2);
      case DSListTileSize.medium:
        return SpacingTokens.listItemPadding;
      case DSListTileSize.large:
        return const EdgeInsets.symmetric(horizontal: SpacingTokens.space6, vertical: SpacingTokens.space4);
    }
  }

  double _getMinVerticalPadding() {
    switch (widget.size) {
      case DSListTileSize.small:
        return SpacingTokens.xs;
      case DSListTileSize.medium:
        return SpacingTokens.sm;
      case DSListTileSize.large:
        return SpacingTokens.md;
    }
  }

  double _getHorizontalTitleGap() {
    switch (widget.type) {
      case DSListTileType.standard:
        return SpacingTokens.md;
      case DSListTileType.avatar:
        return SpacingTokens.md;
      case DSListTileType.image:
        return SpacingTokens.lg;
    }
  }

  double _getMinLeadingWidth() {
    switch (widget.type) {
      case DSListTileType.standard:
        return SpacingTokens.iconSizeMedium;
      case DSListTileType.avatar:
        return 40.0;
      case DSListTileType.image:
        return 56.0;
    }
  }

  double _getLeadingSize() {
    switch (widget.type) {
      case DSListTileType.standard:
        return SpacingTokens.iconSizeMedium;
      case DSListTileType.avatar:
        return 40.0;
      case DSListTileType.image:
        return 56.0;
    }
  }

  double _getLeadingIconSize() {
    switch (widget.type) {
      case DSListTileType.standard:
        return SpacingTokens.iconSizeSmall;
      case DSListTileType.avatar:
        return SpacingTokens.iconSizeMedium;
      case DSListTileType.image:
        return SpacingTokens.iconSizeLarge;
    }
  }

  double _getTrailingIconSize() {
    return SpacingTokens.iconSizeSmall;
  }

  int _getSubtitleMaxLines() {
    switch (widget.size) {
      case DSListTileSize.small:
        return 1;
      case DSListTileSize.medium:
        return 2;
      case DSListTileSize.large:
        return 3;
    }
  }
}

/// 列表项配置类
class _ListTileConfig {
  final Color backgroundColor;
  final Color selectedBackgroundColor;
  final TextStyle titleStyle;
  final TextStyle subtitleStyle;
  final Color leadingColor;
  final Color trailingColor;

 const _ListTileConfig({
    required this.backgroundColor,
    required this.selectedBackgroundColor,
    required this.titleStyle,
    required this.subtitleStyle,
    required this.leadingColor,
    required this.trailingColor,
  });
}

/// 列表项类型枚举
enum DSListTileType {
  standard, // 标准列表项
  avatar,   // 头像列表项
  image,    // 图片列表项
}

/// 列表项尺寸枚举
enum DSListTileSize {
  small,   // 小尺寸
  medium,  // 中等尺寸
  large,   // 大尺寸
}