import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Design System 虚线边框绘制器
/// 
/// 用于绘制虚线边框，支持自定义颜色、线宽和虚线间隔
class DSDashedBorderPainter extends CustomPainter {
  final Color color;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;
  final BorderRadius? borderRadius;

  const DSDashedBorderPainter({
    required this.color,
    this.strokeWidth = 2.0,
    this.dashWidth = 8.0,
    this.dashSpace = 4.0,
    this.borderRadius,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = strokeWidth
      ..style = PaintingStyle.stroke;

    if (borderRadius != null) {
      _paintRoundedRectBorder(canvas, size, paint);
    } else {
      _paintRectBorder(canvas, size, paint);
    }
  }

  void _paintRoundedRectBorder(Canvas canvas, Size size, Paint paint) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final rrect = borderRadius!.toRRect(rect);
    
    final path = Path()..addRRect(rrect);
    
    _paintDashedPath(canvas, path, paint);
  }

  void _paintRectBorder(Canvas canvas, Size size, Paint paint) {
    final path = Path()
      ..moveTo(0, 0)
      ..lineTo(size.width, 0)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height)
      ..close();
    
    _paintDashedPath(canvas, path, paint);
  }

  void _paintDashedPath(Canvas canvas, Path path, Paint paint) {
    final pathMetrics = path.computeMetrics();
    
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      bool draw = true;
      
      while (distance < pathMetric.length) {
        final nextDistance = distance + (draw ? dashWidth : dashSpace);
        
        if (draw && distance < pathMetric.length) {
          final extractPath = pathMetric.extractPath(
            distance,
            math.min(nextDistance, pathMetric.length),
          );
          canvas.drawPath(extractPath, paint);
        }
        
        distance = nextDistance;
        draw = !draw;
      }
    }
  }

  @override
  bool shouldRepaint(DSDashedBorderPainter oldDelegate) {
    return color != oldDelegate.color ||
        strokeWidth != oldDelegate.strokeWidth ||
        dashWidth != oldDelegate.dashWidth ||
        dashSpace != oldDelegate.dashSpace ||
        borderRadius != oldDelegate.borderRadius;
  }
}

/// 虚线容器组件
/// 
/// 封装了虚线边框绘制器的便捷组件
class DSDashedContainer extends StatelessWidget {
  final Widget child;
  final Color borderColor;
  final double strokeWidth;
  final double dashWidth;
  final double dashSpace;
  final BorderRadius? borderRadius;
  final Color? backgroundColor;
  final EdgeInsetsGeometry? padding;

  const DSDashedContainer({
    super.key,
    required this.child,
    required this.borderColor,
    this.strokeWidth = 2.0,
    this.dashWidth = 8.0,
    this.dashSpace = 4.0,
    this.borderRadius,
    this.backgroundColor,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: borderRadius,
      ),
      child: CustomPaint(
        painter: DSDashedBorderPainter(
          color: borderColor,
          strokeWidth: strokeWidth,
          dashWidth: dashWidth,
          dashSpace: dashSpace,
          borderRadius: borderRadius,
        ),
        child: child,
      ),
    );
  }
}