import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 区域标题组件
/// 
/// 带有装饰条的区域标题，用于分隔和标识不同的表单区域
class DSSectionTitle extends StatelessWidget {
  final String title;
  final Color? decoratorColor;
  final double decoratorWidth;
  final double decoratorHeight;
  final TextStyle? titleStyle;
  final EdgeInsetsGeometry? margin;
  final Widget? trailing;

  const DSSectionTitle({
    super.key,
    required this.title,
    this.decoratorColor,
    this.decoratorWidth = 4,
    this.decoratorHeight = 20,
    this.titleStyle,
    this.margin,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveDecoratorColor = decoratorColor ?? theme.colorScheme.primary;
    
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: SpacingTokens.space2),
      child: Row(
        children: [
          // 装饰条
          Container(
            width: decoratorWidth,
            height: decoratorHeight,
            decoration: BoxDecoration(
              color: effectiveDecoratorColor,
              borderRadius: BorderRadius.circular(decoratorWidth / 2),
            ),
          ),
          
          const SizedBox(width: SpacingTokens.space3),
          
          // 标题文本
          Expanded(
            child: Text(
              title,
              style: titleStyle ?? 
                  theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
            ),
          ),
          
          // 右侧组件
          if (trailing != null) ...[
            const SizedBox(width: SpacingTokens.space3),
            trailing!,
          ],
        ],
      ),
    );
  }
}

/// 简化版区域标题（无装饰条）
class DSSimpleTitle extends StatelessWidget {
  final String title;
  final TextStyle? titleStyle;
  final EdgeInsetsGeometry? margin;
  final Widget? trailing;

  const DSSimpleTitle({
    super.key,
    required this.title,
    this.titleStyle,
    this.margin,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: margin ?? const EdgeInsets.symmetric(vertical: SpacingTokens.space2),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: titleStyle ?? 
                  theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
            ),
          ),
          
          if (trailing != null) ...[
            const SizedBox(width: SpacingTokens.space3),
            trailing!,
          ],
        ],
      ),
    );
  }
}

/// 带有计数的区域标题
class DSSectionTitleWithCounter extends DSSectionTitle {
  const DSSectionTitleWithCounter({
    super.key,
    required super.title,
    required int count,
    int? maxCount,
    super.decoratorColor,
    super.decoratorWidth = 4,
    super.decoratorHeight = 20,
    super.titleStyle,
    super.margin,
  }) : super(
          trailing: _CounterChip(
            count: count,
            maxCount: maxCount,
          ),
        );
}

/// 计数器芯片组件
class _CounterChip extends StatelessWidget {
  final int count;
  final int? maxCount;

  const _CounterChip({
    required this.count,
    this.maxCount,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayText = maxCount != null ? '$count/$maxCount' : '$count';
    
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space2,
        vertical: SpacingTokens.space1,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
      ),
      child: Text(
        displayText,
        style: theme.textTheme.labelSmall?.copyWith(
          color: theme.colorScheme.onPrimaryContainer,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}