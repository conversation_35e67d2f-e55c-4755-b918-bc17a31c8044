import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System Chip Component - Material 3 标准芯片组件
class DSChip extends StatelessWidget {
  final String label;
  final IconData? icon;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;
  final bool isSelected;
  final bool isEnabled;
  final DSChipType type;
  final DSChipSize size;
  final Color? backgroundColor;
  final Color? textColor;
  final Color? borderColor;

  const DSChip({
    super.key,
    required this.label,
    this.icon,
    this.onTap,
    this.onDelete,
    this.isSelected = false,
    this.isEnabled = true,
    this.type = DSChipType.filter,
    this.size = DSChipSize.medium,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  });

  /// 创建输入芯片（带删除功能）
  const DSChip.input({
    super.key,
    required this.label,
    this.icon,
    this.onTap,
    required this.onDelete,
    this.isEnabled = true,
    this.size = DSChipSize.medium,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  }) : type = DSChipType.input,
       isSelected = false;

  /// 创建选择芯片
  const DSChip.choice({
    super.key,
    required this.label,
    this.icon,
    required this.onTap,
    this.isSelected = false,
    this.isEnabled = true,
    this.size = DSChipSize.medium,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  }) : type = DSChipType.choice,
       onDelete = null;

  /// 创建过滤芯片
  const DSChip.filter({
    super.key,
    required this.label,
    this.icon,
    required this.onTap,
    this.isSelected = false,
    this.isEnabled = true,
    this.size = DSChipSize.medium,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  }) : type = DSChipType.filter,
       onDelete = null;

  /// 创建动作芯片
  const DSChip.action({
    super.key,
    required this.label,
    this.icon,
    required this.onTap,
    this.isEnabled = true,
    this.size = DSChipSize.medium,
    this.backgroundColor,
    this.textColor,
    this.borderColor,
  }) : type = DSChipType.action,
       isSelected = false,
       onDelete = null;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final chipColors = _getChipColors(colorScheme);
    final chipSizes = _getChipSizes();

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isEnabled ? (onTap ?? onDelete) : null,
        borderRadius: BorderRadius.circular(chipSizes.borderRadius),
        child: AnimatedContainer(
          duration: MotionTokens.durationMedium,
          curve: MotionTokens.curveStandard,
          height: chipSizes.height,
          decoration: BoxDecoration(
            color: chipColors.backgroundColor,
            borderRadius: BorderRadius.circular(chipSizes.borderRadius),
            border: Border.all(
              color: chipColors.borderColor,
              width: 1,
            ),
          ),
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: chipSizes.horizontalPadding),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (icon != null) ...[
                  Icon(
                    icon,
                    size: chipSizes.iconSize,
                    color: chipColors.iconColor,
                  ),
                  SizedBox(width: SpacingTokens.space1),
                ],
                if (isSelected && type == DSChipType.filter) ...[
                  Icon(
                    Icons.check,
                    size: chipSizes.iconSize,
                    color: chipColors.iconColor,
                  ),
                  SizedBox(width: SpacingTokens.space1),
                ],
                Flexible(
                  child: Text(
                    label,
                    style: chipSizes.textStyle.copyWith(
                      color: chipColors.textColor,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                if (onDelete != null) ...[
                  SizedBox(width: SpacingTokens.space1),
                  GestureDetector(
                    onTap: isEnabled ? onDelete : null,
                    child: Icon(
                      Icons.close,
                      size: chipSizes.deleteIconSize,
                      color: chipColors.deleteIconColor,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  _ChipColors _getChipColors(ColorScheme colorScheme) {
    // 如果提供了自定义颜色，使用自定义颜色
    if (backgroundColor != null || textColor != null) {
      return _ChipColors(
        backgroundColor: backgroundColor ?? colorScheme.surface,
        textColor: textColor ?? colorScheme.onSurface,
        iconColor: textColor ?? colorScheme.onSurface,
        borderColor: borderColor ?? colorScheme.outline,
        deleteIconColor: textColor ?? colorScheme.onSurface,
      );
    }

    // 根据状态和类型确定颜色
    switch (type) {
      case DSChipType.input:
        return _ChipColors(
          backgroundColor: colorScheme.surfaceVariant,
          textColor: colorScheme.onSurfaceVariant,
          iconColor: colorScheme.onSurfaceVariant,
          borderColor: colorScheme.outline,
          deleteIconColor: colorScheme.onSurfaceVariant,
        );
      
      case DSChipType.choice:
      case DSChipType.filter:
        if (isSelected) {
          return _ChipColors(
            backgroundColor: colorScheme.secondaryContainer,
            textColor: colorScheme.onSecondaryContainer,
            iconColor: colorScheme.onSecondaryContainer,
            borderColor: colorScheme.secondaryContainer,
            deleteIconColor: colorScheme.onSecondaryContainer,
          );
        } else {
          return _ChipColors(
            backgroundColor: colorScheme.surface,
            textColor: colorScheme.onSurface,
            iconColor: colorScheme.onSurface,
            borderColor: colorScheme.outline,
            deleteIconColor: colorScheme.onSurface,
          );
        }
      
      case DSChipType.action:
        return _ChipColors(
          backgroundColor: colorScheme.surface,
          textColor: colorScheme.primary,
          iconColor: colorScheme.primary,
          borderColor: colorScheme.outline,
          deleteIconColor: colorScheme.onSurface,
        );
    }
  }

  _ChipSizes _getChipSizes() {
    switch (size) {
      case DSChipSize.small:
        return const _ChipSizes(
          height: 24,
          horizontalPadding: SpacingTokens.space2,
          borderRadius: SpacingTokens.space2,
          iconSize: 14,
          deleteIconSize: 12,
          textStyle: TextStyle(
            fontSize: TypographyTokens.sizeLabelSmall,
            fontWeight: TypographyTokens.weightMedium,
          ),
        );
      
      case DSChipSize.medium:
        return const _ChipSizes(
          height: 32,
          horizontalPadding: SpacingTokens.space3,
          borderRadius: SpacingTokens.space2,
          iconSize: 16,
          deleteIconSize: 14,
          textStyle: TextStyle(
            fontSize: TypographyTokens.sizeLabelMedium,
            fontWeight: TypographyTokens.weightMedium,
          ),
        );
      
      case DSChipSize.large:
        return const _ChipSizes(
          height: 40,
          horizontalPadding: SpacingTokens.space4,
          borderRadius: SpacingTokens.space3,
          iconSize: 18,
          deleteIconSize: 16,
          textStyle: TextStyle(
            fontSize: TypographyTokens.sizeLabelLarge,
            fontWeight: TypographyTokens.weightMedium,
          ),
        );
    }
  }
}

/// 芯片颜色配置
class _ChipColors {
  final Color backgroundColor;
  final Color textColor;
  final Color iconColor;
  final Color borderColor;
  final Color deleteIconColor;

  const _ChipColors({
    required this.backgroundColor,
    required this.textColor,
    required this.iconColor,
    required this.borderColor,
    required this.deleteIconColor,
  });
}

/// 芯片尺寸配置
class _ChipSizes {
  final double height;
  final double horizontalPadding;
  final double borderRadius;
  final double iconSize;
  final double deleteIconSize;
  final TextStyle textStyle;

  const _ChipSizes({
    required this.height,
    required this.horizontalPadding,
    required this.borderRadius,
    required this.iconSize,
    required this.deleteIconSize,
    required this.textStyle,
  });
}

/// 芯片类型枚举
enum DSChipType {
  /// 输入芯片 - 用于显示用户输入的信息，通常可删除
  input,
  
  /// 选择芯片 - 用于单选场景
  choice,
  
  /// 过滤芯片 - 用于多选筛选场景
  filter,
  
  /// 动作芯片 - 用于触发动作
  action,
}

/// 芯片尺寸枚举
enum DSChipSize {
  /// 小尺寸芯片
  small,
  
  /// 中等尺寸芯片（默认）
  medium,
  
  /// 大尺寸芯片
  large,
}