import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 章节标题组件
/// 
/// 用于展示带有图标和描述的章节标题，遵循 Material 3 设计规范
class DSSectionHeader extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color? iconColor;
  final Widget? trailing;
  final EdgeInsetsGeometry? padding;

  const DSSectionHeader({
    super.key,
    required this.title,
    required this.subtitle,
    required this.icon,
    this.iconColor,
    this.trailing,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: padding ?? const EdgeInsets.all(SpacingTokens.space5),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
            theme.colorScheme.secondaryContainer.withValues(alpha: 0.2),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(SpacingTokens.space3),
            decoration: BoxDecoration(
              color: iconColor ?? theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(SpacingTokens.space3),
              boxShadow: [
                BoxShadow(
                  color: (iconColor ?? theme.colorScheme.primary)
                      .withValues(alpha: 0.3),
                  blurRadius: SpacingTokens.space2,
                  offset: const Offset(0, SpacingTokens.space1),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: theme.colorScheme.onPrimary,
              size: SpacingTokens.space6,
            ),
          ),
          
          const SizedBox(width: SpacingTokens.space4),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: SpacingTokens.space1),
                Text(
                  subtitle,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          
          if (trailing != null) ...[
            const SizedBox(width: SpacingTokens.space4),
            trailing!,
          ],
        ],
      ),
    );
  }
}