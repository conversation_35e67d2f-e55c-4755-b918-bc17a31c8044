import 'package:flutter/material.dart';
import 'package:user_app/core/core.dart';

/// Design System 单选按钮组件
class DSRadioButton<T> extends StatelessWidget {
  const DSRadioButton({
    super.key,
    required this.value,
    required this.groupValue,
    required this.onChanged,
    required this.title,
    this.subtitle,
    this.dense = false,
  });

  final T value;
  final T? groupValue;
  final ValueChanged<T?>? onChanged;
  final String title;
  final String? subtitle;
  final bool dense;

  @override
  Widget build(BuildContext context) {
    final isSelected = value == groupValue;
    
    return InkWell(
      onTap: onChanged != null ? () => onChanged!(value) : null,
      borderRadius: ShapeTokens.cardShape,
      child: Container(
        padding: dense 
            ? EdgeInsets.all(SpacingTokens.space2)
            : EdgeInsets.all(SpacingTokens.space3),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primaryContainer
              : ColorTokens.transparent,
          borderRadius: ShapeTokens.cardShape,
          border: Border.all(
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.outline.withOpacity(0.5),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: SpacingTokens.space5,
              height: SpacingTokens.space5,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: isSelected 
                    ? Theme.of(context).colorScheme.primary
                    : ColorTokens.transparent,
                border: Border.all(
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.outline,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Icon(
                      Icons.check,
                      size: SpacingTokens.space3,
                      color: Theme.of(context).colorScheme.onPrimary,
                    )
                  : null,
            ),
            SizedBox(width: SpacingTokens.space2),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimaryContainer
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  if (subtitle != null) ...[
                    SizedBox(height: SpacingTokens.space1),
                    Text(
                      subtitle!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isSelected
                            ? Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8)
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}