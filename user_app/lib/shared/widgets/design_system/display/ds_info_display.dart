import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 信息显示组件
/// 
/// 用于显示键值对信息，如坐标、状态等
class DSInfoDisplay extends StatelessWidget {
  final String label;
  final String value;
  final IconData? icon;
  final Color? labelColor;
  final Color? valueColor;
  final TextStyle? labelStyle;
  final TextStyle? valueStyle;
  final EdgeInsetsGeometry? padding;
  final CrossAxisAlignment alignment;
  final bool showDivider;

  const DSInfoDisplay({
    super.key,
    required this.label,
    required this.value,
    this.icon,
    this.labelColor,
    this.valueColor,
    this.labelStyle,
    this.valueStyle,
    this.padding,
    this.alignment = CrossAxisAlignment.start,
    this.showDivider = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(
        vertical: SpacingTokens.space2,
      ),
      decoration: showDivider
          ? BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            )
          : null,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // 图标
          if (icon != null) ...[
            Icon(
              icon,
              size: SpacingTokens.space4,
              color: labelColor ?? theme.colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: SpacingTokens.space2),
          ],
          
          // 标签和值
          Expanded(
            child: Column(
              crossAxisAlignment: alignment,
              children: [
                Text(
                  label,
                  style: labelStyle ?? 
                      theme.textTheme.bodySmall?.copyWith(
                        color: labelColor ?? theme.colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                ),
                const SizedBox(height: SpacingTokens.space1),
                Text(
                  value,
                  style: valueStyle ?? 
                      theme.textTheme.bodyMedium?.copyWith(
                        color: valueColor ?? theme.colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 坐标信息显示组件
class DSCoordinateDisplay extends StatelessWidget {
  final double latitude;
  final double longitude;
  final int precision;
  final EdgeInsetsGeometry? padding;

  const DSCoordinateDisplay({
    super.key,
    required this.latitude,
    required this.longitude,
    this.precision = 6,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.all(SpacingTokens.space3),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space2),
      ),
      child: Row(
        children: [
          Expanded(
            child: DSInfoDisplay(
              label: '纬度',
              value: latitude.toStringAsFixed(precision),
              icon: Icons.location_on_rounded,
              alignment: CrossAxisAlignment.center,
            ),
          ),
          
          const SizedBox(width: SpacingTokens.space3),
          
          Container(
            width: 1,
            height: SpacingTokens.space8,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
          
          const SizedBox(width: SpacingTokens.space3),
          
          Expanded(
            child: DSInfoDisplay(
              label: '经度',
              value: longitude.toStringAsFixed(precision),
              icon: Icons.navigation_rounded,
              alignment: CrossAxisAlignment.center,
            ),
          ),
        ],
      ),
    );
  }
}

/// 状态显示组件
class DSStatusDisplay extends StatelessWidget {
  final String status;
  final Color statusColor;
  final IconData? statusIcon;
  final String? description;

  const DSStatusDisplay({
    super.key,
    required this.status,
    required this.statusColor,
    this.statusIcon,
    this.description,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space3),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(SpacingTokens.space2),
        border: Border.all(
          color: statusColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          if (statusIcon != null) ...[
            Icon(
              statusIcon,
              color: statusColor,
              size: SpacingTokens.space5,
            ),
            const SizedBox(width: SpacingTokens.space3),
          ],
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (description != null) ...[
                  const SizedBox(height: SpacingTokens.space1),
                  Text(
                    description!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: statusColor.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 带有操作的信息显示组件
class DSInfoDisplayWithAction extends DSInfoDisplay {
  const DSInfoDisplayWithAction({
    super.key,
    required super.label,
    required super.value,
    required Widget action,
    super.icon,
    super.labelColor,
    super.valueColor,
    super.labelStyle,
    super.valueStyle,
    super.padding,
    super.showDivider = true,
  }) : super(
          alignment: CrossAxisAlignment.start,
        );

  Widget get action => const SizedBox.shrink();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(
        vertical: SpacingTokens.space2,
      ),
      decoration: showDivider
          ? BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            )
          : null,
      child: Row(
        children: [
          // 图标
          if (icon != null) ...[
            Icon(
              icon,
              size: SpacingTokens.space4,
              color: labelColor ?? Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(width: SpacingTokens.space2),
          ],
          
          // 标签和值
          Expanded(
            child: Column(
              crossAxisAlignment: alignment,
              children: [
                Text(
                  label,
                  style: labelStyle ?? 
                      Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: labelColor ?? Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                ),
                const SizedBox(height: SpacingTokens.space1),
                Text(
                  value,
                  style: valueStyle ?? 
                      Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: valueColor ?? Theme.of(context).colorScheme.onSurface,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ],
            ),
          ),
          
          // 操作按钮
          const SizedBox(width: SpacingTokens.space3),
          action,
        ],
      ),
    );
  }
}