import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 带动画的模态弹窗组件
/// 
/// 提供统一的入场动画和可拖拽滚动的模态弹窗
class DSAnimatedModalSheet extends StatefulWidget {
  final Widget child;
  final double initialChildSize;
  final double minChildSize;
  final double maxChildSize;
  final Duration animationDuration;
  final Curve fadeInCurve;
  final Curve slideInCurve;
  final bool enableDragHandle;
  final Color? backgroundColor;
  final BorderRadius? borderRadius;

  const DSAnimatedModalSheet({
    super.key,
    required this.child,
    this.initialChildSize = 0.6,
    this.minChildSize = 0.4,
    this.maxChildSize = 0.8,
    this.animationDuration = const Duration(milliseconds: 400),
    this.fadeInCurve = Curves.easeOut,
    this.slideInCurve = Curves.easeOutCubic,
    this.enableDragHandle = true,
    this.backgroundColor,
    this.borderRadius,
  });

  @override
  State<DSAnimatedModalSheet> createState() => _DSAnimatedModalSheetState();
}

class _DSAnimatedModalSheetState extends State<DSAnimatedModalSheet>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: widget.fadeInCurve,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: widget.slideInCurve,
    ));

    // 开始入场动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// 关闭模态弹窗（带退场动画）
  Future<void> closeModal([dynamic result]) async {
    await _animationController.reverse();
    if (mounted) {
      Navigator.pop(context, result);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveBackgroundColor = widget.backgroundColor ?? theme.colorScheme.surface;
    final effectiveBorderRadius = widget.borderRadius ?? 
        const BorderRadius.vertical(top: Radius.circular(SpacingTokens.space6));
    
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: DraggableScrollableSheet(
              initialChildSize: widget.initialChildSize,
              minChildSize: widget.minChildSize,
              maxChildSize: widget.maxChildSize,
              builder: (context, scrollController) {
                return Container(
                  decoration: BoxDecoration(
                    color: effectiveBackgroundColor,
                    borderRadius: effectiveBorderRadius,
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.shadow.withValues(alpha: 0.15),
                        blurRadius: SpacingTokens.space5,
                        offset: const Offset(0, -SpacingTokens.space1),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      // 拖拽手柄
                      if (widget.enableDragHandle) _buildDragHandle(theme),
                      
                      // 内容区域
                      Expanded(
                        child: widget.child,
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildDragHandle(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.only(top: SpacingTokens.space3),
      width: SpacingTokens.space10,
      height: SpacingTokens.space1,
      decoration: BoxDecoration(
        color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(SpacingTokens.space1),
      ),
    );
  }
}

/// 显示带动画的模态弹窗的便捷方法
Future<T?> showDSAnimatedModalSheet<T>({
  required BuildContext context,
  required Widget child,
  double initialChildSize = 0.6,
  double minChildSize = 0.4,
  double maxChildSize = 0.8,
  Duration animationDuration = const Duration(milliseconds: 400),
  Curve fadeInCurve = Curves.easeOut,
  Curve slideInCurve = Curves.easeOutCubic,
  bool enableDragHandle = true,
  Color? backgroundColor,
  BorderRadius? borderRadius,
  bool isDismissible = true,
  bool enableDrag = true,
}) {
  return showModalBottomSheet<T>(
    context: context,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    backgroundColor: Colors.transparent,
    elevation: 0,
    isScrollControlled: true,
    builder: (context) => DSAnimatedModalSheet(
      initialChildSize: initialChildSize,
      minChildSize: minChildSize,
      maxChildSize: maxChildSize,
      animationDuration: animationDuration,
      fadeInCurve: fadeInCurve,
      slideInCurve: slideInCurve,
      enableDragHandle: enableDragHandle,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
      child: child,
    ),
  );
}