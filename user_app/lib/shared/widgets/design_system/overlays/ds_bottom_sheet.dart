import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// Design System 底部弹窗组件
/// 
/// 符合 Material Design 3 规范的统一底部弹窗
/// 提供一致的视觉样式和交互体验
class DSBottomSheet extends StatelessWidget {
  final String? title;
  final IconData? titleIcon;
  final Widget? titleWidget;
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final bool showHandle;
  final bool isScrollable;
  final double? height;
  final VoidCallback? onClose;

  const DSBottomSheet({
    super.key,
    this.title,
    this.titleIcon,
    this.titleWidget,
    required this.children,
    this.padding,
    this.showHandle = true,
    this.isScrollable = true,
    this.height,
    this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(SpacingTokens.space5),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: SpacingTokens.space5,
            offset: const Offset(0, -SpacingTokens.space1),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 拖拽手柄
            if (showHandle) _buildHandle(context),
            
            // 标题栏
            if (title != null || titleWidget != null)
              _buildTitleSection(context),
            
            // 分隔线
            if (title != null || titleWidget != null)
              const Divider(height: 1),
            
            // 内容区域
            if (isScrollable)
              Flexible(
                child: SingleChildScrollView(
                  padding: padding ?? const EdgeInsets.all(SpacingTokens.space4),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: children,
                  ),
                ),
              )
            else
              Padding(
                padding: padding ?? const EdgeInsets.all(SpacingTokens.space4),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: children,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildHandle(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: SpacingTokens.space3),
      width: SpacingTokens.space10,
      height: SpacingTokens.space1,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
        borderRadius: BorderRadius.circular(SpacingTokens.space1),
      ),
    );
  }

  Widget _buildTitleSection(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      child: Row(
        children: [
          // 标题图标
          if (titleIcon != null) ...[
            Icon(
              titleIcon,
              color: theme.colorScheme.primary,
              size: SpacingTokens.space6,
            ),
            const SizedBox(width: SpacingTokens.space3),
          ],
          
          // 标题内容
          Expanded(
            child: titleWidget ?? 
              Text(
                title!,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
          ),
          
          // 关闭按钮
          if (onClose != null)
            IconButton(
              onPressed: onClose,
              icon: Icon(
                Icons.close_rounded,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
    );
  }
}

/// 显示 Design System 底部弹窗的便捷方法
Future<T?> showDSBottomSheet<T>({
  required BuildContext context,
  required Widget Function(BuildContext) builder,
  bool isDismissible = true,
  bool enableDrag = true,
  Color? backgroundColor,
  double? elevation,
  ShapeBorder? shape,
  Clip? clipBehavior,
  BoxConstraints? constraints,
  AnimationController? transitionAnimationController,
  Duration? duration,
}) {
  return showModalBottomSheet<T>(
    context: context,
    builder: builder,
    isDismissible: isDismissible,
    enableDrag: enableDrag,
    backgroundColor: backgroundColor ?? Colors.transparent,
    elevation: elevation ?? 0,
    shape: shape,
    clipBehavior: clipBehavior,
    constraints: constraints,
    transitionAnimationController: transitionAnimationController,
    duration: duration,
    isScrollControlled: true,
  );
}

/// 可拖拽调整高度的底部弹窗
class DSDraggableBottomSheet extends StatefulWidget {
  final String? title;
  final IconData? titleIcon;
  final Widget? titleWidget;
  final Widget child;
  final double initialChildSize;
  final double minChildSize;
  final double maxChildSize;
  final bool snap;
  final List<double>? snapSizes;
  final VoidCallback? onClose;

  const DSDraggableBottomSheet({
    super.key,
    this.title,
    this.titleIcon,
    this.titleWidget,
    required this.child,
    this.initialChildSize = 0.5,
    this.minChildSize = 0.25,
    this.maxChildSize = 1.0,
    this.snap = false,
    this.snapSizes,
    this.onClose,
  });

  @override
  State<DSDraggableBottomSheet> createState() => _DSDraggableBottomSheetState();
}

class _DSDraggableBottomSheetState extends State<DSDraggableBottomSheet> {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return DraggableScrollableSheet(
      initialChildSize: widget.initialChildSize,
      minChildSize: widget.minChildSize,
      maxChildSize: widget.maxChildSize,
      snap: widget.snap,
      snapSizes: widget.snapSizes,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: const BorderRadius.vertical(
              top: Radius.circular(SpacingTokens.space5),
            ),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: SpacingTokens.space5,
                offset: const Offset(0, -SpacingTokens.space1),
              ),
            ],
          ),
          child: Column(
            children: [
              // 拖拽手柄
              Container(
                margin: const EdgeInsets.symmetric(vertical: SpacingTokens.space3),
                width: SpacingTokens.space10,
                height: SpacingTokens.space1,
                decoration: BoxDecoration(
                  color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                  borderRadius: BorderRadius.circular(SpacingTokens.space1),
                ),
              ),
              
              // 标题栏
              if (widget.title != null || widget.titleWidget != null)
                Container(
                  padding: const EdgeInsets.all(SpacingTokens.space4),
                  child: Row(
                    children: [
                      if (widget.titleIcon != null) ...[
                        Icon(
                          widget.titleIcon,
                          color: theme.colorScheme.primary,
                          size: SpacingTokens.space6,
                        ),
                        const SizedBox(width: SpacingTokens.space3),
                      ],
                      Expanded(
                        child: widget.titleWidget ?? 
                          Text(
                            widget.title!,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                      ),
                      if (widget.onClose != null)
                        IconButton(
                          onPressed: widget.onClose,
                          icon: Icon(
                            Icons.close_rounded,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                    ],
                  ),
                ),
              
              if (widget.title != null || widget.titleWidget != null)
                const Divider(height: 1),
              
              // 可滚动内容
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: widget.child,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}