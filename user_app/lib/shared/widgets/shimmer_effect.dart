import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 微光效果组件 - 高级骨架屏动画效果
class ShimmerEffect extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final Color baseColor;
  final Color highlightColor;
  final Gradient? gradient;
  final bool enabled;

  const ShimmerEffect({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1500),
    this.baseColor = ColorTokens.outlineVariant,
    this.highlightColor = ColorTokens.surfaceVariant,
    this.gradient,
    this.enabled = true,
  });

  /// 创建 Material Design 3 风格的微光效果
  const ShimmerEffect.material3({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1500),
    this.enabled = true,
  }) : baseColor = ColorTokens.surfaceVariant,
       highlightColor = ColorTokens.surfaceContainerHigh,
       gradient = null;

  @override
  State<ShimmerEffect> createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<ShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: -2.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutSine,
    ));
    
    if (widget.enabled) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(ShimmerEffect oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.enabled != oldWidget.enabled) {
      if (widget.enabled) {
        _animationController.repeat();
      } else {
        _animationController.stop();
        _animationController.reset();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return ShaderMask(
          blendMode: BlendMode.srcATop,
          shaderCallback: (bounds) {
            return _createShimmerGradient(bounds).createShader(bounds);
          },
          child: widget.child,
        );
      },
    );
  }

  Gradient _createShimmerGradient(Rect bounds) {
    if (widget.gradient != null) {
      return widget.gradient!;
    }

    // 使用 Material Design 3 的颜色系统
    final baseColor = widget.baseColor == ColorTokens.surfaceVariant
        ? ColorTokens.surfaceVariant 
        : widget.baseColor;
        
    final highlightColor = widget.highlightColor == ColorTokens.surfaceContainerHigh
        ? ColorTokens.surfaceContainerHigh
        : widget.highlightColor;

    return LinearGradient(
      begin: Alignment(-1.0 + _animation.value, -0.3),
      end: Alignment(1.0 + _animation.value, 0.3),
      colors: [
        baseColor,
        baseColor,
        highlightColor,
        baseColor,
        baseColor,
      ],
      stops: const [0.0, 0.35, 0.5, 0.65, 1.0],
    );
  }
}

/// 渐进式加载效果
class ProgressiveLoader extends StatefulWidget {
  final List<Widget> children;
  final Duration delayBetween;
  final Duration animationDuration;
  final Curve curve;
  final bool autoStart;

  const ProgressiveLoader({
    super.key,
    required this.children,
    this.delayBetween = const Duration(milliseconds: 100),
    this.animationDuration = const Duration(milliseconds: 400),
    this.curve = Curves.easeOut,
    this.autoStart = true,
  });

  @override
  State<ProgressiveLoader> createState() => _ProgressiveLoaderState();
}

class _ProgressiveLoaderState extends State<ProgressiveLoader>
    with TickerProviderStateMixin {
  List<AnimationController> _controllers = [];
  List<Animation<double>> _animations = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    
    if (widget.autoStart) {
      _startAnimations();
    }
  }

  void _initializeAnimations() {
    _controllers = List.generate(
      widget.children.length,
      (index) => AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      ),
    );

    _animations = _controllers.map((controller) =>
      CurvedAnimation(
        parent: controller,
        curve: widget.curve,
      ),
    ).toList();
  }

  void _startAnimations() {
    for (int i = 0; i < _controllers.length; i++) {
      Future.delayed(widget.delayBetween * i, () {
        if (mounted) {
          _controllers[i].forward();
        }
      });
    }
  }

  void reset() {
    for (final controller in _controllers) {
      controller.reset();
    }
  }

  void start() {
    _startAnimations();
  }

  @override
  void dispose() {
    for (final controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: List.generate(
        widget.children.length,
        (index) => AnimatedBuilder(
          animation: _animations[index],
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(0, 20 * (1 - _animations[index].value)),
              child: Opacity(
                opacity: _animations[index].value,
                child: widget.children[index],
              ),
            );
          },
        ),
      ),
    );
  }
}

/// 脉动加载效果
class PulseLoader extends StatefulWidget {
  final Widget child;
  final Duration duration;
  final double minOpacity;
  final double maxOpacity;
  final bool enabled;

  const PulseLoader({
    super.key,
    required this.child,
    this.duration = const Duration(milliseconds: 1000),
    this.minOpacity = 0.3,
    this.maxOpacity = 1.0,
    this.enabled = true,
  });

  @override
  State<PulseLoader> createState() => _PulseLoaderState();
}

class _PulseLoaderState extends State<PulseLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: widget.minOpacity,
      end: widget.maxOpacity,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    if (widget.enabled) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(PulseLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.enabled != oldWidget.enabled) {
      if (widget.enabled) {
        _controller.repeat(reverse: true);
      } else {
        _controller.stop();
        _controller.value = widget.maxOpacity;
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.enabled) {
      return widget.child;
    }

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Opacity(
          opacity: _animation.value,
          child: widget.child,
        );
      },
    );
  }
}

/// 波浪加载效果
class WaveLoader extends StatefulWidget {
  final List<Widget> children;
  final Duration duration;
  final double amplitude;
  final bool enabled;

  const WaveLoader({
    super.key,
    required this.children,
    this.duration = const Duration(milliseconds: 1500),
    this.amplitude = 10.0,
    this.enabled = true,
  });

  @override
  State<WaveLoader> createState() => _WaveLoaderState();
}

class _WaveLoaderState extends State<WaveLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );

    if (widget.enabled) {
      _controller.repeat();
    }
  }

  @override
  void didUpdateWidget(WaveLoader oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.enabled != oldWidget.enabled) {
      if (widget.enabled) {
        _controller.repeat();
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: List.generate(
            widget.children.length,
            (index) {
              final delay = index * 0.2;
              final progress = (_controller.value + delay) % 1.0;
              final offset = widget.amplitude * 
                  (0.5 * (1 + math.cos(2 * math.pi * progress)) - 0.5);
              
              return Transform.translate(
                offset: Offset(0, -offset),
                child: widget.children[index],
              );
            },
          ),
        );
      },
    );
  }
}


/// 智能加载状态切换器
class LoadingStateSwitcher extends StatefulWidget {
  final Widget child;
  final Widget loadingWidget;
  final Widget? errorWidget;
  final bool isLoading;
  final bool hasError;
  final Duration switchDuration;
  final Curve switchCurve;

  const LoadingStateSwitcher({
    super.key,
    required this.child,
    required this.loadingWidget,
    this.errorWidget,
    this.isLoading = false,
    this.hasError = false,
    this.switchDuration = const Duration(milliseconds: 300),
    this.switchCurve = Curves.easeInOut,
  });

  @override
  State<LoadingStateSwitcher> createState() => _LoadingStateSwitcherState();
}

class _LoadingStateSwitcherState extends State<LoadingStateSwitcher> {
  @override
  Widget build(BuildContext context) {
    Widget currentWidget;
    
    if (widget.hasError && widget.errorWidget != null) {
      currentWidget = widget.errorWidget!;
    } else if (widget.isLoading) {
      currentWidget = widget.loadingWidget;
    } else {
      currentWidget = widget.child;
    }

    return AnimatedSwitcher(
      duration: widget.switchDuration,
      switchInCurve: widget.switchCurve,
      switchOutCurve: widget.switchCurve,
      transitionBuilder: (child, animation) {
        return FadeTransition(
          opacity: animation,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.1),
              end: Offset.zero,
            ).animate(animation),
            child: child,
          ),
        );
      },
      child: Container(
        key: ValueKey(widget.isLoading ? 'loading' : widget.hasError ? 'error' : 'content'),
        child: currentWidget,
      ),
    );
  }
}

/// 骨架屏工厂类
class SkeletonFactory {
  /// 创建文本骨架
  static Widget text({
    double width = double.infinity,
    double height = 16.0,
    bool animate = true,
  }) {
    return ShimmerEffect.material3(
      enabled: animate,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: ColorTokens.surfaceVariant,
          borderRadius: BorderRadius.circular(SpacingTokens.space1),
        ),
      ),
    );
  }

  /// 创建圆形骨架
  static Widget circle({
    required double size,
    bool animate = true,
  }) {
    return ShimmerEffect.material3(
      enabled: animate,
      child: Container(
        width: size,
        height: size,
        decoration: const BoxDecoration(
          color: ColorTokens.surfaceVariant,
          shape: BoxShape.circle,
        ),
      ),
    );
  }

  /// 创建矩形骨架
  static Widget rectangle({
    required double width,
    required double height,
    BorderRadius? borderRadius,
    bool animate = true,
  }) {
    return ShimmerEffect.material3(
      enabled: animate,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: ColorTokens.surfaceVariant,
          borderRadius: borderRadius ?? BorderRadius.circular(SpacingTokens.space2),
        ),
      ),
    );
  }

  /// 创建卡片骨架
  static Widget card({
    double width = double.infinity,
    double height = 120.0,
    bool animate = true,
  }) {
    return ShimmerEffect.material3(
      enabled: animate,
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          color: ColorTokens.surfaceVariant,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          border: Border.all(
            color: ColorTokens.outlineVariant,
            width: 0.5,
          ),
        ),
      ),
    );
  }

  /// 创建列表项骨架
  static Widget listItem({
    bool showAvatar = true,
    int textLines = 2,
    bool animate = true,
  }) {
    return ShimmerEffect.material3(
      enabled: animate,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: SpacingTokens.space4,
          vertical: SpacingTokens.space3,
        ),
        child: Row(
          children: [
            if (showAvatar) ...[
              Container(
                width: 40,
                height: 40,
                decoration: const BoxDecoration(
                  color: ColorTokens.surfaceVariant,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: SpacingTokens.space3),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: List.generate(
                  textLines,
                  (index) => Padding(
                    padding: EdgeInsets.only(
                      bottom: index < textLines - 1 ? SpacingTokens.space2 : 0,
                    ),
                    child: Container(
                      width: index == textLines - 1 ? 150 : double.infinity,
                      height: 14,
                      decoration: BoxDecoration(
                        color: ColorTokens.surfaceVariant,
                        borderRadius: BorderRadius.circular(SpacingTokens.space1),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}