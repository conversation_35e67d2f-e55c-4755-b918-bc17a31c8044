import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_manager.dart';

/// 智能返回按钮
/// 提供触感反馈、动画效果和智能导航逻辑
class SmartBackButton extends StatelessWidget {
  final Color? color;
  final double? size;
  final VoidCallback? onPressed;
  final String? tooltip;
  final bool showTooltip;
  final IconData? icon;

  const SmartBackButton({
    super.key,
    this.color,
    this.size = SpacingTokens.space6,
    this.onPressed,
    this.tooltip,
    this.showTooltip = true,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final canPop = GoRouter.of(context).canPop();
    final navManager = NavigationManager();
    final effectiveTooltip = tooltip ?? (canPop ? '返回' : '回到首页');

    Widget button = IconButton(
      icon: Icon(
        icon ?? Icons.arrow_back_ios_new_rounded,
        size: size,
        color: color ?? Theme.of(context).iconTheme.color,
      ),
      onPressed: onPressed ?? () => _handleBack(context, navManager, canPop),
      style: IconButton.styleFrom(
        padding: const EdgeInsets.all(SpacingTokens.space2),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
    );

    if (showTooltip) {
      button = Tooltip(
        message: effectiveTooltip,
        child: button,
      );
    }

    return button;
  }

  void _handleBack(
      BuildContext context, NavigationManager navManager, bool canPop) {
    // 触感反馈
    HapticFeedback.lightImpact();

    if (canPop) {
      // 标准返回
      context.pop();
    } else {
      // 智能返回到合适的页面
      navManager.smartGoBack(context);
    }
  }
}

/// 自定义AppBar with 智能返回按钮
class SmartAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String? title;
  final Widget? titleWidget;
  final List<Widget>? actions;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final double? elevation;
  final bool automaticallyImplyLeading;
  final Widget? leading;
  final bool centerTitle;
  final double? titleSpacing;
  final double? toolbarHeight;

  const SmartAppBar({
    Key? key,
    this.title,
    this.titleWidget,
    this.actions,
    this.backgroundColor,
    this.foregroundColor,
    this.elevation,
    this.automaticallyImplyLeading = true,
    this.leading,
    this.centerTitle = true,
    this.titleSpacing,
    this.toolbarHeight,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: titleWidget ?? (title != null ? Text(title!) : null),
      actions: actions,
      backgroundColor: backgroundColor,
      foregroundColor: foregroundColor,
      elevation: elevation,
      centerTitle: centerTitle,
      titleSpacing: titleSpacing,
      toolbarHeight: toolbarHeight,
      automaticallyImplyLeading: false,
      leading: automaticallyImplyLeading
          ? (leading ?? SmartBackButton(color: foregroundColor))
          : leading,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(toolbarHeight ?? kToolbarHeight);
}

/// 智能浮动返回按钮 - 用于全屏页面
class SmartFloatingBackButton extends StatefulWidget {
  final Alignment alignment;
  final EdgeInsets margin;
  final Color? backgroundColor;
  final Color? iconColor;
  final double size;
  final VoidCallback? onPressed;

  const SmartFloatingBackButton({
    Key? key,
    this.alignment = Alignment.topLeft,
    this.margin = const EdgeInsets.all(16),
    this.backgroundColor,
    this.iconColor,
    this.size = 48,
    this.onPressed,
  }) : super(key: key);

  @override
  State<SmartFloatingBackButton> createState() =>
      _SmartFloatingBackButtonState();
}

class _SmartFloatingBackButtonState extends State<SmartFloatingBackButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    // 启动入场动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned.fill(
          child: Align(
            alignment: widget.alignment,
            child: Container(
              margin: widget.margin,
              child: Transform.scale(
                scale: _scaleAnimation.value,
                child: Opacity(
                  opacity: _opacityAnimation.value,
                  child: Container(
                    width: widget.size,
                    height: widget.size,
                    decoration: BoxDecoration(
                      color: widget.backgroundColor ??
                          Theme.of(context)
                              .colorScheme
                              .surface
                              .withOpacity(0.9),
                      borderRadius: BorderRadius.circular(widget.size / 2),
                      boxShadow: [
                        BoxShadow(
                          color: ColorTokens.outline.withOpacity(0.2),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Material(
                      color: ColorTokens.transparent,
                      child: InkWell(
                        borderRadius: BorderRadius.circular(widget.size / 2),
                        onTap: () => _handlePress(context),
                        onTapDown: (_) => _animationController.reverse(),
                        onTapUp: (_) => _animationController.forward(),
                        onTapCancel: () => _animationController.forward(),
                        child: Center(
                          child: Icon(
                            Icons.arrow_back_ios_new_rounded,
                            color: widget.iconColor ??
                                Theme.of(context).iconTheme.color,
                            size: widget.size * 0.5,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  void _handlePress(BuildContext context) {
    if (widget.onPressed != null) {
      widget.onPressed!();
    } else {
      final canPop = GoRouter.of(context).canPop();
      final navManager = NavigationManager();

      HapticFeedback.lightImpact();

      if (canPop) {
        context.pop();
      } else {
        navManager.smartGoBack(context);
      }
    }
  }
}

/// 页面包装器 - 为页面添加智能导航功能
class SmartPageWrapper extends StatelessWidget {
  final Widget child;
  final String? title;
  final bool showAppBar;
  final bool showFloatingBack;
  final List<Widget>? actions;
  final Color? appBarBackgroundColor;
  final Color? appBarForegroundColor;

  const SmartPageWrapper({
    Key? key,
    required this.child,
    this.title,
    this.showAppBar = true,
    this.showFloatingBack = false,
    this.actions,
    this.appBarBackgroundColor,
    this.appBarForegroundColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget body = child;

    // 添加浮动返回按钮
    if (showFloatingBack) {
      body = Stack(
        children: [
          body,
          SmartFloatingBackButton(
            backgroundColor: appBarBackgroundColor?.withOpacity(0.9),
            iconColor: appBarForegroundColor,
          ),
        ],
      );
    }

    // 添加AppBar
    if (showAppBar) {
      return Scaffold(
        appBar: SmartAppBar(
          title: title,
          actions: actions,
          backgroundColor: appBarBackgroundColor,
          foregroundColor: appBarForegroundColor,
        ),
        body: body,
      );
    }

    return body;
  }
}
