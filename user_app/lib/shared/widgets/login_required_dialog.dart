import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';

/// 统一的登录需要对话框组件
class LoginRequiredDialog {
  /// 显示登录需要对话框
  static Future<void> show(
    BuildContext context, {
    final String title = '需要登录',
    final String description = '您需要登录后才能使用此功能',
    List<String>? benefits,
    final String cancelButtonText = '稍后再说',
    final String confirmButtonText = '立即登录',
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
  }) async {
    final defaultBenefits = benefits ?? [
      '创建钓鱼活动',
      '参与其他活动',
      '收藏感兴趣的活动',
    ];

    return showDialog<void>(
      context: context,
      barrierDismissible: false, // 用户必须点击按钮
      builder: (BuildContext context) {
        return AlertDialog(
          shape: const RoundedRectangleBorder(
            borderRadius: ShapeTokens.borderRadiusXl,
          ),
          title: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: ColorTokens.error.withValues(alpha: 0.1),
                  borderRadius: ShapeTokens.borderRadiusFull,
                ),
                child: const Icon(
                  Icons.login,
                  color: ColorTokens.error,
                  size: 20,
                ),
              ),
              SpacingTokens.horizontalSpaceMd,
              Text(
                title,
                style: TypographyTokens.headlineSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: ColorTokens.onSurface,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                description,
                style: TypographyTokens.bodyMedium.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                  height: 1.4,
                ),
              ),
              if (defaultBenefits.isNotEmpty) ...[
                SpacingTokens.verticalSpaceMd,
                Text(
                  '登录后您可以：',
                  style: TypographyTokens.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: ColorTokens.onSurface,
                  ),
                ),
                SpacingTokens.verticalSpaceSm,
                ...defaultBenefits.map((benefit) => Padding(
                  padding: EdgeInsets.only(bottom: SpacingTokens.space1),
                  child: Row(
                    children: [
                     const Icon(
                        Icons.check_circle_outline,
                        size: 16,
                        color: ColorTokens.success,
                      ),
                      SpacingTokens.horizontalSpaceSm,
                      Text(
                        benefit,
                        style: TypographyTokens.bodySmall.copyWith(
                          color: ColorTokens.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                )),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                onCancel?.call();
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
              ),
              child: Text(
                cancelButtonText,
                style: TypographyTokens.bodyMedium.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
              ),
            ),
            SpacingTokens.horizontalSpaceSm,
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop(); // 关闭对话框
                if (onConfirm != null) {
                  onConfirm();
                } else {
                  // 默认跳转到登录页面
                  context.navigateTo(AppRoutes.login);
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorTokens.error,
                foregroundColor: ColorTokens.onError,
                padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space5, vertical: SpacingTokens.space2),
                shape: const RoundedRectangleBorder(
                  borderRadius: ShapeTokens.borderRadiusMd,
                ),
                elevation: 2,
              ),
              child: Text(
                confirmButtonText,
                style: TypographyTokens.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 显示创建活动登录提示
  static Future<void> showForCreateActivity(BuildContext context) async {
    return show(
      context,
      description: '您需要登录后才能创建钓鱼活动',
      benefits: [
        '创建钓鱼活动',
        '管理您的活动',
        '查看参与者信息',
        '与钓友互动交流',
      ],
    );
  }

  /// 显示参与活动登录提示
  static Future<void> showForJoinActivity(BuildContext context) async {
    return show(
      context,
      description: '您需要登录后才能参与钓鱼活动',
      benefits: [
        '参与钓鱼活动',
        '与活动发起者联系',
        '查看活动详细信息',
        '结识更多钓友',
      ],
    );
  }

  /// 显示收藏活动登录提示
  static Future<void> showForBookmarkActivity(BuildContext context) async {
    return show(
      context,
      description: '您需要登录后才能收藏活动',
      benefits: [
        '收藏感兴趣的活动',
        '管理您的收藏列表',
        '接收活动更新通知',
        '快速查找收藏内容',
      ],
    );
  }

  /// 显示通用登录提示
  static Future<void> showGeneral(
    BuildContext context, {
    required String action,
  }) async {
    return show(
      context,
      description: '您需要登录后才能$action',
    );
  }
}