import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 通用加载组件
class LoadingWidget extends StatelessWidget {
  final String? message;
  final Color? color;
  final double? size;

 const LoadingWidget({
    super.key,
    this.message,
    this.color,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size ?? SpacingTokens.space6,
          height: size ?? SpacingTokens.space6,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? ColorTokens.primary,
            ),
            strokeWidth: 2.5,
          ),
        ),
        if (message != null) ...[
          SpacingTokens.verticalSpaceMd,
          Text(
            message!,
            style: TypographyTokens.bodyMedium.copyWith(
              color: ColorTokens.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }
}

/// 全屏加载覆盖层
class LoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isVisible;

 const LoadingOverlay({
    super.key,
    this.message,
    this.isVisible = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) return const SizedBox.shrink();

    return Container(
      color: ColorTokens.shadow.withValues(alpha: 0.54),
      child: Center(
        child: Container(
          padding: SpacingTokens.paddingLg,
          decoration: BoxDecoration(
            color: ColorTokens.surface,
            borderRadius: ShapeTokens.borderRadiusMd,
            boxShadow: [
              BoxShadow(
                color: ColorTokens.shadow.withValues(alpha: 0.1),
                blurRadius: SpacingTokens.space2,
                offset: Offset(0, SpacingTokens.space1),
              ),
            ],
          ),
          child: LoadingWidget(
            message: message,
            size: SpacingTokens.space8,
          ),
        ),
      ),
    );
  }
}