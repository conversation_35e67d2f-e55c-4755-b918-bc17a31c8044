import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 步骤式加载组件，用于显示多步骤处理过程
class StepLoadingWidget extends StatefulWidget {
  final List<LoadingStep> steps;
  final int currentStep;
  final String? title;
  final bool showProgress;

 const StepLoadingWidget({
    super.key,
    required this.steps,
    required this.currentStep,
    this.title,
    this.showProgress = true,
  });

  @override
  State<StepLoadingWidget> createState() => _StepLoadingWidgetState();
}

class _StepLoadingWidgetState extends State<StepLoadingWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: MotionTokens.durationShort,
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(StepLoadingWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentStep != widget.currentStep) {
      _animationController.reset();
      _animationController.forward();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: SpacingTokens.paddingLg,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          // 标题
          if (widget.title != null) ...[
            Text(
              widget.title!,
              style: TypographyTokens.titleMedium.copyWith(
                color: ColorTokens.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SpacingTokens.verticalSpaceLg,
          ],

          // 进度条
          if (widget.showProgress) ...[
            _buildProgressIndicator(),
            SpacingTokens.verticalSpaceLg,
          ],

          // 步骤列表
          _buildStepsList(),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final progress = widget.currentStep / widget.steps.length;
    return Column(
      children: [
        ClipRRect(
          borderRadius: ShapeTokens.borderRadiusMd,
          child: LinearProgressIndicator(
            value: progress,
            backgroundColor: ColorTokens.surfaceContainerHigh,
            valueColor: AlwaysStoppedAnimation<Color>(ColorTokens.primary),
            minHeight: 6,
          ),
        ),
        SpacingTokens.verticalSpaceSm,
        Text(
          '${widget.currentStep}/${widget.steps.length}',
          style: TypographyTokens.labelMedium.copyWith(
            color: ColorTokens.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStepsList() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Column(
        children: widget.steps.asMap().entries.map((entry) {
          final index = entry.key;
          final step = entry.value;
          final isActive = index == widget.currentStep;
          final isCompleted = index < widget.currentStep;

          return _buildStepItem(step, isActive, isCompleted, index);
        }).toList(),
      ),
    );
  }

  Widget _buildStepItem(LoadingStep step, bool isActive, bool isCompleted, int index) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: SpacingTokens.space2),
      child: Row(
        children: [
          // 步骤图标
          Container(
            width: SpacingTokens.space8,
            height: SpacingTokens.space8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isCompleted
                  ? ColorTokens.success
                  : isActive
                      ? ColorTokens.primary
                      : ColorTokens.surfaceContainer,
            ),
            child: isCompleted
                ? const Icon(
                    Icons.check,
                    color: ColorTokens.onSuccess,
                    size: SpacingTokens.space4,
                  )
                : isActive
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(ColorTokens.onPrimary),
                        ),
                      )
                    : Text(
                        '${index + 1}',
                        style: TypographyTokens.labelMedium.copyWith(
                          color: ColorTokens.onSurfaceVariant,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
          ),
          SpacingTokens.horizontalSpaceMd,

          // 步骤内容
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  step.title,
                  style: TypographyTokens.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color: isActive || isCompleted
                        ? ColorTokens.onSurface
                        : ColorTokens.onSurfaceVariant,
                  ),
                ),
                if (step.description != null) ...[
                  SpacingTokens.verticalSpaceXs,
                  Text(
                    step.description!,
                    style: TypographyTokens.bodySmall.copyWith(
                      color: isActive || isCompleted
                          ? ColorTokens.onSurfaceVariant
                          : ColorTokens.outline,
                    ),
                  ),
                ],
              ],
            ),
          ),

          // 状态图标
          if (isCompleted)
           const Icon(
              Icons.check_circle,
              color: ColorTokens.success,
              size: 20,
            ),
        ],
      ),
    );
  }
}

/// 加载步骤数据模型
class LoadingStep {
  final String title;
  final String? description;
  final IconData? icon;

 const LoadingStep({
    required this.title,
    this.description,
    this.icon,
  });
}

/// 活动创建专用的步骤式加载组件
class ActivityCreationLoadingWidget extends StatelessWidget {
  final int currentStep;
  final String? customMessage;

 const ActivityCreationLoadingWidget({
    super.key,
    required this.currentStep,
    this.customMessage,
  });

  static const List<LoadingStep> _defaultSteps = [
    LoadingStep(
      title: '验证模板数据',
      description: '检查模板信息的完整性',
    ),
    LoadingStep(
      title: '解析活动配置',
      description: '处理钓法、鱼种等配置信息',
    ),
    LoadingStep(
      title: '生成活动内容',
      description: '根据模板生成活动详情',
    ),
    LoadingStep(
      title: '保存活动信息',
      description: '将活动信息保存到系统',
    ),
    LoadingStep(
      title: '创建完成',
      description: '准备跳转到活动详情',
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space6, vertical: SpacingTokens.space8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 主标题
         const Text(
            '正在创建钓鱼活动',
            style: TypographyTokens.titleLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
          
          if (customMessage != null) ...[
            SpacingTokens.verticalSpaceSm,
            Text(
              customMessage!,
              style: TypographyTokens.bodyMedium.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          
          SpacingTokens.verticalSpaceXl,
          
          // 步骤加载器
          StepLoadingWidget(
            steps: _defaultSteps,
            currentStep: currentStep.clamp(0, _defaultSteps.length - 1),
            showProgress: true,
          ),
          
         const SizedBox(height: SpacingTokens.space6),
          
          // 底部提示
          Container(
            padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space3),
            decoration: const BoxDecoration(
              color: ColorTokens.surfaceContainer,
              borderRadius: ShapeTokens.borderRadiusMd,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
               const Icon(
                  Icons.info_outline,
                  size: 16,
                  color: ColorTokens.onSurfaceVariant,
                ),
                SpacingTokens.horizontalSpaceSm,
                Text(
                  '请稍候，正在为您创建精彩的钓鱼活动...',
                  style: TypographyTokens.bodySmall.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}