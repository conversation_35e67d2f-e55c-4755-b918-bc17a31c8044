import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/shimmer_effect.dart';

/// 骨架屏加载组件 - Material Design 3 规范
class SkeletonLoader extends StatefulWidget {
  final double width;
  final double height;
  final BorderRadius? borderRadius;
  final BoxShape shape;
  final bool animate;
  final Duration duration;
  final Color? baseColor;
  final Color? highlightColor;

  const SkeletonLoader({
    super.key,
    required this.width,
    required this.height,
    this.borderRadius,
    this.shape = BoxShape.rectangle,
    this.animate = true,
    this.duration = const Duration(milliseconds: 1500),
    this.baseColor,
    this.highlightColor,
  });

  /// 创建圆形骨架加载器
  const SkeletonLoader.circular({
    super.key,
    required double size,
    this.animate = true,
    this.duration = const Duration(milliseconds: 1500),
    this.baseColor,
    this.highlightColor,
  }) : width = size,
       height = size,
       shape = BoxShape.circle,
       borderRadius = null;

  /// 创建文本行骨架加载器
  const SkeletonLoader.text({
    super.key,
    this.width = double.infinity,
    this.height = 16.0,
    this.animate = true,
    this.duration = const Duration(milliseconds: 1500),
    this.baseColor,
    this.highlightColor,
  }) : borderRadius = const BorderRadius.all(Radius.circular(4)),
       shape = BoxShape.rectangle;

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    
    _animation = Tween<double>(
      begin: -2,
      end: 2,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutSine,
    ));

    if (widget.animate) {
      _animationController.repeat();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final baseColor = widget.baseColor ?? ColorTokens.surfaceVariant;
    final highlightColor = widget.highlightColor ?? ColorTokens.surface;

    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height,
          decoration: BoxDecoration(
            shape: widget.shape,
            borderRadius: widget.shape == BoxShape.rectangle ? widget.borderRadius : null,
            gradient: widget.animate 
                ? LinearGradient(
                    begin: Alignment(-1.0 + _animation.value, 0.0),
                    end: Alignment(1.0 + _animation.value, 0.0),
                    colors: [
                      baseColor,
                      highlightColor,
                      baseColor,
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  )
                : null,
            color: widget.animate ? null : baseColor,
          ),
        );
      },
    );
  }
}

/// 钓点详情页骨架屏 - 增强版微光效果
class FishingSpotDetailSkeleton extends StatelessWidget {
  const FishingSpotDetailSkeleton({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerEffect.material3(
      child: CustomScrollView(
        slivers: [
          SliverToBoxAdapter(
            child: ProgressiveLoader(
              delayBetween: const Duration(milliseconds: 80),
              children: [
                // 拖拽手柄
                Center(
                  child: Container(
                    width: SpacingTokens.space10,
                    height: SpacingTokens.space1,
                    margin: const EdgeInsets.symmetric(vertical: SpacingTokens.space3),
                    decoration: BoxDecoration(
                      color: ColorTokens.surfaceVariant,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                ),
                
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: SpacingTokens.space2),

                      // 标题区域
                      _buildTitleSkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space4),

                      // 创建者区域
                      _buildCreatorSkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space4),

                      // 地址区域
                      _buildAddressSkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space4),

                      // 图片画廊
                      _buildImageGallerySkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space4),

                      // 描述区域
                      _buildDescriptionSkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space4),

                      // 详情区域
                      _buildDetailsSkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space4),

                      // 数据统计区域
                      _buildStatsSkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space5),

                      // 操作按钮区域
                      _buildActionButtonsSkeleton(),
                      
                      const SizedBox(height: SpacingTokens.space4),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTitleSkeleton() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 标题
        SkeletonFactory.rectangle(
          width: 200,
          height: 32,
          borderRadius: BorderRadius.circular(SpacingTokens.space2),
        ),
        const SizedBox(height: SpacingTokens.space3),
        // 徽章
        Row(
          children: [
            SkeletonFactory.rectangle(
              width: 80,
              height: 24,
              borderRadius: BorderRadius.circular(SpacingTokens.space4),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCreatorSkeleton() {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space3),
      decoration: BoxDecoration(
        color: ColorTokens.surfaceVariant.withOpacity(0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: ColorTokens.outlineVariant.withOpacity(0.5),
          width: 0.5,
        ),
      ),
      child: Row(
        children: [
          // 头像
          SkeletonFactory.circle(size: 44),
          const SizedBox(width: SpacingTokens.space3),
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SkeletonFactory.text(width: 100, height: 16),
                    const SizedBox(width: SpacingTokens.space2),
                    SkeletonFactory.rectangle(
                      width: 50,
                      height: 20,
                      borderRadius: BorderRadius.circular(SpacingTokens.space3),
                    ),
                  ],
                ),
                const SizedBox(height: SpacingTokens.space2),
                SkeletonFactory.text(width: 150, height: 14),
              ],
            ),
          ),
          // 箭头
          SkeletonFactory.rectangle(
            width: 24,
            height: 24,
            borderRadius: BorderRadius.circular(SpacingTokens.space2),
          ),
        ],
      ),
    );
  }

  Widget _buildAddressSkeleton() {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space3),
      decoration: BoxDecoration(
        color: ColorTokens.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: ColorTokens.outlineVariant.withOpacity(0.7),
          width: 0.5,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SkeletonLoader(
            width: 32,
            height: 32,
            borderRadius: BorderRadius.circular(SpacingTokens.space1),
          ),
          const SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonLoader(
                  width: 60,
                  height: 14,
                  borderRadius: BorderRadius.circular(SpacingTokens.space1),
                ),
                const SizedBox(height: SpacingTokens.space2),
                SkeletonLoader(
                  width: double.infinity,
                  height: 16,
                  borderRadius: BorderRadius.circular(SpacingTokens.space1),
                ),
                const SizedBox(height: SpacingTokens.space1),
                SkeletonLoader(
                  width: 200,
                  height: 16,
                  borderRadius: BorderRadius.circular(SpacingTokens.space1),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImageGallerySkeleton() {
    return Column(
      children: [
        SkeletonFactory.rectangle(
          width: double.infinity,
          height: 220,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
        ),
        const SizedBox(height: SpacingTokens.space2),
        SkeletonFactory.text(width: 120, height: 14),
      ],
    );
  }

  Widget _buildDescriptionSkeleton() {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
        border: Border.all(
          color: ColorTokens.outlineVariant.withOpacity(0.5),
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题行
          Row(
            children: [
              SkeletonLoader(
                width: 20,
                height: 16,
                borderRadius: BorderRadius.circular(SpacingTokens.space1),
              ),
              const SizedBox(width: SpacingTokens.space2),
              SkeletonLoader(
                width: 80,
                height: 16,
                borderRadius: BorderRadius.circular(SpacingTokens.space1),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space3),
          // 描述文本
          SkeletonLoader(
            width: double.infinity,
            height: 16,
            borderRadius: BorderRadius.circular(SpacingTokens.space1),
          ),
          const SizedBox(height: SpacingTokens.space2),
          SkeletonLoader(
            width: double.infinity,
            height: 16,
            borderRadius: BorderRadius.circular(SpacingTokens.space1),
          ),
          const SizedBox(height: SpacingTokens.space2),
          SkeletonLoader(
            width: 180,
            height: 16,
            borderRadius: BorderRadius.circular(SpacingTokens.space1),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailsSkeleton() {
    return Column(
      children: [
        // 详情区域1
        _buildDetailSectionSkeleton(),
        const SizedBox(height: SpacingTokens.space4),
        // 详情区域2
        _buildDetailSectionSkeleton(),
        const SizedBox(height: SpacingTokens.space4),
        // 详情区域3
        _buildDetailSectionSkeleton(),
      ],
    );
  }

  Widget _buildDetailSectionSkeleton() {
    return Container(
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
        border: Border.all(
          color: ColorTokens.outlineVariant,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 区域标题
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: SpacingTokens.space4,
              vertical: SpacingTokens.space3,
            ),
            decoration: BoxDecoration(
              color: ColorTokens.primary.withOpacity(0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(SpacingTokens.space4),
                topRight: Radius.circular(SpacingTokens.space4),
              ),
            ),
            child: Row(
              children: [
                SkeletonLoader(
                  width: 40,
                  height: 40,
                  borderRadius: BorderRadius.circular(SpacingTokens.space2),
                ),
                const SizedBox(width: SpacingTokens.space3),
                SkeletonLoader(
                  width: 100,
                  height: 20,
                  borderRadius: BorderRadius.circular(SpacingTokens.space1),
                ),
              ],
            ),
          ),
          // 区域内容
          Padding(
            padding: EdgeInsets.all(SpacingTokens.space4),
            child: _buildChipsSkeleton(),
          ),
        ],
      ),
    );
  }

  Widget _buildChipsSkeleton() {
    return Wrap(
      spacing: SpacingTokens.space2,
      runSpacing: SpacingTokens.space2,
      children: List.generate(5, (index) => 
        SkeletonLoader(
          width: 60 + (index * 20.0),
          height: 32,
          borderRadius: BorderRadius.circular(SpacingTokens.space4),
        ),
      ),
    );
  }

  Widget _buildStatsSkeleton() {
    return Container(
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
        border: Border.all(
          color: ColorTokens.outlineVariant,
          width: 0.5,
        ),
      ),
      child: Column(
        children: [
          // 标题区域
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: SpacingTokens.space4,
              vertical: SpacingTokens.space3,
            ),
            decoration: BoxDecoration(
              color: ColorTokens.primary.withOpacity(0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(SpacingTokens.space4),
                topRight: Radius.circular(SpacingTokens.space4),
              ),
            ),
            child: Row(
              children: [
                SkeletonLoader(
                  width: 40,
                  height: 40,
                  borderRadius: BorderRadius.circular(SpacingTokens.space2),
                ),
                const SizedBox(width: SpacingTokens.space3),
                SkeletonLoader(
                  width: 80,
                  height: 20,
                  borderRadius: BorderRadius.circular(SpacingTokens.space1),
                ),
              ],
            ),
          ),
          // 统计数据
          Padding(
            padding: EdgeInsets.all(SpacingTokens.space4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: List.generate(3, (index) => _buildStatItemSkeleton()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItemSkeleton() {
    return Column(
      children: [
        SkeletonLoader(
          width: 40,
          height: 40,
          borderRadius: BorderRadius.circular(SpacingTokens.space2),
        ),
        const SizedBox(height: SpacingTokens.space2),
        SkeletonLoader(
          width: 30,
          height: 20,
          borderRadius: BorderRadius.circular(SpacingTokens.space1),
        ),
        const SizedBox(height: SpacingTokens.space1),
        SkeletonLoader(
          width: 40,
          height: 14,
          borderRadius: BorderRadius.circular(SpacingTokens.space1),
        ),
      ],
    );
  }

  Widget _buildActionButtonsSkeleton() {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
        border: Border.all(
          color: ColorTokens.outlineVariant,
          width: 0.5,
        ),
      ),
      child: Column(
        children: [
          // 标题
          Row(
            children: [
              SkeletonLoader(
                width: 20,
                height: 16,
                borderRadius: BorderRadius.circular(SpacingTokens.space1),
              ),
              const SizedBox(width: SpacingTokens.space2),
              SkeletonLoader(
                width: 80,
                height: 16,
                borderRadius: BorderRadius.circular(SpacingTokens.space1),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space3),
          // 按钮组
          Row(
            children: [
              Expanded(
                child: SkeletonLoader(
                  width: double.infinity,
                  height: 48,
                  borderRadius: BorderRadius.circular(SpacingTokens.space3),
                ),
              ),
              const SizedBox(width: SpacingTokens.space3),
              Expanded(
                child: SkeletonLoader(
                  width: double.infinity,
                  height: 48,
                  borderRadius: BorderRadius.circular(SpacingTokens.space3),
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space3),
          // 签到按钮
          SkeletonLoader(
            width: double.infinity,
            height: 48,
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
          ),
        ],
      ),
    );
  }
}

/// 进度式加载指示器
class CustomProgressIndicator extends StatefulWidget {
  final String message;
  final double? progress;
  final bool showPercentage;
  final Color? color;

  const CustomProgressIndicator({
    super.key,
    required this.message,
    this.progress,
    this.showPercentage = false,
    this.color,
  });

  @override
  State<CustomProgressIndicator> createState() => _CustomProgressIndicatorState();
}

class _CustomProgressIndicatorState extends State<CustomProgressIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animation,
      child: Container(
        padding: EdgeInsets.all(SpacingTokens.space4),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (widget.progress != null) ...[
              // 线性进度条
              ClipRRect(
                borderRadius: BorderRadius.circular(SpacingTokens.space1),
                child: LinearProgressIndicator(
                  value: widget.progress,
                  backgroundColor: ColorTokens.surfaceVariant,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    widget.color ?? ColorTokens.primary,
                  ),
                  minHeight: 8,
                ),
              ),
              const SizedBox(height: SpacingTokens.space3),
              if (widget.showPercentage)
                Text(
                  '${(widget.progress! * 100).round()}%',
                  style: TypographyTokens.titleMedium.copyWith(
                    fontWeight: FontWeight.w600,
                    color: widget.color ?? ColorTokens.primary,
                  ),
                ),
            ] else ...[
              // 圆形进度指示器
              CircularProgressIndicator(
                strokeWidth: 3,
                color: widget.color ?? ColorTokens.primary,
              ),
            ],
            const SizedBox(height: SpacingTokens.space3),
            Text(
              widget.message,
              style: TypographyTokens.titleMedium.copyWith(
                fontWeight: FontWeight.w500,
                color: ColorTokens.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// 智能加载状态管理器
class LoadingStateManager extends StatefulWidget {
  final Widget child;
  final Future<void> Function()? onLoad;
  final bool showSkeleton;
  final Widget? customSkeleton;
  final String? loadingMessage;
  final Duration minimumLoadingDuration;

  const LoadingStateManager({
    super.key,
    required this.child,
    this.onLoad,
    this.showSkeleton = true,
    this.customSkeleton,
    this.loadingMessage,
    this.minimumLoadingDuration = const Duration(milliseconds: 500),
  });

  @override
  State<LoadingStateManager> createState() => _LoadingStateManagerState();
}

class _LoadingStateManagerState extends State<LoadingStateManager> {
  bool _isLoading = false;
  bool _hasLoaded = false;

  @override
  void initState() {
    super.initState();
    if (widget.onLoad != null) {
      _performLoad();
    }
  }

  Future<void> _performLoad() async {
    setState(() {
      _isLoading = true;
    });

    final startTime = DateTime.now();
    
    try {
      await widget.onLoad?.call();
    } finally {
      final elapsedTime = DateTime.now().difference(startTime);
      final remainingTime = widget.minimumLoadingDuration - elapsedTime;
      
      if (remainingTime > Duration.zero) {
        await Future.delayed(remainingTime);
      }
      
      setState(() {
        _isLoading = false;
        _hasLoaded = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading && widget.showSkeleton) {
      return widget.customSkeleton ?? 
             const FishingSpotDetailSkeleton();
    }
    
    if (_isLoading) {
      return Center(
        child: CustomProgressIndicator(
          message: widget.loadingMessage ?? '加载中...',
        ),
      );
    }
    
    return widget.child;
  }
}