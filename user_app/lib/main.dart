import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:user_app/config/app_router_meta.dart';
import 'package:user_app/core/auth/auth_state.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/core/theme/app_theme.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/search/SearchSystem/Core/EnhancedSearchConfig.dart';
import 'package:user_app/models/fishing_plan/plan_template.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/providers.dart';
import 'package:user_app/shared/services/oss_service.dart';
import 'package:user_app/utils/preferences_cleanup_util.dart';
import 'package:user_app/utils/shared_preferences_util.dart';

Future<void> main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    await setupInjection();

    // Initialize dart_mappable mappers
    debugPrint('🔧 [MAIN] Initializing dart_mappable mappers...');
    FishingSpotVoMapper.ensureInitialized();
    MomentVoMapper.ensureInitialized();
    PlanTemplateMapper.ensureInitialized();
    debugPrint('✅ [MAIN] Mappers initialized successfully');

    Flutter2dAMap.updatePrivacy(true);
    timeago.setLocaleMessages('zh_CN', timeago.ZhCnMessages());

    // Initialize services
    await Future.wait([
      SharedPreferencesUtil.init(),
      PreferencesCleanupUtil.cleanupSearchHistory(), // 清理搜索历史数据
      Flutter2dAMap.setApiKey(
        iOSKey: '780498fc4a6dadc659230dcc386f1dd8',
        webKey: 'e66be3d5db9b91cd0b7bd1eb1cbdb681',
      ),
      getIt<OssService>().init(), // Initialize OssService
    ]);

    // 初始化应用状态（包括IM初始化）
    try {
      final authViewModel = getIt<AuthViewModel>();
      await authViewModel.initializeAppState();
    } catch (e) {
      debugPrint('❌ [MAIN] 应用状态初始化失败: $e');
    }

    // 导航系统使用简化版，无需特殊初始化
    debugPrint('🧭 [MAIN] 使用简化版导航系统');

    // 初始化搜索系统
    debugPrint('🔍 [MAIN] 正在初始化搜索系统...');
    try {
      final searchService = UnifiedSearchService()..registerDefaultConfigs();
      debugPrint('✅ [MAIN] 搜索系统初始化完成');
      debugPrint('📋 [MAIN] 已注册的搜索配置: ${searchService.getAllConfigKeys()}');
    } catch (e) {
      debugPrint('❌ [MAIN] 搜索系统初始化失败: $e');
    }

    runApp(
      MultiProvider(
        providers: providers(),
        child: Consumer<AuthState>(
          builder: (context, authState, child) {
            return MaterialApp.router(
              debugShowCheckedModeBanner: false,
              // 添加本地化支持
              localizationsDelegates: const [
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('zh', 'CN'), // 中文简体
                Locale('en', 'US'), // 英语
              ],
              locale: const Locale('zh', 'CN'),
              // 设置默认语言为中文
              theme: AppTheme.lightTheme,
              // darkTheme: AppTheme.darkTheme,
              themeMode: ThemeMode.system,
              routerConfig: AppRouterMeta.createRouter(authState: authState),
            );
          },
        ),
      ),
    );
  } catch (e, stackTrace) {
    debugPrint('Error during application startup: $e');
    debugPrint('Stack trace: $stackTrace');
  }
}
