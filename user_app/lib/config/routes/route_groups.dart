import 'package:go_router/go_router.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/navigation/router_meta.dart';

// 导入设置相关页面
import 'package:user_app/features/mine/screens/profile_setting_page.dart';
import 'package:user_app/features/mine/screens/about_page.dart';
import 'package:user_app/features/mine/screens/my_bookmarks_page.dart';
import 'package:user_app/features/mine/screens/blacklist_page.dart';
import 'package:user_app/features/mine/screens/notifications_page.dart';
import 'package:user_app/features/mine/screens/help_center_page.dart';
import 'package:user_app/features/mine/screens/privacy_settings_page.dart';
import 'package:user_app/features/mine/screens/my_album_page.dart';
import 'package:user_app/features/mine/screens/my_spots_page.dart';
import 'package:user_app/features/mine/screens/browsing_history_page.dart';

// 导入钓鱼计划相关页面
import 'package:user_app/features/fishing_plans/screens/fishing_plans_page.dart';
import 'package:user_app/features/fishing_plans/screens/create_center_page.dart';
import 'package:user_app/features/fishing_plans/screens/quick_create_page.dart';
import 'package:user_app/features/fishing_plans/screens/template_selection_page.dart';
import 'package:user_app/features/fishing_plans/screens/create_fishing_plan_page.dart';
import 'package:user_app/features/fishing_plans/screens/friend_outing_page.dart';
import 'package:user_app/features/fishing_plans/screens/publish_event_page.dart';

// 导入搜索相关页面
import 'package:user_app/features/community/screens/community_search_page.dart';

/// 路由分组管理类
/// 将路由按功能模块分组，便于管理和维护
class RouteGroups {
  
  /// 设置相关路由
  static List<RouteBase> get settingsRoutes => [
    metaRoute(
      path: AppRoutes.profileSetting,
      name: 'profileSetting',
      title: (_) => '个人设置',
      builder: (context, state) => const ProfileSettingPage(),
    ),
    metaRoute(
      path: AppRoutes.about,
      name: 'about',
      title: (_) => '关于我们',
      builder: (context, state) => const AboutPage(),
    ),
    metaRoute(
      path: AppRoutes.myBookmarks,
      name: 'myBookmarks',
      title: (_) => '我的收藏',
      builder: (context, state) => const MyBookmarksPage(),
    ),
    metaRoute(
      path: AppRoutes.blacklist,
      name: 'blacklist',
      title: (_) => '黑名单',
      builder: (context, state) => const BlacklistPage(),
    ),
    metaRoute(
      path: AppRoutes.notifications,
      name: 'notifications',
      title: (_) => '通知设置',
      builder: (context, state) => const NotificationsPage(),
    ),
    metaRoute(
      path: AppRoutes.helpCenter,
      name: 'helpCenter',
      title: (_) => '帮助中心',
      builder: (context, state) => const HelpCenterPage(),
    ),
    metaRoute(
      path: AppRoutes.privacySettings,
      name: 'privacySettings',
      title: (_) => '隐私设置',
      builder: (context, state) => const PrivacySettingsPage(),
    ),
  ];

  /// 个人中心相关路由
  static List<RouteBase> get profileRoutes => [
    metaRoute(
      path: AppRoutes.myAlbum,
      name: 'myAlbum',
      title: (_) => '我的相册',
      builder: (context, state) => const MyAlbumPage(),
    ),
    metaRoute(
      path: AppRoutes.mySpots,
      name: 'mySpots',
      title: (_) => '我的钓点',
      builder: (context, state) => const MySpotsPage(),
    ),
    metaRoute(
      path: AppRoutes.browsingHistory,
      name: 'browsingHistory',
      title: (_) => '浏览历史',
      builder: (context, state) => const BrowsingHistoryPage(),
    ),
  ];

  /// 钓鱼计划相关路由
  static List<RouteBase> get fishingPlanRoutes => [
    metaRoute(
      path: AppRoutes.fishingPlans,
      name: 'fishingPlans',
      title: (_) => '钓鱼计划',
      builder: (context, state) => const FishingPlansPage(),
    ),
    metaRoute(
      path: AppRoutes.createCenter,
      name: 'createCenter',
      title: (_) => '创建中心',
      builder: (context, state) => const CreateCenterPage(),
    ),
    metaRoute(
      path: AppRoutes.quickCreate,
      name: 'quickCreate',
      title: (_) => '快速创建',
      builder: (context, state) => const QuickCreatePage(),
    ),
    metaRoute(
      path: AppRoutes.templateSelection,
      name: 'templateSelection',
      title: (_) => '选择模板',
      builder: (context, state) => const TemplateSelectionPage(),
    ),
    metaRoute(
      path: AppRoutes.createFishingPlan,
      name: 'createFishingPlan',
      title: (_) => '创建计划',
      builder: (context, state) => const CreateFishingPlanPage(),
    ),
    metaRoute(
      path: AppRoutes.friendOuting,
      name: 'friendOuting',
      title: (_) => '好友出钓',
      builder: (context, state) => const FriendOutingPage(),
    ),
    metaRoute(
      path: AppRoutes.publishEvent,
      name: 'publishEvent',
      title: (_) => '发布活动',
      builder: (context, state) => const PublishEventPage(),
    ),
  ];

  /// 搜索相关路由
  static List<RouteBase> get searchRoutes => [
    metaRoute(
      path: AppRoutes.search,
      name: 'search',
      title: (_) => '搜索',
      builder: (context, state) => const CommunitySearchPage(),
    ),
  ];

  /// 所有功能路由集合
  static List<RouteBase> get allFeatureRoutes => [
    ...settingsRoutes,
    ...profileRoutes,
    ...fishingPlanRoutes,
    ...searchRoutes,
  ];
}