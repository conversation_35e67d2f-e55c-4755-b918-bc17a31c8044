import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/config/routes/route_groups.dart';
import 'package:user_app/core/auth/auth_state.dart';
import 'package:user_app/core/navigation/navigation_manager.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/core/navigation/router_meta.dart';
import 'package:user_app/features/activities/screens/activities_page.dart';
import 'package:user_app/features/auth/screens/login_page.dart';
import 'package:user_app/features/auth/screens/register_page.dart';
import 'package:user_app/features/community/screens/community_page.dart';
import 'package:user_app/features/fishing_spots/screens/create_new_spot_page.dart';
import 'package:user_app/features/fishing_spots/screens/fishing_spots_page.dart';
import 'package:user_app/features/fishing_spots/screens/fishing_spot_detail_page.dart';
import 'package:user_app/features/fishing_spots/screens/publish_moment_page.dart';
import 'package:user_app/features/messages/screens/message_page.dart';
import 'package:user_app/features/mine/screens/mine_page.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/features/user/screens/user_profile_page.dart';
import 'package:user_app/features/profile/screens/other_profile_page.dart';
import 'package:user_app/fishing_scaffold.dart';

final _rootNavigatorKey = GlobalKey<NavigatorState>();

class AppRouterMeta {
  static GoRouter createRouter({AuthState? authState}) {
    return GoRouter(
      initialLocation: AppRoutes.fishingSpots,
      navigatorKey: _rootNavigatorKey,
      debugLogDiagnostics: kDebugMode,
      refreshListenable: authState,
      observers: [NavObserver()],
      redirect: (ctx, state) => _handleAuthRedirect(authState, state),
      errorBuilder: _errorBuilder,
      routes: [
        // Shell：带底部导航
        ShellRoute(
          builder: (context, state, child) => FishingScaffold(
            selectedIndex: _calculateTabIndex(state.uri.path),
            child: child,
          ),
          routes: [
            // 用 metaRoute 重写各 Tab
            metaRoute(
              path: AppRoutes.fishingSpots,
              name: 'fishingSpots',
              title: (_) => '钓点',
              builder: (context, state) => const FishingSpotsPage(),
            ),
            metaRoute(
              path: AppRoutes.community,
              name: 'community',
              title: (_) => '社区',
              builder: (context, state) => const CommunityPage(),
            ),
            metaRoute(
              path: AppRoutes.activities,
              name: 'activities',
              title: (_) => '活动',
              builder: (context, state) => const ActivitiesPage(),
            ),
            metaRoute(
              path: AppRoutes.message,
              name: 'message',
              title: (_) => '消息',
              builder: (context, state) => const MessagePage(),
            ),
            metaRoute(
              path: AppRoutes.mine,
              name: 'mine',
              title: (_) => '我的',
              builder: (context, state) => const MinePage(),
            ),
          ],
        ),

        // 认证页面（壳层之外）
        metaRoute(
          path: AppRoutes.login,
          name: 'login',
          title: (_) => '登录',
          builder: (context, state) => const LoginPage(),
        ),
        metaRoute(
          path: AppRoutes.register,
          name: 'register',
          title: (_) => '注册',
          builder: (context, state) => const RegisterPage(),
        ),

        // ==================== 独立页面路由（不显示底部导航栏） ====================
        
        // --- 钓点相关页面 ---
        metaRoute(
          path: AppRoutes.createSpot,
          name: 'createSpot',
          title: (_) => '创建钓点',
          builder: (context, state) {
            final extra = state.extra;
            if (extra != null && extra is CreateSpotRouteData) {
              return CreateNewSpotPage(
                onSpotCreated: extra.onSpotCreated ?? (_) {},
              );
            }
            return CreateNewSpotPage(
              onSpotCreated: (_) {},
            );
          },
        ),
        
        metaRoute(
          path: AppRoutes.fishingSpotDetail,
          name: 'fishingSpotDetail',
          title: (_) => '钓点详情',
          builder: (context, state) {
            final spotId = state.extra as int?;
            return FishingSpotDetailPage(spotId: spotId ?? 0);
          },
        ),
        
        // --- 动态相关页面 ---
        metaRoute(
          path: AppRoutes.publishMoment,
          name: 'publishMoment',
          title: (_) => '发布动态',
          builder: (context, state) => const PublishMomentPage(),
        ),
        
        metaRoute(
          path: AppRoutes.momentDetail,
          name: 'momentDetail', 
          title: (_) => '动态详情',
          builder: (context, state) {
            final extra = state.extra as Map<String, dynamic>?;
            return MomentDetailPage(
              momentId: extra?['momentId'] ?? 0,
              initialMoment: extra?['moment'],
            );
          },
        ),
        
        // --- 用户相关页面 ---
        metaRoute(
          path: '${AppRoutes.profile}/:userId',
          name: 'userProfile',
          title: (_) => '用户主页',
          builder: (context, state) {
            final userId = int.tryParse(state.pathParameters['userId'] ?? '0') ?? 0;
            final user = state.extra;
            return OtherProfilePage(
              userId: userId,
            );
          },
        ),
        
        metaRoute(
          path: AppRoutes.userProfile,
          name: 'myProfile',
          title: (_) => '我的主页',
          builder: (context, state) {
            // 从 AuthState 获取当前用户ID，如果没有则使用默认值
            final authState = context.read<AuthState>();
            final userId = authState.user?.id ?? 0;
            return UserProfilePage(userId: userId);
          },
        ),
        
        // ==================== 功能模块路由 ====================
        ...RouteGroups.allFeatureRoutes,
      ],
    );
  }

  /// 认证重定向：未登录拦截 + 已登录访问登录页回跳
  static String? _handleAuthRedirect(AuthState? auth, GoRouterState state) {
    // go_router 14.x 推荐使用 state.uri / state.matchedLocation
    final loc = state.uri.toString();
    final path = state.matchedLocation;

    // RouteMeta暂时不包含requiresAuth字段，先设为false
    final meta = resolveMetaForLocation(path);
    const metaNeedsAuth = false; // TODO: 如果后续RouteMeta添加requiresAuth字段，可以启用

    // 兜底的按前缀受保护路径（可按需增删）
    const protectedPrefixes = <String>[
      AppRoutes.message,
      AppRoutes.createSpot,
      AppRoutes.publishMoment,
      AppRoutes.profileSetting,
      AppRoutes.myBookmarks,
      AppRoutes.myAlbum,
      AppRoutes.mySpots,
      AppRoutes.createCenter,
      AppRoutes.createFishingPlan,
      AppRoutes.friendOuting,
      AppRoutes.publishEvent,
      // AppRoutes.mine, // 如果"我的"未登录也可进，就不要保护
    ];
    bool hitsProtected(String p) =>
        protectedPrefixes.any((r) => p.startsWith(r));

    final goingAuthPage = path == AppRoutes.login || path == AppRoutes.register;
    final needsAuth = metaNeedsAuth || hitsProtected(path);

    // 如果AuthState不存在，跳过认证检查
    if (auth == null) return null;

    // 未登录访问受保护页 → 跳转登录并记住来源
    if (!auth.isLoggedIn && needsAuth) {
      final from = Uri.encodeComponent(loc);
      return '${AppRoutes.login}?from=$from';
    }

    // 已登录访问登录/注册 → 回跳来源或首页
    if (auth.isLoggedIn && goingAuthPage) {
      final from = state.uri.queryParameters['from'];
      return from != null ? Uri.decodeComponent(from) : AppRoutes.fishingSpots;
    }

    return null;
  }

  /// 使用 startsWith 的健壮 Tab 识别
  static int _calculateTabIndex(String path) {
    if (path.startsWith(AppRoutes.fishingSpots)) return 0;
    if (path.startsWith(AppRoutes.community)) return 1;
    if (path.startsWith(AppRoutes.activities)) return 2;
    if (path.startsWith(AppRoutes.message)) return 3;
    if (path.startsWith(AppRoutes.mine)) return 4;
    return 0;
  }

  /// 全局错误页面
  static Widget _errorBuilder(BuildContext context, GoRouterState state) {
    return Scaffold(
      appBar: AppBar(title: const Text('路由错误')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64),
            const SizedBox(height: 16),
            Text('路由错误: ${state.error}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.navigateTo(AppRoutes.fishingSpots),
              child: const Text('返回首页'),
            ),
          ],
        ),
      ),
    );
  }
}
