import 'package:flutter/material.dart';

/// Material Design 3 Spacing Tokens
/// 基于8pt网格系统的间距规范
class SpacingTokens {
  // Prevent instantiation
 SpacingTokens._();
  
  // ==================== Primitive Tokens (原始令牌) ====================
  // 基础8pt网格系统
  
  static const double _baseUnit = 8.0;
  
  // ==================== Semantic Tokens (语义令牌) ====================
  
  /// 无间距
  static const double space0 = 0.0;
  
  /// 最小间距 - 4pt
  static const double space1 = _baseUnit * 0.5; // 4
  
  /// 小间距 - 8pt
  static const double space2 = _baseUnit * 1; // 8
  
  /// 中小间距 - 12pt
  static const double space3 = _baseUnit * 1.5; // 12
  
  /// 中等间距 - 16pt
  static const double space4 = _baseUnit * 2; // 16
  
  /// 中大间距 - 20pt
  static const double space5 = _baseUnit * 2.5; // 20
  
  /// 大间距 - 24pt
  static const double space6 = _baseUnit * 3; // 24
  
  /// 超大间距 - 28pt
  static const double space7 = _baseUnit * 3.5; // 28
  
  /// 超大间距 - 32pt
  static const double space8 = _baseUnit * 4; // 32
  
  /// 巨大间距 - 40pt
  static const double space10 = _baseUnit * 5; // 40
  
  /// 特大间距 - 48pt
  static const double space12 = _baseUnit * 6; // 48
  
  /// 极大间距 - 64pt
  static const double space16 = _baseUnit * 8; // 64
  
  /// 最大间距 - 80pt
  static const double space20 = _baseUnit * 10; // 80
  
  /// 超级间距 - 96pt
  static const double space24 = _baseUnit * 12; // 96
  
  // ==================== 标准化间距别名 ====================
  // 仅提供符合MD3规范的标准间距
  
  /// 标准间距别名 - 基于8pt网格
  static const double xs = space1;   // 4pt - 最小间距
  static const double sm = space2;   // 8pt - 小间距
  static const double md = space4;   // 16pt - 中等间距
  static const double lg = space6;   // 24pt - 大间距
  static const double xl = space8;   // 32pt - 超大间距
  static const double xxl = space20; // 80pt - 页面级间距
  
  // ==================== Component Tokens (组件令牌) ====================
  
  /// 内边距 Padding
  static const EdgeInsets paddingXs = EdgeInsets.all(space1);
  static const EdgeInsets paddingSm = EdgeInsets.all(space2);
  static const EdgeInsets paddingMd = EdgeInsets.all(space4);
  static const EdgeInsets paddingLg = EdgeInsets.all(space6);
  static const EdgeInsets paddingXl = EdgeInsets.all(space8);
  
  /// 水平内边距
  static const EdgeInsets paddingHorizontalXs = EdgeInsets.symmetric(horizontal: space1);
  static const EdgeInsets paddingHorizontalSm = EdgeInsets.symmetric(horizontal: space2);
  static const EdgeInsets paddingHorizontalMd = EdgeInsets.symmetric(horizontal: space4);
  static const EdgeInsets paddingHorizontalLg = EdgeInsets.symmetric(horizontal: space6);
  static const EdgeInsets paddingHorizontalXl = EdgeInsets.symmetric(horizontal: space8);
  
  /// 垂直内边距
  static const EdgeInsets paddingVerticalXs = EdgeInsets.symmetric(vertical: space1);
  static const EdgeInsets paddingVerticalSm = EdgeInsets.symmetric(vertical: space2);
  static const EdgeInsets paddingVerticalMd = EdgeInsets.symmetric(vertical: space4);
  static const EdgeInsets paddingVerticalLg = EdgeInsets.symmetric(vertical: space6);
  static const EdgeInsets paddingVerticalXl = EdgeInsets.symmetric(vertical: space8);
  
  /// 外边距 Margin
  static const EdgeInsets marginXs = EdgeInsets.all(space1);
  static const EdgeInsets marginSm = EdgeInsets.all(space2);
  static const EdgeInsets marginMd = EdgeInsets.all(space4);
  static const EdgeInsets marginLg = EdgeInsets.all(space6);
  static const EdgeInsets marginXl = EdgeInsets.all(space8);
  
  /// SizedBox间距快捷方式
  static const SizedBox gapXs = SizedBox(width: space1, height: space1);
  static const SizedBox gapSm = SizedBox(width: space2, height: space2);
  static const SizedBox gapMd = SizedBox(width: space4, height: space4);
  static const SizedBox gapLg = SizedBox(width: space6, height: space6);
  static const SizedBox gapXl = SizedBox(width: space8, height: space8);
  
  /// 垂直间距
  static const SizedBox verticalSpaceXs = SizedBox(height: space1);
  static const SizedBox verticalSpaceSm = SizedBox(height: space2);
  static const SizedBox verticalSpaceMd = SizedBox(height: space4);
  static const SizedBox verticalSpaceLg = SizedBox(height: space6);
  static const SizedBox verticalSpaceXl = SizedBox(height: space8);
  static const SizedBox verticalSpaceXxl = SizedBox(height: space10);
  static const SizedBox verticalSpace2xl = SizedBox(height: space10);
  static const SizedBox verticalSpace3xl = SizedBox(height: space12);
  
  /// 水平间距
  static const SizedBox horizontalSpaceXs = SizedBox(width: space1);
  static const SizedBox horizontalSpaceSm = SizedBox(width: space2);
  static const SizedBox horizontalSpaceMd = SizedBox(width: space4);
  static const SizedBox horizontalSpaceLg = SizedBox(width: space6);
  static const SizedBox horizontalSpaceXl = SizedBox(width: space8);
  
  // ==================== 页面布局间距 ====================
  
  /// 页面边距
  static const EdgeInsets pageMargin = EdgeInsets.symmetric(horizontal: space4);
  static const EdgeInsets pageMarginLarge = EdgeInsets.symmetric(horizontal: space6);
  
  /// 页面内边距
  static const EdgeInsets pagePadding = EdgeInsets.all(space4);
  static const EdgeInsets pagePaddingLarge = EdgeInsets.all(space6);
  
  /// 卡片间距
  static const EdgeInsets cardPadding = EdgeInsets.all(space4);
  static const EdgeInsets cardPaddingLarge = EdgeInsets.all(space6);
  static const EdgeInsets cardMargin = EdgeInsets.all(space2);
  
  /// 列表项间距
  static const EdgeInsets listItemPadding = EdgeInsets.symmetric(
    horizontal: space4,
    vertical: space3,
  );
  
  /// 按钮内边距
  static const EdgeInsets buttonPaddingSmall = EdgeInsets.symmetric(
    horizontal: space3,
    vertical: space2,
  );
  static const EdgeInsets buttonPaddingMedium = EdgeInsets.symmetric(
    horizontal: space4,
    vertical: space3,
  );
  static const EdgeInsets buttonPaddingLarge = EdgeInsets.symmetric(
    horizontal: space6,
    vertical: space4,
  );
  
  /// 输入框内边距
  static const EdgeInsets inputPadding = EdgeInsets.all(space4);
  static const EdgeInsets inputPaddingSmall = EdgeInsets.all(space2);
  static const EdgeInsets inputPaddingLarge = EdgeInsets.all(space6);
  
  // ==================== 按钮高度令牌 ====================
  
  /// 按钮高度
  static const double buttonHeightSmall = 36.0;
  static const double buttonHeightMedium = 44.0;
  static const double buttonHeightLarge = 52.0;
  
  /// 图标尺寸
  static const double iconSizeSmall = 16.0;
  static const double iconSizeMedium = 20.0;
  static const double iconSizeLarge = 24.0;
  
  // ==================== 实用方法 ====================
  
  /// 创建自定义的SizedBox
  static SizedBox vertical(double height) => SizedBox(height: height);
  static SizedBox horizontal(double width) => SizedBox(width: width);
  
  /// 创建自定义的EdgeInsets
  static EdgeInsets all(double value) => EdgeInsets.all(value);
  static EdgeInsets symmetric({double vertical = 0, double horizontal = 0}) =>
      EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal);
  static EdgeInsets only({
    final double left = 0,
    final double top = 0,
    final double right = 0,
    final double bottom = 0,
  }) => EdgeInsets.only(left: left, top: top, right: right, bottom: bottom);
}

/// Spacing扩展，提供更便捷的使用方式
extension SpacingExtension on double {
  /// 转换为垂直间距SizedBox
  SizedBox get verticalSpace => SizedBox(height: this);
  
  /// 转换为水平间距SizedBox
  SizedBox get horizontalSpace => SizedBox(width: this);
  
  /// 转换为正方形SizedBox
  SizedBox get gap => SizedBox(width: this, height: this);
  
  /// 转换为EdgeInsets.all
  EdgeInsets get padding => EdgeInsets.all(this);
  
  /// 转换为水平EdgeInsets
  EdgeInsets get horizontalPadding => EdgeInsets.symmetric(horizontal: this);
  
  /// 转换为垂直EdgeInsets
  EdgeInsets get verticalPadding => EdgeInsets.symmetric(vertical: this);
}