import 'package:flutter/material.dart';
import 'package:user_app/core/services/mobile_interaction_service.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 移动端优化的通用按钮组件
class MobileOptimizedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget child;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsets? padding;
  final BorderRadius? borderRadius;
  final String? tooltip;
  final bool isLoading;
  final double? elevation;

 const MobileOptimizedButton({
    super.key,
    required this.onPressed,
    required this.child,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
    this.tooltip,
    this.isLoading = false,
    this.elevation,
  });

  @override
  Widget build(BuildContext context) {
    final button = ElevatedButton(
      onPressed: isLoading ? null : _handlePress,
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: foregroundColor,
        padding: padding ?? SpacingTokens.buttonPaddingMedium,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? ShapeTokens.borderRadiusSm,
        ),
        elevation: elevation ?? 2,
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: isLoading 
        ? const SizedBox(
            width: SpacingTokens.space4,
            height: SpacingTokens.space4,
            child: CircularProgressIndicator(strokeWidth: 2),
          )
        : child,
    );

    if (tooltip != null) {
      return Tooltip(
        message: tooltip!,
        child: button,
      );
    }

    return button;
  }

  Future<void> _handlePress() async {
    await MobileInteractionService.lightImpact();
    onPressed?.call();
  }
}

/// 移动端优化的搜索栏组件
class MobileOptimizedSearchBar extends StatefulWidget {
  final String hintText;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final VoidCallback? onClear;
  final bool autofocus;
  final TextInputType? keyboardType;

 const MobileOptimizedSearchBar({
    super.key,
    required this.hintText,
    this.controller,
    this.onChanged,
    this.onClear,
    this.autofocus = false,
    this.keyboardType,
  });

  @override
  State<MobileOptimizedSearchBar> createState() => _MobileOptimizedSearchBarState();
}

class _MobileOptimizedSearchBarState extends State<MobileOptimizedSearchBar> {
  late TextEditingController _controller;
  bool _showClear = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _controller.addListener(_onTextChanged);
    _showClear = _controller.text.isNotEmpty;
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _showClear) {
      setState(() {
        _showClear = hasText;
      });
    }
    widget.onChanged?.call(_controller.text);
  }

  Future<void> _onClear() async {
    await MobileInteractionService.lightImpact();
    _controller.clear();
    widget.onClear?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: ShapeTokens.inputDecoration(
        color: ColorTokens.surfaceContainer,
        border: Border.all(color: ColorTokens.outline),
      ),
      child: TextField(
        controller: _controller,
        autofocus: widget.autofocus,
        keyboardType: widget.keyboardType,
        decoration: InputDecoration(
          hintText: widget.hintText,
          border: InputBorder.none,
          contentPadding: SpacingTokens.buttonPaddingMedium,
          prefixIcon: const Icon(Icons.search, color: ColorTokens.onSurfaceVariant),
          suffixIcon: _showClear
            ? IconButton(
                onPressed: _onClear,
                icon: const Icon(Icons.clear, color: ColorTokens.onSurfaceVariant),
              )
            : null,
        ),
      ),
    );
  }
}

/// 移动端优化的标签页组件
class MobileOptimizedTabBar extends StatelessWidget {
  final List<MobileTab> tabs;
  final int currentIndex;
  final ValueChanged<int>? onTap;

 const MobileOptimizedTabBar({
    super.key,
    required this.tabs,
    required this.currentIndex,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 48,
      decoration: ShapeTokens.containerDecoration(
        color: ColorTokens.surfaceContainer,
      ),
      child: Row(
        children: tabs.asMap().entries.map((entry) {
          final index = entry.key;
          final tab = entry.value;
          final isSelected = index == currentIndex;

          return Expanded(
            child: GestureDetector(
              onTap: () async {
                await MobileInteractionService.lightImpact();
                onTap?.call(index);
              },
              child: Container(
                decoration: ShapeTokens.containerDecoration(
                  color: isSelected ? ColorTokens.primary : ColorTokens.transparent,
                  borderRadius: ShapeTokens.borderRadiusXs,
                ),
                child: Center(
                  child: Text(
                    tab.label,
                    style: TextStyle(
                      color: isSelected ? ColorTokens.onPrimary : ColorTokens.onSurfaceVariant,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                      fontSize: 14,
                    ),
                    semanticsLabel: tab.semanticLabel,
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }
}

/// 移动端标签页项
class MobileTab {
  final String label;
  final String? semanticLabel;

 const MobileTab({
    required this.label,
    this.semanticLabel,
  });
}

/// 移动端优化的加载指示器
class MobileLoadingIndicator extends StatelessWidget {
  final String? message;
  final double? size;

 const MobileLoadingIndicator({
    super.key,
    this.message,
    this.size,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size ?? SpacingTokens.space8,
            height: size ?? SpacingTokens.space8,
            child: const CircularProgressIndicator(
              strokeWidth: 3,
              valueColor: AlwaysStoppedAnimation<Color>(ColorTokens.primary),
            ),
          ),
          if (message != null) ...[
            SpacingTokens.verticalSpaceMd,
            Text(
              message!,
              style: const TextStyle(
                color: ColorTokens.onSurfaceVariant,
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}

/// 移动端优化的底部弹窗
class MobileBottomSheet {
  static Future<T?> show<T>({
    required BuildContext context,
    required Widget child,
    String? title,
    final bool isDismissible = true,
    final bool enableDrag = true,
  }) {
    return showModalBottomSheet<T>(
      context: context,
      isScrollControlled: true,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: ShapeTokens.borderRadiusTopXl,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (title != null) ...[
              Container(
                padding: SpacingTokens.paddingMd,
                decoration: const BoxDecoration(
                  border: Border(bottom: BorderSide(color: ColorTokens.divider)),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: ColorTokens.onSurface,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close),
                    ),
                  ],
                ),
              ),
            ],
            Flexible(
              child: Container(
               constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.9,
                ),
                child: child,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 移动端优化的列表项组件
class MobileOptimizedListTile extends StatelessWidget {
  final Widget? leading;
  final Widget? title;
  final Widget? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final EdgeInsets? contentPadding;
  final bool selected;
  final Color? selectedColor;
  final Color? tileColor;
  final Color? selectedTileColor;
  final bool enabled;
  final bool dense;

 const MobileOptimizedListTile({
    super.key,
    this.leading,
    this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.contentPadding,
    this.selected = false,
    this.selectedColor,
    this.tileColor,
    this.selectedTileColor,
    this.enabled = true,
    this.dense = false,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: leading,
      title: title,
      subtitle: subtitle,
      trailing: trailing,
      onTap: enabled ? () {
        if (onTap != null) {
          MobileInteractionService.lightImpact();
          onTap!();
        }
      } : null,
      onLongPress: enabled ? () {
        if (onLongPress != null) {
          MobileInteractionService.longPressFeedback();
          onLongPress!();
        }
      } : null,
      contentPadding: contentPadding ?? SpacingTokens.paddingHorizontalMd,
      selected: selected,
      selectedColor: selectedColor,
      tileColor: tileColor,
      selectedTileColor: selectedTileColor,
      enabled: enabled,
      dense: dense,
    );
  }
}