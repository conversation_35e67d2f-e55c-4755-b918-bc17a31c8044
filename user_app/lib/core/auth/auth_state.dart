import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/models/user.dart';

/// 认证状态枚举
enum AuthStatus {
  unknown,        // 初始状态，尚未确定
  unauthenticated, // 未认证
  authenticated,   // 已认证
}

/// 认证状态单一事实来源
/// 负责管理认证状态、用户信息、token和意图跳转路由
class AuthState extends ChangeNotifier {
  static const String _tokenKey = 'access_token';
  static const String _userKey = 'cached_user';
  static const String _refreshTokenKey = 'refresh_token';
  
  AuthStatus _status = AuthStatus.unknown;
  User? _user;
  String? _token;
  String? _refreshToken;
  String? _intendedLocation; // 登录后要回跳的路由
  
  final SharedPreferences _prefs;
  
  AuthState(this._prefs) {
    _initialize();
  }
  
  // ==================== Getters ====================
  
  /// 认证状态
  AuthStatus get status => _status;
  
  /// 是否已登录
  bool get isLoggedIn => _status == AuthStatus.authenticated && _token != null;
  
  /// 是否未登录
  bool get isUnauthenticated => _status == AuthStatus.unauthenticated;
  
  /// 是否状态未知
  bool get isUnknown => _status == AuthStatus.unknown;
  
  /// 当前用户
  User? get user => _user;
  
  /// 访问令牌
  String? get token => _token;
  
  /// 刷新令牌
  String? get refreshToken => _refreshToken;
  
  /// 登录后意图跳转的路由
  String? get intendedLocation => _intendedLocation;
  
  // ==================== 初始化 ====================
  
  /// 从本地存储初始化认证状态
  Future<void> _initialize() async {
    try {
      // 加载token
      _token = _prefs.getString(_tokenKey);
      _refreshToken = _prefs.getString(_refreshTokenKey);
      
      // 加载用户信息
      final userJson = _prefs.getString(_userKey);
      if (userJson != null) {
        final userData = jsonDecode(userJson);
        _user = User.fromJson(userData);
      }
      
      // 根据token存在与否确定初始状态
      _status = (_token != null && _user != null) 
          ? AuthStatus.authenticated 
          : AuthStatus.unauthenticated;
      
      debugPrint('🔐 [AuthState] 初始化完成: ${_status.name}, user: ${_user?.name}, token: ${_token != null}');
      
    } catch (e) {
      debugPrint('❌ [AuthState] 初始化失败: $e');
      _status = AuthStatus.unauthenticated;
    }
    
    notifyListeners();
  }
  
  // ==================== 认证操作 ====================
  
  /// 登录成功
  Future<void> setAuthenticated({
    required User user,
    required String token,
    String? refreshToken,
  }) async {
    _user = user;
    _token = token;
    _refreshToken = refreshToken;
    _status = AuthStatus.authenticated;
    
    // 持久化存储
    await _saveToStorage();
    
    debugPrint('✅ [AuthState] 用户登录: ${user.name}');
    notifyListeners();
  }
  
  /// 登出
  Future<void> setUnauthenticated() async {
    _user = null;
    _token = null;
    _refreshToken = null;
    _intendedLocation = null;
    _status = AuthStatus.unauthenticated;
    
    // 清除存储
    await _clearStorage();
    
    debugPrint('🚪 [AuthState] 用户登出');
    notifyListeners();
  }
  
  /// 更新用户信息
  Future<void> updateUser(User user) async {
    if (_status != AuthStatus.authenticated) return;
    
    _user = user;
    await _saveUserToStorage();
    
    debugPrint('🔄 [AuthState] 更新用户信息: ${user.name}');
    notifyListeners();
  }
  
  /// 更新token
  Future<void> updateToken(String token, {String? refreshToken}) async {
    if (_status != AuthStatus.authenticated) return;
    
    _token = token;
    if (refreshToken != null) {
      _refreshToken = refreshToken;
    }
    
    await _saveTokenToStorage();
    
    debugPrint('🔄 [AuthState] Token已更新');
    notifyListeners();
  }
  
  // ==================== 意图跳转管理 ====================
  
  /// 设置登录后要跳转的路由
  void setIntendedLocation(String location) {
    _intendedLocation = location;
    debugPrint('📍 [AuthState] 设置登录后跳转路由: $location');
  }
  
  /// 清除意图跳转路由并返回
  String? consumeIntendedLocation() {
    final location = _intendedLocation;
    _intendedLocation = null;
    debugPrint('📍 [AuthState] 消费登录后跳转路由: $location');
    return location;
  }
  
  // ==================== Token 验证 ====================
  
  /// 验证当前token是否有效
  /// 此方法应该由具体的业务层（如 AuthService）来调用
  /// AuthState 只负责状态管理，不负责网络请求
  Future<void> markTokenInvalid() async {
    if (_status == AuthStatus.authenticated) {
      debugPrint('❌ [AuthState] Token已失效，标记为未认证');
      await setUnauthenticated();
    }
  }
  
  // ==================== 存储操作 ====================
  
  /// 保存所有数据到本地存储
  Future<void> _saveToStorage() async {
    await Future.wait([
      _saveTokenToStorage(),
      _saveUserToStorage(),
    ]);
  }
  
  /// 保存token到本地存储
  Future<void> _saveTokenToStorage() async {
    if (_token != null) {
      await _prefs.setString(_tokenKey, _token!);
    }
    if (_refreshToken != null) {
      await _prefs.setString(_refreshTokenKey, _refreshToken!);
    }
  }
  
  /// 保存用户信息到本地存储
  Future<void> _saveUserToStorage() async {
    if (_user != null) {
      final userJson = jsonEncode(_user!.toJson());
      await _prefs.setString(_userKey, userJson);
    }
  }
  
  /// 清除本地存储
  Future<void> _clearStorage() async {
    await Future.wait([
      _prefs.remove(_tokenKey),
      _prefs.remove(_refreshTokenKey),
      _prefs.remove(_userKey),
    ]);
  }
  
  // ==================== 调试信息 ====================
  
  @override
  String toString() {
    return 'AuthState{status: ${_status.name}, user: ${_user?.name}, hasToken: ${_token != null}, intendedLocation: $_intendedLocation}';
  }
}