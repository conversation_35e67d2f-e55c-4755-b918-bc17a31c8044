import 'package:flutter/material.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/navigation/navigation_manager.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

/// 深度链接处理器
/// 支持从外部URL直接跳转到应用内特定页面
class DeepLinkHandler {
  static final DeepLinkHandler _instance = DeepLinkHandler._internal();
  DeepLinkHandler._internal();
  factory DeepLinkHandler() => _instance;

  // 深度链接模式映射
  static final Map<RegExp, DeepLinkRoute> _patterns = {
    // 钓点详情: fishing://spot/123
    RegExp(r'^fishing://spot/(\d+)$'): DeepLinkRoute(
      builder: (matches) => '${AppRoutes.fishingSpotDetail}?spotId=${matches[1]}',
      requiresAuth: false,
    ),
    
    // 用户资料: fishing://user/456  
    RegExp(r'^fishing://user/(\d+)$'): DeepLinkRoute(
      builder: (matches) => '${AppRoutes.profile}/${matches[1]}',
      requiresAuth: false,
    ),
    
    // 动态详情: fishing://moment/789
    RegExp(r'^fishing://moment/(\d+)$'): DeepLinkRoute(
      builder: (matches) => '${AppRoutes.momentDetail}?momentId=${matches[1]}',
      requiresAuth: false,
    ),
    
    // 创建钓点: fishing://create/spot
    RegExp(r'^fishing://create/spot$'): DeepLinkRoute(
      builder: (_) => AppRoutes.createSpot,
      requiresAuth: true,
    ),
    
    // 聊天: fishing://chat/user/123
    RegExp(r'^fishing://chat/user/(\d+)$'): DeepLinkRoute(
      builder: (matches) => '/chat/simple/c2c_${matches[1]}',
      requiresAuth: true,
    ),
    
    // 活动详情: fishing://activity/456
    RegExp(r'^fishing://activity/(\d+)$'): DeepLinkRoute(
      builder: (matches) => '${AppRoutes.activityDetail}?activityId=${matches[1]}',
      requiresAuth: false,
    ),
    
    // 搜索: fishing://search?type=spot&q=keyword
    RegExp(r'^fishing://search\?(.+)$'): DeepLinkRoute(
      builder: (matches) {
        final params = Uri.splitQueryString(matches[1]!);
        final type = params['type'] ?? 'universal';
        final query = params['q'] ?? '';
        
        return switch (type) {
          'spot' => '${AppRoutes.spotSearch}?q=$query',
          'user' => '${AppRoutes.userSearch}?q=$query',
          'moment' => '${AppRoutes.momentSearch}?q=$query',
          _ => '${AppRoutes.universalSearch}?q=$query',
        };
      },
      requiresAuth: false,
    ),
  };

  /// 处理深度链接
  Future<bool> handleDeepLink(BuildContext context, String url) async {
    debugPrint('🔗 处理深度链接: $url');
    
    // 尝试匹配每个模式
    for (final entry in _patterns.entries) {
      final pattern = entry.key;
      final route = entry.value;
      final match = pattern.firstMatch(url);
      
      if (match != null) {
        // 提取匹配组
        final matches = <String>[];
        for (int i = 0; i <= match.groupCount; i++) {
          matches.add(match.group(i) ?? '');
        }
        
        // 构建目标路由
        final targetRoute = route.builder(matches);
        
        // 检查是否需要认证
        if (route.requiresAuth) {
          // TODO: 检查用户登录状态
          final isAuthenticated = await _checkAuthentication(context);
          if (!isAuthenticated) {
            // 保存深度链接，登录后跳转
            _saveDeepLinkForLater(url);
            context.navigateTo('${AppRoutes.login}?from=$targetRoute');
            return true;
          }
        }
        
        // 导航到目标页面
        NavigationManager().navigateTo(context, targetRoute);
        
        // 记录深度链接使用情况
        _trackDeepLinkUsage(url, targetRoute);
        
        return true;
      }
    }
    
    debugPrint('⚠️ 无法识别的深度链接: $url');
    return false;
  }

  /// 解析通用URL（HTTP/HTTPS）
  Future<bool> handleUniversalLink(BuildContext context, String url) async {
    final uri = Uri.tryParse(url);
    if (uri == null) return false;
    
    // 支持的域名
    const supportedHosts = ['fishing.app', 'www.fishing.app', 'link.fishing.app'];
    
    if (!supportedHosts.contains(uri.host)) {
      return false;
    }
    
    // 转换为应用内路由
    final appRoute = _convertWebUrlToAppRoute(uri);
    if (appRoute != null) {
      NavigationManager().navigateTo(context, appRoute);
      return true;
    }
    
    return false;
  }

  /// 转换Web URL到应用路由
  String? _convertWebUrlToAppRoute(Uri uri) {
    final path = uri.path;
    final params = uri.queryParameters;
    
    // 钓点详情页: https://fishing.app/spot/123
    if (path.startsWith('/spot/')) {
      final spotId = path.substring(6);
      return '${AppRoutes.fishingSpotDetail}?spotId=$spotId';
    }
    
    // 用户资料页: https://fishing.app/user/456
    if (path.startsWith('/user/')) {
      final userId = path.substring(6);
      return '${AppRoutes.profile}/$userId';
    }
    
    // 搜索页: https://fishing.app/search?q=keyword
    if (path == '/search' && params.containsKey('q')) {
      return '${AppRoutes.universalSearch}?q=${params['q']}';
    }
    
    return null;
  }

  /// 检查用户认证状态
  Future<bool> _checkAuthentication(BuildContext context) async {
    // TODO: 实现认证检查逻辑
    return true;
  }

  /// 保存深度链接供登录后使用
  void _saveDeepLinkForLater(String url) {
    // TODO: 保存到本地存储
    debugPrint('💾 保存深度链接供登录后使用: $url');
  }

  /// 获取保存的深度链接
  Future<String?> getSavedDeepLink() async {
    // TODO: 从本地存储获取
    return null;
  }

  /// 清除保存的深度链接
  void clearSavedDeepLink() {
    // TODO: 清除本地存储
  }

  /// 跟踪深度链接使用情况
  void _trackDeepLinkUsage(String originalUrl, String targetRoute) {
    debugPrint('📊 深度链接使用统计:');
    debugPrint('  原始URL: $originalUrl');
    debugPrint('  目标路由: $targetRoute');
    // TODO: 发送统计数据
  }

  /// 生成分享链接
  String generateShareLink({
    required DeepLinkType type,
    required String id,
    Map<String, String>? params,
  }) {
    final baseUrl = 'https://fishing.app';
    
    switch (type) {
      case DeepLinkType.spot:
        return '$baseUrl/spot/$id';
      case DeepLinkType.user:
        return '$baseUrl/user/$id';
      case DeepLinkType.moment:
        return '$baseUrl/moment/$id';
      case DeepLinkType.activity:
        return '$baseUrl/activity/$id';
      case DeepLinkType.search:
        final query = params?['q'] ?? '';
        final type = params?['type'] ?? 'universal';
        return '$baseUrl/search?type=$type&q=${Uri.encodeComponent(query)}';
    }
  }
}

/// 深度链接路由配置
class DeepLinkRoute {
  final String Function(List<String> matches) builder;
  final bool requiresAuth;

  DeepLinkRoute({
    required this.builder,
    this.requiresAuth = false,
  });
}

/// 深度链接类型
enum DeepLinkType {
  spot,
  user,
  moment,
  activity,
  search,
}