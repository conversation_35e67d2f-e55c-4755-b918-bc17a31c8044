import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_manager.dart';

/// 为 BuildContext 提供统一、语义清晰的导航入口。
/// 约束：UI 侧不直接操作 GoRouter，所有导航统一转发到 NavigationManager。
extension NavigationContextExtension on BuildContext {
  /// Push：导航到新页面（等同 GoRouter.push）
  void navigateTo(
    String route, {
    Object? extra,
  }) {
    NavigationManager().navigateTo(this, route, extra: extra);
  }

  /// Push with result：导航到新页面并等待返回值
  Future<T?> navigateToWithResult<T>(
    String route, {
    Object? extra,
  }) {
    return GoRouter.of(this).push<T>(route, extra: extra);
  }

  /// Replace：替换当前页面（等同 GoRouter.pushReplacement）
  void replaceWith(
    String route, {
    Object? extra,
  }) {
    NavigationManager().replaceTo(this, route, extra: extra);
  }

  /// 智能返回：
  /// - 若当前路由栈可回退 -> pop()
  /// - 否则 -> 回到首页（主 Tab 0）
  void smartGoBack() {
    NavigationManager().popOrBackToTab(this);
  }

  /// “Pop Until”的等价实现：重置到目标路由（等同 GoRouter.go）
  /// 注意：go 语义不会额外压栈，会把中间页面全部清掉。
  void goUntilRoute(String targetRoute) {
    NavigationManager().goTo(this, targetRoute);
  }

  /// 兼容旧 API：命名具有 PopUntil 语义，但实现已改为 go（正确语义）。
  /// 请迁移到 `goUntilRoute(targetRoute)`。
  @Deprecated(
      'Use goUntilRoute(targetRoute); this now performs a go() to the target route.')
  void popUntilRoute(String targetRoute) {
    NavigationManager().goTo(this, targetRoute);
  }

  /// （可选）根据历史回退到最近一次出现的目标路由；
  /// 若历史中不存在则 go 到目标。
  /// - [inclusive]：true 时连同目标也弹出（再往前一层）
  /// - [matchPrefix]：true 时按前缀匹配父级（如 /activities/:id -> /activities）
  void backToRoute(
    String targetRoute, {
    bool inclusive = false,
    bool matchPrefix = false,
  }) {
    NavigationManager().backTo(
      this,
      targetRoute,
      inclusive: inclusive,
      matchPrefix: matchPrefix,
    );
  }

  /// 回到首页（主 Tab 0）
  void goHome() {
    NavigationManager().navigateToTab(this, 0);
  }

  /// 便捷只读：当前 Tab 下标（转发 Manager）
  int get currentTabIndex => NavigationManager().currentTabIndex;

  /// 便捷只读：是否可后退（基于历史）
  bool get canGoBack => NavigationManager().canGoBack;

  /// 打开模态框 - 使用 showModalBottomSheet
  void openModal({
    required Widget child,
    bool isScrollControlled = true,
    bool useSafeArea = true,
    double? height,
  }) {
    showModalBottomSheet(
      context: this,
      isScrollControlled: isScrollControlled,
      useSafeArea: useSafeArea,
      builder: (context) => height != null
          ? SizedBox(height: height, child: child)
          : child,
    );
  }
}
