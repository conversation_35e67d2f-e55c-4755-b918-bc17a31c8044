import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

enum SlideDirection { up, down, left, right }

class RouteAnimations {
  static CustomTransitionPage<T> fade<T>({
    required Widget child,
    required LocalKey key,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeInOut,
  }) {
    return CustomTransitionPage<T>(
      key: key,
      child: child,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(parent: animation, curve: curve),
          child: child,
        );
      },
    );
  }

  static CustomTransitionPage<T> bottomSlide<T>({
    required Widget child,
    required LocalKey key,
    Duration duration = const Duration(milliseconds: 250),
    Curve curve = Curves.easeOutCubic,
  }) {
    final tween = Tween<Offset>(begin: const Offset(0, 1), end: Offset.zero);
    return CustomTransitionPage<T>(
      key: key,
      child: child,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position:
              CurvedAnimation(parent: animation, curve: curve).drive(tween),
          child: child,
        );
      },
    );
  }

  static CustomTransitionPage<T> slide<T>({
    required Widget child,
    required LocalKey key,
    SlideDirection direction = SlideDirection.right,
    Duration duration = const Duration(milliseconds: 200),
    Curve curve = Curves.easeOutCubic,
  }) {
    final begin = switch (direction) {
      SlideDirection.up => const Offset(0, 1),
      SlideDirection.down => const Offset(0, -1),
      SlideDirection.left => const Offset(1, 0),
      SlideDirection.right => const Offset(-1, 0), // 修正：从左侧进场
    };
    return CustomTransitionPage<T>(
      key: key,
      child: child,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        return SlideTransition(
          position: CurvedAnimation(parent: animation, curve: curve)
              .drive(Tween<Offset>(begin: begin, end: Offset.zero)),
          child: child,
        );
      },
    );
  }

  static CustomTransitionPage<T> scale<T>({
    required Widget child,
    required LocalKey key,
    Duration duration = const Duration(milliseconds: 200),
  }) {
    final scaleTween = Tween<double>(begin: 0.8, end: 1);
    final fadeTween = Tween<double>(begin: 0, end: 1);
    return CustomTransitionPage<T>(
      key: key,
      child: child,
      transitionDuration: duration,
      reverseTransitionDuration: duration,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        final scale =
            CurvedAnimation(parent: animation, curve: Curves.easeOutBack)
                .drive(scaleTween);
        final fade = CurvedAnimation(parent: animation, curve: Curves.easeOut)
            .drive(fadeTween);
        return ScaleTransition(
            scale: scale, child: FadeTransition(opacity: fade, child: child));
      },
    );
  }
}
