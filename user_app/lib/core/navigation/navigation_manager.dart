import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';

import 'router_meta.dart';

class NavigationStateItem {
  /// 规范化后的 location（只保留 path + 排序后的 query；无 fragment）
  final String location;
  final DateTime timestamp;
  final String? title;

  const NavigationStateItem({
    required this.location,
    required this.timestamp,
    required this.title,
  });
}

/// 统一导航与历史的状态容器
/// 关键点：历史写入只在 Observer 回调中完成（单一事实来源）
class NavigationManager extends ChangeNotifier {
  static final NavigationManager _instance = NavigationManager._internal();

  factory NavigationManager() => _instance;

  NavigationManager._internal();

  // Tab 索引/路由（按你项目里的）
  static const Map<int, String> _tabRoutes = {
    0: '/fishing_spots',
    1: '/community',
    2: '/activities',
    3: '/message',
    4: '/mine',
  };

  int _currentTabIndex = 0;

  int get currentTabIndex => _currentTabIndex;

  final List<NavigationStateItem> _history = [];

  List<NavigationStateItem> get history => List.unmodifiable(_history);

  String? _lastRecordedLocation; // 去重哨兵（存放规范化后的 location）

  // —— 工具：规范化 location（只保留 path + 排序后的 query；去掉 fragment）—— //
  static String _normalize(String location) {
    final u = Uri.parse(location);
    if (u.queryParameters.isEmpty) return u.path;
    final sorted = Map.fromEntries(
      u.queryParametersAll.entries.toList()
        ..sort((a, b) => a.key.compareTo(b.key)),
    );
    final q = Uri(
        queryParameters: sorted.map(
      (k, v) => MapEntry(k, v.length == 1 ? v.first : v),
    )).query;
    return '${u.path}?$q';
  }

  // —— Tab 计算：根据 path 前缀 —— //
  static int calculateTabIndex(String location) {
    final p = Uri.parse(location).path;
    if (p.startsWith('/fishing_spots')) return 0;
    if (p.startsWith('/community')) return 1;
    if (p.startsWith('/activities')) return 2;
    if (p.startsWith('/message')) return 3;
    if (p.startsWith('/mine')) return 4;
    return 0;
  }

  // —— 标题：优先使用注册的 titleBuilder —— //
  String titleFor(String location) {
    final uri = Uri.parse(location);
    final meta = resolveMetaForLocation(location);
    if (meta != null) {
      final params = extractPathParams(meta.pathPattern, uri.path);
      return meta.titleBuilder(TitleContext(uri: uri, pathParameters: params));
    }
    // 未注册路由的兜底
    return '';
  }

  // —— 外部（Observer）回调 —— //
  void onRouteChange(String rawLocation) {
    final location = _normalize(rawLocation);
    if (location == _lastRecordedLocation) return;
    _lastRecordedLocation = location;

    _currentTabIndex = calculateTabIndex(location);
    _record(location);
    notifyListeners();
  }

  // —— 对外导航 API（不直接写历史，让 Observer 写） —— //
  /// go：重置到目标（等价 GoRouter.go）——“pop until” 的正确实现
  void goTo(BuildContext context, String location, {bool withHaptic = true}) {
    if (withHaptic && !kIsWeb) HapticFeedback.lightImpact();
    GoRouter.of(context).go(location);
  }

  /// push：入栈
  void navigateTo(BuildContext context, String location,
      {Object? extra, bool withHaptic = true}) {
    if (withHaptic && !kIsWeb) HapticFeedback.lightImpact();
    GoRouter.of(context).push(location, extra: extra);
  }

  /// pushReplacement：替换当前页
  void replaceTo(BuildContext context, String location,
      {Object? extra, bool withHaptic = true}) {
    if (withHaptic && !kIsWeb) HapticFeedback.lightImpact();
    GoRouter.of(context).pushReplacement(location, extra: extra);
  }

  /// 切换底部 Tab（用 go 语义避免堆栈增长）
  void navigateToTab(BuildContext context, int index,
      {bool withHaptic = true}) {
    if (index < 0 || index > 4 || index == _currentTabIndex) return;
    if (withHaptic && !kIsWeb) HapticFeedback.lightImpact();
    final target = _tabRoutes[index]!;
    _currentTabIndex = index;
    GoRouter.of(context).go(target);
  }

  /// 智能返回：能 pop 就 pop，否则回退到兜底 Tab
  void popOrBackToTab(BuildContext context,
      {int fallbackTab = 0, bool withHaptic = true}) {
    if (withHaptic && !kIsWeb) HapticFeedback.lightImpact();
    final router = GoRouter.of(context);
    if (router.canPop()) {
      router.pop(); // 历史由 Observer 记录
    } else {
      navigateToTab(context, fallbackTab, withHaptic: false);
    }
  }

  /// 是否可以后退（基于历史）
  bool get canGoBack => _history.length > 1;

  /// （可选）回退到历史中最近一次出现的目标路由；找不到则 go 到目标
  Future<void> backTo(
    BuildContext context,
    String target, {
    bool inclusive = false,
    bool matchPrefix = false,
    bool withHaptic = true,
  }) async {
    if (withHaptic && !kIsWeb) HapticFeedback.lightImpact();
    final router = GoRouter.of(context);
    if (_history.isEmpty) {
      router.go(target);
      return;
    }

    String _pathOf(String loc) => Uri.parse(loc).path;
    final targetPath = _pathOf(target);

    int idx = -1;
    for (int i = _history.length - 1; i >= 0; i--) {
      final hp = _pathOf(_history[i].location);
      final ok = matchPrefix ? hp.startsWith(targetPath) : (hp == targetPath);
      if (ok) {
        idx = i;
        break;
      }
    }

    if (idx < 0) {
      router.go(target);
      return;
    }

    var pops = (_history.length - 1) - idx + (inclusive ? 1 : 0);
    while (pops-- > 0 && router.canPop()) {
      router.pop();
    }
    if (pops >= 0) {
      router.go(target); // 栈不够时兜底
    }
  }

  // —— 历史写入（内部，仅 Observer 调用） —— //
  void _record(String normalizedLocation) {
    final now = DateTime.now();
    final item = NavigationStateItem(
      location: normalizedLocation,
      timestamp: now,
      title: titleFor(normalizedLocation),
    );

    if (_history.isEmpty) {
      _history.add(item);
    } else {
      final last = _history.last;
      if (last.location == item.location) {
        _history[_history.length - 1] = item; // 刷新标题/时间戳
      } else {
        _history.add(item);
      }
    }

    // 上限，避免无界增长
    if (_history.length > 50) {
      _history.removeRange(0, _history.length - 50);
    }
  }

  // 清理过期（可手动调用）
  void cleanExpiredHistory({Duration maxAge = const Duration(hours: 24)}) {
    final now = DateTime.now();
    _history.removeWhere((e) => now.difference(e.timestamp) > maxAge);
    notifyListeners();
  }
}

/// 更精简的 Observer：pop 后 location 已更新，直接读取即可
class NavObserver extends NavigatorObserver {
  GoRouter get _router => GoRouter.of(navigator!.context);

  void _notify() {
    final String location =
        _router.routerDelegate.currentConfiguration.uri.toString();
    NavigationManager().onRouteChange(location);
  }

  @override
  void didPush(Route route, Route? previousRoute) => _notify();

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) => _notify();

  @override
  void didPop(Route route, Route? previousRoute) => _notify();

  @override
  void didRemove(Route route, Route? previousRoute) => _notify();
}
