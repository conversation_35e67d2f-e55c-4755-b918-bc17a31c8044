import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';

/// 你可在 title 内部需要的上下文：URI + 路径参数
class TitleContext {
  final Uri uri;
  final Map<String, String> pathParameters;
  const TitleContext({required this.uri, required this.pathParameters});
}

/// 标题生成函数：根据当前 URI 和提取的 path 参数生成标题
typedef TitleBuilder = String Function(TitleContext ctx);

class RouteMeta {
  final String name;
  final String pathPattern; // 声明时的 '/detail/:id' 等
  final TitleBuilder titleBuilder;

  const RouteMeta({
    required this.name,
    required this.pathPattern,
    required this.titleBuilder,
  });
}

/// 全局注册表（路由名 -> Meta）
final Map<String, RouteMeta> routeMetaRegistry = {};

/// 根据 location（/a/b?...）解析到一个 RouteMeta（基于 pathPattern 匹配）
/// 简单按段匹配，优先最长命中（更具体的优先）
RouteMeta? resolveMetaForLocation(String location) {
  final uri = Uri.parse(location);
  final path = uri.path;
  RouteMeta? best;
  int bestScore = -1;

  for (final meta in routeMetaRegistry.values) {
    final ok = _matchPattern(meta.pathPattern, path);
    if (ok) {
      final score = meta.pathPattern.length; // 粗略：pattern 越长越具体
      if (score > bestScore) {
        best = meta;
        bestScore = score;
      }
    }
  }
  return best;
}

/// 从 pattern 提取 pathParameters（只处理 ':id' 这类）
Map<String, String> extractPathParams(String pattern, String path) {
  final params = <String, String>{};
  final pSegs = pattern.split('/').where((e) => e.isNotEmpty).toList();
  final sSegs = path.split('/').where((e) => e.isNotEmpty).toList();
  for (int i = 0; i < pSegs.length && i < sSegs.length; i++) {
    final seg = pSegs[i];
    if (seg.startsWith(':') && seg.length > 1) {
      params[seg.substring(1)] = sSegs[i];
    }
  }
  return params;
}

bool _matchPattern(String pattern, String path) {
  final pSegs = pattern.split('/').where((e) => e.isNotEmpty).toList();
  final sSegs = path.split('/').where((e) => e.isNotEmpty).toList();
  if (sSegs.length < pSegs.length) return false;
  for (int i = 0; i < pSegs.length; i++) {
    final p = pSegs[i];
    final s = sSegs[i];
    if (p.startsWith(':')) continue;
    if (p != s) return false;
  }
  // 允许 path 更长（当 pattern 是父级）
  return true;
}

/// 声明路由时使用：自动把 titleBuilder 和 pattern 与 name 绑定
GoRoute metaRoute({
  required String path,
  required String name,
  Widget Function(BuildContext, GoRouterState)? builder,
  Page<dynamic> Function(BuildContext, GoRouterState)? pageBuilder,
  required TitleBuilder title,
  List<RouteBase> routes = const [],
}) {
  assert(builder != null || pageBuilder != null, 'builder or pageBuilder must be provided');
  routeMetaRegistry[name] = RouteMeta(
    name: name,
    pathPattern: path,
    titleBuilder: title,
  );
  return GoRoute(
    path: path,
    name: name,
    builder: builder,
    pageBuilder: pageBuilder,
    routes: routes,
  );
}