import 'package:algoliasearch/algoliasearch_lite.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:user_app/config/global_config.dart';
import 'package:user_app/features/album/view_models/preview_image_view_model.dart';
import 'package:user_app/features/auth/data/session_api.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/features/auth/providers/login_view_model.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/providers/user_profile_view_model.dart';
import 'package:user_app/features/auth/services/session_service.dart';
import 'package:user_app/features/auth/services/verification_code_service.dart';
import 'package:user_app/features/bookmarks/data/bookmark_api.dart';
import 'package:user_app/features/bookmarks/view_models/bookmark_view_model.dart';
import 'package:user_app/features/chat/data/chat_api.dart';
import 'package:user_app/features/chat/services/chat_service.dart';
import 'package:user_app/features/chat/view_models/chat_view_model.dart';
import 'package:user_app/features/community/data/comment_api.dart';
import 'package:user_app/features/community/services/comment_service.dart';
import 'package:user_app/features/community/view_models/comment_view_model.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/features/fishing_plans/data/fishing_plan_api.dart';
import 'package:user_app/features/fishing_plans/services/fishing_plan_service.dart';
import 'package:user_app/features/fishing_plans/services/plan_config_service.dart';
import 'package:user_app/features/fishing_plans/services/plan_reminder_service.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/fishing_spots/data/fishing_spot_api.dart';
import 'package:user_app/features/fishing_spots/services/fish_type_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_recommend_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_search_service.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/view_models/create_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/location_selection_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/map_state_provider.dart';
import 'package:user_app/features/fishing_spots/view_models/moment_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/search_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/locations/services/region_service.dart';
import 'package:user_app/features/locations/view_models/region_view_model.dart';
import 'package:user_app/features/map/data/map_api.dart';
import 'package:user_app/features/moments/data/moment_api.dart';
import 'package:user_app/features/moments/services/moment_service.dart';
import 'package:user_app/features/notifications/data/notification_api.dart';
import 'package:user_app/features/notifications/services/notification_service.dart';
import 'package:user_app/features/notifications/view_models/notification_view_model.dart';
import 'package:user_app/features/photo/data/photo_api.dart';
import 'package:user_app/features/profile/view_models/other_profile_page_view_model.dart';
import 'package:user_app/features/reports/data/report_api.dart';
import 'package:user_app/features/search/data/search_api.dart';
import 'package:user_app/features/search/services/algolia_search_service.dart';
import 'package:user_app/features/search/services/search_service.dart';
import 'package:user_app/features/social/data/social_insights_api.dart';
import 'package:user_app/features/social/data/user_follow_api.dart';
import 'package:user_app/features/social/services/social_insights_service.dart';
import 'package:user_app/features/social/view_models/follow_button_view_model.dart';
import 'package:user_app/features/social/view_models/social_insights_view_model.dart';
import 'package:user_app/features/statistics/data/fishing_statistics_api.dart';
import 'package:user_app/features/statistics/data/statistics_api.dart';
import 'package:user_app/features/statistics/services/fishing_statistics_service.dart';
import 'package:user_app/features/statistics/services/statistics_service.dart';
import 'package:user_app/features/tips/view_models/tips_view_model.dart';
import 'package:user_app/features/user/data/user_api.dart';
import 'package:user_app/features/user/data/user_profile_api.dart';
import 'package:user_app/features/user/services/user_service.dart';
import 'package:user_app/features/user/view_models/album_view_model.dart';
import 'package:user_app/features/user/view_models/fans_view_model.dart';
import 'package:user_app/features/user/view_models/following_view_model.dart';
import 'package:user_app/features/user/view_models/personal_moment_list_view_model.dart';
import 'package:user_app/features/user/view_models/user_profile_view_model.dart'
    as user_profile;
import 'package:user_app/services/fee_type_service.dart';
import 'package:user_app/services/fishing_method_service.dart';
import 'package:user_app/services/plan_template_service.dart';
import 'package:user_app/shared/data/fish_type_api.dart';
import 'package:user_app/shared/data/fishing_recommend_api.dart';
import 'package:user_app/shared/data/oss_api.dart';
import 'package:user_app/shared/data/region_api.dart';
import 'package:user_app/shared/data/sms_api.dart';
import 'package:user_app/shared/services/app_service.dart';
import 'package:user_app/shared/services/map_service.dart';
import 'package:user_app/shared/services/oss_service.dart';
import 'package:user_app/shared/services/sms_service.dart';

import 'package:user_app/features/fishing_spots/view_models/publish_moment_view_model.dart';

final GetIt getIt = GetIt.instance;

class ServiceRegistration<T extends Object> {
  final T Function(GetIt) factory;

  const ServiceRegistration(this.factory);
}

class _DIRegistration {
  static void _registerApis(GetIt i) {
    i
      ..registerLazySingleton<ChatApi>(() => ChatApi(i<Dio>()))
      ..registerLazySingleton<CommentApi>(() => CommentApi(i<Dio>()))
      ..registerLazySingleton<MapApi>(() => MapApi(i<Dio>()))
      ..registerLazySingleton<MomentApi>(() => MomentApi(i<Dio>()))
      ..registerLazySingleton<OssApi>(() => OssApi(i<Dio>()))
      ..registerLazySingleton<SessionApi>(() => SessionApi(i<Dio>()))
      ..registerLazySingleton<SmsApi>(() => SmsApi(i<Dio>()))
      ..registerLazySingleton<SocialInsightsApi>(
          () => SocialInsightsApi(i<Dio>()))
      ..registerLazySingleton<StatisticsApi>(() => StatisticsApi(i<Dio>()))
      ..registerLazySingleton<UserApi>(() => UserApi(i<Dio>()))
      ..registerLazySingleton<UserFollowApi>(() => UserFollowApi(i<Dio>()))
      ..registerLazySingleton<PhotoApi>(() => PhotoApi(i<Dio>()))
      ..registerLazySingleton<FishingSpotApi>(() => FishingSpotApi(i<Dio>()))
      ..registerLazySingleton<FishTypeApi>(() => FishTypeApi(i<Dio>()))
      ..registerLazySingleton<FishingRecommendApi>(
          () => FishingRecommendApi(i<Dio>()))
      ..registerLazySingleton<SearchApi>(() => SearchApi(i<Dio>()))
      ..registerLazySingleton<BookmarkApi>(() => BookmarkApi(i<Dio>()))
      ..registerLazySingleton<NotificationApi>(() => NotificationApi(i<Dio>()))
      ..registerLazySingleton<ReportApi>(() => ReportApi(i<Dio>()))
      ..registerLazySingleton<UserProfileApi>(() => UserProfileApi(i<Dio>()))
      ..registerLazySingleton<FishingPlanApi>(() => FishingPlanApi(i<Dio>()))
      ..registerLazySingleton<FishingStatisticsApi>(
          () => FishingStatisticsApi(i<Dio>()))
      ..registerLazySingleton<RegionApi>(() => RegionApi(i<Dio>()));
  }

  static void _registerServices(GetIt i) {
    i
      ..registerLazySingleton<ChatService>(() => ChatService(i<ChatApi>()))
      ..registerLazySingleton<CommentService>(
          () => CommentService(i<CommentApi>()))
      ..registerLazySingleton<MapService>(() => MapService(i<MapApi>()))
      ..registerLazySingleton<MomentService>(
          () => MomentService(i<MomentApi>()))
      ..registerLazySingleton<OssService>(() => OssService(ossApi: i<OssApi>()))
      ..registerLazySingleton<SearchService>(
          () => SearchService(i<SearchApi>()))
      ..registerLazySingleton<AlgoliaSearchService>(
          () => AlgoliaSearchService())
      ..registerLazySingleton<SessionService>(
          () => SessionService(i<SessionApi>()))
      ..registerLazySingleton<SmsService>(() => SmsService(i<SmsApi>()))
      ..registerLazySingleton<SocialInsightsService>(
          () => SocialInsightsService(i<SocialInsightsApi>()))
      ..registerLazySingleton<StatisticsService>(
          () => StatisticsService(i<StatisticsApi>()))
      ..registerLazySingleton<UserService>(() =>
          UserService(i<UserApi>(), i<UserFollowApi>(), i<SharedPreferences>()))
      ..registerLazySingleton<FishingSpotService>(
          () => FishingSpotService(i<FishingSpotApi>(), i<SearchService>()))
      ..registerLazySingleton<FishTypeService>(
          () => FishTypeService(i<FishTypeApi>()))
      ..registerLazySingleton<FishingRecommendService>(
          () => FishingRecommendService(i<FishingRecommendApi>()))
      ..registerLazySingleton<FishingSpotSearchService>(
          () => FishingSpotSearchService(i<SearchClient>()))
      ..registerLazySingleton<NotificationService>(
          () => NotificationService(i<NotificationApi>()))
      ..registerLazySingleton<FishingPlanService>(
          () => FishingPlanService(i<Dio>()))
      ..registerLazySingleton<PlanConfigService>(
          () => PlanConfigService(i<Dio>()))
      ..registerLazySingleton<PlanReminderService>(() => PlanReminderService())
      ..registerLazySingleton<FishingStatisticsService>(
          () => FishingStatisticsService(i<FishingStatisticsApi>()))
      ..registerLazySingleton<RegionService>(() => RegionService(i<Dio>()))
      ..registerLazySingleton<PlanTemplateService>(
          () => PlanTemplateService(i<Dio>()))
      ..registerLazySingleton<EquipmentTemplateService>(
          () => EquipmentTemplateService(i<Dio>()))
      ..registerLazySingleton<FishingMethodService>(
          () => FishingMethodService(i<Dio>()))
      ..registerLazySingleton<FeeTypeService>(() => FeeTypeService(i<Dio>()))
      ..registerLazySingleton<AppServices>(() => AppServices(
            userService: i<UserService>(),
            ossService: i<OssService>(),
            commentService: i<CommentService>(),
            momentService: i<MomentService>(),
            sessionService: i<SessionService>(),
            smsService: i<SmsService>(),
            statisticsService: i<StatisticsService>(),
            searchService: i<SearchService>(),
            chatService: i<ChatService>(),
            mapService: i<MapService>(),
            fishingSpotService: i<FishingSpotService>(),
            notificationService: i<NotificationService>(),
          ));
  }

  static void _registerViewModels(GetIt i) {
    _registerAuthViewModels(i);

    // Register providers
    i
      ..registerFactory<MomentViewModel>(
          () => MomentViewModel(i<MomentService>()))
      ..registerLazySingleton<AuthViewModel>(
        () => AuthViewModel(
          appServices: i<AppServices>(),
          sharedPreferences: i<SharedPreferences>(),
        ),
      )
      ..registerLazySingleton<ChatViewModel>(
        () => ChatViewModel(appServices: i<AppServices>()),
      )

      // Regular factory registrations

      ..registerFactory<BookmarkViewModel>(
        () => BookmarkViewModel(bookmarkApi: i<BookmarkApi>()),
      )
      ..registerFactory<PreviewImageViewModel>(
        () => PreviewImageViewModel(),
      )
      ..registerFactory<SocialInsightsViewModel>(
        () => SocialInsightsViewModel(i<SocialInsightsService>()),
      )
      ..registerFactory<FishingPlanViewModel>(
        () => FishingPlanViewModel(i<FishingPlanService>(), i<AuthViewModel>()),
      )

      // Special parameter-based registrations
      ..registerFactoryParam<OtherProfilePageViewModel, int, void>(
        (userId, _) => OtherProfilePageViewModel(
          userId: userId,
          appServices: i<AppServices>(),
        ),
      )
      ..registerFactoryParam<FollowButtonViewModel, int, void>(
        (userId, _) =>
            FollowButtonViewModel(userId, i<AppServices>(), i<AuthViewModel>()),
      )
      ..registerFactoryParam<user_profile.UserProfileViewModel, int, void>(
        (userId, _) => user_profile.UserProfileViewModel(),
      )

      // More factory registrations (separate cascade due to mixed types above)

      ..registerFactory<FishingSpotViewModel>(
        () => FishingSpotViewModel(
          fishSpotService: i<FishingSpotService>(),
          fishTypeService: i<FishTypeService>(),
        ),
      )
      ..registerFactory<WeatherCardViewModel>(
        () => WeatherCardViewModel(
          mapService: i<MapService>(),
          fishingRecommendService: i<FishingRecommendService>(),
        ),
      )
      ..registerFactory<PublishMomentViewModel>(
        () => PublishMomentViewModel(
          momentViewModel: i<MomentViewModel>(),
          ossService: i<OssService>(),
        ),
      )
      ..registerFactory<CommunityViewModel>(
        () => CommunityViewModel(
          i<MomentService>(),
          i<SearchService>(),
          i<UserFollowApi>(),
          i<BookmarkApi>(),
          i<ReportApi>(),
        ),
      )
      ..registerFactory<TipsViewModel>(
        () => TipsViewModel(
          i<MomentService>(),
        ),
      )
      ..registerFactory<MapStateViewModel>(
        () => MapStateViewModel(),
      )
      ..registerFactory<CreateSpotViewModel>(
        () => CreateSpotViewModel(
          ossService: i<OssService>(),
          fishingSpotViewModel: i<FishingSpotViewModel>(),
          fishSpotService: i<FishingSpotService>(),
        ),
      )
      ..registerFactory<LocationSelectionViewModel>(
        () => LocationSelectionViewModel(
          fishingSpotService: i<FishingSpotService>(),
        ),
      )
      ..registerFactory<CommentViewModel>(
        () => CommentViewModel(i<CommentService>()),
      )
      ..registerFactory<SearchViewModel>(
        () => SearchViewModel(
          searchService: i<FishingSpotSearchService>(),
          prefs: i<SharedPreferences>(),
        ),
      )
      ..registerFactory<NotificationViewModel>(
        () => NotificationViewModel(notificationApi: i<NotificationApi>()),
      )
      ..registerFactory<RegionViewModel>(
        () => RegionViewModel(i<RegionService>()),
      )

      // Register user-related ViewModels

      ..registerFactory<FansViewModel>(
        () => FansViewModel(),
      )
      ..registerFactory<FollowingViewModel>(
        () => FollowingViewModel(),
      )
      ..registerFactory<AlbumViewModel>(
        () => AlbumViewModel(),
      )
      ..registerFactory<PersonalMomentListViewModel>(
        () => PersonalMomentListViewModel(),
      );
  }

  static void _registerAuthViewModels(GetIt i) {
    // Register auth singletons
    i
      ..registerLazySingleton<BaseAuthViewModel>(() => BaseAuthViewModel(
            userService: i<UserService>(),
            sharedPreferences: i<SharedPreferences>(),
          ))
      ..registerLazySingleton<VerificationCodeService>(
          () => VerificationCodeService(
                smsService: i<SmsService>(),
                sharedPreferences: i<SharedPreferences>(),
              ))

      // Register auth factories

      ..registerFactory<LoginViewModel>(() => LoginViewModel(
            baseAuthViewModel: i<BaseAuthViewModel>(),
            sessionService: i<SessionService>(),
          ))
      ..registerFactory<RegisterViewModel>(() => RegisterViewModel(
            baseAuthViewModel: i<BaseAuthViewModel>(),
            sessionService: i<SessionService>(),
            verificationCodeService: i<VerificationCodeService>(),
          ))
      ..registerFactory<ResetPasswordViewModel>(() => ResetPasswordViewModel(
            baseAuthViewModel: i<BaseAuthViewModel>(),
            sessionService: i<SessionService>(),
            verificationCodeService: i<VerificationCodeService>(),
          ))
      ..registerFactory<UserProfileViewModel>(() => UserProfileViewModel(
            baseAuthViewModel: i<BaseAuthViewModel>(),
            statisticsService: i<StatisticsService>(),
          ));
  }

  static Future<void> setupDependencies(GetIt i) async {
    // Register core dependencies
    await _registerCoreDependencies(i);

    // Register APIs
    _registerApis(i);

    // Register Services
    _registerServices(i);

    // Register ViewModels
    _registerViewModels(i);
  }

  static Future<void> _registerCoreDependencies(GetIt i) async {
    // Register SharedPreferences synchronously
    final sharedPreferences = await SharedPreferences.getInstance();

    i
      ..registerLazySingleton<SharedPreferences>(() => sharedPreferences)
      ..registerLazySingleton<SearchClient>(
        () => SearchClient(
          appId: GlobalConfig.algoliaAppId,
          apiKey: GlobalConfig.algoliaApiKey,
        ),
      )

      // Register Dio
      ..registerLazySingleton<Dio>(() {
        final dio = Dio(BaseOptions(
          baseUrl: GlobalConfig.baseUrl,
          connectTimeout: const Duration(seconds: 60),
          receiveTimeout: const Duration(seconds: 60),
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          responseType: ResponseType.json,
        ));

        dio.interceptors.add(InterceptorsWrapper(
          onRequest: (options, handler) async {
            debugPrint('=== HTTP Request ===');
            debugPrint('URL: ${options.baseUrl}${options.path}');
            debugPrint('Method: ${options.method}');
            debugPrint('Headers: ${options.headers}');
            debugPrint('Data: ${options.data}');

            // 移除GET请求的Content-Type
            if (options.method == 'GET' &&
                options.headers.containsKey('Content-Type')) {
              options.headers.remove('Content-Type');
              debugPrint('🔧 Removed Content-Type from GET request');
            }

            try {
              final sharedPreferences = getIt<SharedPreferences>();
              final accessToken = sharedPreferences.getString('token');

              debugPrint(
                  '🔍 [HTTP拦截器] SharedPreferences实例: ${sharedPreferences.hashCode}');
              debugPrint(
                  '🔍 [HTTP拦截器] Token检查结果: ${accessToken != null ? "存在(${accessToken.length}字符)" : "不存在"}');

              if (accessToken != null) {
                options.headers['Authorization'] = 'Bearer $accessToken';
                debugPrint(
                    '✅ Authorization header added with token length: ${accessToken.length}');
              } else {
                debugPrint('⚠️ No token found in SharedPreferences');
              }
            } catch (e) {
              debugPrint('❌ Error getting token from SharedPreferences: $e');
            }

            return handler.next(options);
          },
          onResponse: (response, handler) {
            debugPrint('=== HTTP Response ===');
            debugPrint('Status Code: ${response.statusCode}');
            debugPrint('Response Data: ${response.data}');

            if (response.statusCode == 401) {
              getIt<SharedPreferences>().remove('token');
            }
            return handler.next(response);
          },
          onError: (DioException err, handler) {
            debugPrint('=== HTTP Error ===');
            debugPrint('Error Type: ${err.type}');
            debugPrint('Error Message: ${err.message}');
            debugPrint('Status Code: ${err.response?.statusCode}');
            debugPrint('Response Data: ${err.response?.data}');

            if (err.error is FormatException) {
              return handler.resolve(Response(
                requestOptions: err.requestOptions,
                data: {
                  'message': 'Format Error',
                },
              ));
            }

            if (err.response?.statusCode == 401) {
              getIt<SharedPreferences>()
                ..remove('token')
                ..remove('user');

              // 通知应用用户已登出
              debugPrint('🚪 [HTTP拦截器] 检测到401错误，清除用户数据');

              // 可以发送全局事件通知UI更新
              // EventBus.instance.fire(UserLogoutEvent());
            }
            return handler.next(err);
          },
        ));

        return dio;
      });
  }
}

Future<void> setupInjection() async {
  await _DIRegistration.setupDependencies(getIt);
}
