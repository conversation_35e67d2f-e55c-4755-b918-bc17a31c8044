import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../design_tokens/design_tokens.dart';

/// Material Design 3 主题配置
/// 严格遵循MD3规范和Design Tokens系统
class AppTheme {
  // ==================== 禁用区域 ====================
  // 根据CLAUDE.md要求，以下做法被严格禁止：
  // ❌ 禁止直接使用 Colors.* 或 Color(0xFF...)
  // ❌ 禁止硬编码字体大小、颜色、间距
  // ❌ 禁止混用Material 2和Material 3组件
  
  // ==================== 主题构建 ====================
  
  /// 创建Light主题
  static ThemeData get lightTheme {
    final colorScheme = ColorTokens.lightColorScheme;
    
    return ThemeData(
      useMaterial3: true, // 强制使用Material 3
      colorScheme: colorScheme,
      textTheme: TypographyTokens.textTheme,
      
      // ==================== 组件主题 ====================
      
      /// AppBar主题
      appBarTheme: AppBarTheme(
        elevation: ElevationTokens.level0,
        scrolledUnderElevation: ElevationTokens.level2,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        titleTextStyle: TypographyTokens.titleLarge.copyWith(
          color: colorScheme.onSurface,
        ),
        shape: ShapeTokens.appBarShape,
        systemOverlayStyle: SystemUiOverlayStyle.dark,
      ),
      
      /// Card主题
      cardTheme: CardThemeData(
        elevation: ElevationTokens.level1,
        color: colorScheme.surface,
        shadowColor: colorScheme.shadow,
        shape: ShapeTokens.cardShapeLarge,
        margin: SpacingTokens.marginSm,
      ),
      
      /// ElevatedButton主题
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          elevation: ElevationTokens.level1,
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.onSurface.withValues(alpha: 0.12),
          disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
          shape: ShapeTokens.buttonShapeMedium,
          padding: SpacingTokens.buttonPaddingMedium,
          textStyle: TypographyTokens.labelLarge,
          minimumSize: const Size(64, 40),
        ),
      ),
      
      /// FilledButton主题
      filledButtonTheme: FilledButtonThemeData(
        style: FilledButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          disabledBackgroundColor: colorScheme.onSurface.withValues(alpha: 0.12),
          disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
          shape: ShapeTokens.buttonShapeMedium,
          padding: SpacingTokens.buttonPaddingMedium,
          textStyle: TypographyTokens.labelLarge,
          minimumSize: const Size(64, 40),
        ),
      ),
      
      /// OutlinedButton主题
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: colorScheme.primary,
          disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
          side: BorderSide(color: colorScheme.outline),
          shape: ShapeTokens.buttonShapeMedium,
          padding: SpacingTokens.buttonPaddingMedium,
          textStyle: TypographyTokens.labelLarge,
          minimumSize: const Size(64, 40),
        ),
      ),
      
      /// TextButton主题
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.primary,
          disabledForegroundColor: colorScheme.onSurface.withValues(alpha: 0.38),
          shape: ShapeTokens.buttonShapeMedium,
          padding: SpacingTokens.buttonPaddingMedium,
          textStyle: TypographyTokens.labelLarge,
          minimumSize: const Size(64, 40),
        ),
      ),
      
      /// InputDecoration主题
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest,
        border: ShapeTokens.inputBorderDefault,
        enabledBorder: ShapeTokens.inputBorderDefault.copyWith(
          borderSide: BorderSide(color: colorScheme.outline),
        ),
        focusedBorder: ShapeTokens.inputBorderDefault.copyWith(
          borderSide: BorderSide(color: colorScheme.primary, width: 2),
        ),
        errorBorder: ShapeTokens.inputBorderDefault.copyWith(
          borderSide: BorderSide(color: colorScheme.error),
        ),
        focusedErrorBorder: ShapeTokens.inputBorderDefault.copyWith(
          borderSide: BorderSide(color: colorScheme.error, width: 2),
        ),
        labelStyle: TypographyTokens.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        hintStyle: TypographyTokens.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
        contentPadding: SpacingTokens.inputPadding,
      ),
      
      /// FloatingActionButton主题
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        elevation: ElevationTokens.level3,
        backgroundColor: colorScheme.primaryContainer,
        foregroundColor: colorScheme.onPrimaryContainer,
        shape: ShapeTokens.fabShapeLarge,
      ),
      
      /// Dialog主题
      dialogTheme: DialogThemeData(
        elevation: ElevationTokens.level3,
        backgroundColor: colorScheme.surface,
        shape: ShapeTokens.dialogShape,
        titleTextStyle: TypographyTokens.headlineSmall.copyWith(
          color: colorScheme.onSurface,
        ),
        contentTextStyle: TypographyTokens.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      
      /// BottomSheet主题
      bottomSheetTheme: BottomSheetThemeData(
        elevation: ElevationTokens.level3,
        backgroundColor: colorScheme.surface,
        shape: ShapeTokens.bottomSheetShape,
      ),
      
      /// ListTile主题
      listTileTheme: ListTileThemeData(
        contentPadding: SpacingTokens.listItemPadding,
        titleTextStyle: TypographyTokens.bodyLarge.copyWith(
          color: colorScheme.onSurface,
        ),
        subtitleTextStyle: TypographyTokens.bodyMedium.copyWith(
          color: colorScheme.onSurfaceVariant,
        ),
      ),
      
      /// Chip主题
      chipTheme: ChipThemeData(
        backgroundColor: colorScheme.surfaceContainerHighest,
        deleteIconColor: colorScheme.onSurfaceVariant,
        disabledColor: colorScheme.onSurface.withValues(alpha: 0.12),
        selectedColor: colorScheme.secondaryContainer,
        secondarySelectedColor: colorScheme.secondaryContainer,
        shadowColor: ColorTokens.transparent,
        elevation: 0,
        pressElevation: 0,
        shape: ShapeTokens.chipShape,
        labelStyle: TypographyTokens.labelMedium,
        secondaryLabelStyle: TypographyTokens.labelMedium,
        padding: SpacingTokens.paddingHorizontalSm,
      ),
      
      /// Divider主题
      dividerTheme: DividerThemeData(
        color: colorScheme.divider,
        thickness: 1,
        space: 1,
      ),
      
      /// 其他
      scaffoldBackgroundColor: colorScheme.surface,
      canvasColor: colorScheme.surface,
      disabledColor: colorScheme.onSurface.withValues(alpha: 0.38),
      highlightColor: colorScheme.primary.withValues(alpha: 0.12),
      splashColor: colorScheme.primary.withValues(alpha: 0.12),
    );
  }
  
  /// 创建Dark主题
  static ThemeData get darkTheme {
    final colorScheme = ColorTokens.darkColorScheme;
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: TypographyTokens.textTheme,
      brightness: Brightness.dark,
      
      // 组件主题配置与lightTheme类似，但使用darkColorScheme
      // 为简化示例，这里省略详细配置
      // 在实际项目中应完整配置所有组件主题
      
      appBarTheme: AppBarTheme(
        elevation: ElevationTokens.level0,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        systemOverlayStyle: SystemUiOverlayStyle.light,
      ),
      
      scaffoldBackgroundColor: colorScheme.surface,
    );
  }
  
  // ==================== 动画配置 ====================
  
  // 使用 MotionTokens 替代硬编码动画值
  static Duration get durationFast => MotionTokens.durationFast;
  static Duration get durationNormal => MotionTokens.durationMedium;
  static Duration get durationSlow => MotionTokens.durationSlow;
  
  static Curve get curveDefault => MotionTokens.standard;
  static Curve get curveEaseOut => MotionTokens.standardDecelerate;
  static Curve get curveEaseIn => MotionTokens.standardAccelerate;
  
  // ==================== 辅助方法 ====================
  
  /// 获取当前主题的ColorScheme
  static ColorScheme colorSchemeOf(BuildContext context) =>
      Theme.of(context).colorScheme;
  
  /// 获取当前主题的TextTheme
  static TextTheme textThemeOf(BuildContext context) =>
      Theme.of(context).textTheme;
  
  /// 判断是否为暗黑模式
  static bool isDarkMode(BuildContext context) =>
      Theme.of(context).brightness == Brightness.dark;
}