import 'package:user_app/models/photo/photo_data_index.dart';
import 'package:user_app/models/fishing_spot/fishing_spot.dart';
import 'package:user_app/models/moment/moment.dart';
import 'package:user_app/models/activity/activity.dart';
import 'package:user_app/models/catch_record/catch_record.dart';

/// 照片数据关联服务 - 核心业务逻辑
class PhotoDataService {
  // 单例模式
  static final PhotoDataService _instance = PhotoDataService._internal();
  factory PhotoDataService() => _instance;
  PhotoDataService._internal();

  // 模拟数据存储 - 实际应该连接数据库
  final List<PhotoDataIndex> _photoIndexes = [];
  final Map<String, List<String>> _tagIndex = {}; // 标签索引

  /// 创建照片数据索引（在用户上传照片时调用）
  Future<PhotoDataIndex> createPhotoIndex({
    required String photoId,
    required String photoUrl,
    String? thumbnailUrl,
    required DateTime takenAt,
    PhotoLocation? location,
    required PhotoSource source,
    required String sourceId,
    List<String>? initialTags,
  }) async {
    final photoIndex = PhotoDataIndex(
      photoId: photoId,
      photoUrl: photoUrl,
      thumbnailUrl: thumbnailUrl,
      takenAt: takenAt,
      location: location,
      source: source,
      sourceId: sourceId,
      userTags: initialTags ?? [],
    );

    // 自动关联数据
    final enrichedIndex = await _autoAssociateData(photoIndex);
    
    // 存储索引
    _photoIndexes.add(enrichedIndex);
    
    // 更新标签索引
    _updateTagIndex(enrichedIndex);
    
    return enrichedIndex;
  }

  /// 自动关联相关数据
  Future<PhotoDataIndex> _autoAssociateData(PhotoDataIndex photoIndex) async {
    final relatedSpots = await _findRelatedSpots(photoIndex);
    final relatedMoments = await _findRelatedMoments(photoIndex);
    final relatedActivities = await _findRelatedActivities(photoIndex);
    final relatedCatches = await _findRelatedCatches(photoIndex);
    final autoTags = await _generateAutoTags(photoIndex);

    return photoIndex.copyWith(
      relatedSpotIds: relatedSpots,
      relatedMomentIds: relatedMoments,
      relatedActivityIds: relatedActivities,
      relatedCatchIds: relatedCatches,
      autoTags: autoTags,
    );
  }

  /// 基于时间和位置找到相关钓点
  Future<List<String>> _findRelatedSpots(PhotoDataIndex photoIndex) async {
    if (photoIndex.location == null) return [];

    // TODO: 实际实现应该查询数据库
    // 这里是示例逻辑：在100米范围内，时间差在24小时内的钓点
    return []; // 返回相关钓点ID列表
  }

  /// 找到相关动态
  Future<List<String>> _findRelatedMoments(PhotoDataIndex photoIndex) async {
    // TODO: 根据时间、位置、用户查找相关动态
    return [];
  }

  /// 找到相关活动
  Future<List<String>> _findRelatedActivities(PhotoDataIndex photoIndex) async {
    // TODO: 根据时间范围查找相关活动
    return [];
  }

  /// 找到相关钓获记录
  Future<List<String>> _findRelatedCatches(PhotoDataIndex photoIndex) async {
    // TODO: 根据时间、位置查找相关钓获记录
    return [];
  }

  /// 生成自动标签
  Future<List<String>> _generateAutoTags(PhotoDataIndex photoIndex) async {
    final tags = <String>[];
    
    // 基于来源生成标签
    tags.add(photoIndex.source.displayName);
    
    // 基于时间生成标签
    final hour = photoIndex.takenAt.hour;
    if (hour >= 5 && hour <= 11) {
      tags.add('早晨');
    } else if (hour >= 12 && hour <= 17) {
      tags.add('下午');
    } else if (hour >= 18 && hour <= 22) {
      tags.add('傍晚');
    } else {
      tags.add('夜晚');
    }
    
    // 基于季节生成标签
    final month = photoIndex.takenAt.month;
    if (month >= 3 && month <= 5) {
      tags.add('春季');
    } else if (month >= 6 && month <= 8) {
      tags.add('夏季');
    } else if (month >= 9 && month <= 11) {
      tags.add('秋季');
    } else {
      tags.add('冬季');
    }

    // TODO: 集成AI图像识别，自动识别鱼类、装备等
    
    return tags;
  }

  /// 更新标签索引
  void _updateTagIndex(PhotoDataIndex photoIndex) {
    for (final tag in photoIndex.allTags) {
      _tagIndex[tag] = (_tagIndex[tag] ?? [])..add(photoIndex.photoId);
    }
  }

  /// 搜索照片（多维度搜索）
  Future<List<PhotoDataIndex>> searchPhotos({
    String? query,
    List<String>? tags,
    PhotoSource? source,
    DateTimeRange? dateRange,
    PhotoLocation? nearLocation,
    double radiusKm = 5.0,
  }) async {
    var results = List<PhotoDataIndex>.from(_photoIndexes);

    // 文本查询
    if (query != null && query.isNotEmpty) {
      results = results.where((photo) {
        return photo.allTags.any((tag) => 
          tag.toLowerCase().contains(query.toLowerCase()));
      }).toList();
    }

    // 标签筛选
    if (tags != null && tags.isNotEmpty) {
      results = results.where((photo) {
        return tags.every((tag) => photo.hasTag(tag));
      }).toList();
    }

    // 来源筛选
    if (source != null) {
      results = results.where((photo) => photo.source == source).toList();
    }

    // 时间范围筛选
    if (dateRange != null) {
      results = results.where((photo) {
        return photo.takenAt.isAfter(dateRange.start) && 
               photo.takenAt.isBefore(dateRange.end);
      }).toList();
    }

    // 位置筛选
    if (nearLocation != null) {
      results = results.where((photo) {
        if (photo.location == null) return false;
        final distance = _calculateDistance(
          photo.location!.latitude, photo.location!.longitude,
          nearLocation.latitude, nearLocation.longitude,
        );
        return distance <= radiusKm;
      }).toList();
    }

    // 按时间倒序排列
    results.sort((a, b) => b.takenAt.compareTo(a.takenAt));
    
    return results;
  }

  /// 获取照片的详细关联数据
  Future<PhotoRelatedData> getPhotoRelatedData(String photoId) async {
    final photoIndex = _photoIndexes.firstWhere(
      (p) => p.photoId == photoId,
      orElse: () => throw Exception('Photo not found'),
    );

    // TODO: 实际实现应该并行查询数据库
    final relatedSpots = await _getSpotsByIds(photoIndex.relatedSpotIds);
    final relatedMoments = await _getMomentsByIds(photoIndex.relatedMomentIds);
    final relatedActivities = await _getActivitiesByIds(photoIndex.relatedActivityIds);
    final relatedCatches = await _getCatchesByIds(photoIndex.relatedCatchIds);

    return PhotoRelatedData(
      photoIndex: photoIndex,
      relatedSpots: relatedSpots,
      relatedMoments: relatedMoments,
      relatedActivities: relatedActivities,
      relatedCatches: relatedCatches,
    );
  }

  /// 添加用户标签
  Future<void> addUserTag(String photoId, String tag) async {
    final index = _photoIndexes.indexWhere((p) => p.photoId == photoId);
    if (index != -1) {
      final photo = _photoIndexes[index];
      if (!photo.userTags.contains(tag)) {
        _photoIndexes[index] = photo.copyWith(
          userTags: [...photo.userTags, tag],
        );
        _updateTagIndex(_photoIndexes[index]);
      }
    }
  }

  /// 获取热门标签
  List<String> getPopularTags({int limit = 20}) {
    final tagCounts = <String, int>{};
    for (final entry in _tagIndex.entries) {
      tagCounts[entry.key] = entry.value.length;
    }
    
    final sortedTags = tagCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedTags.take(limit).map((e) => e.key).toList();
  }

  /// 获取统计信息
  PhotoStatistics getStatistics() {
    final totalPhotos = _photoIndexes.length;
    final sourceStats = <PhotoSource, int>{};
    final monthlyStats = <String, int>{};
    
    for (final photo in _photoIndexes) {
      sourceStats[photo.source] = (sourceStats[photo.source] ?? 0) + 1;
      
      final monthKey = '${photo.takenAt.year}-${photo.takenAt.month.toString().padLeft(2, '0')}';
      monthlyStats[monthKey] = (monthlyStats[monthKey] ?? 0) + 1;
    }
    
    return PhotoStatistics(
      totalPhotos: totalPhotos,
      sourceDistribution: sourceStats,
      monthlyDistribution: monthlyStats,
      totalTags: _tagIndex.length,
    );
  }

  // 辅助方法
  double _calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    // 简化的距离计算，实际应该使用更精确的公式
    final dLat = (lat2 - lat1).abs();
    final dLon = (lon2 - lon1).abs();
    return (dLat * 111 + dLon * 85); // 近似转换为公里
  }

  Future<List<FishingSpot>> _getSpotsByIds(List<String> ids) async {
    // TODO: 实际查询钓点数据
    return [];
  }

  Future<List<Moment>> _getMomentsByIds(List<String> ids) async {
    // TODO: 实际查询动态数据
    return [];
  }

  Future<List<Activity>> _getActivitiesByIds(List<String> ids) async {
    // TODO: 实际查询活动数据
    return [];
  }

  Future<List<CatchRecord>> _getCatchesByIds(List<String> ids) async {
    // TODO: 实际查询钓获记录数据
    return [];
  }
}

/// 照片相关数据的完整视图
class PhotoRelatedData {
  final PhotoDataIndex photoIndex;
  final List<FishingSpot> relatedSpots;
  final List<Moment> relatedMoments;
  final List<Activity> relatedActivities;
  final List<CatchRecord> relatedCatches;

  const PhotoRelatedData({
    required this.photoIndex,
    required this.relatedSpots,
    required this.relatedMoments,
    required this.relatedActivities,
    required this.relatedCatches,
  });

  /// 获取总关联数据数量
  int get totalRelatedItems {
    return relatedSpots.length + 
           relatedMoments.length + 
           relatedActivities.length + 
           relatedCatches.length;
  }
}

/// 照片统计信息
class PhotoStatistics {
  final int totalPhotos;
  final Map<PhotoSource, int> sourceDistribution;
  final Map<String, int> monthlyDistribution;
  final int totalTags;

  const PhotoStatistics({
    required this.totalPhotos,
    required this.sourceDistribution,
    required this.monthlyDistribution,
    required this.totalTags,
  });
}

/// 日期时间范围
class DateTimeRange {
  final DateTime start;
  final DateTime end;

  const DateTimeRange({
    required this.start,
    required this.end,
  });
}