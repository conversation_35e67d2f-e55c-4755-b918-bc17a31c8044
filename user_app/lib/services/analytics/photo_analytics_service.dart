import 'package:user_app/models/photo/photo_data_index.dart';
import 'package:user_app/services/photo/photo_data_service.dart';

/// 基于照片的数据分析洞察服务 - 通过照片数据挖掘用户的钓鱼行为模式和洞察
class PhotoAnalyticsService {
  // 单例模式
  static final PhotoAnalyticsService _instance = PhotoAnalyticsService._internal();
  factory PhotoAnalyticsService() => _instance;
  PhotoAnalyticsService._internal();

  final PhotoDataService _photoDataService = PhotoDataService();

  /// 获取用户钓鱼活动概览
  Future<FishingActivityOverview> getFishingActivityOverview({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final photos = await _photoDataService.searchPhotos(
      dateRange: timeRange,
    );

    return FishingActivityOverview(
      totalPhotos: photos.length,
      totalFishingDays: _calculateFishingDays(photos),
      favoriteSpots: await _getFavoriteSpots(photos),
      mostActiveTimeOfDay: _getMostActiveTimeOfDay(photos),
      averagePhotosPerTrip: _getAveragePhotosPerTrip(photos),
      fishingFrequency: _calculateFishingFrequency(photos),
      seasonalActivity: _getSeasonalActivity(photos),
      weatherPreferences: _getWeatherPreferences(photos),
    );
  }

  /// 钓点分析 - 分析用户最喜欢的钓点和特征
  Future<SpotAnalytics> getSpotAnalytics({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final photos = await _photoDataService.searchPhotos(
      dateRange: timeRange,
    );

    final spotVisits = <String, List<PhotoDataIndex>>{};
    for (final photo in photos) {
      for (final spotId in photo.relatedSpotIds) {
        spotVisits[spotId] = (spotVisits[spotId] ?? [])..add(photo);
      }
    }

    return SpotAnalytics(
      totalSpotsVisited: spotVisits.length,
      mostVisitedSpots: _getMostVisitedSpots(spotVisits),
      averageVisitsPerSpot: _getAverageVisitsPerSpot(spotVisits),
      spotSuccessRates: await _calculateSpotSuccessRates(spotVisits),
      distanceFromHome: await _getAverageDistanceFromHome(spotVisits),
      newSpotsDiscovered: _getNewSpotsDiscovered(photos, timeRange),
    );
  }

  /// 鱼获分析 - 分析用户的钓获模式和偏好
  Future<CatchAnalytics> getCatchAnalytics({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final photos = await _photoDataService.searchPhotos(
      dateRange: timeRange,
    );

    // 从标签中提取鱼类信息
    final fishSpeciesCounts = <String, int>{};
    final fishSizeDistribution = <String, List<String>>{};
    
    for (final photo in photos) {
      for (final tag in photo.allTags) {
        if (_isFishSpeciesTag(tag)) {
          final species = _extractFishSpecies(tag);
          fishSpeciesCounts[species] = (fishSpeciesCounts[species] ?? 0) + 1;
          
          final size = _extractFishSize(tag);
          if (size != null) {
            fishSizeDistribution[species] = (fishSizeDistribution[species] ?? [])..add(size);
          }
        }
      }
    }

    return CatchAnalytics(
      totalCatches: fishSpeciesCounts.values.fold(0, (a, b) => a + b),
      speciesVariety: fishSpeciesCounts.keys.length,
      mostCaughtSpecies: _getMostCaughtSpecies(fishSpeciesCounts),
      catchSuccessRate: _calculateCatchSuccessRate(photos),
      bestFishingTimes: _getBestFishingTimes(photos),
      seasonalCatchPatterns: _getSeasonalCatchPatterns(photos),
      averageCatchSize: _getAverageCatchSize(fishSizeDistribution),
      catchTrends: _getCatchTrends(photos),
    );
  }

  /// 装备分析 - 分析用户使用的钓鱼装备偏好
  Future<EquipmentAnalytics> getEquipmentAnalytics({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final photos = await _photoDataService.searchPhotos(
      dateRange: timeRange,
    );

    final equipmentUsage = <String, int>{};
    final equipmentEffectiveness = <String, double>{};

    for (final photo in photos) {
      for (final tag in photo.allTags) {
        if (_isEquipmentTag(tag)) {
          final equipment = _extractEquipmentType(tag);
          equipmentUsage[equipment] = (equipmentUsage[equipment] ?? 0) + 1;
        }
      }
    }

    return EquipmentAnalytics(
      mostUsedEquipment: _getMostUsedEquipment(equipmentUsage),
      equipmentEffectiveness: equipmentEffectiveness,
      equipmentDiversity: equipmentUsage.keys.length,
      recommendedUpgrades: await _getEquipmentRecommendations(equipmentUsage),
      investmentValue: _calculateEquipmentInvestmentValue(equipmentUsage),
    );
  }

  /// 社交分析 - 分析用户的社交钓鱼模式
  Future<SocialAnalytics> getSocialAnalytics({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final photos = await _photoDataService.searchPhotos(
      dateRange: timeRange,
    );

    return SocialAnalytics(
      soloVsGroupRatio: _getSoloVsGroupRatio(photos),
      frequentCompanions: await _getFrequentCompanions(photos),
      socialSpots: await _getSocialSpots(photos),
      communityEngagement: _getCommunityEngagement(photos),
      sharedMemories: _getSharedMemories(photos),
    );
  }

  /// 进步分析 - 分析用户的钓鱼技能进步
  Future<ProgressAnalytics> getProgressAnalytics({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final photos = await _photoDataService.searchPhotos(
      dateRange: timeRange,
    );

    // 按时间排序分析进步趋势
    photos.sort((a, b) => a.takenAt.compareTo(b.takenAt));

    return ProgressAnalytics(
      skillImprovement: _calculateSkillImprovement(photos),
      catchRateImprovement: _getCatchRateImprovement(photos),
      explorationGrowth: _getExplorationGrowth(photos),
      techniqueEvolution: _getTechniqueEvolution(photos),
      milestones: _identifyMilestones(photos),
      learningCurve: _analyzeLearningCurve(photos),
    );
  }

  /// 天气与成功率分析
  Future<WeatherAnalytics> getWeatherAnalytics({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final photos = await _photoDataService.searchPhotos(
      dateRange: timeRange,
    );

    final weatherSuccess = <String, WeatherSuccessData>{};
    
    for (final photo in photos) {
      final weather = _extractWeatherFromTags(photo.allTags);
      if (weather != null) {
        final data = weatherSuccess[weather] ?? WeatherSuccessData(weather);
        data.totalTrips++;
        if (_hasFishCatch(photo)) {
          data.successfulTrips++;
        }
        weatherSuccess[weather] = data;
      }
    }

    return WeatherAnalytics(
      bestWeatherConditions: _getBestWeatherConditions(weatherSuccess),
      worstWeatherConditions: _getWorstWeatherConditions(weatherSuccess),
      weatherSuccessRates: weatherSuccess,
      seasonalWeatherPatterns: _getSeasonalWeatherPatterns(photos),
    );
  }

  /// 个性化推荐
  Future<PersonalizedRecommendations> getPersonalizedRecommendations({
    String? userId,
  }) async {
    final overview = await getFishingActivityOverview(userId: userId);
    final spotAnalytics = await getSpotAnalytics(userId: userId);
    final catchAnalytics = await getCatchAnalytics(userId: userId);
    
    return PersonalizedRecommendations(
      recommendedSpots: await _getRecommendedSpots(spotAnalytics),
      bestFishingTimes: _getRecommendedFishingTimes(overview),
      equipmentSuggestions: await _getEquipmentSuggestions(catchAnalytics),
      newTechniques: _getNewTechniqueRecommendations(catchAnalytics),
      socialOpportunities: _getSocialRecommendations(overview),
    );
  }

  /// 导出分析报告
  Future<AnalyticsReport> exportAnalyticsReport({
    String? userId,
    DateTimeRange? timeRange,
  }) async {
    final results = await Future.wait([
      getFishingActivityOverview(userId: userId, timeRange: timeRange),
      getSpotAnalytics(userId: userId, timeRange: timeRange),
      getCatchAnalytics(userId: userId, timeRange: timeRange),
      getEquipmentAnalytics(userId: userId, timeRange: timeRange),
      getSocialAnalytics(userId: userId, timeRange: timeRange),
      getProgressAnalytics(userId: userId, timeRange: timeRange),
      getWeatherAnalytics(userId: userId, timeRange: timeRange),
    ]);

    return AnalyticsReport(
      generatedAt: DateTime.now(),
      timeRange: timeRange,
      overview: results[0] as FishingActivityOverview,
      spotAnalytics: results[1] as SpotAnalytics,
      catchAnalytics: results[2] as CatchAnalytics,
      equipmentAnalytics: results[3] as EquipmentAnalytics,
      socialAnalytics: results[4] as SocialAnalytics,
      progressAnalytics: results[5] as ProgressAnalytics,
      weatherAnalytics: results[6] as WeatherAnalytics,
    );
  }

  // 私有辅助方法

  int _calculateFishingDays(List<PhotoDataIndex> photos) {
    final days = photos.map((p) => 
        DateTime(p.takenAt.year, p.takenAt.month, p.takenAt.day)
    ).toSet();
    return days.length;
  }

  Future<List<FavoriteSpot>> _getFavoriteSpots(List<PhotoDataIndex> photos) async {
    final spotCounts = <String, int>{};
    for (final photo in photos) {
      for (final spotId in photo.relatedSpotIds) {
        spotCounts[spotId] = (spotCounts[spotId] ?? 0) + 1;
      }
    }

    final sortedSpots = spotCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedSpots.take(5).map((entry) => FavoriteSpot(
      spotId: entry.key,
      visitCount: entry.value,
      // TODO: 获取钓点详细信息
      name: '钓点 ${entry.key}',
    )).toList();
  }

  String _getMostActiveTimeOfDay(List<PhotoDataIndex> photos) {
    final timeSlots = <String, int>{
      '早晨': 0,
      '上午': 0, 
      '下午': 0,
      '傍晚': 0,
      '夜晚': 0,
    };

    for (final photo in photos) {
      final hour = photo.takenAt.hour;
      if (hour >= 5 && hour < 9) {
        timeSlots['早晨'] = timeSlots['早晨']! + 1;
      } else if (hour >= 9 && hour < 12) {
        timeSlots['上午'] = timeSlots['上午']! + 1;
      } else if (hour >= 12 && hour < 17) {
        timeSlots['下午'] = timeSlots['下午']! + 1;
      } else if (hour >= 17 && hour < 20) {
        timeSlots['傍晚'] = timeSlots['傍晚']! + 1;
      } else {
        timeSlots['夜晚'] = timeSlots['夜晚']! + 1;
      }
    }

    return timeSlots.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  double _getAveragePhotosPerTrip(List<PhotoDataIndex> photos) {
    if (photos.isEmpty) return 0;
    final fishingDays = _calculateFishingDays(photos);
    return fishingDays > 0 ? photos.length / fishingDays : 0;
  }

  Map<String, int> _calculateFishingFrequency(List<PhotoDataIndex> photos) {
    final frequency = <String, int>{
      '每天': 0,
      '每周': 0,
      '每月': 0,
      '偶尔': 0,
    };

    // TODO: 实现频率计算逻辑
    return frequency;
  }

  Map<String, int> _getSeasonalActivity(List<PhotoDataIndex> photos) {
    final seasons = <String, int>{
      '春季': 0,
      '夏季': 0,
      '秋季': 0,
      '冬季': 0,
    };

    for (final photo in photos) {
      final month = photo.takenAt.month;
      if (month >= 3 && month <= 5) {
        seasons['春季'] = seasons['春季']! + 1;
      } else if (month >= 6 && month <= 8) {
        seasons['夏季'] = seasons['夏季']! + 1;
      } else if (month >= 9 && month <= 11) {
        seasons['秋季'] = seasons['秋季']! + 1;
      } else {
        seasons['冬季'] = seasons['冬季']! + 1;
      }
    }

    return seasons;
  }

  Map<String, double> _getWeatherPreferences(List<PhotoDataIndex> photos) {
    final weatherCounts = <String, int>{};
    final totalPhotos = photos.length;

    for (final photo in photos) {
      for (final tag in photo.allTags) {
        if (tag.startsWith('天气_')) {
          final weather = tag.substring(3);
          weatherCounts[weather] = (weatherCounts[weather] ?? 0) + 1;
        }
      }
    }

    return weatherCounts.map((k, v) => 
        MapEntry(k, totalPhotos > 0 ? v / totalPhotos : 0.0));
  }

  // 更多私有辅助方法...
  List<FavoriteSpot> _getMostVisitedSpots(Map<String, List<PhotoDataIndex>> spotVisits) {
    final sortedSpots = spotVisits.entries.toList()
      ..sort((a, b) => b.value.length.compareTo(a.value.length));

    return sortedSpots.take(5).map((entry) => FavoriteSpot(
      spotId: entry.key,
      visitCount: entry.value.length,
      name: '钓点 ${entry.key}',
    )).toList();
  }

  double _getAverageVisitsPerSpot(Map<String, List<PhotoDataIndex>> spotVisits) {
    if (spotVisits.isEmpty) return 0;
    final totalVisits = spotVisits.values.fold(0, (sum, visits) => sum + visits.length);
    return totalVisits / spotVisits.length;
  }

  Future<Map<String, double>> _calculateSpotSuccessRates(Map<String, List<PhotoDataIndex>> spotVisits) async {
    final successRates = <String, double>{};
    
    for (final entry in spotVisits.entries) {
      final spotId = entry.key;
      final photos = entry.value;
      final successfulTrips = photos.where(_hasFishCatch).length;
      successRates[spotId] = photos.isNotEmpty ? successfulTrips / photos.length : 0;
    }
    
    return successRates;
  }

  bool _hasFishCatch(PhotoDataIndex photo) {
    return photo.allTags.any((tag) => _isFishSpeciesTag(tag)) ||
           photo.relatedCatchIds.isNotEmpty;
  }

  bool _isFishSpeciesTag(String tag) {
    const fishKeywords = ['鱼', '草鱼', '鲤鱼', '鲫鱼', '黑鱼', '鲈鱼'];
    return fishKeywords.any((keyword) => tag.contains(keyword));
  }

  String _extractFishSpecies(String tag) {
    // 简化实现，实际应该更复杂
    return tag.split('_').first;
  }

  String? _extractFishSize(String tag) {
    if (tag.contains('大型')) return '大型';
    if (tag.contains('中型')) return '中型';
    if (tag.contains('小型')) return '小型';
    return null;
  }

  bool _isEquipmentTag(String tag) {
    const equipmentKeywords = ['装备_', '鱼竿', '鱼线', '鱼钩', '鱼饵'];
    return equipmentKeywords.any((keyword) => tag.contains(keyword));
  }

  String _extractEquipmentType(String tag) {
    if (tag.startsWith('装备_')) {
      return tag.substring(3);
    }
    return tag;
  }

  String? _extractWeatherFromTags(List<String> tags) {
    for (final tag in tags) {
      if (tag.startsWith('天气_')) {
        return tag.substring(3);
      }
    }
    return null;
  }

  // 其他所需的辅助方法...
  Future<double> _getAverageDistanceFromHome(Map<String, List<PhotoDataIndex>> spotVisits) async => 0;
  int _getNewSpotsDiscovered(List<PhotoDataIndex> photos, DateTimeRange? timeRange) => 0;
  Map<String, int> _getMostCaughtSpecies(Map<String, int> fishSpeciesCounts) => {};
  double _calculateCatchSuccessRate(List<PhotoDataIndex> photos) => 0;
  List<TimeOfDaySuccess> _getBestFishingTimes(List<PhotoDataIndex> photos) => [];
  Map<String, int> _getSeasonalCatchPatterns(List<PhotoDataIndex> photos) => {};
  Map<String, double> _getAverageCatchSize(Map<String, List<String>> fishSizeDistribution) => {};
  Map<String, List<int>> _getCatchTrends(List<PhotoDataIndex> photos) => {};
  Map<String, int> _getMostUsedEquipment(Map<String, int> equipmentUsage) => {};
  Future<List<String>> _getEquipmentRecommendations(Map<String, int> equipmentUsage) async => [];
  double _calculateEquipmentInvestmentValue(Map<String, int> equipmentUsage) => 0;
  SoloVsGroupRatio _getSoloVsGroupRatio(List<PhotoDataIndex> photos) => SoloVsGroupRatio(soloTrips: 0, groupTrips: 0);
  Future<List<String>> _getFrequentCompanions(List<PhotoDataIndex> photos) async => [];
  Future<List<String>> _getSocialSpots(List<PhotoDataIndex> photos) async => [];
  double _getCommunityEngagement(List<PhotoDataIndex> photos) => 0;
  int _getSharedMemories(List<PhotoDataIndex> photos) => 0;
  Map<String, double> _calculateSkillImprovement(List<PhotoDataIndex> photos) => {};
  List<double> _getCatchRateImprovement(List<PhotoDataIndex> photos) => [];
  Map<String, int> _getExplorationGrowth(List<PhotoDataIndex> photos) => {};
  Map<String, List<String>> _getTechniqueEvolution(List<PhotoDataIndex> photos) => {};
  List<Milestone> _identifyMilestones(List<PhotoDataIndex> photos) => [];
  LearningCurve _analyzeLearningCurve(List<PhotoDataIndex> photos) => LearningCurve(dataPoints: []);
  List<WeatherCondition> _getBestWeatherConditions(Map<String, WeatherSuccessData> weatherSuccess) => [];
  List<WeatherCondition> _getWorstWeatherConditions(Map<String, WeatherSuccessData> weatherSuccess) => [];
  Map<String, Map<String, double>> _getSeasonalWeatherPatterns(List<PhotoDataIndex> photos) => {};
  Future<List<String>> _getRecommendedSpots(SpotAnalytics analytics) async => [];
  List<String> _getRecommendedFishingTimes(FishingActivityOverview overview) => [];
  Future<List<String>> _getEquipmentSuggestions(CatchAnalytics analytics) async => [];
  List<String> _getNewTechniqueRecommendations(CatchAnalytics analytics) => [];
  List<String> _getSocialRecommendations(FishingActivityOverview overview) => [];
}

// 数据模型类定义

class DateTimeRange {
  final DateTime start;
  final DateTime end;
  DateTimeRange({required this.start, required this.end});
}

class FishingActivityOverview {
  final int totalPhotos;
  final int totalFishingDays;
  final List<FavoriteSpot> favoriteSpots;
  final String mostActiveTimeOfDay;
  final double averagePhotosPerTrip;
  final Map<String, int> fishingFrequency;
  final Map<String, int> seasonalActivity;
  final Map<String, double> weatherPreferences;

  FishingActivityOverview({
    required this.totalPhotos,
    required this.totalFishingDays,
    required this.favoriteSpots,
    required this.mostActiveTimeOfDay,
    required this.averagePhotosPerTrip,
    required this.fishingFrequency,
    required this.seasonalActivity,
    required this.weatherPreferences,
  });
}

class FavoriteSpot {
  final String spotId;
  final String name;
  final int visitCount;

  FavoriteSpot({
    required this.spotId,
    required this.name,
    required this.visitCount,
  });
}

class SpotAnalytics {
  final int totalSpotsVisited;
  final List<FavoriteSpot> mostVisitedSpots;
  final double averageVisitsPerSpot;
  final Map<String, double> spotSuccessRates;
  final double distanceFromHome;
  final int newSpotsDiscovered;

  SpotAnalytics({
    required this.totalSpotsVisited,
    required this.mostVisitedSpots,
    required this.averageVisitsPerSpot,
    required this.spotSuccessRates,
    required this.distanceFromHome,
    required this.newSpotsDiscovered,
  });
}

class CatchAnalytics {
  final int totalCatches;
  final int speciesVariety;
  final Map<String, int> mostCaughtSpecies;
  final double catchSuccessRate;
  final List<TimeOfDaySuccess> bestFishingTimes;
  final Map<String, int> seasonalCatchPatterns;
  final Map<String, double> averageCatchSize;
  final Map<String, List<int>> catchTrends;

  CatchAnalytics({
    required this.totalCatches,
    required this.speciesVariety,
    required this.mostCaughtSpecies,
    required this.catchSuccessRate,
    required this.bestFishingTimes,
    required this.seasonalCatchPatterns,
    required this.averageCatchSize,
    required this.catchTrends,
  });
}

class TimeOfDaySuccess {
  final String timeSlot;
  final double successRate;
  final int totalTrips;

  TimeOfDaySuccess({
    required this.timeSlot,
    required this.successRate,
    required this.totalTrips,
  });
}

class EquipmentAnalytics {
  final Map<String, int> mostUsedEquipment;
  final Map<String, double> equipmentEffectiveness;
  final int equipmentDiversity;
  final List<String> recommendedUpgrades;
  final double investmentValue;

  EquipmentAnalytics({
    required this.mostUsedEquipment,
    required this.equipmentEffectiveness,
    required this.equipmentDiversity,
    required this.recommendedUpgrades,
    required this.investmentValue,
  });
}

class SocialAnalytics {
  final SoloVsGroupRatio soloVsGroupRatio;
  final List<String> frequentCompanions;
  final List<String> socialSpots;
  final double communityEngagement;
  final int sharedMemories;

  SocialAnalytics({
    required this.soloVsGroupRatio,
    required this.frequentCompanions,
    required this.socialSpots,
    required this.communityEngagement,
    required this.sharedMemories,
  });
}

class SoloVsGroupRatio {
  final int soloTrips;
  final int groupTrips;

  SoloVsGroupRatio({
    required this.soloTrips,
    required this.groupTrips,
  });

  double get ratio => groupTrips > 0 ? soloTrips / groupTrips : soloTrips.toDouble();
}

class ProgressAnalytics {
  final Map<String, double> skillImprovement;
  final List<double> catchRateImprovement;
  final Map<String, int> explorationGrowth;
  final Map<String, List<String>> techniqueEvolution;
  final List<Milestone> milestones;
  final LearningCurve learningCurve;

  ProgressAnalytics({
    required this.skillImprovement,
    required this.catchRateImprovement,
    required this.explorationGrowth,
    required this.techniqueEvolution,
    required this.milestones,
    required this.learningCurve,
  });
}

class Milestone {
  final String title;
  final String description;
  final DateTime achievedAt;
  final String type;

  Milestone({
    required this.title,
    required this.description,
    required this.achievedAt,
    required this.type,
  });
}

class LearningCurve {
  final List<DataPoint> dataPoints;

  LearningCurve({required this.dataPoints});
}

class DataPoint {
  final DateTime date;
  final double value;

  DataPoint({required this.date, required this.value});
}

class WeatherAnalytics {
  final List<WeatherCondition> bestWeatherConditions;
  final List<WeatherCondition> worstWeatherConditions;
  final Map<String, WeatherSuccessData> weatherSuccessRates;
  final Map<String, Map<String, double>> seasonalWeatherPatterns;

  WeatherAnalytics({
    required this.bestWeatherConditions,
    required this.worstWeatherConditions,
    required this.weatherSuccessRates,
    required this.seasonalWeatherPatterns,
  });
}

class WeatherCondition {
  final String condition;
  final double successRate;
  final int totalTrips;

  WeatherCondition({
    required this.condition,
    required this.successRate,
    required this.totalTrips,
  });
}

class WeatherSuccessData {
  final String weather;
  int totalTrips = 0;
  int successfulTrips = 0;

  WeatherSuccessData(this.weather);

  double get successRate => totalTrips > 0 ? successfulTrips / totalTrips : 0;
}

class PersonalizedRecommendations {
  final List<String> recommendedSpots;
  final List<String> bestFishingTimes;
  final List<String> equipmentSuggestions;
  final List<String> newTechniques;
  final List<String> socialOpportunities;

  PersonalizedRecommendations({
    required this.recommendedSpots,
    required this.bestFishingTimes,
    required this.equipmentSuggestions,
    required this.newTechniques,
    required this.socialOpportunities,
  });
}

class AnalyticsReport {
  final DateTime generatedAt;
  final DateTimeRange? timeRange;
  final FishingActivityOverview overview;
  final SpotAnalytics spotAnalytics;
  final CatchAnalytics catchAnalytics;
  final EquipmentAnalytics equipmentAnalytics;
  final SocialAnalytics socialAnalytics;
  final ProgressAnalytics progressAnalytics;
  final WeatherAnalytics weatherAnalytics;

  AnalyticsReport({
    required this.generatedAt,
    this.timeRange,
    required this.overview,
    required this.spotAnalytics,
    required this.catchAnalytics,
    required this.equipmentAnalytics,
    required this.socialAnalytics,
    required this.progressAnalytics,
    required this.weatherAnalytics,
  });
}