import 'package:flutter/material.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class SearchBarWidget extends StatelessWidget {
 const SearchBarWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: SpacingTokens.paddingMd,
      child: InkWell(
        onTap: () {
          context.navigateTo(AppRoutes.universalSearch);
        },
        child: Container(
          height: 40,
          decoration: BoxDecoration(
            color: ColorTokens.surface,
            borderRadius: ShapeTokens.borderRadiusXl,
            boxShadow: [
              BoxShadow(
                color: ColorTokens.shadow.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            decoration: InputDecoration(
              hintText: '搜索',
              hintStyle: TypographyTokens.bodyMedium.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
              prefixIcon: const Icon(Icons.search, color: ColorTokens.onSurfaceVariant),
              border: InputBorder.none,
              contentPadding: SpacingTokens.paddingMd,
            ),
            onTap: () {
              context.navigateTo(AppRoutes.universalSearch);
            },
          ),
        ),
      ),
    );
  }
}
