import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

/// 需要登录才能访问的页面包装器
class AuthRequiredWidget extends StatelessWidget {
  final Widget child;
  final String? title;

 const AuthRequiredWidget({
    super.key,
    required this.child,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Selector<AuthViewModel, bool>(
      selector: (context, auth) => auth.isUserLoggedIn(),
      builder: (context, isLoggedIn, _) {
        if (isLoggedIn) {
          return child;
        } else {
          return _buildLoginRequiredPage(context);
        }
      },
    );
  }

  Widget _buildLoginRequiredPage(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.surfaceContainer,
      appBar: AppBar(
        title: Text(title ?? '需要登录'),
        backgroundColor: ColorTokens.surface,
        foregroundColor: ColorTokens.onSurface,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Center(
        child: Padding(
          padding: SpacingTokens.paddingXl,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: ColorTokens.surfaceContainer,
                  borderRadius: BorderRadius.circular(60), // Keep circular for avatar
                ),
                child: const Icon(
                  Icons.person_outline,
                  size: 60,
                  color: ColorTokens.onSurfaceVariant,
                ),
              ),
              SpacingTokens.verticalSpaceXl,
              Text(
                '需要登录才能访问',
                style: TypographyTokens.titleLarge.copyWith(
                  color: ColorTokens.onSurface,
                ),
              ),
              SpacingTokens.verticalSpaceSm,
              Text(
                '登录后即可查看${title ?? '此页面'}',
                style: TypographyTokens.bodyMedium.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              SpacingTokens.verticalSpaceXl,
              SizedBox(
                width: double.infinity,
                height: 48,
                child: ElevatedButton(
                  onPressed: () => _goToLogin(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorTokens.primary,
                    foregroundColor: ColorTokens.onPrimary,
                    shape: const RoundedRectangleBorder(
                      borderRadius: ShapeTokens.borderRadiusLg,
                    ),
                    elevation: 0,
                  ),
                  child: const Text(
                    '立即登录',
                    style: TypographyTokens.titleMedium,
                  ),
                ),
              ),
              SpacingTokens.verticalSpaceMd,
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(
                  '返回上一页',
                  style: TypographyTokens.bodyMedium.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _goToLogin(BuildContext context) {
    // 保存当前路由，登录成功后可以返回
    final currentLocation = GoRouterState.of(context).uri.toString();
    context.navigateTo('${AppRoutes.login}?redirect=$currentLocation');
  }
}
