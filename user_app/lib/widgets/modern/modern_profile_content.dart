import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/models/moment/moment_vo.dart';

enum ContentType { moment, spot }

class ModernProfileContent extends StatelessWidget {
  final List<dynamic> items;
  final bool isLoading;
  final bool hasMore;
  final ContentType type;

 const ModernProfileContent({
    super.key,
    required this.items,
    required this.isLoading,
    required this.hasMore,
    required this.type,
  });

  @override
  Widget build(BuildContext context) {
    return SliverGrid(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: SpacingTokens.space3,
        mainAxisSpacing: SpacingTokens.space3,
        childAspectRatio: 0.8,
      ),
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          if (index < items.length) {
            return type == ContentType.moment
                ? _ModernMomentCard(moment: items[index] as MomentVo)
                : _ModernSpotCard(spot: items[index] as FishingSpotVo);
          } else if (isLoading && hasMore) {
            return const _LoadingCard();
          }
          return null;
        },
        childCount: items.length + (isLoading && hasMore ? 2 : 0),
      ),
    );
  }
}

class _ModernMomentCard extends StatelessWidget {
  final MomentVo moment;

 const _ModernMomentCard({required this.moment});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 导航到动态详情
        context.navigateTo(
          AppRoutes.momentDetail,
          extra: MomentDetailRouteData(moment: moment),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: ColorTokens.surfaceContainerLowest,
          borderRadius: ShapeTokens.borderRadiusLg,
          boxShadow: ElevationTokens.cardShadow(context),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片区域
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      borderRadius: ShapeTokens.borderRadiusTopLg,
                      color: ColorTokens.surfaceVariant,
                    ),
                    child: moment.images != null && moment.images!.isNotEmpty
                        ? ClipRRect(
                            borderRadius: ShapeTokens.borderRadiusTopLg,
                            child: CachedNetworkImage(
                              imageUrl: moment.images!.first.imageUrl,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                            ),
                          )
                        : Center(
                            child: const Icon(
                              Icons.article_outlined,
                              size: SpacingTokens.space10,
                              color: ColorTokens.disabled,
                            ),
                          ),
                  ),

                  // 类型标签
                  Positioned(
                    top: SpacingTokens.space3,
                    left: SpacingTokens.space3,
                    child: Container(
                      padding: SpacingTokens.symmetric(
                        horizontal: SpacingTokens.space2,
                        vertical: SpacingTokens.space1,
                      ),
                      decoration: BoxDecoration(
                        color: _getMomentTypeColor(moment.momentType),
                        borderRadius: ShapeTokens.borderRadiusSm,
                      ),
                      child: Text(
                        _getMomentTypeLabel(moment.momentType),
                        style: TypographyTokens.labelMedium.copyWith(
                          color: ColorTokens.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),

                  // 图片数量
                  if (moment.images != null && moment.images!.length > 1)
                    Positioned(
                      bottom: SpacingTokens.space2,
                      right: SpacingTokens.space2,
                      child: Container(
                        padding: SpacingTokens.symmetric(
                          horizontal: SpacingTokens.space2,
                          vertical: SpacingTokens.space1,
                        ),
                        decoration: BoxDecoration(
                          color: ColorTokens.shadow.withValues(alpha: 0.6),
                          borderRadius: ShapeTokens.borderRadiusMd,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                           const Icon(
                              Icons.photo_library,
                              size: SpacingTokens.space4,
                              color: ColorTokens.onPrimary,
                            ),
                            SpacingTokens.horizontalSpaceXs,
                            Text(
                              '${moment.images!.length}',
                              style: TypographyTokens.labelMedium.copyWith(
                                color: ColorTokens.onPrimary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // 内容区域
            Expanded(
              flex: 2,
              child: Padding(
                padding: SpacingTokens.paddingMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      moment.content ?? '暂无内容',
                      style: TypographyTokens.titleSmall.copyWith(
                        color: ColorTokens.onSurface,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                   const Spacer(),
                    Row(
                      children: [
                       const Icon(
                          Icons.favorite_border,
                          size: SpacingTokens.space4,
                          color: ColorTokens.onSurfaceVariant,
                        ),
                        SpacingTokens.horizontalSpaceXs,
                        Text(
                          '${moment.likeCount ?? 0}',
                          style: TypographyTokens.labelMedium.copyWith(
                            color: ColorTokens.onSurfaceVariant,
                          ),
                        ),
                        SpacingTokens.horizontalSpaceMd,
                       const Icon(
                          Icons.chat_bubble_outline,
                          size: SpacingTokens.space4,
                          color: ColorTokens.onSurfaceVariant,
                        ),
                        SpacingTokens.horizontalSpaceXs,
                        Text(
                          '${moment.commentCount ?? 0}',
                          style: TypographyTokens.labelMedium.copyWith(
                            color: ColorTokens.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getMomentTypeColor(String? type) {
    switch (type) {
      case 'fishing_record':
        return ColorTokens.primary;
      case 'experience_share':
        return ColorTokens.success;
      case 'question':
        return ColorTokens.warning;
      default:
        return ColorTokens.outline;
    }
  }

  String _getMomentTypeLabel(String? type) {
    switch (type) {
      case 'fishing_record':
        return '钓鱼记录';
      case 'experience_share':
        return '经验分享';
      case 'question':
        return '问答';
      default:
        return '动态';
    }
  }
}

class _ModernSpotCard extends StatelessWidget {
  final FishingSpotVo spot;

 const _ModernSpotCard({required this.spot});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // 导航到钓点详情
        context.navigateTo(
          AppRoutes.fishingSpotDetail,
          extra: FishingSpotDetailRouteData(spot: spot),
        );
      },
      child: Container(
        decoration: BoxDecoration(
          color: ColorTokens.surfaceContainerLowest,
          borderRadius: ShapeTokens.borderRadiusLg,
          boxShadow: ElevationTokens.cardShadow(context),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片区域
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  Container(
                    decoration: const BoxDecoration(
                      borderRadius: ShapeTokens.borderRadiusTopLg,
                      color: ColorTokens.surfaceVariant,
                    ),
                    child: (spot.mainImage != null && spot.mainImage!.isNotEmpty) ||
                            (spot.images != null && spot.images!.isNotEmpty)
                        ? ClipRRect(
                            borderRadius: ShapeTokens.borderRadiusTopLg,
                            child: CachedNetworkImage(
                              imageUrl: spot.mainImage?.isNotEmpty == true 
                                  ? spot.mainImage! 
                                  : spot.images!.first,
                              fit: BoxFit.cover,
                              width: double.infinity,
                              height: double.infinity,
                              errorWidget: (context, url, error) => Center(
                                child: const Icon(
                                  Icons.location_on_outlined,
                                  size: SpacingTokens.space10,
                                  color: ColorTokens.disabled,
                                ),
                              ),
                            ),
                          )
                        : Center(
                            child: const Icon(
                              Icons.location_on_outlined,
                              size: SpacingTokens.space10,
                              color: ColorTokens.disabled,
                            ),
                          ),
                  ),

                  // 官方认证标识
                  if (spot.isOfficial == true)
                    Positioned(
                      top: SpacingTokens.space3,
                      left: SpacingTokens.space3,
                      child: Container(
                        padding: SpacingTokens.symmetric(
                          horizontal: SpacingTokens.space2,
                          vertical: SpacingTokens.space1,
                        ),
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            colors: [ColorTokens.primary, ColorTokens.secondary],
                          ),
                          borderRadius: ShapeTokens.borderRadiusSm,
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                           const Icon(
                              Icons.verified,
                              size: SpacingTokens.space3,
                              color: ColorTokens.onPrimary,
                            ),
                            SpacingTokens.horizontalSpaceXs,
                            Text(
                              '官方',
                              style: TypographyTokens.labelMedium.copyWith(
                                color: ColorTokens.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // 内容区域
            Expanded(
              flex: 2,
              child: Padding(
                padding: SpacingTokens.paddingMd,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      spot.name,
                      style: TypographyTokens.titleSmall.copyWith(
                        color: ColorTokens.onSurface,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SpacingTokens.verticalSpaceXs,
                    Row(
                      children: [
                        Icon(
                          Icons.location_on,
                          size: TypographyTokens.bodyMedium.fontSize!,
                          color: ColorTokens.onSurfaceVariant,
                        ),
                        SpacingTokens.horizontal(SpacingTokens.space1 * 0.5),
                        Expanded(
                          child: Text(
                            spot.address,
                            style: TypographyTokens.labelMedium.copyWith(
                              color: ColorTokens.onSurfaceVariant,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                   const Spacer(),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: SpacingTokens.space1 + SpacingTokens.space1 * 0.5,
                            vertical: SpacingTokens.space1 * 0.5,
                          ),
                          decoration: BoxDecoration(
                            color: (spot.isPaid == true)
                                ? ColorTokens.warningContainer
                                : ColorTokens.successContainer,
                            borderRadius: ShapeTokens.borderRadiusXs,
                          ),
                          child: Text(
                            (spot.isPaid == true) ? '收费' : '免费',
                            style: TypographyTokens.labelSmall.copyWith(
                              color: (spot.isPaid == true)
                                  ? ColorTokens.onWarningContainer
                                  : ColorTokens.onSuccessContainer,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                       const Spacer(),
                        Icon(
                          Icons.check_circle_outline,
                          size: TypographyTokens.bodyMedium.fontSize!,
                          color: ColorTokens.onSurfaceVariant,
                        ),
                        SpacingTokens.horizontal(SpacingTokens.space1 * 0.5),
                        Text(
                          '${spot.checkinCount}',
                          style: TypographyTokens.labelMedium.copyWith(
                            color: ColorTokens.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// 加载卡片
class _LoadingCard extends StatelessWidget {
 const _LoadingCard();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorTokens.surfaceContainerLowest,
        borderRadius: ShapeTokens.borderRadiusLg,
        boxShadow: ElevationTokens.cardShadow(context),
      ),
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: SpacingTokens.space1 * 0.5,
          color: ColorTokens.disabled,
        ),
      ),
    );
  }
}
