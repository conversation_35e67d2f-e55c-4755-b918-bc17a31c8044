import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class ModernFeatureGrid extends StatelessWidget {
  const ModernFeatureGrid({super.key});

  @override
  Widget build(BuildContext context) {
    final features = [
      FeatureItem(
        icon: Icons.bookmark_outline,
        title: '我的收藏',
        color: Theme.of(context).colorScheme.error,
        route: AppRoutes.myBookmarks,
      ),
      FeatureItem(
        icon: Icons.location_on_outlined,
        title: '钓点记录',
        color: Theme.of(context).colorScheme.success,
        route: AppRoutes.mySpots,
      ),
      FeatureItem(
        icon: Icons.photo_library_outlined,
        title: '相册',
        color: Theme.of(context).colorScheme.secondary,
        route: AppRoutes.myAlbum,
      ),
      FeatureItem(
        icon: Icons.history,
        title: '浏览历史',
        color: Theme.of(context).colorScheme.info,
        route: AppRoutes.browsingHistory,
      ),
      FeatureItem(
        icon: Icons.analytics_outlined,
        title: '钓鱼统计',
        color: Theme.of(context).colorScheme.primary,
        route: AppRoutes.fishingStatistics,
      ),
      FeatureItem(
        icon: Icons.groups_outlined,
        title: '钓友圈',
        color: Theme.of(context).colorScheme.tertiary,
        route: AppRoutes.followedUsers,
      ),
      FeatureItem(
        icon: Icons.calendar_today_outlined,
        title: '钓鱼计划',
        color: Theme.of(context).colorScheme.warning,
        route: AppRoutes.fishingPlans,
      ),
    ];

    return Container(
      margin: SpacingTokens.marginLg,
      child: Container(
        padding: SpacingTokens.paddingLg,
        decoration: ShapeTokens.cardDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: ElevationTokens.shadow3(context),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Static Header
            Row(
              children: [
                Container(
                  padding: SpacingTokens.paddingSm,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primaryContainer,
                        Theme.of(context).colorScheme.secondaryContainer,
                      ],
                    ),
                    borderRadius: ShapeTokens.borderRadiusMd,
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline,
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.apps,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 22,
                  ),
                ),
                SpacingTokens.horizontalSpaceMd,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '功能中心',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                    Text(
                      '探索更多精彩功能',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                    ),
                  ],
                ),
              ],
            ),
            SpacingTokens.verticalSpaceLg,
            // Enhanced grid with staggered animations
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 4,
                childAspectRatio: 0.95,
                crossAxisSpacing: SpacingTokens.space3,
                mainAxisSpacing: SpacingTokens.space4,
              ),
              itemCount: features.length,
              itemBuilder: (context, index) {
                return _buildFeatureItem(context, features[index], index);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureItem(BuildContext context, FeatureItem item, int index) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        if (item.customAction != null) {
          item.customAction!();
        } else if (item.comingSoon) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${item.title}功能即将上线')),
          );
        } else if (item.route != null) {
          // 使用新的导航扩展方法
          context.navigateTo(item.route!);
        }
      },
      child: Container(
        decoration: ShapeTokens.cardDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color: item.color.withValues(alpha: 0.1),
              blurRadius: SpacingTokens.space2,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    item.color.withValues(alpha: 0.15),
                    item.color.withValues(alpha: 0.25),
                  ],
                ),
                borderRadius: ShapeTokens.borderRadiusLg,
                border: Border.all(
                  color: item.color.withValues(alpha: 0.3),
                  width: 1.5,
                ),
                boxShadow: ElevationTokens.shadow2(context),
              ),
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Subtle glow effect
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: RadialGradient(
                        colors: [
                          item.color.withValues(alpha: 0.1),
                          ColorTokens.transparent,
                        ],
                      ),
                    ),
                  ),
                  Icon(
                    item.icon,
                    color: item.color,
                    size: 16,
                  ),
                  if (item.comingSoon)
                    Positioned(
                      top: -2,
                      right: -2,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 1,
                        ),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              Theme.of(context).colorScheme.error,
                              Theme.of(context).colorScheme.warning,
                            ],
                          ),
                          borderRadius: ShapeTokens.borderRadiusSm,
                          boxShadow: ElevationTokens.shadow1(context),
                        ),
                        child: Text(
                          'SOON',
                          style: Theme.of(context)
                              .textTheme
                              .labelSmall
                              ?.copyWith(
                                fontWeight: TypographyTokens.weightBold,
                                color: Theme.of(context).colorScheme.onError,
                                fontSize: 8,
                              ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            const SizedBox(height: SpacingTokens.space1),
            // Enhanced title with better spacing
            Text(
              item.title,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: TypographyTokens.weightMedium,
                    fontSize: 10,
                  ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}

class FeatureItem {
  final IconData icon;
  final String title;
  final Color color;
  final String? route;
  final bool comingSoon;
  final VoidCallback? customAction;

  const FeatureItem({
    required this.icon,
    required this.title,
    required this.color,
    this.route,
    this.comingSoon = false,
    this.customAction,
  });
}
