import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';

class ModernProfileHeader extends StatefulWidget {
 const ModernProfileHeader({super.key});

  @override
  State<ModernProfileHeader> createState() => _ModernProfileHeaderState();
}

class _ModernProfileHeaderState extends State<ModernProfileHeader>
    with TickerProviderStateMixin {
  late AnimationController _shimmerController;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();
    _shimmerController = AnimationController(
      duration: MotionTokens.durationExtraLong,
      vsync: this,
    )..repeat();

    _shimmerAnimation = CurvedAnimation(
      parent: _shimmerController,
      curve: Curves.linear,
    );
  }

  @override
  void dispose() {
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: SpacingTokens.space24 * 3.5, // ≈ 336pt, following 8pt grid
      margin: SpacingTokens.paddingLg,
      child: Stack(
        children: [
          // Modern gradient card with better readability
          Container(
            decoration: BoxDecoration(
              borderRadius: ShapeTokens.borderRadiusXl,
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary,
                ],
              ),
              boxShadow: [
                ...ElevationTokens.shadow4(context),
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: SpacingTokens.space4,
                  offset: const Offset(0, SpacingTokens.space1),
                ),
              ],
            ),
            child: Stack(
              children: [
                // Shimmer effect
                Positioned.fill(
                  child: AnimatedBuilder(
                    animation: _shimmerAnimation,
                    builder: (context, child) {
                      return Container(
                        decoration: BoxDecoration(
                          borderRadius: ShapeTokens.borderRadiusXl,
                          gradient: LinearGradient(
                            begin: Alignment(-1 + 2 * _shimmerAnimation.value, -1),
                            end: Alignment(1 + 2 * _shimmerAnimation.value, 1),
                            colors: [
                              ColorTokens.transparent,
                              Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.05),
                              ColorTokens.transparent,
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                // Content
                Padding(
                  padding: SpacingTokens.paddingLg,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          // Avatar with glow effect
                          GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              // 头像点击跳转到设置页面，已登录用户直接跳转
                              context.navigateTo(AppRoutes.profileSetting);
                            },
                            child: Hero(
                              tag: 'user_avatar',
                              child: Container(
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
                                      blurRadius: SpacingTokens.space5,
                                      spreadRadius: SpacingTokens.space1 / 2,
                                    ),
                                  ],
                                ),
                                child: Container(
                                  padding: const EdgeInsets.all(SpacingTokens.xs / 2),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    gradient: LinearGradient(
                                      colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.tertiary],
                                    ),
                                  ),
                                  child: Container(
                                    padding: const EdgeInsets.all(SpacingTokens.xs / 2),
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Theme.of(context).colorScheme.onPrimary,
                                    ),
                                    child: Selector<AuthViewModel, String?>(
                                      selector: (context, viewModel) =>
                                          viewModel.currentUser?.avatarUrl,
                                      builder: (context, avatarUrl, child) {
                                        return CircleAvatar(
                                          radius: SpacingTokens.space12 - SpacingTokens.space1, // ≈ 44pt
                                          backgroundImage: avatarUrl != null &&
                                                  avatarUrl.isNotEmpty
                                              ? CachedNetworkImageProvider(
                                                  '$avatarUrl?x-oss-process=image/resize,m_lfit,w_200/crop,g_center/quality,Q_100',
                                                )
                                              : const AssetImage(
                                                      'assets/default_avatar.png')
                                                  as ImageProvider,
                                        );
                                      },
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                         SpacingTokens.horizontalSpaceLg,
                          // User info
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Selector<AuthViewModel, String?>(
                                  selector: (context, viewModel) =>
                                      viewModel.currentUser?.name,
                                  builder: (context, name, child) {
                                    return Text(
                                      name ?? '未设置昵称',
                                      style: TypographyTokens.headlineMedium.copyWith(
                                        color: Theme.of(context).colorScheme.onPrimary,
                                        fontWeight: TypographyTokens.weightBold,
                                        letterSpacing: 0.5,
                                      ),
                                    );
                                  },
                                ),
                               SpacingTokens.verticalSpaceSm,
                                Selector<AuthViewModel, String?>(
                                  selector: (context, viewModel) =>
                                      viewModel.currentUser?.introduce,
                                  builder: (context, description, child) {
                                    return Text(
                                      description ?? '这个人很懒，什么都没留下~',
                                      style: TypographyTokens.bodyMedium.copyWith(
                                        color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.9),
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                     const Spacer(),
                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.edit_outlined,
                              label: '编辑资料',
                              onTap: () {
                                HapticFeedback.lightImpact();
                                // 编辑资料功能，已登录用户直接跳转
                                context.navigateTo(AppRoutes.profileSetting);
                              },
                            ),
                          ),
                         SpacingTokens.horizontalSpaceMd,
                          Expanded(
                            child: _buildActionButton(
                              icon: Icons.qr_code_outlined,
                              label: '我的二维码',
                              onTap: () {
                                HapticFeedback.lightImpact();
                                // 二维码功能，已登录用户直接跳转
                                context.navigateTo(AppRoutes.myQrCode);
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Notification badge
          Positioned(
            top: SpacingTokens.space5,
            right: SpacingTokens.space5,
            child: GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                // 通知功能，已登录用户直接跳转
                context.navigateTo(AppRoutes.notifications);
              },
              child: Container(
                padding: SpacingTokens.paddingMd,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                  borderRadius: ShapeTokens.borderRadiusLg,
                  border: Border.all(
                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Stack(
                  children: [
                   Icon(
                      Icons.notifications_outlined,
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: SpacingTokens.space6,
                    ),
                    Selector<AuthViewModel, int>(
                      selector: (context, viewModel) => viewModel.unreadNotifications,
                      builder: (context, unreadCount, child) {
                        if (unreadCount > 0) {
                          return Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              width: SpacingTokens.space2,
                              height: SpacingTokens.space2,
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.error,
                                shape: BoxShape.circle,
                              ),
                            ),
                          );
                        }
                        return const SizedBox.shrink();
                      },
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: SpacingTokens.paddingVerticalMd,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.1),
          borderRadius: ShapeTokens.borderRadiusLg,
          border: Border.all(
            color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.onPrimary,
              size: SpacingTokens.space4 + SpacingTokens.space1, // 20pt
            ),
           SpacingTokens.horizontalSpaceSm,
            Text(
              label,
              style: TypographyTokens.labelLarge.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
          ],
        ),
      ),
    );
  }
}