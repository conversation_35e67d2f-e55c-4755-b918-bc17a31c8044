import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class ModernSettingsSection extends StatefulWidget {
 const ModernSettingsSection({super.key});

  @override
  State<ModernSettingsSection> createState() => _ModernSettingsSectionState();
}

class _ModernSettingsSectionState extends State<ModernSettingsSection>
    with TickerProviderStateMixin {
  late List<AnimationController> _controllers;
  late List<Animation<double>> _scaleAnimations;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();
   const itemCount = 2;
    _controllers = List.generate(itemCount, (index) => AnimationController(
      duration: Duration(
        milliseconds: MotionTokens.durationLong4.inMilliseconds + (index * MotionTokens.durationShort2.inMilliseconds),
      ),
      vsync: this,
    ));

    _scaleAnimations = _controllers.map((controller) => 
      Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(
        parent: controller,
        curve: MotionTokens.emphasized,
      ))
    ).toList();

    _slideAnimations = _controllers.map((controller) => 
      Tween<Offset>(begin: const Offset(0.3, 0), end: Offset.zero).animate(CurvedAnimation(
        parent: controller,
        curve: MotionTokens.emphasizedDecelerate,
      ))
    ).toList();

    // Start animations with staggered delay
    WidgetsBinding.instance.addPostFrameCallback((_) {
      for (int i = 0; i < _controllers.length; i++) {
        Future.delayed(Duration(milliseconds: i * MotionTokens.durationShort3.inMilliseconds), () {
          if (mounted) _controllers[i].forward();
        });
      }
    });
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settingsItems = [
      SettingsItem(
        icon: Icons.settings_outlined,
        title: '设置',
        subtitle: '账号与隐私设置',
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primaryContainer,
          ],
        ),
        onTap: () {
          // 用户已登录，直接跳转设置页面
          context.navigateTo(AppRoutes.profileSetting);
        },
      ),
      SettingsItem(
        icon: Icons.help_outline,
        title: '帮助与反馈',
        subtitle: '联系我们',
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.tertiary,
            Theme.of(context).colorScheme.tertiaryContainer,
          ],
        ),
        onTap: () {
          // 帮助与反馈不需要登录
          context.navigateTo(AppRoutes.feedback);
        },
      ),
    ];

    return Container(
      margin: SpacingTokens.marginLg,
      child: Column(
        children: settingsItems.asMap().entries.map((entry) {
          final index = entry.key;
          final item = entry.value;
          return Padding(
            padding: SpacingTokens.paddingVerticalMd,
            child: AnimatedBuilder(
              animation: _controllers[index],
              builder: (context, child) {
                return SlideTransition(
                  position: _slideAnimations[index],
                  child: ScaleTransition(
                    scale: _scaleAnimations[index],
                    child: _buildSettingsItem(context, item, index),
                  ),
                );
              },
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSettingsItem(BuildContext context, SettingsItem item, int index) {
    return GestureDetector(
      onTapDown: (_) => _controllers[index].reverse(),
      onTapUp: (_) => _controllers[index].forward(),
      onTapCancel: () => _controllers[index].forward(),
      onTap: () {
        HapticFeedback.lightImpact();
        item.onTap();
      },
      child: Container(
        padding: SpacingTokens.paddingLg,
        decoration: BoxDecoration(
          borderRadius: ShapeTokens.borderRadiusXl,
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.surface,
              Theme.of(context).colorScheme.surfaceContainerHighest,
            ],
          ),
          boxShadow: [
            ...ElevationTokens.shadow3(context),
            BoxShadow(
              color: item.gradient.colors.first.withValues(alpha: 0.1),
              blurRadius: 15,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Subtle floating orb
            Positioned(
              top: -SpacingTokens.space2,
              right: -SpacingTokens.space2,
              child: Container(
                width: SpacingTokens.space16,
                height: SpacingTokens.space16,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      item.gradient.colors.first.withValues(alpha: 0.05),
                      Theme.of(context).colorScheme.surface.withValues(alpha: 0.0),
                    ],
                  ),
                ),
              ),
            ),
            // Main content
            Row(
              children: [
                // Enhanced icon container
                Container(
                  padding: SpacingTokens.paddingMd,
                  decoration: BoxDecoration(
                    gradient: item.gradient,
                    borderRadius: ShapeTokens.borderRadiusLg,
                    boxShadow: ElevationTokens.shadow2(context),
                  ),
                  child: Icon(
                    item.icon,
                    color: Theme.of(context).colorScheme.onPrimary,
                    size: SpacingTokens.space6,
                  ),
                ),
                SpacingTokens.horizontalSpaceLg,
                // Enhanced text content
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SpacingTokens.verticalSpaceXs,
                      Text(
                        item.subtitle,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                // Enhanced arrow icon
                Container(
                  padding: SpacingTokens.paddingSm,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: ShapeTokens.borderRadiusMd,
                  ),
                  child: Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: SpacingTokens.space4,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class SettingsItem {
  final IconData icon;
  final String title;
  final String subtitle;
  final Gradient gradient;
  final VoidCallback onTap;

 const SettingsItem({
    required this.icon,
    required this.title,
    required this.subtitle,
    required this.gradient,
    required this.onTap,
  });
}
