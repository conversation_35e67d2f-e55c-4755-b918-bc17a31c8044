import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class ModernProfileSkeleton extends StatefulWidget {
 const ModernProfileSkeleton({super.key});

  @override
  State<ModernProfileSkeleton> createState() => _ModernProfileSkeletonState();
}

class _ModernProfileSkeletonState extends State<ModernProfileSkeleton>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: MotionTokens.durationLong2,
      vsync: this,
    )..repeat(reverse: true);

    _waveController = AnimationController(
      duration: MotionTokens.durationLong4,
      vsync: this,
    )..repeat();

    _pulseAnimation = CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    );

    _waveAnimation = CurvedAnimation(
      parent: _waveController,
      curve: Curves.linear,
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.surfaceContainerLowest,
      body: Stack(
        children: [
          // Background gradient with subtle animation
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  ColorTokens.surface,
                  ColorTokens.surfaceContainer,
                  ColorTokens.surfaceContainerHigh,
                ],
              ),
            ),
          ),
          // Floating orbs animation
          AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return Stack(
                children: [
                  Positioned(
                    top: SpacingTokens.space12 * 2 + SpacingTokens.space1 + (_waveAnimation.value * SpacingTokens.space5),
                    right: SpacingTokens.space12 + SpacingTokens.space1,
                    child: Container(
                      width: SpacingTokens.space16 - SpacingTokens.space1,
                      height: SpacingTokens.space16 - SpacingTokens.space1,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            ColorTokens.primary.withValues(alpha: 0.03),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: SpacingTokens.space24 * 2 + SpacingTokens.space2 + (_waveAnimation.value * -SpacingTokens.space3 + SpacingTokens.space1),
                    left: SpacingTokens.space8 - SpacingTokens.space1,
                    child: Container(
                      width: SpacingTokens.space20,
                      height: SpacingTokens.space20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: RadialGradient(
                          colors: [
                            ColorTokens.secondary.withValues(alpha: 0.03),
                            Colors.transparent,
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          // Main content
          SafeArea(
            child: Shimmer.fromColors(
              baseColor: ColorTokens.outline.withValues(alpha: 0.15),
              highlightColor: ColorTokens.outline.withValues(alpha: 0.05),
              period: MotionTokens.loadingDuration,
              child: SingleChildScrollView(
                physics: const BouncingScrollPhysics(),
                child: Column(
                  children: [
                    // Enhanced header skeleton
                    Container(
                      height: SpacingTokens.space24 * 3 - SpacingTokens.space2,
                      margin: SpacingTokens.marginLg,
                      decoration: BoxDecoration(
                        borderRadius: ShapeTokens.borderRadiusXl,
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: const [
                            ColorTokens.surface,
                            ColorTokens.surfaceContainerLowest,
                          ],
                        ),
                        boxShadow: ElevationTokens.shadow5(context),
                      ),
                      child: Stack(
                        children: [
                          // Avatar placeholder with pulse animation
                          Positioned(
                            top: SpacingTokens.space8 - SpacingTokens.space1,
                            left: SpacingTokens.space6,
                            child: AnimatedBuilder(
                              animation: _pulseAnimation,
                              builder: (context, child) {
                                return Container(
                                  width: SpacingTokens.space20 + SpacingTokens.space10 + (_pulseAnimation.value * SpacingTokens.space1),
                                  height: SpacingTokens.space20 + SpacingTokens.space10 + (_pulseAnimation.value * SpacingTokens.space1),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: ColorTokens.outline.withValues(alpha: 0.2 + (_pulseAnimation.value * 0.1)),
                                  ),
                                );
                              },
                            ),
                          ),
                          // Name and description placeholders
                          Positioned(
                            top: SpacingTokens.space10,
                            left: SpacingTokens.space24 * 1.5 - SpacingTokens.space2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Container(
                                  width: SpacingTokens.space24 * 1.25,
                                  height: SpacingTokens.space5,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.outline.withValues(alpha: 0.3),
                                    borderRadius: ShapeTokens.borderRadiusSm,
                                  ),
                                ),
                                SpacingTokens.verticalSpaceMd,
                                Container(
                                  width: SpacingTokens.space20 * 2,
                                  height: SpacingTokens.space3 + SpacingTokens.space1,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.outline.withValues(alpha: 0.25),
                                    borderRadius: BorderRadius.circular(ShapeTokens.radiusXs + 3),
                                  ),
                                ),
                                SpacingTokens.verticalSpaceSm,
                                Container(
                                  width: SpacingTokens.space24 * 1.5 - SpacingTokens.space8,
                                  height: SpacingTokens.space3 + SpacingTokens.space1,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.outline.withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(ShapeTokens.radiusXs + 3),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // Action buttons placeholders
                          Positioned(
                            bottom: SpacingTokens.space6,
                            left: SpacingTokens.space6,
                            right: SpacingTokens.space6,
                            child: Row(
                              children: [
                                Expanded(
                                  child: Container(
                                    height: SpacingTokens.space10,
                                    decoration: BoxDecoration(
                                      color: ColorTokens.outline.withValues(alpha: 0.25),
                                      borderRadius: ShapeTokens.borderRadiusLg,
                                    ),
                                  ),
                                ),
                                SpacingTokens.horizontalSpaceMd,
                                Expanded(
                                  child: Container(
                                    height: SpacingTokens.space10,
                                    decoration: BoxDecoration(
                                      color: ColorTokens.outline.withValues(alpha: 0.25),
                                      borderRadius: ShapeTokens.borderRadiusLg,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Enhanced stats skeleton
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: SpacingTokens.space5),
                      padding: SpacingTokens.paddingLg,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: const [
                            ColorTokens.surface,
                            ColorTokens.surfaceContainerLowest,
                          ],
                        ),
                        borderRadius: ShapeTokens.borderRadiusXl,
                        boxShadow: ElevationTokens.shadow5(context),
                      ),
                      child: Row(
                        children: List.generate(3, (index) {
                          return Expanded(
                            child: Column(
                              children: [
                                AnimatedBuilder(
                                  animation: _pulseAnimation,
                                  builder: (context, child) {
                                    return Container(
                                      width: SpacingTokens.space16 - SpacingTokens.space1 + (_pulseAnimation.value * SpacingTokens.space1),
                                      height: SpacingTokens.space16 - SpacingTokens.space1 + (_pulseAnimation.value * SpacingTokens.space1),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: ColorTokens.outline.withValues(alpha: 0.2 + (_pulseAnimation.value * 0.05)),
                                      ),
                                    );
                                  },
                                ),
                                SpacingTokens.verticalSpaceMd,
                                Container(
                                  width: SpacingTokens.space12 + SpacingTokens.space1,
                                  height: SpacingTokens.space3,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.outline.withValues(alpha: 0.25),
                                    borderRadius: BorderRadius.circular(ShapeTokens.radiusXs + 2),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),
                      ),
                    ),
                    
                    SpacingTokens.verticalSpaceLg,
                    
                    // Enhanced quick actions skeleton
                    Container(
                      height: SpacingTokens.space16 * 2 + SpacingTokens.space1,
                      margin: EdgeInsets.symmetric(horizontal: SpacingTokens.space5),
                      child: Row(
                        children: List.generate(3, (index) {
                          return Expanded(
                            child: Container(
                              margin: EdgeInsets.only(
                                left: index > 0 ? SpacingTokens.space2 - 0.5 : 0,
                                right: index < 2 ? SpacingTokens.space2 - 0.5 : 0,
                              ),
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: const [
                                    ColorTokens.surfaceContainerHigh,
                                    ColorTokens.surfaceContainer,
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(ShapeTokens.radiusXl - 3),
                                boxShadow: ElevationTokens.shadow3(context),
                              ),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    width: SpacingTokens.space12 + SpacingTokens.space1,
                                    height: SpacingTokens.space12 + SpacingTokens.space1,
                                    decoration: BoxDecoration(
                                      color: ColorTokens.outline.withValues(alpha: 0.3),
                                      borderRadius: ShapeTokens.borderRadiusLg,
                                    ),
                                  ),
                                  SpacingTokens.verticalSpaceMd,
                                  Container(
                                    width: SpacingTokens.space16 - SpacingTokens.space1,
                                    height: SpacingTokens.space3,
                                    decoration: BoxDecoration(
                                      color: ColorTokens.outline.withValues(alpha: 0.25),
                                      borderRadius: BorderRadius.circular(ShapeTokens.radiusXs + 2),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                    
                    SpacingTokens.verticalSpaceLg,
                    
                    // Enhanced feature grid skeleton
                    Container(
                      margin: SpacingTokens.marginLg,
                      padding: SpacingTokens.paddingLg,
                      decoration: BoxDecoration(
                        gradient: const LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: const [
                            ColorTokens.surface,
                            ColorTokens.surfaceContainerLowest,
                          ],
                        ),
                        borderRadius: ShapeTokens.borderRadiusXl,
                        boxShadow: ElevationTokens.shadow5(context),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Header
                          Row(
                            children: [
                              Container(
                                width: SpacingTokens.space10 - SpacingTokens.space1,
                                height: SpacingTokens.space10 - SpacingTokens.space1,
                                decoration: BoxDecoration(
                                  color: ColorTokens.outline.withValues(alpha: 0.25),
                                  borderRadius: ShapeTokens.borderRadiusMd,
                                ),
                              ),
                             const SizedBox(width: SpacingTokens.space3 + SpacingTokens.space1),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: SpacingTokens.space20,
                                    height: SpacingTokens.space4,
                                    decoration: BoxDecoration(
                                      color: ColorTokens.outline.withValues(alpha: 0.3),
                                      borderRadius: ShapeTokens.borderRadiusSm,
                                    ),
                                  ),
                                 const SizedBox(height: SpacingTokens.space2 - SpacingTokens.space1),
                                  Container(
                                    width: SpacingTokens.space24 * 1.25,
                                    height: SpacingTokens.space3,
                                    decoration: BoxDecoration(
                                      color: ColorTokens.outline.withValues(alpha: 0.2),
                                      borderRadius: BorderRadius.circular(ShapeTokens.radiusXs + 2),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          SpacingTokens.verticalSpaceLg,
                          // Grid items
                          GridView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 4,
                              childAspectRatio: 0.9,
                              crossAxisSpacing: 16,
                              mainAxisSpacing: 24,
                            ),
                            itemCount: 8,
                            itemBuilder: (context, index) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: ColorTokens.surface,
                                  borderRadius: BorderRadius.circular(ShapeTokens.radiusLg + 4),
                                  boxShadow: ElevationTokens.shadow2(context),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      width: SpacingTokens.space12 + SpacingTokens.space1,
                                      height: SpacingTokens.space12 + SpacingTokens.space1,
                                      decoration: BoxDecoration(
                                        color: ColorTokens.outline.withValues(alpha: 0.25),
                                        borderRadius: ShapeTokens.borderRadiusLg,
                                      ),
                                    ),
                                    SpacingTokens.verticalSpaceMd,
                                    Container(
                                      width: SpacingTokens.space10,
                                      height: SpacingTokens.space2 + SpacingTokens.space1,
                                      decoration: BoxDecoration(
                                        color: ColorTokens.outline.withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(ShapeTokens.radiusXs + 1),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                    
                   const SizedBox(height: SpacingTokens.space8 - SpacingTokens.space1),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
