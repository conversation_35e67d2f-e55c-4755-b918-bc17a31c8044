import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class ModernQuickActions extends StatelessWidget {
 const ModernQuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    final actions = [
      QuickAction(
        icon: Icons.camera_alt_outlined,
        title: '发布动态',
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.secondary,
          ],
        ),
        onTap: () {
          // 使用新的导航扩展方法
          context.navigateTo(AppRoutes.publishMoment);
        },
      ),
      QuickAction(
        icon: Icons.location_on_outlined,
        title: '添加钓点',
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.tertiary,
            Theme.of(context).colorScheme.primary,
          ],
        ),
        onTap: () {
          // 使用新的导航扩展方法
          context.navigateTo(
            AppRoutes.createSpot,
            extra: {
              'onSpotCreated': (int spotId) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('钓点创建成功！')),
                );
              },
            },
          );
        },
      ),
      QuickAction(
        icon: Icons.explore_outlined,
        title: '探索',
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.info,
            Theme.of(context).colorScheme.primary,
          ],
        ),
        onTap: () {
          // 使用新的导航扩展方法
          context.navigateTo(AppRoutes.universalSearch);
        },
      ),
    ];

    return Container(
      height: 130,
      margin: SpacingTokens.symmetric(
        horizontal: SpacingTokens.space5,
        vertical: SpacingTokens.space3,
      ),
      child: Row(
        children: actions.asMap().entries.map((entry) {
          final index = entry.key;
          final action = entry.value;
          return Expanded(
            child: Padding(
              padding: SpacingTokens.only(
                left: index > 0 ? SpacingTokens.space2 * 0.9375 : 0, // 7.5
                right: index < actions.length - 1 ? SpacingTokens.space2 * 0.9375 : 0,
              ),
              child: _buildActionCard(context, action, index),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildActionCard(BuildContext context, QuickAction action, int index) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        action.onTap();
      },
      child: Container(
        decoration: ShapeTokens.buttonDecoration(
          gradient: action.gradient,
          borderRadius: BorderRadius.circular(SpacingTokens.space6 + SpacingTokens.space1), // 25
          boxShadow: [
            BoxShadow(
              color: action.gradient.colors.first.withValues(alpha: 0.4),
              blurRadius: SpacingTokens.space5,
              offset: const Offset(0, 10),
            ),
            BoxShadow(
              color: action.gradient.colors.last.withValues(alpha: 0.2),
              blurRadius: SpacingTokens.space3,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Enhanced gloss effect
            Positioned(
              top: -SpacingTokens.space8 + SpacingTokens.space2, // -30
              right: -SpacingTokens.space8 + SpacingTokens.space2,
              child: Container(
                width: SpacingTokens.space20,
                height: SpacingTokens.space20,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Color.fromRGBO(255, 255, 255, 0.15),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),
            // Subtle floating orb
            Positioned(
              bottom: -SpacingTokens.space5,
              left: -SpacingTokens.space5,
              child: Container(
                width: SpacingTokens.space16 - SpacingTokens.space1, // 60
                height: SpacingTokens.space16 - SpacingTokens.space1,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      Color.fromRGBO(255, 255, 255, 0.08),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            ),

            // Content
            Padding(
              padding: SpacingTokens.paddingMd.add(SpacingTokens.paddingXs), // 18
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Icon with enhanced styling
                  Container(
                    padding: SpacingTokens.paddingMd.subtract(SpacingTokens.paddingXs), // 14
                    decoration: BoxDecoration(
                      color: ColorTokens.onPrimary.withValues(alpha: 0.25),
                      borderRadius: BorderRadius.circular(
                        SpacingTokens.space4 + SpacingTokens.space1, // 18
                      ),
                      border: Border.all(
                        color: ColorTokens.onPrimary.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Icon(
                      action.icon,
                      color: ColorTokens.onPrimary,
                      size: SpacingTokens.space8 - SpacingTokens.space1, // 28
                    ),
                  ),
                  const SizedBox(height: SpacingTokens.space3),
                  // Title with better typography
                  Flexible(
                    child: Text(
                      action.title,
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: ColorTokens.onPrimary,
                        fontWeight: TypographyTokens.weightBold,
                        shadows: [
                          const Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3,
                            color: Color.fromRGBO(0, 0, 0, 0.26),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class QuickAction {
  final IconData icon;
  final String title;
  final Gradient gradient;
  final VoidCallback onTap;

 const QuickAction({
    required this.icon,
    required this.title,
    required this.gradient,
    required this.onTap,
  });
}
