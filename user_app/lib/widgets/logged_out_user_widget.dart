import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class LoggedOutUserWidget extends StatelessWidget {
  const LoggedOutUserWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.surfaceVariant,
      body: Stack(
        children: [
          // Subtle background pattern
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  ColorTokens.surface,
                  ColorTokens.surfaceVariant,
                ],
              ),
            ),
          ),
          // Static particles (subtle)
          ...List.generate(3, (index) {
            return Positioned(
              left: 50 + (index * 80).toDouble(),
              top: 100 + (index * 120).toDouble(),
              child: Container(
                width: 60 + (index * 15).toDouble(),
                height: 60 + (index * 15).toDouble(),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: RadialGradient(
                    colors: [
                      ColorTokens.primary.withValues(alpha: 0.05),
                      Colors.transparent,
                    ],
                  ),
                ),
              ),
            );
          }),
          // Main content
          SafeArea(
            child: Padding(
              padding: SpacingTokens.paddingLg,
              child: Column(
                children: [
                  // Top login icon
                  Align(
                    alignment: Alignment.topRight,
                    child: GestureDetector(
                      onTap: () => context.navigateTo(AppRoutes.login),
                      child: Container(
                        padding: SpacingTokens.paddingMd,
                        decoration: BoxDecoration(
                          color: ColorTokens.surface,
                          borderRadius: ShapeTokens.borderRadiusLg,
                          boxShadow: [
                            BoxShadow(
                              color:
                                  ColorTokens.onSurface.withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.login_outlined,
                          color: Colors.grey.shade700,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                  const Spacer(),
                  // Central welcome section
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Static Avatar
                      Container(
                        padding: SpacingTokens.paddingSm,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: const LinearGradient(
                            colors: [ColorTokens.primary, ColorTokens.secondary],
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: ColorTokens.primary
                                  .withValues(alpha: 0.3),
                              blurRadius: 20,
                              spreadRadius: 3,
                            ),
                          ],
                        ),
                        child: Container(
                          padding: SpacingTokens.paddingXs,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: ColorTokens.surface,
                          ),
                          child: const CircleAvatar(
                            radius: 60,
                            backgroundImage:
                                AssetImage('assets/default_avatar.png'),
                            backgroundColor: Colors.transparent,
                          ),
                        ),
                      ),
                      const SizedBox(height: 40),
                      // Welcome text with glassmorphism
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 30, vertical: 25),
                        decoration: BoxDecoration(
                          borderRadius: ShapeTokens.borderRadiusXl,
                          color: ColorTokens.surface,
                          boxShadow: [
                            BoxShadow(
                              color:
                                  ColorTokens.onSurface.withValues(alpha: 0.08),
                              blurRadius: 20,
                              offset: Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            Text(
                              '欢迎来到钓鱼世界',
                              style: TextStyle(
                                // fontSize: 24, // 使用 TypographyTokens.headlineMedium 默认字体大小
                                fontWeight: FontWeight.bold,
                                color: Colors.grey.shade800,
                              ),
                            ),
                            const SizedBox(height: SpacingTokens.space2),
                            Text(
                              '登录后体验更多精彩功能',
                              style: TextStyle(
                                // fontSize: 16, // 使用 Typography Tokens 默认字体大小
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.login_outlined,
                          label: '立即登录',
                          onTap: () {
                            HapticFeedback.lightImpact();
                            context.navigateTo(AppRoutes.login);
                          },
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: _buildActionButton(
                          icon: Icons.person_add_outlined,
                          label: '注册账号',
                          onTap: () {
                            HapticFeedback.lightImpact();
                            context.navigateTo(AppRoutes.register);
                          },
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: SpacingTokens.space5),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 16, horizontal: 24),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [ColorTokens.primary, ColorTokens.secondary],
          ),
          borderRadius: ShapeTokens.borderRadiusXl,
          boxShadow: [
            BoxShadow(
              color: Colors.purple.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: ColorTokens.surface,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                color: ColorTokens.surface,
                // fontSize: 16, // 使用 Typography Tokens 默认字体大小
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
