import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/widgets/notification_badge.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class ProfileHeader extends StatelessWidget {
 const ProfileHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: SpacingTokens.paddingMd,
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: const [ColorTokens.primary, ColorTokens.primaryContainer],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: ShapeTokens.borderRadiusXl,
        boxShadow: [
          BoxShadow(
            color: ColorTokens.primary.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          // 头像
          GestureDetector(
            onTap: () => context.navigateTo(AppRoutes.profileSetting),
            child: Hero(
              tag: 'user_avatar',
              child: Container(
                padding: EdgeInsets.all(SpacingTokens.space1),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: ColorTokens.onPrimary, width: 3),
                ),
                child: Selector<AuthViewModel, String?>(
                  selector: (context, viewModel) =>
                      viewModel.currentUser?.avatarUrl,
                  builder: (context, avatarUrl, child) {
                    return CircleAvatar(
                      radius: 35,
                      backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
                          ? CachedNetworkImageProvider(
                              '$avatarUrl?x-oss-process=image/resize,m_lfit,w_200/crop,g_center/quality,Q_100',
                            )
                          : const AssetImage('assets/default_avatar.png')
                              as ImageProvider,
                    );
                  },
                ),
              ),
            ),
          ),
          SpacingTokens.horizontalSpaceMd,

          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Selector<AuthViewModel, String?>(
                  selector: (context, viewModel) => viewModel.currentUser?.name,
                  builder: (context, name, child) {
                    return Text(
                      name ?? '钓友',
                      style: TypographyTokens.titleLarge.copyWith(
                        color: ColorTokens.onPrimary,
                      ),
                    );
                  },
                ),
                SpacingTokens.verticalSpaceXs,
                Selector<AuthViewModel, String?>(
                  selector: (context, viewModel) =>
                      viewModel.currentUser?.introduce,
                  builder: (context, introduce, child) {
                    return Text(
                      introduce?.isNotEmpty == true
                          ? introduce!
                          : '这个人很懒，什么都没留下~',
                      style: TypographyTokens.bodyMedium.copyWith(
                        color: ColorTokens.onPrimary.withValues(alpha: 0.9),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    );
                  },
                ),
              ],
            ),
          ),

          // 快捷操作菜单
          Row(
            children: [
              // 通知图标
              Selector<AuthViewModel, int>(
                selector: (context, viewModel) => viewModel.unreadNotifications,
                builder: (context, unreadNotifications, child) {
                  return NotificationBadge(
                    count: unreadNotifications,
                    size: 20,
                    child: IconButton(
                      icon: const Icon(Icons.notifications_outlined,
                          color: ColorTokens.onPrimary),
                      onPressed: () => context.navigateTo(AppRoutes.notifications),
                    ),
                  );
                },
              ),
              // 二维码图标
              IconButton(
                icon: const Icon(Icons.qr_code, color: ColorTokens.onPrimary),
                onPressed: () => context.navigateTo(AppRoutes.myQrCode),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
