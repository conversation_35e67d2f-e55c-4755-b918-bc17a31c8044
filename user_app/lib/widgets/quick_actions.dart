import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class QuickActions extends StatelessWidget {
 const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [ColorTokens.primary, ColorTokens.primaryContainer],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(ShapeTokens.radiusLg),
        boxShadow: ElevationTokens.level2,
      ),
      child: Padding(
        padding: EdgeInsets.all(SpacingTokens.space4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
               const Icon(
                  Icons.flash_on,
                  color: ColorTokens.onPrimary,
                  size: SpacingTokens.space5,
                ),
                SizedBox(width: SpacingTokens.space3),
                Text(
                  '快捷操作',
                  style: TypographyTokens.bodyLarge.copyWith(
                    color: ColorTokens.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
               const Spacer(),
                Text(
                  '一键直达',
                  style: TypographyTokens.bodySmall.copyWith(
                    color: ColorTokens.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
            SizedBox(height: SpacingTokens.space4),
            Row(
              children: [
                Expanded(
                  child: _buildQuickAction(
                    context,
                    icon: Icons.add_photo_alternate_outlined,
                    title: '发布动态',
                    onTap: () => context.navigateTo(AppRoutes.publishMoment),
                  ),
                ),
                SizedBox(width: SpacingTokens.space4),
                Expanded(
                  child: _buildQuickAction(
                    context,
                    icon: Icons.location_on_outlined,
                    title: '添加钓点',
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                       const SnackBar(content: Text('添加钓点功能即将上线')),
                      );
                    },
                  ),
                ),
                SizedBox(width: SpacingTokens.space4),
                Expanded(
                  child: _buildQuickAction(
                    context,
                    icon: Icons.search,
                    title: '搜索',
                    onTap: () => context.navigateTo(AppRoutes.universalSearch),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAction(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: SpacingTokens.space3, horizontal: SpacingTokens.space2),
        decoration: BoxDecoration(
          color: ColorTokens.onPrimary.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
          border: Border.all(
            color: ColorTokens.onPrimary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: ColorTokens.onPrimary,
              size: SpacingTokens.space6,
            ),
            SizedBox(height: SpacingTokens.space1),
            Text(
              title,
              style: TypographyTokens.bodySmall.copyWith(
                color: ColorTokens.onPrimary,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}