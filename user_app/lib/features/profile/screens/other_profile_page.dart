import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/profile/view_models/other_profile_page_view_model.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/widgets/modern/modern_profile_content.dart';


class OtherProfilePage extends StatelessWidget {
  final num userId;

 const OtherProfilePage({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider<OtherProfilePageViewModel>(
      create: (context) =>
          getIt<OtherProfilePageViewModel>(param1: userId)..init(),
      child: _ModernOtherProfileContent(),
    );
  }
}

class _ModernOtherProfileContent extends StatefulWidget {
  @override
  State<_ModernOtherProfileContent> createState() =>
      _ModernOtherProfileContentState();
}

class _ModernOtherProfileContentState extends State<_ModernOtherProfileContent>
    with TickerProviderStateMixin {
  late AnimationController _headerAnimationController;
  late AnimationController _statsAnimationController;
  late AnimationController _contentAnimationController;

  late Animation<double> _headerFadeAnimation;
  late Animation<Offset> _headerSlideAnimation;
  late List<Animation<double>> _statsScaleAnimations;

  @override
  void initState() {
    super.initState();

    // 初始化动画控制器
    _headerAnimationController = AnimationController(
      duration: MotionTokens.durationSlow,
      vsync: this,
    );

    _statsAnimationController = AnimationController(
      duration: MotionTokens.durationExtraLong,
      vsync: this,
    );

    _contentAnimationController = AnimationController(
      duration: MotionTokens.durationMedium,
      vsync: this,
    );

    // 设置动画
    _headerFadeAnimation = CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOut,
    );

    _headerSlideAnimation = Tween<Offset>(
      begin: Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _headerAnimationController,
      curve: Curves.easeOutCubic,
    ));

    // 统计数据的错开动画
    _statsScaleAnimations = List.generate(4, (index) {
      return Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(
        CurvedAnimation(
          parent: _statsAnimationController,
          curve: Interval(
            index * 0.15,
            0.4 + index * 0.15,
            curve: Curves.elasticOut,
          ),
        ),
      );
    });

    // 启动动画
    _headerAnimationController.forward();
    Future.delayed(MotionTokens.durationFast, () {
      _statsAnimationController.forward();
    });
    Future.delayed(MotionTokens.durationMedium, () {
      _contentAnimationController.forward();
    });
  }

  @override
  void dispose() {
    _headerAnimationController.dispose();
    _statsAnimationController.dispose();
    _contentAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.surfaceContainer,
      body: Consumer<OtherProfilePageViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading && viewModel.user == null) {
            return const _ModernLoadingState();
          }

          if (viewModel.error != null) {
            return _ModernErrorState(
              error: viewModel.error!,
              onRetry: () => viewModel.retry(),
            );
          }

          return RefreshIndicator(
            onRefresh: () => viewModel.refreshAllData(),
            child: CustomScrollView(
              controller: viewModel.scrollController,
              physics: const BouncingScrollPhysics(),
              slivers: [
                _buildRefinedSliverAppBar(viewModel),

                // 用户信息部分
                SliverToBoxAdapter(
                  child: _buildAnimatedUserInfo(viewModel),
                ),

                // 统计数据部分
                SliverToBoxAdapter(
                  child: _buildAnimatedStatistics(viewModel),
                ),

                // 操作按钮部分
                SliverToBoxAdapter(
                  child: _buildAnimatedActionButtons(viewModel),
                ),

                // Tab栏
                SliverPersistentHeader(
                  pinned: true,
                  delegate: _ModernTabBarDelegate(
                    currentTab: viewModel.currentTab,
                    onTabChanged: (tab) => viewModel.switchTab(tab),
                  ),
                ),

                // 内容区域
                _buildContent(viewModel),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildModernSliverAppBar(OtherProfilePageViewModel viewModel) {
    return SliverAppBar(
      expandedHeight: 200,
      floating: false,
      pinned: true,
      stretch: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      leading: IconButton(
        icon: Container(
          padding: SpacingTokens.paddingSm,
          decoration: BoxDecoration(
            color: ColorTokens.surface.withValues(alpha: 0.9),
            borderRadius: ShapeTokens.borderRadiusMd,
            boxShadow: ElevationTokens.shadow2(context),
          ),
          child: Icon(Icons.arrow_back_ios_new,
              size: SpacingTokens.space5, color: ColorTokens.onSurface),
        ),
        onPressed: () {
          HapticFeedback.lightImpact();
          Navigator.pop(context);
        },
      ),
      actions: [
        if (viewModel.user != null)
          IconButton(
            icon: Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: ColorTokens.surface.withValues(alpha: 0.9),
                borderRadius: ShapeTokens.borderRadiusMd,
                boxShadow: ElevationTokens.shadow2(context),
              ),
              child: Icon(Icons.more_vert,
                  size: SpacingTokens.space5, color: ColorTokens.onSurface),
            ),
            onPressed: () => _showMoreOptions(context, viewModel),
          ),
       const SizedBox(width: 8),
      ],
      flexibleSpace: FlexibleSpaceBar(
        background: Stack(
          fit: StackFit.expand,
          children: [
            // 背景图片或渐变
            if (viewModel.user?.backgroundUrl != null)
              CachedNetworkImage(
                imageUrl: viewModel.user!.backgroundUrl!,
                fit: BoxFit.cover,
              )
            else
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      ColorTokens.primary,
                      ColorTokens.secondary,
                    ],
                  ),
                ),
              ),

            // 渐变遮罩
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.transparent,
                    ColorTokens.shadow.withValues(alpha: 0.3),
                  ],
                ),
              ),
            ),

            // 装饰性元素
            Positioned(
              top: -50,
              right: -50,
              child: Container(
                width: 200,
                height: 200,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: ColorTokens.surface.withValues(alpha: 0.1),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRefinedSliverAppBar(OtherProfilePageViewModel viewModel) {
    return SliverAppBar(
      expandedHeight: 140,
      // 稍微减小高度
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: Colors.transparent,
      automaticallyImplyLeading: false,
      // 禁用默认的返回按钮
      flexibleSpace: LayoutBuilder(
        builder: (context, constraints) {
          final expandRatio =
              (constraints.maxHeight - kToolbarHeight) / (140 - kToolbarHeight);
          final isCollapsed = expandRatio < 0.5;

          return Stack(
            fit: StackFit.expand,
            children: [
              // 背景容器
              Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                ),
              ),

              // 主卡片层 - 带圆角和更丰富的视觉效果
              AnimatedContainer(
                duration: MotionTokens.durationFast,
                margin: EdgeInsets.only(
                  left: SpacingTokens.space5 * expandRatio,
                  right: SpacingTokens.space5 * expandRatio,
                  top: 0,
                  bottom: SpacingTokens.space5 * expandRatio,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    bottomLeft:
                        Radius.circular(ShapeTokens.radiusXxl * expandRatio),
                    bottomRight:
                        Radius.circular(ShapeTokens.radiusXxl * expandRatio),
                  ),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      ColorTokens.primary,
                      ColorTokens.secondary,
                    ],
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: ColorTokens.primary
                          .withValues(alpha: 0.3 * expandRatio),
                      blurRadius: ElevationTokens.level5 * 2 * expandRatio,
                      offset: Offset(0, ElevationTokens.level5 * expandRatio),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.only(
                    bottomLeft:
                        Radius.circular(ShapeTokens.radiusXxl * expandRatio),
                    bottomRight:
                        Radius.circular(ShapeTokens.radiusXxl * expandRatio),
                  ),
                  child: Stack(
                    children: [
                      // 背景纹理
                      if (expandRatio > 0.3)
                        Positioned.fill(
                          child: Opacity(
                            opacity: 0.1 * expandRatio,
                            child: CustomPaint(
                              painter: _GeometricPatternPainter(),
                            ),
                          ),
                        ),

                      // 光晕效果
                      Positioned(
                        top: -100,
                        right: -100,
                        child: Container(
                          width: 300,
                          height: 300,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            gradient: RadialGradient(
                              colors: [
                                ColorTokens.surface
                                    .withValues(alpha: 0.2 * expandRatio),
                                ColorTokens.surface.withValues(alpha: 0),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // 底部波浪装饰
                      if (expandRatio > 0.5)
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Opacity(
                            opacity: expandRatio,
                            child: SizedBox(
                              height: 50,
                              child: CustomPaint(
                                painter: _SmoothWavePainter(),
                              ),
                            ),
                          ),
                        ),

                      // 浮动装饰元素
                      if (expandRatio > 0.7)
                        Positioned(
                          left: 30,
                          bottom: 30,
                          child: Opacity(
                            opacity: 0.3 * expandRatio,
                            child: Container(
                              width: 60,
                              height: 60,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: ColorTokens.surface
                                      .withValues(alpha: 0.3),
                                  width: 2,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),

              // 导航栏
              SafeArea(
                child: Container(
                  height: kToolbarHeight,
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Row(
                    children: [
                      // 返回按钮
                      IconButton(
                        icon: AnimatedContainer(
                          duration: MotionTokens.durationFast,
                          padding: SpacingTokens.paddingMd,
                          decoration: BoxDecoration(
                            color: isCollapsed
                                ? ColorTokens.surfaceContainer
                                : ColorTokens.surface.withValues(alpha: 0.2),
                            borderRadius: ShapeTokens.borderRadiusMd,
                            border: Border.all(
                              color: isCollapsed
                                  ? ColorTokens.outline
                                  : ColorTokens.surface.withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Icon(
                            Icons.arrow_back_ios_new,
                            size: 18,
                            color: isCollapsed
                                ? ColorTokens.onSurface
                                : ColorTokens.onPrimary,
                          ),
                        ),
                        onPressed: () {
                          HapticFeedback.lightImpact();
                          Navigator.pop(context);
                        },
                      ),

                     const Spacer(),

                      // 折叠时显示用户名
                      if (isCollapsed && viewModel.user != null)
                        Expanded(
                          child: Center(
                            child: Text(
                              viewModel.user!.username ?? '',
                              style: TypographyTokens.titleMedium.copyWith(
                                fontWeight: FontWeight.w600,
                                color: ColorTokens.onSurface,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),

                      if (!isCollapsed) const Spacer(),

                      // 更多按钮
                      if (viewModel.user != null)
                        IconButton(
                          icon: AnimatedContainer(
                            duration: const Duration(milliseconds: 200),
                            padding: EdgeInsets.all(10),
                            decoration: BoxDecoration(
                              color: isCollapsed
                                  ? Colors.grey.shade100
                                  : Colors.white.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(14),
                              border: Border.all(
                                color: isCollapsed
                                    ? Colors.grey.shade300
                                    : Colors.white.withValues(alpha: 0.3),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              Icons.more_horiz_rounded,
                              size: 20,
                              color:
                                  isCollapsed ? Colors.black87 : Colors.white,
                            ),
                          ),
                          onPressed: () => _showMoreOptions(context, viewModel),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildAnimatedUserInfo(OtherProfilePageViewModel viewModel) {
    final user = viewModel.user;
    if (user == null) return const SizedBox.shrink();

    return SlideTransition(
      position: _headerSlideAnimation,
      child: FadeTransition(
        opacity: _headerFadeAnimation,
        child: Container(
          padding: SpacingTokens.paddingXl,
          child: Column(
            children: [
              // 头像部分
              Stack(
                alignment: Alignment.center,
                children: [
                  // 动画背景圆圈
                  AnimatedBuilder(
                    animation: _headerAnimationController,
                    builder: (context, child) {
                      return Container(
                        width: 120 + (_headerFadeAnimation.value * 20),
                        height: 120 + (_headerFadeAnimation.value * 20),
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              Color(0xFF667EEA).withValues(alpha: 0.2),
                              Color(0xFF764BA2).withValues(alpha: 0.2),
                            ],
                          ),
                        ),
                      );
                    },
                  ),

                  // 头像
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: Colors.white,
                        width: 4,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Color(0xFF667EEA).withValues(alpha: 0.3),
                          blurRadius: 20,
                          offset: Offset(0, 10),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: user.avatarUrl != null &&
                              user.avatarUrl!.isNotEmpty
                          ? CachedNetworkImage(
                              imageUrl: user.avatarUrl!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => Container(
                                color: Colors.grey[200],
                                child: const Center(
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                  ),
                                ),
                              ),
                              errorWidget: (context, url, error) => Container(
                                color: Colors.grey[200],
                                child: Image.asset(
                                  'assets/default_avatar.png',
                                  fit: BoxFit.cover,
                                ),
                              ),
                            )
                          : Container(
                              color: Colors.grey[200],
                              child: Image.asset(
                                'assets/default_avatar.png',
                                fit: BoxFit.cover,
                              ),
                            ),
                    ),
                  ),

                  // 认证标识
                  if (user.isVerified ?? false)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        padding: EdgeInsets.all(4),
                        decoration: const BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Container(
                          padding: EdgeInsets.all(4),
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                ColorTokens.primary,
                                ColorTokens.secondary
                              ],
                            ),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                ],
              ),

             const SizedBox(height: SpacingTokens.space4),

              // 用户名
              Text(
                user.name ?? '未知用户',
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF2C3E50),
                ),
              ),

             const SizedBox(height: SpacingTokens.space2),

              // 个性签名
              if (user.introduce != null && user.introduce!.isNotEmpty)
                Container(
                  margin: EdgeInsets.symmetric(horizontal: 32),
                  child: Text(
                    user.introduce!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                      height: 1.5,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

             const SizedBox(height: SpacingTokens.space4),

              // 标签
              if (user.tags != null && user.tags!.isNotEmpty)
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: user.tags!.map((tag) {
                    return Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFF667EEA).withValues(alpha: 0.1),
                            Color(0xFF764BA2).withValues(alpha: 0.1),
                          ],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        border: Border.all(
                          color: Color(0xFF667EEA).withValues(alpha: 0.3),
                        ),
                      ),
                      child: Text(
                        tag,
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF667EEA),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    );
                  }).toList(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedStatistics(OtherProfilePageViewModel viewModel) {
    final user = viewModel.user;
    if (user == null) return const SizedBox.shrink();

    final stats = [
      {
        'label': '粉丝',
        'count': user.followCount ?? 0,
        'route': AppRoutes.fansPage
            .replaceFirst(':userId', viewModel.userId.toString()),
        'icon': Icons.favorite_rounded,
        'color': Colors.pink,
      },
      {
        'label': '关注',
        'count': user.attentionCount ?? 0,
        'route': AppRoutes.attentionsPage
            .replaceFirst(':userId', viewModel.userId.toString()),
        'icon': Icons.person_add_rounded,
        'color': Colors.blue,
      },
      {
        'label': '动态',
        'count': viewModel.moments.length, // 使用实际加载的动态数量
        'route': null,
        'icon': Icons.article_rounded,
        'color': Colors.orange,
      },
      {
        'label': '钓点',
        'count': viewModel.spots.length, // 使用实际加载的钓点数量
        'route': null,
        'icon': Icons.location_on_rounded,
        'color': Colors.green,
      },
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: stats.asMap().entries.map((entry) {
          final index = entry.key;
          final stat = entry.value;

          return Expanded(
            child: ScaleTransition(
              scale: _statsScaleAnimations[index],
              child: GestureDetector(
                onTap: stat['route'] != null
                    ? () {
                        HapticFeedback.lightImpact();
                        context.navigateTo(stat['route'] as String);
                      }
                    : null,
                child: Container(
                  margin: EdgeInsets.symmetric(horizontal: 4),
                  padding: EdgeInsets.symmetric(vertical: 20),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: (stat['color'] as Color).withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        stat['icon'] as IconData,
                        color: stat['color'] as Color,
                        size: 24,
                      ),
                     const SizedBox(height: SpacingTokens.space2),
                      Text(
                        _formatCount(stat['count'] as int),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF2C3E50),
                        ),
                      ),
                     const SizedBox(height: 4),
                      Text(
                        stat['label'] as String,
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildAnimatedActionButtons(OtherProfilePageViewModel viewModel) {
    // 如果是自己的页面，不显示关注和私信按钮
    if (viewModel.isOwnProfile) {
      return const SizedBox.shrink();
    }

    // 如果未登录，显示登录提示按钮
    if (!viewModel.isLoggedIn) {
      return FadeTransition(
        opacity: _contentAnimationController,
        child: Container(
          padding: SpacingTokens.paddingXl,
          child: SizedBox(
            width: double.infinity,
            height: 48,
            child: ElevatedButton(
              onPressed: () {
                context.navigateTo(AppRoutes.login);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.grey[400],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child: const Text(
                '登录后查看更多内容',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
            ),
          ),
        ),
      );
    }

    return FadeTransition(
      opacity: _contentAnimationController,
      child: Container(
        padding: SpacingTokens.paddingXl,
        child: Row(
          children: [
            // 关注按钮
            Expanded(
              flex: 2,
              child: _ModernFollowButton(
                userId: viewModel.userId,
                isFollowing: viewModel.user?.isFollowing ?? false,
                onFollowChanged: () async {
                  try {
                    await viewModel.toggleFollow();
                    // 成功后的提示
                    if (context.mounted) {
                      final isNowFollowing =
                          viewModel.user?.isFollowing ?? false;
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                              Icon(
                                isNowFollowing
                                    ? Icons.check_circle
                                    : Icons.remove_circle,
                                color: Colors.white,
                                size: 20,
                              ),
                             const SizedBox(width: 8),
                              Text(isNowFollowing ? '关注成功' : '取消关注成功'),
                            ],
                          ),
                          backgroundColor:
                              isNowFollowing ? Colors.green : Colors.orange,
                          duration: const Duration(seconds: 2),
                        ),
                      );
                    }
                  } catch (e) {
                    // 显示错误提示
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Row(
                            children: [
                             const Icon(Icons.error_outline,
                                  color: Colors.white, size: 20),
                             const SizedBox(width: 8),
                              Expanded(child: Text(e.toString())),
                            ],
                          ),
                          backgroundColor: Colors.red,
                          duration: const Duration(seconds: 3),
                          action: SnackBarAction(
                            label: '重试',
                            textColor: Colors.white,
                            onPressed: () {
                              // 重试关注操作
                              viewModel.toggleFollow();
                            },
                          ),
                        ),
                      );
                    }
                  }
                },
              ),
            ),

           const SizedBox(width: 12),

            // 私信按钮
            Expanded(
              flex: 1,
              child: _ModernMessageButton(
                onTap: () => viewModel.navigateToChat(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContent(OtherProfilePageViewModel viewModel) {
    return viewModel.currentTab == ProfileTab.moments
        ? _buildMomentsGrid(viewModel)
        : _buildSpotsGrid(viewModel);
  }

  Widget _buildMomentsGrid(OtherProfilePageViewModel viewModel) {
    if (viewModel.moments.isEmpty && !viewModel.isLoading) {
      return SliverPadding(
        padding: SpacingTokens.paddingLg,
        sliver: const SliverToBoxAdapter(
          child: _ModernEmptyState(
            icon: Icons.article_outlined,
            title: '暂无动态',
            subtitle: '用户还没有发布任何动态',
          ),
        ),
      );
    }

    return SliverPadding(
      padding: SpacingTokens.paddingMd,
      sliver: ModernProfileContent(
        items: viewModel.moments,
        isLoading: viewModel.isLoading,
        hasMore: viewModel.hasMore,
        type: ContentType.moment,
      ),
    );
  }

  Widget _buildSpotsGrid(OtherProfilePageViewModel viewModel) {
    if (viewModel.spots.isEmpty && !viewModel.isLoading) {
      return const SliverPadding(
        padding: SpacingTokens.paddingLg,
        sliver: SliverToBoxAdapter(
          child: _ModernEmptyState(
            icon: Icons.location_off_outlined,
            title: '暂无钓点',
            subtitle: '用户还没有创建任何钓点',
          ),
        ),
      );
    }

    return SliverPadding(
      padding: SpacingTokens.paddingMd,
      sliver: ModernProfileContent(
        items: viewModel.spots,
        isLoading: viewModel.isLoading,
        hasMore: viewModel.hasMore,
        type: ContentType.spot,
      ),
    );
  }

  void _showMoreOptions(
      BuildContext context, OtherProfilePageViewModel viewModel) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _ModernOptionsSheet(
        user: viewModel.user!,
        onBlockUser: () {
          // 实现拉黑功能
          Navigator.pop(context);
          _showBlockConfirmation(context);
        },
        onReport: () {
          // 实现举报功能
          Navigator.pop(context);
          _showReportDialog(context);
        },
        onShare: () {
          // 实现分享功能
          Navigator.pop(context);
          _shareProfile();
        },
      ),
    );
  }

  String _formatCount(int count) {
    if (count >= 10000) {
      return '${(count / 10000).toStringAsFixed(1)}万';
    } else if (count >= 1000) {
      return '${(count / 1000).toStringAsFixed(1)}k';
    }
    return count.toString();
  }

  void _showBlockConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Row(
          children: [
            Icon(Icons.block, color: Colors.red, size: 24),
           const SizedBox(width: 8),
            Text('拉黑用户'),
          ],
        ),
        content: const Text('拉黑后您将无法看到该用户的动态和钓点，该用户也无法向您发送私信。确定要拉黑吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performBlockUser(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
            ),
            child: const Text('确定拉黑'),
          ),
        ],
      ),
    );
  }

  Future<void> _performBlockUser(BuildContext context) async {
    try {
      // 显示加载提示
      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
             const SizedBox(width: 12),
              Text('正在拉黑用户...'),
            ],
          ),
          duration: Duration(seconds: 2),
        ),
      );

      // TODO: 调用实际的拉黑API
      // await userService.blockUser(widget.userId);

      // 模拟API调用
      await Future.delayed(const Duration(seconds: 1));

      if (context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
               const SizedBox(width: 8),
                Text('用户已被拉黑'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );

        // 拉黑成功后返回上一页
        Navigator.pop(context);
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('拉黑失败: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showReportDialog(BuildContext context) {
    String selectedReason = '';
    String customReason = '';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            title: const Row(
              children: [
                Icon(Icons.flag, color: Colors.orange, size: 24),
               const SizedBox(width: 8),
                Text('举报用户'),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                 const Text('请选择举报原因：'),
                 const SizedBox(height: 12),
                  ...['发布不当内容', '恶意骚扰', '虚假信息', '垃圾广告', '其他'].map(
                    (reason) => RadioListTile<String>(
                      title: Text(reason),
                      value: reason,
                      groupValue: selectedReason,
                      onChanged: (value) {
                        setState(() {
                          selectedReason = value!;
                        });
                      },
                      dense: true,
                    ),
                  ),
                  if (selectedReason == '其他') ...[
                   const SizedBox(height: SpacingTokens.space2),
                    TextField(
                      decoration: const InputDecoration(
                        hintText: '请详细描述举报原因',
                        border: OutlineInputBorder(),
                      ),
                      maxLines: 3,
                      onChanged: (value) => customReason = value,
                    ),
                  ],
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: selectedReason.isEmpty
                    ? null
                    : () async {
                        Navigator.pop(context);
                        await _performReport(
                          context,
                          selectedReason == '其他'
                              ? customReason
                              : selectedReason,
                        );
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                ),
                child: const Text('提交举报'),
              ),
            ],
          );
        },
      ),
    );
  }

  Future<void> _performReport(BuildContext context, String reason) async {
    try {
      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Row(
            children: [
              SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
             const SizedBox(width: 12),
              Text('正在提交举报...'),
            ],
          ),
          duration: Duration(seconds: 2),
        ),
      );

      // TODO: 调用实际的举报API
      // await userService.reportUser(widget.userId, reason);

      // 模拟API调用
      await Future.delayed(const Duration(seconds: 1));

      if (context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
               const SizedBox(width: 8),
                Text('举报已提交，我们会尽快处理'),
              ],
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).hideCurrentSnackBar();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('举报失败: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _shareProfile() async {
    try {
      // TODO: 实现实际的分享功能
      final viewModel = Provider.of<OtherProfilePageViewModel>(context, listen: false);
      final shareText = '${viewModel.user?.name ?? '用户'}的个人主页 - 一起来钓鱼吧！';

      // 这里可以使用 share_plus 包来实现分享
      // await Share.share(shareText, subject: '分享用户主页');

      // 临时实现：复制到剪贴板
      await Clipboard.setData(ClipboardData(text: shareText));

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Row(
              children: [
                Icon(Icons.copy, color: Colors.white),
               const SizedBox(width: 8),
                Text('分享链接已复制到剪贴板'),
              ],
            ),
            backgroundColor: Colors.blue,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('分享失败: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

// 继续在同一文件中
class _ModernTabBarDelegate extends SliverPersistentHeaderDelegate {
  final ProfileTab currentTab;
  final ValueChanged<ProfileTab> onTabChanged;

  _ModernTabBarDelegate({
    required this.currentTab,
    required this.onTabChanged,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: ColorTokens.surfaceContainer,
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 24, vertical: 8),
        padding: EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            Expanded(
              child: _buildTab(
                label: '动态',
                icon: Icons.article_rounded,
                isSelected: currentTab == ProfileTab.moments,
                onTap: () => onTabChanged(ProfileTab.moments),
              ),
            ),
            Expanded(
              child: _buildTab(
                label: '钓点',
                icon: Icons.location_on_rounded,
                isSelected: currentTab == ProfileTab.spots,
                onTap: () => onTabChanged(ProfileTab.spots),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTab({
    required String label,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.selectionClick();
        onTap();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: isSelected ? ColorTokens.primary : null,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 20,
              color: isSelected ? Colors.white : Colors.grey[600],
            ),
           const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Colors.white : Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  double get maxExtent => 68;

  @override
  double get minExtent => 68;

  @override
  bool shouldRebuild(covariant _ModernTabBarDelegate oldDelegate) {
    return oldDelegate.currentTab != currentTab;
  }
}

// 加载状态
class _ModernLoadingState extends StatelessWidget {
 const _ModernLoadingState();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Color(0xFF667EEA),
            Color(0xFF764BA2),
          ],
        ),
      ),
      child: const Center(
        child: CircularProgressIndicator(
          color: Colors.white,
          strokeWidth: 3,
        ),
      ),
    );
  }
}

// 错误状态
class _ModernErrorState extends StatelessWidget {
  final String error;
  final VoidCallback onRetry;

 const _ModernErrorState({
    required this.error,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(SpacingTokens.space24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: Colors.red[100],
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.error_outline,
                size: 40,
                color: Colors.red[400],
              ),
            ),
           const SizedBox(height: SpacingTokens.space6),
            Text(
              '加载失败',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.grey[800],
              ),
            ),
           const SizedBox(height: SpacingTokens.space2),
            Text(
              error,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
           const SizedBox(height: 32),
            ElevatedButton(
              onPressed: onRetry,
              style: ElevatedButton.styleFrom(
                padding:
                    EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text('重新加载'),
            ),
          ],
        ),
      ),
    );
  }
}

// 空状态
class _ModernEmptyState extends StatelessWidget {
  final IconData icon;
  final String title;
  final String subtitle;

 const _ModernEmptyState({
    required this.icon,
    required this.title,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(SpacingTokens.space24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.grey[200]!,
                    Colors.grey[300]!,
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 40,
                color: Colors.grey[400],
              ),
            ),
           const SizedBox(height: SpacingTokens.space6),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
           const SizedBox(height: SpacingTokens.space2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

// 选项底部弹窗
class _ModernOptionsSheet extends StatelessWidget {
  final User user;
  final VoidCallback onBlockUser;
  final VoidCallback onReport;
  final VoidCallback onShare;

 const _ModernOptionsSheet({
    required this.user,
    required this.onBlockUser,
    required this.onReport,
    required this.onShare,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              margin: EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
           const SizedBox(height: SpacingTokens.space5),
            _buildOption(
              icon: Icons.share_outlined,
              label: '分享主页',
              onTap: onShare,
            ),
            _buildOption(
              icon: Icons.block_outlined,
              label: '拉黑用户',
              onTap: onBlockUser,
              isDestructive: true,
            ),
            _buildOption(
              icon: Icons.flag_outlined,
              label: '举报',
              onTap: onReport,
              isDestructive: true,
            ),
           const SizedBox(height: SpacingTokens.space2),
            Container(
              height: 8,
              color: Colors.grey[100],
            ),
            _buildOption(
              icon: Icons.close,
              label: '取消',
              onTap: () => Navigator.pop(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    final bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        child: Row(
          children: [
            Icon(
              icon,
              color: isDestructive ? Colors.red : Colors.grey[700],
              size: 24,
            ),
           const SizedBox(width: 16),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: isDestructive ? Colors.red : Colors.grey[800],
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ModernFollowButton extends StatefulWidget {
  final num userId;
  final bool isFollowing;
  final Future<void> Function()? onFollowChanged;

 const _ModernFollowButton({
    required this.userId,
    required this.isFollowing,
    this.onFollowChanged,
  });

  @override
  State<_ModernFollowButton> createState() => _ModernFollowButtonState();
}

class _ModernFollowButtonState extends State<_ModernFollowButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isLoading = false;
  late bool _isFollowing;

  @override
  void initState() {
    super.initState();
    _isFollowing = widget.isFollowing;
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _animationController.forward(),
      onTapUp: (_) => _animationController.reverse(),
      onTapCancel: () => _animationController.reverse(),
      onTap: _isLoading ? null : _handleFollowToggle,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Container(
          height: 48,
          decoration: BoxDecoration(
            color: _isFollowing ? Colors.white : ColorTokens.primary,
            borderRadius: BorderRadius.circular(16),
            border: _isFollowing
                ? Border.all(
                    color: Color(0xFF667EEA),
                    width: 2,
                  )
                : null,
            boxShadow: [
              BoxShadow(
                color: _isFollowing
                    ? Colors.black.withValues(alpha: 0.05)
                    : Color(0xFF667EEA).withValues(alpha: 0.3),
                blurRadius: 12,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: Center(
            child: _isLoading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(Color(0xFF667EEA)),
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        _isFollowing
                            ? Icons.check_circle_outline
                            : Icons.person_add_rounded,
                        size: 20,
                        color: _isFollowing
                            ? Color(0xFF667EEA)
                            : Colors.white,
                      ),
                     const SizedBox(width: 8),
                      Text(
                        _isFollowing ? '已关注' : '关注',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: _isFollowing
                              ? Color(0xFF667EEA)
                              : Colors.white,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  Future<void> _handleFollowToggle() async {
    HapticFeedback.mediumImpact();

    setState(() => _isLoading = true);

    try {
      await widget.onFollowChanged?.call();

      setState(() {
        _isFollowing = !_isFollowing;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      // 错误处理在上层处理
    }
  }
}

// 私信按钮
class _ModernMessageButton extends StatelessWidget {
  final VoidCallback onTap;

 const _ModernMessageButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        height: 48,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.grey[300]!,
            width: 1.5,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            Icons.chat_bubble_outline_rounded,
            color: Colors.grey[700],
            size: 24,
          ),
        ),
      ),
    );
  }
}

class _GeometricPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

   const spacing = 50.0;

    // 绘制网格线
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    // 绘制装饰圆点
    paint.style = PaintingStyle.fill;
    for (double x = 0; x < size.width; x += spacing) {
      for (double y = 0; y < size.height; y += spacing) {
        canvas.drawCircle(Offset(x, y), 3, paint);
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class _SmoothWavePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
        colors: [
          Colors.white.withValues(alpha: 0.1),
          Colors.white.withValues(alpha: 0.05),
        ],
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();

    // 创建平滑的波浪
    path.moveTo(0, size.height * 0.4);

    // 第一个波浪
    path.cubicTo(
      size.width * 0.25,
      size.height * 0.2,
      size.width * 0.25,
      size.height * 0.6,
      size.width * 0.5,
      size.height * 0.4,
    );

    // 第二个波浪
    path.cubicTo(
      size.width * 0.75,
      size.height * 0.2,
      size.width * 0.75,
      size.height * 0.6,
      size.width,
      size.height * 0.4,
    );

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
