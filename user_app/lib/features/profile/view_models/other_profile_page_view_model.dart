import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/extensions/auth_context_extension.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/models/moment/moment_list_request.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/shared/services/app_service.dart';
import 'package:user_app/features/chat/services/chat_service.dart';
import 'package:user_app/features/moments/services/moment_service.dart';
import 'package:user_app/features/user/services/user_service.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';

enum ProfileTab { moments, spots }

class OtherProfilePageViewModel extends BaseViewModel {
  final num userId;
  final UserService _userService;
  final MomentService _momentService;
  final FishingSpotService _fishingSpotService;
  User? user;
  String? error;
  
  // 当前登录用户相关
  User? _currentUser;
  bool get isOwnProfile => _currentUser != null && _currentUser!.id == userId;
  bool get isLoggedIn => _currentUser != null;
  
  // Tab相关
  ProfileTab _currentTab = ProfileTab.moments;
  
  // 动态相关
  List<MomentVo> _moments = [];
  int _currentMomentsPage = 0;
  final int _pageSize = 10;
  bool _hasMoments = true;
  
  // 钓点相关
  List<FishingSpotVo> _spots = [];
  int _currentSpotsPage = -1; // 初始化为-1，这样第一次调用时会变成0
  bool _hasSpots = true;
  
  final ScrollController scrollController = ScrollController();
  late ChatService _chatService;

  OtherProfilePageViewModel(
      {required this.userId, required AppServices appServices})
      : _userService = appServices.userService,
        _momentService = appServices.momentService,
        _fishingSpotService = appServices.fishingSpotService {
    scrollController.addListener(_onScroll);
    _chatService = appServices.chatService;
  }

  void _onScroll() {
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 200) {
      if (!super.busy && hasMore) {
        if (_currentTab == ProfileTab.moments) {
          fetchMoments();
        } else {
          fetchSpots();
        }
      }
    }
  }

  // Getters
  ProfileTab get currentTab => _currentTab;
  List<MomentVo> get moments => _moments;
  List<FishingSpotVo> get spots => _spots;
  bool get hasMore => _currentTab == ProfileTab.moments ? _hasMoments : _hasSpots;

  Future<void> init() async {
    setBusy(true);
    error = null;
    
    try {
      // 获取当前登录用户信息
      _currentUser = _userService.getCachedUser();
      
      // 获取目标用户信息
      var targetUser = await _userService.getUserProfile(userId);
      
      // 如果已登录且不是自己的页面，检查关注状态
      if (_currentUser != null && !isOwnProfile) {
        try {
          final isFollowing = await _userService.isFollowingUser(userId);
          targetUser = targetUser.copyWith(isFollowing: isFollowing);
        } catch (e) {
          debugPrint('⚠️ [ProfileVM] 获取关注状态失败: $e');
          // 关注状态获取失败不影响主流程
        }
      }
      
      // 处理用户数据，生成默认标签如果没有的话
      user = _enhanceUserData(targetUser);
      
      // 加载动态数据
      await fetchMoments(reset: true);
      
      // 加载钓点数据（修复钓点数量显示问题）
      await fetchSpots(reset: true);
      
    } catch (e) {
      error = e.toString();
      debugPrint('❌ [ProfileVM] 初始化失败: $e');
    }
    
    setBusy(false);
  }

  /// 增强用户数据，生成标签等
  User _enhanceUserData(User user) {
    List<String> enhancedTags = user.tags?.toList() ?? [];
    
    // 如果没有标签，根据用户数据生成一些默认标签
    if (enhancedTags.isEmpty) {
      // 根据关注数生成标签
      if ((user.followCount ?? 0) > 1000) {
        enhancedTags.add('人气用户');
      } else if ((user.followCount ?? 0) > 100) {
        enhancedTags.add('活跃用户');
      }
      
      // 根据动态数生成标签
      if ((user.momentCount ?? 0) > 50) {
        enhancedTags.add('分享达人');
      } else if ((user.momentCount ?? 0) > 10) {
        enhancedTags.add('爱分享');
      }
      
      // 根据地区生成标签
      if (user.province != null && user.province!.isNotEmpty) {
        enhancedTags.add('${user.province}钓友');
      }
      
      // 限制标签数量
      if (enhancedTags.length > 3) {
        enhancedTags = enhancedTags.take(3).toList();
      }
    }
    
    return user.copyWith(tags: enhancedTags.isNotEmpty ? enhancedTags : null);
  }
  
  // Tab切换
  void switchTab(ProfileTab tab) {
    if (_currentTab == tab) return;
    
    _currentTab = tab;
    notifyListeners();
    
    if (tab == ProfileTab.spots && _spots.isEmpty) {
      fetchSpots(reset: true);
    }
  }

  Future<void> fetchMoments({bool reset = false}) async {
    if (reset) {
      _hasMoments = true;
      _moments = [];
      _currentMomentsPage = 0;
    }

    if (_hasMoments == false) return;

    setBusy(true);
    
    try {
      _currentMomentsPage++;
      MomentListRequest request = MomentListRequest(
        userId: userId,
        pageSize: _pageSize,
        pageNum: _currentMomentsPage,
      );
      final momentResponse = await _momentService.getMoments(request);
      _moments.addAll(momentResponse.records);

      if (momentResponse.total <= _moments.length) {
        _hasMoments = false;
      }
      
      // 清除错误状态
      error = null;
    } catch (e) {
      debugPrint('❌ [ProfileVM] 获取动态失败: $e');
      // 如果是第一页加载失败，设置错误状态
      if (_currentMomentsPage == 1) {
        error = '获取动态失败: ${e.toString()}';
      }
      // 回滚页码
      _currentMomentsPage--;
    }
    
    setBusy(false);
  }
  
  Future<void> fetchSpots({bool reset = false}) async {
    if (reset) {
      _hasSpots = true;
      _spots = [];
      _currentSpotsPage = -1; // 重置为-1，这样第一次增加后就是0
    }

    if (_hasSpots == false) return;

    setBusy(true);
    try {
      _currentSpotsPage++;
      
      // 调用钓点服务获取用户创建的钓点
      final spots = await _fishingSpotService.getUserCreatedSpots(
        userId: userId.toInt(),
        page: _currentSpotsPage, // 现在使用0-based分页
        size: _pageSize,
      );
      
      if (reset) {
        _spots = spots;
      } else {
        _spots.addAll(spots);
      }
      
      if (spots.length < _pageSize) {
        _hasSpots = false;
      }
      
      // 清除错误状态
      error = null;
      
      debugPrint('✅ [ProfileVM] 获取到 ${spots.length} 个用户钓点，总共 ${_spots.length} 个');
    } catch (e) {
      debugPrint('❌ [ProfileVM] 获取用户钓点失败: $e');
      // 如果是第一页加载失败，设置错误状态
      if (_currentSpotsPage == 0) {
        error = '获取钓点失败: ${e.toString()}';
      }
      // 回滚页码
      _currentSpotsPage--;
    }
    setBusy(false);
  }

  // 刷新所有数据
  Future<void> refreshAllData() async {
    try {
      error = null;
      setBusy(true);
      
      // 重新加载用户信息
      final refreshedUser = await _userService.getUserProfile(userId);
      user = _enhanceUserData(refreshedUser);
      
      // 重新加载当前Tab的数据
      if (_currentTab == ProfileTab.moments) {
        await fetchMoments(reset: true);
      } else {
        await fetchSpots(reset: true);
      }
      
      debugPrint('✅ [ProfileVM] 数据刷新成功');
    } catch (e) {
      debugPrint('❌ [ProfileVM] 数据刷新失败: $e');
      error = '刷新失败: ${e.toString()}';
    } finally {
      setBusy(false);
    }
  }

  // 重试机制
  Future<void> retry() async {
    error = null;
    await init();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  // 关注/取消关注功能
  Future<bool> toggleFollow() async {
    if (user == null) return false;
    
    try {
      setBusy(true);
      
      final isCurrentlyFollowing = user!.isFollowing ?? false;
      
      if (isCurrentlyFollowing) {
        await _userService.unfollowUser(userId);
      } else {
        await _userService.followUser(userId);
      }
      
      // 更新本地状态和粉丝数
      final newFollowCount = isCurrentlyFollowing 
          ? (user!.followCount ?? 0) - 1 
          : (user!.followCount ?? 0) + 1;
      
      user = user!.copyWith(
        isFollowing: !isCurrentlyFollowing,
        followCount: newFollowCount,
      );
      
      setBusy(false);
      return !isCurrentlyFollowing;
    } catch (e) {
      debugPrint('❗️ [关注操作] 失败: $e');
      setBusy(false);
      throw Exception('关注操作失败，请检查网络连接后重试');
    }
  }

  Future<void> navigateToChat(BuildContext context) async {
    context.executeWithLoginCheck(
      () => _performNavigateToChat(context),
      message: '请先登录后再进行聊天',
    );
  }

  Future<void> _performNavigateToChat(BuildContext context) async {
    if (user == null) return;

    try {
      // 导航到聊天页面
      final conversationId = 'c2c_${user!.id}';
      context.navigateTo('${AppRoutes.chatDetail}/$conversationId');
    } catch (e) {
      debugPrint('❗️ [聊天导航] 失败: $e');
    }
  }
}
