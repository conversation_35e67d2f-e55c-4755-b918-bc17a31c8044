/// 认证文档模型
class CertificationDocument {
  final String id;
  final String name;
  final String url;
  final String type;
  final DateTime uploadTime;
  final _CertificationFile file;

  CertificationDocument({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.uploadTime,
    required this.file,
  });

  factory CertificationDocument.fromJson(Map<String, dynamic> json) {
    return CertificationDocument(
      id: json['id'] as String,
      name: json['name'] as String,
      url: json['url'] as String,
      type: json['type'] as String,
      uploadTime: DateTime.parse(json['uploadTime'] as String),
      file: _CertificationFile.fromJson(json['file'] as Map<String, dynamic>),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type,
      'uploadTime': uploadTime.toIso8601String(),
      'file': file.toJson(),
    };
  }
}

/// 认证文件信息
class _CertificationFile {
  final String? name;
  final String? path;
  final int? size;
  final String? mimeType;

  _CertificationFile({
    this.name,
    this.path,
    this.size,
    this.mimeType,
  });

  factory _CertificationFile.fromJson(Map<String, dynamic> json) {
    return _CertificationFile(
      name: json['name'] as String?,
      path: json['path'] as String?,
      size: json['size'] as int?,
      mimeType: json['mimeType'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'path': path,
      'size': size,
      'mimeType': mimeType,
    };
  }
}