import 'package:flutter/material.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/fishing_spots/models/spot_map_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/view_models/fishing_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/map_view_model.dart';
import 'package:user_app/features/fishing_spots/view_models/weather_card_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/filter_section.dart';
import 'package:user_app/features/fishing_spots/widgets/fishing_spot_details_modal.dart';
import 'package:user_app/features/fishing_spots/widgets/floating_action_section.dart';
import 'package:user_app/features/fishing_spots/widgets/real_spot_filter_widget.dart';
import 'package:user_app/features/fishing_spots/widgets/spot_list_view.dart';
import 'package:user_app/features/fishing_spots/widgets/spot_map_view.dart';
import 'package:user_app/features/fishing_spots/widgets/weather_details_sheet.dart';
import 'package:user_app/features/search/SearchSystem/SearchSystem.dart';
import 'package:user_app/shared/widgets/design_system/ds_app_bar.dart';

class FishingSpotsPage extends StatefulWidget {
  const FishingSpotsPage({super.key});

  @override
  State<FishingSpotsPage> createState() => _FishingSpotsPageState();
}

class _FishingSpotsPageState extends State<FishingSpotsPage>
    with TickerProviderStateMixin {
  // Controllers
  final ScrollController _scrollController = ScrollController();
  late AnimationController _fabAnimationController;
  late AnimationController _viewSwitchController;

  AMap2DController? _mapController;

  // State
  bool _isMapView = false;
  String _selectedFilter = '全部';
  final List<String> _filterOptions = ['全部', '官方认证', '用户推荐', '免费钓场', '付费钓场'];
  List<String> _selectedFishTypes = [];
  bool _filterHasFacilities = false;
  bool _filterHasParking = false;

  // 新增的筛选状态
  double? _maxDistance;
  double? _minRating;

  // Header visibility state
  bool _isHeaderVisible = true;
  double _lastScrollOffset = 0.0;

  // View Models
  late FishingSpotViewModel _spotViewModel;
  late MapViewModel _mapViewModel;

  @override
  void initState() {
    super.initState();
    _spotViewModel = context.read<FishingSpotViewModel>();
    _mapViewModel =
        MapViewModel(fishingSpotService: context.read<FishingSpotService>());
    _initializeAnimations();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initialLoad();
    });
    _scrollController.addListener(_scrollListener);
  }

  void _initializeAnimations() {
    _fabAnimationController = AnimationController(
      duration: MotionTokens.fabScaleDuration,
      vsync: this,
    );
    _fabAnimationController.forward();

    _viewSwitchController = AnimationController(
      duration: MotionTokens.pageTransitionDuration,
      vsync: this,
    );
  }

  Future<void> _initialLoad() async {
    await _spotViewModel.loadFishTypes();
    await _loadData(refresh: true);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fabAnimationController.dispose();
    _viewSwitchController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    // 处理分页加载
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent -
            SpacingTokens.space20 * 2.5) {
      if (!_spotViewModel.isBusy && _spotViewModel.hasMore) {
        _loadData();
      }
    }

    // 处理头部隐藏/显示 (只在列表视图时生效)
    if (!_isMapView) {
      _handleHeaderVisibility();
    }
  }

  void _handleHeaderVisibility() {
    final currentOffset = _scrollController.offset;
    final isScrollingDown = currentOffset > _lastScrollOffset;
    final isScrollingUp = currentOffset < _lastScrollOffset;

    // 只有在滚动距离超过一定阈值时才触发动画
    if ((currentOffset - _lastScrollOffset).abs() > SpacingTokens.space1 + 1) {
      if (isScrollingUp && !_isHeaderVisible) {
        // 上滑显示 header 和 filter
        setState(() {
          _isHeaderVisible = true;
        });
      } else if (isScrollingDown &&
          _isHeaderVisible &&
          currentOffset > SpacingTokens.space20 + 5) {
        // 下滑隐藏 header 和 filter (滚动超过100像素后才开始隐藏)
        setState(() {
          _isHeaderVisible = false;
        });
      }
      _lastScrollOffset = currentOffset;
    }
  }

  Future<void> _loadData({bool refresh = false}) async {
    if (refresh) {
      _spotViewModel.applyFilters(
        filter: _selectedFilter,
        fishTypes: _selectedFishTypes,
        hasFacilities: _filterHasFacilities,
        hasParking: _filterHasParking,
      );
    }
    await _spotViewModel.loadFishingSpotSummaries(refresh: refresh);
  }

  void _toggleView() {
    setState(() {
      _isMapView = !_isMapView;
      // 切换视图时总是显示头部
      _isHeaderVisible = true;
    });
    if (_isMapView) {
      _viewSwitchController.forward();
      debugPrint('🗺️ [FishingSpotsPage] Switched to map view');
      // 切换到地图视图时，立即开始业务流程：获取位置 -> 加载数据
      _initializeMapView();
    } else {
      _viewSwitchController.reverse();
    }
  }

  /// 初始化地图视图的业务流程
  Future<void> _initializeMapView() async {
    debugPrint('🗺️ [FishingSpotsPage] Initializing map view...');

    try {
      // 尝试获取用户位置
      final position = await _getUserLocation();

      if (position != null) {
        // 使用真实位置初始化
        await _mapViewModel.initializeMapView(
          defaultLatitude: position.latitude,
          defaultLongitude: position.longitude,
        );

        // 移动地图到用户位置（如果地图已准备好）
        if (_mapController != null) {
          await _mapController!.move(
            position.latitude.toString(),
            position.longitude.toString(),
          );
          await _mapController!.setZoom(zoomLevel: 15);
        }
      } else {
        // 使用默认位置初始化
        const defaultLat = 39.9042; // 北京
        const defaultLng = 116.4074;

        await _mapViewModel.initializeMapView(
          defaultLatitude: defaultLat,
          defaultLongitude: defaultLng,
        );

        // 移动地图到默认位置（如果地图已准备好）
        if (_mapController != null) {
          await _mapController!.move(
            defaultLat.toString(),
            defaultLng.toString(),
          );
          await _mapController!.setZoom(zoomLevel: 12);
        }
      }
    } catch (e) {
      debugPrint('❌ [FishingSpotsPage] Map view initialization failed: $e');
    }
  }

  /// 获取用户位置（不抛出异常）
  Future<Position?> _getUserLocation() async {
    try {
      debugPrint('🗺️ [FishingSpotsPage] Requesting user location...');

      // 检查位置权限
      final permission = await Permission.location.request();
      if (!permission.isGranted) {
        debugPrint('❌ [FishingSpotsPage] Location permission denied');
        return null;
      }

      // 获取当前位置
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      debugPrint(
          '✅ [FishingSpotsPage] Got user location: ${position.latitude}, ${position.longitude}');
      return position;
    } catch (e) {
      debugPrint('❌ [FishingSpotsPage] Error getting user location: $e');
      return null;
    }
  }

  void _onFilterChanged(String filter) {
    setState(() {
      _selectedFilter = filter;
    });
    _loadData(refresh: true);
  }

  void _showAdvancedFilterSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.surface.withValues(alpha: 0),
      builder: (context) => RealSpotFilterWidget(
        selectedPriceFilter: _selectedFilter,
        selectedFishTypes: _selectedFishTypes,
        hasFacilities: _filterHasFacilities,
        hasParking: _filterHasParking,
        maxDistance: _maxDistance,
        minRating: _minRating,
        availableFishTypes: _spotViewModel.availableFishTypesObjects,
        maxDistanceLimit: 100.0,
        // 100公里限制
        maxRatingLimit: 5.0,
        // 5分评分限制
        onFiltersChanged: ({
          String? priceFilter,
          List<String>? fishTypes,
          bool? hasFacilities,
          bool? hasParking,
          double? maxDistance,
          double? minRating,
        }) {
          setState(() {
            if (priceFilter != null) _selectedFilter = priceFilter;
            if (fishTypes != null) _selectedFishTypes = fishTypes;
            if (hasFacilities != null) _filterHasFacilities = hasFacilities;
            if (hasParking != null) _filterHasParking = hasParking;
            _maxDistance = maxDistance;
            _minRating = minRating;
          });
          _loadData(refresh: true);
        },
      ),
    );
  }

  void _showSearchSheet() {
    // 使用新的 UnifiedSearchService 打开钓点搜索
    UnifiedSearchService().quickSearch(
      context,
      UnifiedSearchService.SPOT_SEARCH,
      mode: SearchMode.bottomSheet,
    );
  }

  void _showAddSpotDialog() {
    // 使用新的导航扩展方法，Auth Guard 会在 GoRouter 中处理
    context.navigateTo(
      AppRoutes.createSpot,
      extra: CreateSpotRouteData(
        onSpotCreated: (int spotId) {
          _loadData(refresh: true);
        },
      ),
    );
  }

  void _showSpotDetailsById(int spotId) {
    // 使用新的模态管理系统，自动处理页面栈状态
    context.openModal(
      child: DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => FishingSpotDetailsModal(
          spotId: spotId,
          scrollController: scrollController,
        ),
      ),
    );
  }

  void _showSpotDetails(SpotSummaryVo spot) {
    // 直接使用明确的类型，无需类型判断
    final int spotId = spot.id;
    final String address = spot.address;
    final double latitude = spot.latitude;
    final double longitude = spot.longitude;

    // 使用新的模态管理系统，自动处理页面栈状态
    context.openModal(
      child: DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) => FishingSpotDetailsModal(
          spotId: spotId,
          scrollController: scrollController,
          onNavigationPressed: () {
            Navigator.pop(context);
            // 使用新的导航扩展方法到路线规划页面
            context.navigateTo(
              AppRoutes.routePlanning,
              extra: RoutePlanningRouteData(
                address: address,
                destination: LngLat(longitude, latitude),
              ),
            );
          },
          onCheckinPressed: () {
            // Handle checkin
          },
        ),
      ),
    );
  }

  /// 导航到钓点
  void _navigateToSpot(SpotMapVo mapSpot) {
    // 使用新的导航扩展方法导航到路线规划页面
    context.navigateTo(
      AppRoutes.routePlanning,
      extra: RoutePlanningRouteData(
        address: mapSpot.address,
        destination: LngLat(mapSpot.longitude, mapSpot.latitude),
      ),
    );
  }

  /// 构建带动画的头部组件
  Widget _buildAnimatedHeader() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            ColorTokens.surface,
            ColorTokens.surface.withValues(alpha: 0.95),
            ColorTokens.surface.withValues(alpha: 0),
          ],
          stops: const [0, 0.8, 1],
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: Column(
          children: [
            // 使用 DSCustomHeader 作为基础结构
            DSCustomHeader(
              title: '',
              // 不显示标题文本
              titleWidget: _buildWeatherWidget(),
              // 天气信息作为标题区域
              height: kToolbarHeight + SpacingTokens.space2,
              // 增加高度以容纳天气组件
              padding: const EdgeInsets.symmetric(
                horizontal: SpacingTokens.space4,
                vertical: SpacingTokens.space3, // 增加垂直内边距
              ),
              decoration: null,
              // 移除默认装饰，使用外层容器的渐变
              actions: [
                _buildSearchButton(),
              ],
            ),
            // Filter Section
            FilterSection(
              selectedFilter: _selectedFilter,
              filterOptions: _filterOptions,
              hasActiveAdvancedFilters: _selectedFishTypes.isNotEmpty ||
                  _filterHasFacilities ||
                  _filterHasParking ||
                  _maxDistance != null ||
                  _minRating != null,
              activeAdvancedFiltersCount: _selectedFishTypes.length +
                  (_filterHasFacilities ? 1 : 0) +
                  (_filterHasParking ? 1 : 0) +
                  (_maxDistance != null ? 1 : 0) +
                  (_minRating != null ? 1 : 0),
              onFilterChanged: _onFilterChanged,
              onAdvancedFilterTapped: _showAdvancedFilterSheet,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建天气信息组件
  Widget _buildWeatherWidget() {
    return Consumer<WeatherCardViewModel>(
      builder: (context, weatherViewModel, _) {
        return GestureDetector(
          onTap: () {
            showModalBottomSheet(
              context: context,
              isScrollControlled: true,
              backgroundColor: ColorTokens.surface.withValues(alpha: 0),
              builder: (context) => const WeatherDetailsSheet(),
            );
          },
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: SpacingTokens.space3,
              vertical: SpacingTokens.space0 + 2, // 进一步减少垂直内边距到2dp
            ),
            constraints: const BoxConstraints(
              maxWidth: 240, // 限制最大宽度防止溢出
            ),
            decoration: BoxDecoration(
              color: ColorTokens.surface,
              borderRadius: ShapeTokens.borderRadiusMd,
              boxShadow: [
                BoxShadow(
                  color: ColorTokens.shadow.withValues(alpha: 0.05),
                  blurRadius: ElevationTokens.level1,
                  offset: const Offset(0, SpacingTokens.space1),
                ),
              ],
            ),
            child: IntrinsicHeight(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center, // 行内居中对齐
                children: [
                  // Weather Icon
                  Container(
                    width: SpacingTokens.space8,
                    height: SpacingTokens.space8,
                    decoration: const BoxDecoration(
                      color: ColorTokens.primaryContainer,
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      weatherViewModel.weatherData.weather.contains('晴')
                          ? Icons.wb_sunny
                          : weatherViewModel.weatherData.weather.contains('雨')
                              ? Icons.grain
                              : weatherViewModel.weatherData.weather
                                      .contains('云')
                                  ? Icons.cloud
                                  : Icons.wb_cloudy,
                      color: ColorTokens.primary,
                      size: SpacingTokens.space4,
                    ),
                  ),
                  const SizedBox(width: SpacingTokens.space2),
                  // Weather Info - 使用单行布局避免垂直溢出
                  Expanded(
                    child: Text.rich(
                      TextSpan(
                        children: [
                          TextSpan(
                            text:
                                '${weatherViewModel.weatherData.temperature}°C ${weatherViewModel.weatherData.weather}',
                            style: TypographyTokens.bodySmall.copyWith(
                              fontWeight: FontWeight.w600,
                              fontSize: 13,
                            ),
                          ),
                          TextSpan(
                            text:
                                ' • ${weatherViewModel.weatherData.windPower} ${weatherViewModel.weatherData.humidity}%湿度',
                            style: TypographyTokens.bodySmall.copyWith(
                              color: ColorTokens.onSurfaceVariant,
                              fontSize: 11,
                            ),
                          ),
                        ],
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const Icon(
                    Icons.chevron_right,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建搜索按钮
  Widget _buildSearchButton() {
    return IconButton(
      onPressed: _showSearchSheet,
      icon: Container(
        width: SpacingTokens.space10,
        height: SpacingTokens.space10,
        decoration: BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: ShapeTokens.borderRadiusMd,
          boxShadow: [
            BoxShadow(
              color: ColorTokens.shadow.withValues(alpha: 0.05),
              blurRadius: ElevationTokens.level2,
              offset: const Offset(0, SpacingTokens.space1),
            ),
          ],
        ),
        child: const Icon(
          Icons.search,
          color: ColorTokens.onSurface,
          size: SpacingTokens.space6,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<FishingSpotViewModel>(
      builder: (context, spotViewModel, child) {
        return Scaffold(
          backgroundColor: ColorTokens.surface,
          body: Stack(
            children: [
              // Main Content
              AnimatedSwitcher(
                duration: MotionTokens.pageTransitionDuration,
                child: _isMapView
                    ? ChangeNotifierProvider.value(
                        value: _mapViewModel,
                        child: Consumer<MapViewModel>(
                          builder: (context, mapViewModel, child) {
                            return SpotMapView(
                              key: const ValueKey('map'),
                              spots: mapViewModel.mapSpots,
                              // 使用轻量级地图数据
                              onMapCreated: (controller) async {
                                setState(() {
                                  _mapController = controller;
                                });
                                debugPrint(
                                    '🗺️ [FishingSpotsPage] Map created and ready');
                                // 地图创建完成，但不在这里触发业务逻辑
                                // 业务逻辑已经在 _initializeMapView() 中处理
                              },
                              onSpotTapped: (mapSpot) {
                                // 保持原有的点击行为作为备用
                                debugPrint(
                                    '🗺️ [FishingSpotsPage] Spot tapped: ${mapSpot.name}');
                              },
                              onSpotDetailsRequested: (mapSpot) {
                                // 通过气泡的详情按钮触发
                                _showSpotDetailsById(mapSpot.id);
                              },
                              onSpotNavigationRequested: (mapSpot) {
                                // 通过气泡的导航按钮触发
                                _navigateToSpot(mapSpot);
                              },
                              onRegionChange: (bounds, zoom) {
                                // 当地图区域变化时更新ViewModel
                                mapViewModel.updateMapRegion(bounds, zoom);
                              },
                              enableLocationButton: true,
                              enableZoomControls: true,
                              enableClustering:
                                  mapViewModel.mapSpots.length > 20,
                            );
                          },
                        ),
                      )
                    : SpotListView(
                        key: const ValueKey('list'),
                        scrollController: _scrollController,
                        spots: spotViewModel.fishingSpotSummaries,
                        // 列表视图使用摘要数据
                        isLoading: spotViewModel.isBusy,
                        hasMore: spotViewModel.hasMore,
                        error: spotViewModel.errorMessage,
                        onSpotTapped: _showSpotDetails,
                        onRefresh: () => _loadData(refresh: true),
                        onRetry: () => _loadData(refresh: true),
                      ),
              ),

              // Top Bar with animation using DSCustomHeader
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: AnimatedContainer(
                  duration: MotionTokens.durationShort4,
                  height: _isHeaderVisible ? null : 0,
                  curve: MotionTokens.standard,
                  child: AnimatedOpacity(
                    duration: MotionTokens.durationShort4,
                    opacity: _isHeaderVisible ? 1.0 : 0.0,
                    child: _isHeaderVisible
                        ? _buildAnimatedHeader()
                        : const SizedBox.shrink(),
                  ),
                ),
              ),

              // Floating Actions
              FloatingActionSection(
                fabAnimationController: _fabAnimationController,
                isMapView: _isMapView,
                isVisible: _isHeaderVisible,
                onAddSpot: _showAddSpotDialog,
                onToggleView: _toggleView,
              ),
            ],
          ),
        );
      },
    );
  }
}
