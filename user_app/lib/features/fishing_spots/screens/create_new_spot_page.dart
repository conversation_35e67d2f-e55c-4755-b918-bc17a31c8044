import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/di/injection.dart' as app_di;
import 'package:user_app/core/navigation/navigation_manager.dart';
import 'package:user_app/features/fishing_spots/services/spot_name_suggestion_service.dart';
import 'package:user_app/features/fishing_spots/screens/map_location_picker_page.dart';
import 'package:user_app/features/fishing_spots/view_models/create_spot_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/modern_create_spot_step_0.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/modern_create_spot_step_1.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/modern_create_spot_step_2.dart';

class CreateNewSpotPage extends StatefulWidget {
  final Function(int) onSpotCreated;

  const CreateNewSpotPage({super.key, required this.onSpotCreated});

  @override
  State<CreateNewSpotPage> createState() => _CreateNewSpotPageState();
}

class _CreateNewSpotPageState extends State<CreateNewSpotPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late CreateSpotViewModel _viewModel;
  late AnimationController _progressController;
  late AnimationController _fadeController;
  late AnimationController _slideController;

  int _currentStep = 0;
  final int _totalSteps = 3;

  @override
  void initState() {
    super.initState();
    _viewModel = app_di.getIt<CreateSpotViewModel>();
    _viewModel.initializeFormData();
    _viewModel.loadFishTypes();

    _progressController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    // Start animations
    _progressController.forward();
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<CreateSpotViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            backgroundColor: Theme.of(context).colorScheme.surface,
            body: Stack(
              children: [
                // 背景装饰
                _buildBackgroundDecoration(),

                // 主内容
                CustomScrollView(
                  physics: const ClampingScrollPhysics(),
                  slivers: [
                    // 现代化 AppBar
                    _buildModernAppBar(),

                    // 进度指示器
                    SliverToBoxAdapter(
                      child: _buildAnimatedProgressIndicator(),
                    ),

                    // 步骤内容
                    SliverToBoxAdapter(
                      child: AnimatedSwitcher(
                        duration: const Duration(milliseconds: 400),
                        switchInCurve: Curves.easeInOut,
                        switchOutCurve: Curves.easeInOut,
                        transitionBuilder: (child, animation) {
                          return SlideTransition(
                            position: Tween<Offset>(
                              begin: Offset(0.2, 0),
                              end: Offset.zero,
                            ).animate(CurvedAnimation(
                              parent: animation,
                              curve: Curves.easeOutCubic,
                            )),
                            child: FadeTransition(
                              opacity: animation,
                              child: child,
                            ),
                          );
                        },
                        child: Container(
                          key: ValueKey(_currentStep),
                          padding: EdgeInsets.all(SpacingTokens.space5),
                          child: _buildStepContent(_currentStep, viewModel),
                        ),
                      ),
                    ),

                    // 底部操作按钮
                    SliverToBoxAdapter(
                      child: _buildModernActionButtons(viewModel),
                    ),

                    SliverToBoxAdapter(
                      child: SizedBox(height: SpacingTokens.space10),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackgroundDecoration() {
    return Positioned.fill(
      child: Stack(
        children: [
          // 白色背景
          Container(
            height: 300,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
            ),
          ),

          // 装饰性圆圈
          Positioned(
            top: -100,
            right: -100,
            child: Container(
              width: 300,
              height: 300,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context)
                    .colorScheme
                    .primary
                    .withValues(alpha: 0.1),
              ),
            ),
          ),

          Positioned(
            bottom: 100,
            left: -50,
            child: Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Theme.of(context)
                    .colorScheme
                    .secondary
                    .withValues(alpha: 0.05),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 80,
      floating: false,
      pinned: true,
      backgroundColor: Theme.of(context).colorScheme.surface,
      elevation: ElevationTokens.level0,
      leading: IconButton(
        icon: Container(
          padding: EdgeInsets.all(SpacingTokens.space2),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant,
            borderRadius: ShapeTokens.borderRadiusMd,
          ),
          child: Icon(
            Icons.arrow_back_ios_new,
            size: TypographyTokens.sizeBodyLarge,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        onPressed: () {
          HapticFeedback.lightImpact();
          // 使用智能返回，如果无法返回则跳转到主页
          if (Navigator.of(context).canPop()) {
            Navigator.pop(context);
          } else {
            // 如果不能返回，导航到钓点列表页面
            // 使用 go 替换整个堆栈
            context.goUntilRoute(AppRoutes.fishingSpots);
          }
        },
      ),
      title: Text(
        '创建钓点',
        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                  fontWeight: TypographyTokens.weightBold,
                ) ??
            const TextStyle(),
      ),
      centerTitle: true,
    );
  }

  Widget _buildAnimatedProgressIndicator() {
    return Container(
      margin: EdgeInsets.symmetric(
          horizontal: SpacingTokens.space5, vertical: SpacingTokens.space4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: ShapeTokens.borderRadiusXl,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            blurRadius: SpacingTokens.space5,
            offset: Offset(0, SpacingTokens.space2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(SpacingTokens.space5),
        child: Column(
          children: [
            // 步骤指示器容器
            Container(
              alignment: Alignment.center, // 确保内容居中
              child: IntrinsicWidth(
                // 使用IntrinsicWidth让内容自适应宽度
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(_totalSteps, (index) {
                    final isActive = index <= _currentStep;
                    final isCompleted = index < _currentStep;

                    return Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // 步骤圆圈
                        AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          width: SpacingTokens.space10,
                          height: SpacingTokens.space10,
                          decoration: BoxDecoration(
                            color: isActive
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.outline,
                            shape: BoxShape.circle,
                            boxShadow: isActive
                                ? [
                                    BoxShadow(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withValues(alpha: 0.3),
                                      blurRadius: SpacingTokens.space2,
                                      offset:
                                          const Offset(0, SpacingTokens.space1),
                                    ),
                                  ]
                                : null,
                          ),
                          child: Center(
                            child: AnimatedSwitcher(
                              duration: const Duration(milliseconds: 200),
                              child: isCompleted
                                  ? Icon(
                                      Icons.check,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onPrimary,
                                      size: SpacingTokens.space5,
                                      key: const ValueKey('check'),
                                    )
                                  : Text(
                                      '${index + 1}',
                                      key: ValueKey('number_$index'),
                                      style: TextStyle(
                                        color: isActive
                                            ? Theme.of(context)
                                                .colorScheme
                                                .onPrimary
                                            : Theme.of(context)
                                                .colorScheme
                                                .onSurfaceVariant,
                                        fontWeight: TypographyTokens.weightBold,
                                        fontSize:
                                            TypographyTokens.sizeBodyLarge,
                                      ),
                                    ),
                            ),
                          ),
                        ),
                        // 连接线
                        if (index < _totalSteps - 1)
                          AnimatedContainer(
                            duration: const Duration(milliseconds: 300),
                            width: SpacingTokens.space12,
                            height: SpacingTokens.space1 / 2,
                            margin: const EdgeInsets.symmetric(
                                horizontal: SpacingTokens.space2),
                            decoration: BoxDecoration(
                              color: isCompleted
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.outline,
                              borderRadius: ShapeTokens.borderRadiusXs,
                            ),
                          ),
                      ],
                    );
                  }),
                ),
              ),
            ),

            const SizedBox(height: SpacingTokens.space5),

            // 步骤标题 - 确保文本居中
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Text(
                _getStepTitle(_currentStep),
                key: ValueKey(_currentStep),
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface,
                      fontWeight: TypographyTokens.weightBold,
                    ),
                textAlign: TextAlign.center,
              ),
            ),

            const SizedBox(height: SpacingTokens.space2),

            // 步骤描述 - 确保文本居中
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 300),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: SpacingTokens.space4),
                // 添加水平padding防止文字太靠边
                child: Text(
                  _getStepDescription(_currentStep),
                  key: ValueKey('${_currentStep}_desc'),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        height: 1.4,
                      ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernActionButtons(CreateSpotViewModel viewModel) {
    return Container(
      padding: SpacingTokens.paddingLg,
      child: Row(
        children: [
          // 上一步按钮
          if (_currentStep > 0)
            Expanded(
              flex: 1,
              child: AnimatedScale(
                scale: _currentStep > 0 ? 1.0 : 0.0,
                duration: const Duration(milliseconds: 300),
                child: Container(
                  margin: const EdgeInsets.only(right: SpacingTokens.space3),
                  child: OutlinedButton(
                    onPressed: () {
                      HapticFeedback.lightImpact();
                      setState(() => _currentStep--);
                    },
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          vertical: SpacingTokens.space4),
                      shape: const RoundedRectangleBorder(
                        borderRadius: ShapeTokens.borderRadiusLg,
                      ),
                      side: BorderSide(
                        color: Theme.of(context).colorScheme.outline,
                        width: SpacingTokens.space1 / 2,
                      ),
                    ),
                    child: Text(
                      '上一步',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: TypographyTokens.weightMedium,
                          ),
                    ),
                  ),
                ),
              ),
            ),

          // 下一步/提交按钮
          Expanded(
            flex: 2,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: ShapeTokens.borderRadiusLg,
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: Offset(0, 8),
                  ),
                ],
              ),
              child: MaterialButton(
                onPressed: viewModel.isSubmitting
                    ? null
                    : () {
                        HapticFeedback.mediumImpact();
                        _handleNext(viewModel);
                      },
                padding: EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: ShapeTokens.borderRadiusLg,
                ),
                child: AnimatedSwitcher(
                  duration: const Duration(milliseconds: 200),
                  child: viewModel.isSubmitting
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: ColorTokens.onPrimary,
                            strokeWidth: 3,
                          ),
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              _currentStep == _totalSteps - 1 ? '提交钓点' : '下一步',
                              style: const TextStyle(
                                color: ColorTokens.onPrimary,
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            SizedBox(width: SpacingTokens.space2),
                            Icon(
                              _currentStep == _totalSteps - 1
                                  ? Icons.check_circle_outline
                                  : Icons.arrow_forward_rounded,
                              color: Theme.of(context).colorScheme.onPrimary,
                              size: SpacingTokens.space5,
                            ),
                          ],
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(int step, CreateSpotViewModel viewModel) {
    switch (step) {
      case 0:
        return ModernBasicInfoLocationStep(
          formKey: _formKey,
          nameController: viewModel.nameController,
          addressController: viewModel.addressController,
          descriptionController: viewModel.descriptionController,
          latitude: viewModel.latitude,
          longitude: viewModel.longitude,
          formattedAddress: viewModel.formattedAddress,
          useCurrentLocation: viewModel.useCurrentLocation,
          onShowMapModal: _showModernMapModal,
          getSuggestedName: _getSuggestedName,
          getSuggestedNames: _getSuggestedNames,
        );
      case 1:
        return ModernImagesFishTypesStep(
          spotImages: viewModel.spotImages,
          availableFishTypes:
              viewModel.customFishTypes.isEmpty && viewModel.isLoadingFishTypes
                  ? []
                  : viewModel.getAvailableFishTypes(),
          selectedFishTypeIds: viewModel.selectedFishTypeIds,
          onAddImages: viewModel.pickImages,
          onRemoveImage: viewModel.removeImage,
          onToggleFishType: (fishTypeId) {
            // Find the fish type by ID and toggle it
            final fishType = viewModel
                .getAvailableFishTypes()
                .firstWhere((type) => type.id == fishTypeId);
            final isSelected =
                viewModel.selectedFishTypeIds.contains(fishTypeId);
            viewModel.toggleFishType(fishType, !isSelected);
          },
          onAddCustomFishType: (fishName) {
            viewModel.addCustomFishTypeByName(fishName);
          },
          isLoadingFishTypes: viewModel.isLoadingFishTypes,
        );
      case 2:
        return ModernFacilitiesPriceStep(
          hasFacilities: viewModel.hasFacilities,
          isPaid: viewModel.isPaid,
          isOfficial: viewModel.isOfficial,
          facilities: viewModel.facilities,
          prices: viewModel.prices,
          priceController: viewModel.priceController,
          availableFishTypes: [
            ...viewModel.getAvailableFishTypes(),
            ...viewModel.customFishTypes
          ],
          spotVisibility: viewModel.spotVisibility,
          certificationDocuments: viewModel.certificationDocuments,
          isUploadingDocuments: viewModel.isUploadingDocuments,
          onHasFacilitiesChanged: (value) {
            viewModel.hasFacilities = value;
            viewModel.notifyListeners();
          },
          onToggleFacility: viewModel.toggleFacility,
          onAddNewFacility: () => _addNewFacility(viewModel),
          onIsPaidChanged: (value) {
            viewModel.isPaid = value;
            viewModel.notifyListeners();
          },
          onAddPrice: viewModel.addPrice,
          onRemovePrice: viewModel.removePrice,
          onIsOfficialChanged: (value) {
            viewModel.isOfficial = value;
            viewModel.notifyListeners();
          },
          onUploadDocuments: () => _handleUploadDocuments(viewModel),
          onVisibilityChanged: (value) {
            viewModel.spotVisibility = value;
            viewModel.notifyListeners();
          },
          onRemoveDocument: viewModel.removeDocument,
          onCertificationInfoChanged: (certificationData) {
            // 处理认证信息更新，保存到viewModel中
            viewModel.updateCertificationInfo(certificationData);
          },
        );
      default:
        return const SizedBox.shrink();
    }
  }

  Future<void> _showModernMapModal() async {
    FocusScope.of(context).unfocus();

    final result = await context.navigateToWithResult<LocationResult>(
      AppRoutes.mapLocationPicker,
      extra: MapLocationPickerRouteData(
        initialLocation: LocationData(
          latitude: _viewModel.latitude,
          longitude: _viewModel.longitude,
        ),
      ),
    );

    if (result != null && mounted) {
      _viewModel.updateLocation(
        latitude: result.latitude,
        longitude: result.longitude,
        formattedAddress: result.formattedAddress,
        province: result.province,
        city: result.city,
        county: result.county,
        useCurrentLocation: result.useCurrentLocation,
      );
    }
  }

  void _handleNext(CreateSpotViewModel viewModel) {
    if (_currentStep < _totalSteps - 1) {
      if (_validateCurrentStep()) {
        setState(() => _currentStep++);
      }
    } else {
      _submitSpot(viewModel);
    }
  }

  bool _validateCurrentStep() {
    if (_currentStep == 0) {
      if (_viewModel.latitude == 0 || _viewModel.longitude == 0) {
        _showValidationError('请先在地图上选择钓点位置');
        return false;
      }
      if (!_formKey.currentState!.validate()) {
        return false; // Let the form validator handle specific field messages
      }
    } else if (_currentStep == 1) {
      if (_viewModel.spotImages.isEmpty) {
        _showValidationError('请至少上传一张图片');
        return false;
      }
      if (_viewModel.spotImages.any((img) => img.hasError)) {
        _showValidationError('部分图片上传失败，请删除或重试');
        return false;
      }
      if (_viewModel.spotImages.any((img) => img.isUploading)) {
        _showValidationError('图片正在上传中，请稍候');
        return false;
      }
      if (_viewModel.selectedFishTypeIds.isEmpty) {
        _showValidationError('请至少选择或添加一种鱼类');
        return false;
      }
    } else if (_currentStep == 2) {
      if (_viewModel.isPaid) {
        bool priceValid = false;
        if (_viewModel.prices.isNotEmpty) {
          priceValid = true; // Multi-price exists
        } else if (_viewModel.priceController.text.isNotEmpty &&
            double.tryParse(_viewModel.priceController.text.trim()) != null &&
            double.parse(_viewModel.priceController.text.trim()) > 0) {
          priceValid = true; // Single price is valid
        }
        if (!priceValid) {
          _showValidationError('请设置有效的钓场价格');
          return false;
        }
      }
      if (_viewModel.hasFacilities &&
          !_viewModel.facilities.any((f) => f.isSelected)) {
        _showValidationError('请至少选择一个场地设施');
        return false;
      }

      // Add validation for certification documents
      if (_viewModel.isOfficial && _viewModel.certificationDocuments.isEmpty) {
        _showValidationError('请上传官方认证所需的证明文件');
        return false;
      }

      if (_viewModel.isOfficial &&
          _viewModel.certificationDocuments.any((doc) => doc.isUploading)) {
        _showValidationError('证明文件正在上传中，请稍候');
        return false;
      }

      if (_viewModel.isOfficial &&
          _viewModel.certificationDocuments.any((doc) => doc.hasError)) {
        _showValidationError('部分证明文件上传失败，请删除或重试');
        return false;
      }
    }

    return true; // Assume valid if checks pass
  }

  Future<void> _submitSpot(CreateSpotViewModel viewModel) async {
    FocusScope.of(context).unfocus(); // Hide keyboard

    // Use ViewModel's validation
    if (!viewModel.validateAllSteps()) {
      if (viewModel.latitude == 0 || viewModel.longitude == 0) {
        _showValidationError('请先在地图上选择钓点位置');
        setState(() => _currentStep = 0);
        return;
      }
      if (!viewModel.validateImagesAndFishTypes()) {
        if (viewModel.spotImages.isEmpty) {
          _showValidationError('请至少上传一张图片');
        } else if (viewModel.selectedFishTypeIds.isEmpty) {
          _showValidationError('请至少选择或添加一种鱼类');
        }
        setState(() => _currentStep = 1);
        return;
      }
      if (!viewModel.validateFacilitiesAndPrices()) {
        _showValidationError('请设置有效的钓场价格');
        setState(() => _currentStep = 2);
        return;
      }
      return;
    }

    try {
      final newSpot = await viewModel.submitSpot();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: const Text('钓点创建成功！'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Theme.of(context).colorScheme.primary),
        );
        widget.onSpotCreated(newSpot);
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('创建失败: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating),
        );
      }
    }
  }

  void _showValidationError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Theme.of(context).colorScheme.error,
        shape: const RoundedRectangleBorder(
            borderRadius: ShapeTokens.borderRadiusMd),
        margin: const EdgeInsets.all(SpacingTokens.space3),
      ),
    );
  }

  String _getStepTitle(int step) {
    switch (step) {
      case 0:
        return '基本信息';
      case 1:
        return '图片与鱼种';
      case 2:
        return '设施与价格';
      default:
        return '';
    }
  }

  String _getStepDescription(int step) {
    switch (step) {
      case 0:
        return '请准确填写钓点信息，让钓友更容易找到';
      case 1:
        return '优质的图片和详细的鱼种信息能吸引更多钓友';
      case 2:
        return '完善的设施信息让钓友了解更多细节';
      default:
        return '';
    }
  }

  String _getSuggestedName() {
    if (_viewModel.formattedAddress.isEmpty) return '新钓场';
    
    // 使用智能推荐服务生成名称
    return SpotNameSuggestionService.generateSuggestion(
      formattedAddress: _viewModel.formattedAddress,
      district: _viewModel.county,
      city: _viewModel.city,
    );
  }
  
  /// 获取多个推荐名称
  List<String> _getSuggestedNames() {
    if (_viewModel.formattedAddress.isEmpty) return ['新钓场'];
    
    return SpotNameSuggestionService.generateSuggestions(
      formattedAddress: _viewModel.formattedAddress,
      district: _viewModel.county,
      city: _viewModel.city,
      maxSuggestions: 3,
    );
  }

  String _extractFeatureName(String feature) {
    final index = _viewModel.formattedAddress.indexOf(feature);
    if (index == -1) return '';
    int startIndex = index;
    while (startIndex > 0 &&
        !'省市区县镇乡村路街道 '.contains(_viewModel.formattedAddress[startIndex - 1])) {
      startIndex--;
    }
    int endIndex = index + feature.length;
    if (startIndex < 0) startIndex = 0;
    if (endIndex > _viewModel.formattedAddress.length) {
      endIndex = _viewModel.formattedAddress.length;
    }
    if (startIndex >= endIndex) return ''; // Avoid errors

    return _viewModel.formattedAddress.substring(startIndex, endIndex);
  }

// Add a new method to handle document upload
  Future<void> _handleUploadDocuments(CreateSpotViewModel viewModel) async {
    if (viewModel.isUploadingDocuments) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('正在上传文件，请稍候...'),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    try {
      await viewModel.pickDocumentImages();
      if (mounted && viewModel.certificationDocuments.isNotEmpty) {
        // 成功上传文档后，设置为官方认证状态
        viewModel.isOfficial = true;
        viewModel.notifyListeners();

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '已上传 ${viewModel.certificationDocuments.length} 个证明文件，官方认证已开启'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('上传证明文件失败: $e'),
            behavior: SnackBarBehavior.floating,
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _addNewFacility(CreateSpotViewModel viewModel) {
    final nameController = TextEditingController();
    final descriptionController = TextEditingController();
    String selectedIcon = 'apps'; // Default icon

    final List<Map<String, String>> iconOptions = [
      {'name': '默认', 'icon': 'apps'},
      {'name': '烧烤', 'icon': 'outdoor_grill'},
      {'name': '船只', 'icon': 'directions_boat'},
      {'name': '游泳', 'icon': 'pool'},
      {'name': '网络', 'icon': 'wifi'},
      {'name': '淋浴', 'icon': 'shower'},
    ];

    bool hasError = false;

    void updateNameFromIcon(String iconName) {
      final selectedIconData = iconOptions.firstWhere(
        (icon) => icon['icon'] == iconName,
        orElse: () => {'name': '自定义设施', 'icon': 'apps'},
      );

      // Only update name if it's empty or matches a previous icon name
      if (nameController.text.isEmpty ||
          iconOptions.any((icon) => icon['name'] == nameController.text)) {
        nameController.text = selectedIconData['name'] ?? '自定义设施';
      }
    }

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setStateDialog) {
            return Dialog(
              shape: const RoundedRectangleBorder(
                borderRadius: ShapeTokens.borderRadiusLg,
              ),
              child: Container(
                constraints:
                    const BoxConstraints(maxWidth: SpacingTokens.space20 * 25),
                child: Padding(
                  padding: const EdgeInsets.all(SpacingTokens.space4),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Dialog header
                      Row(
                        children: [
                          Container(
                            padding: SpacingTokens.paddingSm,
                            decoration: BoxDecoration(
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withValues(alpha: 0.1),
                              shape: BoxShape.circle,
                            ),
                            child: Icon(
                              Icons.add_circle_outline,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          SizedBox(width: SpacingTokens.space3),
                          Text(
                            '添加场地设施',
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall
                                ?.copyWith(
                                  fontWeight: TypographyTokens.weightBold,
                                ),
                          ),
                        ],
                      ),
                      const SizedBox(height: SpacingTokens.space6),

                      Flexible(
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(Icons.palette_outlined,
                                      size: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary),
                                  const SizedBox(width: SpacingTokens.space2),
                                  Text(
                                    '选择图标',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: SpacingTokens.space3),
                              GridView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 4,
                                  mainAxisSpacing: SpacingTokens.space3,
                                  crossAxisSpacing: SpacingTokens.space3,
                                  childAspectRatio: 0.9,
                                ),
                                itemCount: iconOptions.length,
                                itemBuilder: (context, index) {
                                  final iconData = iconOptions[index];
                                  final iconName = iconData['icon'] ?? 'apps';
                                  final isSelected = selectedIcon == iconName;
                                  return _buildIconSelectionItem(
                                      context, iconData, isSelected, () {
                                    setStateDialog(() {
                                      selectedIcon = iconName;
                                      updateNameFromIcon(iconName);
                                    });
                                  });
                                },
                              ),
                              SizedBox(height: SpacingTokens.space6),
                              Row(
                                children: [
                                  Icon(Icons.edit_outlined,
                                      size: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary),
                                  const SizedBox(width: SpacingTokens.space2),
                                  Text(
                                    '设施名称',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: SpacingTokens.space2),
                              TextField(
                                controller: nameController,
                                decoration: InputDecoration(
                                  hintText: '设施名称',
                                  border: const OutlineInputBorder(),
                                  errorText: hasError ? '请填写设施名称' : null,
                                ),
                                onChanged: (_) => setStateDialog(() {
                                  if (hasError) hasError = false;
                                }),
                              ),
                              const SizedBox(height: SpacingTokens.space5),
                              Row(
                                children: [
                                  Icon(Icons.description_outlined,
                                      size: 18,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary),
                                  SizedBox(width: SpacingTokens.space2),
                                  Text(
                                    '描述 (可选)',
                                    style: TextStyle(
                                      fontWeight: FontWeight.w600,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .secondary,
                                      fontSize: 16,
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: SpacingTokens.space2),
                              TextField(
                                controller: descriptionController,
                                decoration: const InputDecoration(
                                  hintText: '添加设施详情',
                                  border: OutlineInputBorder(),
                                ),
                                maxLines: 3,
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: SpacingTokens.space6),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          OutlinedButton(
                            onPressed: () => Navigator.pop(context),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: SpacingTokens.space4,
                                  vertical: SpacingTokens.space3),
                            ),
                            child: const Text('取消'),
                          ),
                          const SizedBox(width: SpacingTokens.space3),
                          FilledButton.icon(
                            icon: const Icon(Icons.check),
                            label: const Text('添加'),
                            style: FilledButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: SpacingTokens.space4,
                                  vertical: SpacingTokens.space3),
                            ),
                            onPressed: () {
                              final name = nameController.text.trim();
                              final description =
                                  descriptionController.text.trim();
                              if (name.isEmpty) {
                                setStateDialog(() {
                                  hasError = true;
                                });
                                return;
                              }

                              viewModel.addFacility(
                                name: name,
                                icon: selectedIcon,
                                description:
                                    description.isNotEmpty ? description : null,
                              );

                              Navigator.pop(context);
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildIconSelectionItem(
      BuildContext context, Map iconData, bool isSelected, VoidCallback onTap) {
    return Tooltip(
      message: iconData['name'] as String,
      child: InkWell(
        onTap: onTap,
        borderRadius: ShapeTokens.borderRadiusMd,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).colorScheme.primaryContainer
                : Theme.of(context).colorScheme.surfaceVariant,
            borderRadius: ShapeTokens.borderRadiusMd,
            border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.outline,
                width: isSelected ? 2 : 1),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.2),
                      spreadRadius: 1,
                      blurRadius: 3,
                      offset: const Offset(0, SpacingTokens.space1 / 2),
                    )
                  ]
                : null,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                _getIconData(iconData['icon'] as String),
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                size: SpacingTokens.space6 + SpacingTokens.space1,
              ),
              const SizedBox(height: SpacingTokens.space2),
              Text(
                iconData['name'] as String,
                style: TextStyle(
                  fontSize: TypographyTokens.sizeLabelMedium,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'outdoor_grill':
        return Icons.outdoor_grill;
      case 'directions_boat':
        return Icons.directions_boat;
      case 'pool':
        return Icons.pool;
      case 'wifi':
        return Icons.wifi;
      case 'shower':
        return Icons.shower;
      default:
        return Icons.apps;
    }
  }
}
