import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/models/spot_detail_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/widgets/fishing_spot_details_content.dart';
import 'package:user_app/shared/widgets/skeleton_loading.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/utils/browsing_record_manager.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class FishingSpotDetailPage extends StatefulWidget {
  final int spotId;
  final FishingSpotVo? initialSpot;

 const FishingSpotDetailPage({
    super.key,
    required this.spotId,
    this.initialSpot,
  });

  @override
  State<FishingSpotDetailPage> createState() => _FishingSpotDetailPageState();
}

class _FishingSpotDetailPageState extends State<FishingSpotDetailPage> with BrowsingRecordMixin {
  SpotDetailVo? spotDetail;
  FishingSpotVo? spot; // 保留用于兼容性
  bool isLoading = false;
  String? error;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    if (widget.initialSpot != null) {
      spot = widget.initialSpot;
      // 如果有初始钓点数据，直接记录浏览
      _recordInitialSpotBrowsing();
    } else {
      _loadSpotDetail();
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    // 结束浏览记录
    endBrowsingRecord('spot', widget.spotId);
    super.dispose();
  }

  Future<void> _loadSpotDetail() async {
    debugPrint('=== FishingSpotDetailPage _loadSpotDetail 调试信息 ===');
    debugPrint('请求的spotId: ${widget.spotId}');
    debugPrint('spotId类型: ${widget.spotId.runtimeType}');
    setState(() {
      isLoading = true;
      error = null;
    });

    try {
      final fishingSpotService = context.read<FishingSpotService>();
      debugPrint('开始调用 fishingSpotService.getFishingSpotDetailVO(${widget.spotId})');
      final loadedSpotDetail = await fishingSpotService.getFishingSpotDetailVO(widget.spotId);
      debugPrint('API调用成功，获得钓点详情数据: ${loadedSpotDetail.id}');
      debugPrint('createdBy: ${loadedSpotDetail.createdBy}');
      debugPrint('creator: ${loadedSpotDetail.creator}');
      
      if (mounted) {
        setState(() {
          spotDetail = loadedSpotDetail;
          spot = loadedSpotDetail.toFishingSpotVo(); // 转换为兼容格式
          isLoading = false;
        });
        
        // 添加浏览记录
        _recordSpotBrowsing(loadedSpotDetail);
      }
    } catch (e) {
      debugPrint('API调用失败: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      if (mounted) {
        setState(() {
          error = e.toString();
          isLoading = false;
        });
      }
    }
  }

  /// 记录初始钓点浏览（有initialSpot时）
  void _recordInitialSpotBrowsing() {
    if (spot != null) {
      recordSpotBrowsing(
        spotId: spot!.id,
        spotName: spot!.name,
        description: spot!.description,
        imageUrl: spot!.images?.isNotEmpty == true ? spot!.images!.first : null,
      );
    }
  }

  /// 记录钓点浏览（从API加载详情后）
  void _recordSpotBrowsing(SpotDetailVo spotDetail) {
    recordSpotBrowsing(
      spotId: spotDetail.id,
      spotName: spotDetail.name,
      description: spotDetail.description,
      imageUrl: spotDetail.images.isNotEmpty ? spotDetail.images.first : null,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(spot?.name ?? '钓点详情'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        elevation: 0,
        surfaceTintColor: ColorTokens.transparent,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    // 如果正在加载且没有钓点数据，显示骨架屏
    if (isLoading && spot == null) {
      return const FishingSpotDetailSkeleton();
    }

    // 如果有错误，显示错误页面
    if (error != null) {
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(SpacingTokens.space6),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(SpacingTokens.space6),
                  decoration: BoxDecoration(
                    color: ColorTokens.errorContainer.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.error_outline,
                    size: 64,
                    color: ColorTokens.error,
                  ),
                ),
                const SizedBox(height: SpacingTokens.space6),
                Text(
                  '加载失败',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: ColorTokens.onSurface,
                  ),
                ),
                const SizedBox(height: SpacingTokens.space3),
                Text(
                  error!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: SpacingTokens.space6),
                FilledButton.icon(
                  onPressed: _loadSpotDetail,
                  icon: const Icon(Icons.refresh),
                  label: const Text('重试'),
                ),
              ],
            ),
          ),
        ),
      );
    }

    // 如果没有钓点数据，显示"钓点不存在"
    if (spot == null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(SpacingTokens.space6),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(SpacingTokens.space6),
                decoration: BoxDecoration(
                  color: ColorTokens.surfaceVariant,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.location_off_outlined,
                  size: 64,
                  color: ColorTokens.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: SpacingTokens.space6),
              Text(
                '钓点不存在',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: ColorTokens.onSurface,
                ),
              ),
              const SizedBox(height: SpacingTokens.space3),
              Text(
                '该钓点可能已被删除或不存在',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // 显示钓点详情内容
    return FishingSpotDetailsContent(
      spot: spot!,
      scrollController: _scrollController,
      onCreatorTap: _navigateToUserProfile,
      onCheckinSuccess: _handleCheckinSuccess,
    );
  }

  void _navigateToUserProfile(User user) {
    final route = '${AppRoutes.profile}/${user.id}';
    debugPrint('🚀 [FishingSpotDetail] 导航到用户主页: $route');
    debugPrint('🚀 [FishingSpotDetail] 用户信息: 姓名=${user.name}, ID=${user.id}');
    context.navigateTo(route);
  }

  void _handleCheckinSuccess(int newCheckinCount) {
    debugPrint('✅ [FishingSpotDetail] 收到签到成功回调，更新签到次数: $newCheckinCount');
    
    if (mounted && spot != null) {
      setState(() {
        spot = spot!.copyWith(checkinCount: spot!.checkinCount + newCheckinCount);
      });
      debugPrint('✅ [FishingSpotDetail] 页面签到次数已更新为: ${spot!.checkinCount}');
    }
  }
}