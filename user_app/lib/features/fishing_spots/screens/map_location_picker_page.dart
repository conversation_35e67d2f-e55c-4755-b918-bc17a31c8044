import 'dart:async';

import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:flutter/services.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class LocationResult {
  final double latitude;
  final double longitude;
  final String formattedAddress;
  final String province;
  final String city;
  final String county;
  final bool useCurrentLocation;

  LocationResult({
    required this.latitude,
    required this.longitude,
    required this.formattedAddress,
    required this.province,
    required this.city,
    required this.county,
    required this.useCurrentLocation,
  });
}

class MapLocationPickerPage extends StatefulWidget {
  final double initialLatitude;
  final double initialLongitude;
  final String initialFormattedAddress;

  const MapLocationPickerPage({
    super.key,
    required this.initialLatitude,
    required this.initialLongitude,
    this.initialFormattedAddress = '',
  });

  @override
  State<MapLocationPickerPage> createState() => _MapLocationPickerPageState();
}

class _MapLocationPickerPageState extends State<MapLocationPickerPage> {
  AMap2DController? _mapController;
  final _searchController = TextEditingController();
  List<PoiSearch> _searchResults = [];
  bool _isSearching = false;
  bool _isLoadingLocation = false;

  // Add debounce timer for address resolution
  Timer? _addressUpdateTimer;
  Timer? _mapCenterCheckTimer;
  static const Duration _mapCenterCheckInterval =
      Duration(milliseconds: 100); // Check every 100ms
  double _lastCheckedLat = 0.0;
  double _lastCheckedLng = 0.0;
  bool _isMapReady = false;
  bool _isMapMoving = false;
  DateTime _lastMovementTime = DateTime.now();

  // 全局防抖和去重机制
  Timer? _globalGeocodeTimer;
  String? _lastGeocodeLocation;
  bool _isGeocoding = false;
  bool _isConfirming = false;

  double _currentLatitude = 0.0;
  double _currentLongitude = 0.0;
  String _currentFormattedAddress = '';
  String _currentProvince = '';
  String _currentCity = '';
  String _currentCounty = '';
  bool _useCurrentLocation = false;

  @override
  void initState() {
    super.initState();

    // 初始化位置数据
    if (widget.initialLatitude != 0 && widget.initialLongitude != 0) {
      _currentLatitude = widget.initialLatitude;
      _currentLongitude = widget.initialLongitude;
      _currentFormattedAddress = widget.initialFormattedAddress;
      debugPrint('Initial position set: $_currentLatitude, $_currentLongitude');
    } else {
      debugPrint('No initial position, will auto-locate');
    }

    // 添加搜索监听
    _searchController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _addressUpdateTimer?.cancel(); // Cancel timer on dispose
    _mapCenterCheckTimer?.cancel(); // Cancel center check timer
    _globalGeocodeTimer?.cancel(); // Cancel global geocode timer
    try {
      _mapController?.clearMarkers();
      _mapController = null;
    } catch (e) {
      debugPrint('Error disposing map controller: $e');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: Stack(
        children: [
          // 地图
          GestureDetector(
            onPanStart: (_) {
              debugPrint('Map pan started');
              if (!_isMapMoving) {
                setState(() {
                  _isMapMoving = true;
                });
              }
            },
            onPanEnd: (_) {
              debugPrint('Map pan ended');
              // 不需要手动获取中心位置，定时器会自动处理
              // 只需要更新最后移动时间
              _lastMovementTime = DateTime.now();
            },
            child: AMap2DView(
              onAMap2DViewCreated: _onMapCreated,
              onGetLocation: _onGetLocation,
              onReGeocode: _onReGeocode,
              onPoiSearched: _onPoiSearched,
              onClick: _onMapClick,
            ),
          ),

          // 中心标记点
          Center(
            child: Container(
              margin: const EdgeInsets.only(bottom: 40), // 稍微向上偏移，让大头针"扎"在地图上
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // 标记点图标
                  AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: _isMapMoving
                          ? ColorTokens.warning
                          : Theme.of(context).colorScheme.primary,
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: (_isMapMoving ? ColorTokens.warning : ColorTokens.onSurface)
                              .withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: _isMapMoving
                        ? const Padding(
                            padding: EdgeInsets.all(12),
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: ColorTokens.onPrimary,
                            ),
                          )
                        : const Icon(
                            Icons.location_on,
                            color: ColorTokens.onPrimary,
                            size: 32,
                          ),
                  ),
                  // 小三角形指向地图
                  CustomPaint(
                    size: const Size(20, 10),
                    painter: TrianglePainter(
                      color: _isMapMoving
                          ? ColorTokens.warning
                          : Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 顶部搜索栏和返回按钮
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: Container(
              decoration: BoxDecoration(
                color: ColorTokens.onPrimary,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: SafeArea(
                child: Padding(
                  padding: SpacingTokens.paddingMd,
                  child: Column(
                    children: [
                      // 标题栏
                      Row(
                        children: [
                          IconButton(
                            icon: Container(
                              padding: SpacingTokens.paddingSm,
                              decoration: BoxDecoration(
                                color: Colors.grey.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Icon(
                                Icons.arrow_back_ios_new,
                                size: 20,
                                color: Color(0xFF2C3E50),
                              ),
                            ),
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              Navigator.pop(context);
                            },
                          ),
                          const Expanded(
                            child: Text(
                              '选择位置',
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF2C3E50),
                              ),
                            ),
                          ),
                          const SizedBox(width: 48), // 平衡布局
                        ],
                      ),

                      const SizedBox(height: SpacingTokens.space4),

                      // 搜索框
                      _buildSearchBar(),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // 搜索结果列表
          if (_searchResults.isNotEmpty)
            Positioned.fill(
              child: Container(
                color: ColorTokens.onPrimary,
                child: Column(
                  children: [
                    // 占位空间，避免与顶部搜索栏重叠
                    SizedBox(height: MediaQuery.of(context).padding.top + 120),

                    // 搜索结果标题
                    Container(
                      padding: SpacingTokens.paddingMd,
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        border: Border(
                          bottom: BorderSide(color: Colors.grey[200]!),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.search_rounded,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '找到 ${_searchResults.length} 个相关地点',
                            style: TextStyle(
                              color: Colors.grey[600],
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const Spacer(),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                _searchResults.clear();
                              });
                            },
                            child: Icon(
                              Icons.close_rounded,
                              color: Colors.grey[600],
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // 搜索结果列表
                    Expanded(
                      child: ListView.builder(
                        itemCount: _searchResults.length,
                        itemBuilder: (context, index) {
                          final result = _searchResults[index];
                          return Container(
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: Colors.grey[100]!,
                                  width: 1,
                                ),
                              ),
                            ),
                            child: ListTile(
                              contentPadding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              ),
                              leading: Container(
                                padding: SpacingTokens.paddingSm,
                                decoration: BoxDecoration(
                                  color: const Color(0xFF667EEA)
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.location_on_rounded,
                                  color: Color(0xFF667EEA),
                                  size: 20,
                                ),
                              ),
                              title: Text(
                                result.title ?? '未知地点',
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (result.address != null &&
                                      result.address!.isNotEmpty)
                                    Text(
                                      result.address!,
                                      style: TextStyle(
                                        color: Colors.grey[600],
                                        fontSize: 14,
                                      ),
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  if (result.cityName != null ||
                                      result.provinceName != null)
                                    Text(
                                      '${result.provinceName ?? ''}${result.cityName ?? ''}',
                                      style: TextStyle(
                                        color: Colors.grey[500],
                                        fontSize: 12,
                                      ),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                ],
                              ),
                              trailing: const Icon(
                                Icons.arrow_forward_ios_rounded,
                                size: 16,
                                color: Colors.grey,
                              ),
                              onTap: () => _selectSearchResult(result),
                            ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),

          // 右侧工具按钮
          Positioned(
            right: 16,
            bottom: 200,
            child: Column(
              children: [
                // 定位到当前位置按钮
                _buildActionButton(
                  icon: Icons.my_location_rounded,
                  onTap: _getCurrentLocation,
                ),
              ],
            ),
          ),

          // 底部位置信息和确认按钮
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Container(
              decoration: BoxDecoration(
                color: ColorTokens.onPrimary,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: Padding(
                  padding: SpacingTokens.paddingMd,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // 位置信息显示
                      Container(
                        width: double.infinity,
                        padding: SpacingTokens.paddingMd,
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.grey[200]!),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  color: Color(0xFF667EEA),
                                  size: 20,
                                ),
                                SizedBox(width: 8),
                                Text(
                                  '选中位置',
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: SpacingTokens.space2),
                            if (_currentFormattedAddress.isNotEmpty)
                              Text(
                                _currentFormattedAddress,
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 14,
                                ),
                              )
                            else
                              Text(
                                '纬度: ${_currentLatitude.toStringAsFixed(6)}, 经度: ${_currentLongitude.toStringAsFixed(6)}',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 12,
                                  fontFamily: 'monospace',
                                ),
                              ),
                          ],
                        ),
                      ),

                      const SizedBox(height: SpacingTokens.space4),

                      // 确认按钮
                      SizedBox(
                        width: double.infinity,
                        child: Container(
                          decoration: BoxDecoration(
                            color: ColorTokens.primary,
                            borderRadius: BorderRadius.circular(16),
                            boxShadow: [
                              BoxShadow(
                                color: const Color(0xFF667EEA)
                                    .withValues(alpha: 0.3),
                                blurRadius: 20,
                                offset: const Offset(0, 8),
                              ),
                            ],
                          ),
                          child: MaterialButton(
                            onPressed: _confirmCenterLocation,
                            padding: const EdgeInsets.symmetric(vertical: 18),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: _isLoadingLocation
                                ? const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          color: ColorTokens.onPrimary,
                                        ),
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        '正在解析位置...',
                                        style: TextStyle(
                                          color: ColorTokens.onPrimary,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  )
                                : const Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        Icons.check_circle_rounded,
                                        color: ColorTokens.onPrimary,
                                        size: 24,
                                      ),
                                      SizedBox(width: 12),
                                      Text(
                                        '确认选择此位置',
                                        style: TextStyle(
                                          color: ColorTokens.onPrimary,
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),

          // 加载指示器
          if (_isLoadingLocation)
            Positioned.fill(
              child: Container(
                color: Colors.black.withValues(alpha: 0.3),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Color(0xFF667EEA),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onSubmitted: _performSearch,
        decoration: InputDecoration(
          hintText: '搜索地点...',
          hintStyle: TextStyle(color: Colors.grey[400]),
          prefixIcon: const Icon(
            Icons.search_rounded,
            color: Color(0xFF667EEA),
          ),
          suffixIcon: _isSearching
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: Padding(
                    padding: EdgeInsets.all(12),
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Color(0xFF667EEA),
                    ),
                  ),
                )
              : _searchController.text.isNotEmpty
                  ? IconButton(
                      icon: const Icon(Icons.clear_rounded),
                      onPressed: () {
                        _searchController.clear();
                        setState(() {
                          _searchResults.clear();
                        });
                      },
                    )
                  : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 20,
            vertical: 16,
          ),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 56,
        height: 56,
        decoration: BoxDecoration(
          color: ColorTokens.onPrimary,
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: Color(0xFF667EEA).withValues(alpha: 0.3),
              blurRadius: 16,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: const Color(0xFF667EEA),
          size: 28,
        ),
      ),
    );
  }

  // 地图点击事件
  void _onMapClick(num lat, num lon) {
    debugPrint('Map clicked at: $lat, $lon');

    // 如果正在确认中，忽略点击事件
    if (_isConfirming) {
      debugPrint('Ignoring map click during confirmation');
      return;
    }

    // 更新当前位置
    setState(() {
      _currentLatitude = lat.toDouble();
      _currentLongitude = lon.toDouble();
      _lastCheckedLat = lat.toDouble();
      _lastCheckedLng = lon.toDouble();
      _isMapMoving = false; // 点击后立即停止移动状态
    });

    // 使用全局地理编码方法
    _performGlobalGeocode(lat.toDouble(), lon.toDouble());
  }

  // 地图相关方法
  void _onMapCreated(AMap2DController controller) {
    debugPrint('Map controller created: ${controller.runtimeType}');
    _mapController = controller;
    _isMapReady = true;

    // 如果有初始位置，移动到初始位置
    if (_currentLatitude != 0 && _currentLongitude != 0) {
      _moveMapToLocation(_currentLatitude, _currentLongitude);
    } else {
      // 如果传入的坐标是0,0，立即自动获取当前位置
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted &&
            _mapController != null &&
            _currentLatitude == 0 &&
            _currentLongitude == 0) {
          _autoGetCurrentLocation();
        }
      });
    }

    // 立即开始检查地图中心点位置
    if (mounted && _mapController != null) {
      debugPrint('Starting map center checking immediately...'); // Debug print

      // 首先立即获取一次当前中心点位置
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted && _mapController != null) {
          _updateCenterLocationDisplay();
        }
      });

      // 然后开始定时检查
      _startMapCenterChecking();
    }
  }

  // Start periodic checking of map center position
  void _startMapCenterChecking() {
    debugPrint('Starting map center checking timer...');
    _mapCenterCheckTimer?.cancel();
    _mapCenterCheckTimer = Timer.periodic(_mapCenterCheckInterval, (timer) {
      if (!mounted || _mapController == null) {
        timer.cancel();
        return;
      }
      _checkMapCenterChange();
    });
  }

  // Check if map center has changed and update accordingly
  Future<void> _checkMapCenterChange() async {
    if (_mapController == null || !mounted || !_isMapReady || _isConfirming) {
      return;
    }

    try {
      final center = await _mapController?.getMapCenter();

      if (center != null && center.isNotEmpty) {
        final lat = center['latitude'] ?? 0.0;
        final lng = center['longitude'] ?? 0.0;

        // Check if position has changed
        const double threshold =
            0.00001; // Very small threshold to detect any movement
        final latDiff = (lat - _lastCheckedLat).abs();
        final lngDiff = (lng - _lastCheckedLng).abs();

        if (lat != 0.0 && lng != 0.0) {
          // If position changed, mark as moving
          if (latDiff > threshold || lngDiff > threshold) {
            debugPrint(
                'Map is moving - lat diff: $latDiff, lng diff: $lngDiff');
            _lastCheckedLat = lat;
            _lastCheckedLng = lng;
            _lastMovementTime = DateTime.now();

            if (!_isMapMoving) {
              setState(() {
                _isMapMoving = true;
                // 清空之前的地址，显示坐标
                _currentFormattedAddress =
                    '纬度: ${lat.toStringAsFixed(6)}, 经度: ${lng.toStringAsFixed(6)}';
              });
            }

            // Update coordinates immediately
            if (mounted) {
              setState(() {
                _currentLatitude = lat;
                _currentLongitude = lng;
              });
            }

            // Cancel any pending address resolution
            _globalGeocodeTimer?.cancel();
          } else {
            // If position hasn't changed for 500ms, consider map stopped and resolve address
            final timeSinceLastMovement =
                DateTime.now().difference(_lastMovementTime).inMilliseconds;
            if (_isMapMoving && timeSinceLastMovement > 500) {
              debugPrint(
                  'Map stopped moving after ${timeSinceLastMovement}ms, triggering address resolution');
              setState(() {
                _isMapMoving = false;
              });

              // Use global geocode method to prevent duplicate requests
              _performGlobalGeocode(lat, lng);
            }
          }
        }
      } else {
        debugPrint('Failed to get map center - center is null or empty');
      }
    } catch (e) {
      debugPrint('Error checking map center change: $e');
    }
  }

  // 删除了之前的_onMapPanEnd方法，因为我们现在使用定时器自动检测地图移动

  // 全局地理编码方法，防止重复请求
  void _performGlobalGeocode(double lat, double lng) {
    if (_mapController == null || !mounted || !_isMapReady || _isGeocoding) {
      return;
    }

    final String currentLocation =
        '${lat.toStringAsFixed(6)},${lng.toStringAsFixed(6)}';

    // 检查是否是重复请求
    if (_lastGeocodeLocation == currentLocation) {
      debugPrint('Skipping duplicate geocode request for: $currentLocation');
      return;
    }

    // 取消之前的定时器
    _globalGeocodeTimer?.cancel();

    // 使用防抖机制
    _globalGeocodeTimer = Timer(const Duration(milliseconds: 300), () async {
      if (!mounted || _isGeocoding) return;

      _isGeocoding = true;
      _lastGeocodeLocation = currentLocation;

      // 只有在地图移动时才显示"正在解析地址..."
      if (_isMapMoving && mounted) {
        setState(() {
          _currentFormattedAddress = '正在解析地址...';
        });
      }

      try {
        debugPrint('Global geocoding for coordinates: $lat, $lng');
        await _mapController?.reGeocode(lat, lng);
      } catch (e) {
        debugPrint('Global geocode error: $e');
        if (mounted) {
          setState(() {
            _currentFormattedAddress =
                '纬度: ${lat.toStringAsFixed(6)}, 经度: ${lng.toStringAsFixed(6)}';
            _isMapMoving = false;
          });
        }
      } finally {
        _isGeocoding = false;
      }
    });
  }

  // 更新中心位置显示（不移动地图，只更新显示）
  Future<void> _updateCenterLocationDisplay() async {
    if (_mapController == null || !_isMapReady) return;

    try {
      final center = await _mapController?.getMapCenter();
      debugPrint('Initial center location: $center'); // Debug print

      if (center != null && center.isNotEmpty) {
        final lat = center['latitude'] ?? 0.0;
        final lng = center['longitude'] ?? 0.0;

        debugPrint(
            'Initial parsed coordinates: lat=$lat, lng=$lng'); // Debug print

        if (lat != 0.0 && lng != 0.0) {
          setState(() {
            _currentLatitude = lat;
            _currentLongitude = lng;
            _lastCheckedLat = lat; // Initialize tracking variables
            _lastCheckedLng = lng;
          });

          // 初始加载使用全局地理编码方法
          _performGlobalGeocode(lat, lng);
        }
      } else {
        debugPrint('Initial center location is null or empty'); // Debug print
      }
    } catch (e) {
      debugPrint('Error updating center location display: $e');
    }
  }

  // 自动获取当前位置的方法
  Future<void> _autoGetCurrentLocation() async {
    if (_isLoadingLocation) {
      debugPrint('Auto location already in progress, skipping');
      return;
    }

    setState(() => _isLoadingLocation = true);
    debugPrint('Starting auto location...');

    try {
      await Future.delayed(const Duration(milliseconds: 300));

      if (_mapController == null) {
        debugPrint('Map controller not available for location');
        setState(() => _isLoadingLocation = false);
        return;
      }

      if (_currentLatitude != 0 && _currentLongitude != 0) {
        debugPrint('Location already available, skipping auto location');
        setState(() => _isLoadingLocation = false);
        return;
      }

      debugPrint('Calling map controller location method...');
      await _mapController?.location();

      // 显示正在定位的提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('正在获取当前位置...'),
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }

      Future.delayed(const Duration(seconds: 10), () {
        if (mounted &&
            _isLoadingLocation &&
            _currentLatitude == 0 &&
            _currentLongitude == 0) {
          setState(() => _isLoadingLocation = false);
          debugPrint(
              'Location timeout - user can still manually select location');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('定位超时，您可以手动选择位置'),
              duration: Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      });
    } catch (e) {
      debugPrint('Auto location error: $e');
      setState(() => _isLoadingLocation = false);
    }
  }

  void _getCurrentLocation() {
    if (_isLoadingLocation) {
      debugPrint('Location request already in progress');
      return;
    }

    setState(() => _isLoadingLocation = true);
    debugPrint('Manual location request started...');

    try {
      if (_mapController == null) {
        debugPrint('Map controller not available for manual location');
        setState(() => _isLoadingLocation = false);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('地图未准备就绪，请稍后再试'),
              duration: Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      _mapController?.location();

      Future.delayed(const Duration(seconds: 10), () {
        if (mounted && _isLoadingLocation) {
          setState(() => _isLoadingLocation = false);
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('获取当前位置超时，请检查定位权限或手动选择位置'),
              duration: Duration(seconds: 3),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      });
    } catch (e) {
      debugPrint('Get location error: $e');
      setState(() => _isLoadingLocation = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('定位失败，请检查定位权限或手动选择位置'),
            duration: Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  void _moveMapToLocation(double latitude, double longitude) {
    debugPrint('Moving map to: $latitude, $longitude');
    try {
      if (_mapController != null) {
        _mapController?.move(latitude.toString(), longitude.toString());
        debugPrint('Map moved successfully to: $latitude, $longitude');
      }
    } catch (e) {
      debugPrint('Error moving map: $e');
    }
  }

  void _onGetLocation(dynamic location) {
    double lat = 0.0;
    double lng = 0.0;

    if (location != null) {
      try {
        lat = (location.latitude as num?)?.toDouble() ?? 0.0;
        lng = (location.longitude as num?)?.toDouble() ?? 0.0;
      } catch (e) {
        debugPrint('Error parsing location: $e');
        return;
      }
    }

    if (lat != 0.0 && lng != 0.0) {
      _moveMapToLocation(lat, lng);
      setState(() {
        _currentLatitude = lat;
        _currentLongitude = lng;
        _useCurrentLocation = true;
        _isLoadingLocation = false;
      });

      // 获取当前位置的地址信息，使用全局地理编码方法
      _performGlobalGeocode(lat, lng);

      // 显示成功定位的提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('已定位到当前位置'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } else {
      setState(() => _isLoadingLocation = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('获取位置信息失败，请重试'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  void _onReGeocode(dynamic result) {
    setState(() {
      try {
        debugPrint('ReGeocode result type: ${result.runtimeType}');
        debugPrint('ReGeocode result: $result');

        String address = '';
        String province = '';
        String city = '';
        String district = '';

        if (result != null) {
          if (result is ReGeocodeResult) {
            address = result.regeocode.formattedAddress;
            final addressComponent = result.regeocode.addressComponent;
            if (addressComponent != null) {
              province = addressComponent.province;
              city = addressComponent.city;
              district = addressComponent.district;
            }
            debugPrint(
                'Extracted - Address: $address, Province: $province, City: $city, District: $district');
          } else {
            try {
              final dynamic regeocode = (result as dynamic).regeocode;
              if (regeocode != null) {
                address = regeocode.formattedAddress?.toString() ?? '';
                final dynamic addressComponent = regeocode.addressComponent;
                if (addressComponent != null) {
                  province = addressComponent.province?.toString() ?? '';
                  city = addressComponent.city?.toString() ?? '';
                  district = addressComponent.district?.toString() ?? '';
                }
              }
            } catch (e) {
              address = result.toString();
              if (address == 'Instance of \'ReGeocodeResult\'') {
                address = '地址解析中...';
              }
              debugPrint('Using toString fallback: $address');
            }
          }
        }

        _currentFormattedAddress =
            address.isNotEmpty && address != 'Instance of \'ReGeocodeResult\''
                ? address
                : '位置信息获取中...';
        _currentProvince = province;
        _currentCity = city;
        _currentCounty = district;

        debugPrint('Final parsed address: $_currentFormattedAddress');

        // 地址解析完成后，更新移动状态
        _isMapMoving = false;
      } catch (e) {
        debugPrint('Error parsing regeocode result: $e');
        _currentFormattedAddress = '位置解析失败';
        _currentProvince = '';
        _currentCity = '';
        _currentCounty = '';
        _isMapMoving = false;
      }
      _isLoadingLocation = false;
    });
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) return;

    setState(() => _isSearching = true);

    try {
      String city = '';
      if (_currentCity.isNotEmpty) {
        city = _currentCity;
      } else if (_currentProvince.isNotEmpty) {
        city = _currentProvince;
      }

      await _mapController?.search(query, city: city);

      Future.delayed(const Duration(seconds: 5), () {
        if (mounted && _isSearching) {
          setState(() {
            _isSearching = false;
            if (_searchResults.isEmpty) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('未找到相关地点，请尝试其他关键词'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          });
        }
      });
    } catch (e) {
      debugPrint('Search error: $e');
      setState(() => _isSearching = false);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('搜索失败，请检查网络连接'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  void _onPoiSearched(List<PoiSearch> results) {
    setState(() {
      final validResults = results.where((result) {
        final latStr = result.latitude;
        final lonStr = result.longitude;

        if (latStr == null ||
            lonStr == null ||
            latStr.isEmpty ||
            lonStr.isEmpty) {
          return false;
        }

        try {
          final lat = double.parse(latStr);
          final lng = double.parse(lonStr);
          return _isValidCoordinate(lat, lng);
        } catch (e) {
          return false;
        }
      }).toList();

      _searchResults = validResults;
      _isSearching = false;

      debugPrint(
          'Search completed: ${results.length} total results, ${validResults.length} valid results');
    });
  }

  bool _isValidCoordinate(double lat, double lng) {
    if (lat < -90 || lat > 90) return false;
    if (lng < -180 || lng > 180) return false;
    if (lat == 0 && lng == 0) return false;
    return true;
  }

  Future<void> _selectSearchResult(PoiSearch result) async {
    final latStr = result.latitude;
    final lonStr = result.longitude;

    if (latStr != null &&
        lonStr != null &&
        latStr.isNotEmpty &&
        lonStr.isNotEmpty) {
      try {
        final latDouble = double.parse(latStr);
        final lonDouble = double.parse(lonStr);

        if (!_isValidCoordinate(latDouble, lonDouble)) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('坐标无效: ${result.title ?? "地点"}'),
              duration: Duration(seconds: 2),
              backgroundColor: Colors.orange,
            ),
          );
          return;
        }

        setState(() {
          _searchResults.clear();
          _currentLatitude = latDouble;
          _currentLongitude = lonDouble;
          _currentFormattedAddress = result.address ?? result.title ?? '';
          _currentProvince = result.provinceName ?? '';
          _currentCity = result.cityName ?? '';
          _currentCounty = result.adName ?? '';
          _useCurrentLocation = false;
        });

        _searchController.clear();
        _moveMapToLocation(latDouble, lonDouble);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已选择: ${result.title ?? "地点"}'),
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      } catch (e) {
        debugPrint('Error parsing coordinates: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('位置信息有误，请重新选择'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    }
  }

  // 确认中心位置
  Future<void> _confirmCenterLocation() async {
    if (_mapController == null || _isConfirming) {
      debugPrint('Map controller not available or already confirming');
      return;
    }

    try {
      // 设置确认状态，停止定时器检查
      setState(() {
        _isLoadingLocation = true;
        _isConfirming = true;
      });

      // 暂停定时器避免冲突
      _mapCenterCheckTimer?.cancel();
      _globalGeocodeTimer?.cancel();

      // 获取地图中心点的坐标
      final center = await _mapController?.getMapCenter();
      debugPrint('Confirm center location result: $center'); // Debug print

      if (center != null && center.isNotEmpty) {
        final lat = center['latitude'] ?? 0.0;
        final lng = center['longitude'] ?? 0.0;

        debugPrint(
            'Confirm parsed coordinates: lat=$lat, lng=$lng'); // Debug print

        if (lat != 0.0 && lng != 0.0) {
          debugPrint('Center point location: $lat, $lng');

          // 确保使用按钮点击时的精确坐标
          setState(() {
            _currentLatitude = lat;
            _currentLongitude = lng;
            _useCurrentLocation = false;
            _lastCheckedLat = lat;
            _lastCheckedLng = lng;
          });

          // 如果当前地址为空或是坐标格式，需要重新解析
          final bool needGeocode = _currentFormattedAddress.isEmpty ||
              _currentFormattedAddress.startsWith('纬度:') ||
              _currentFormattedAddress == '正在解析地址...' ||
              _currentFormattedAddress == '位置信息获取中...';

          if (needGeocode) {
            debugPrint('Need to geocode for final confirmation');
            try {
              // 直接调用，不使用全局方法，确保立即执行
              setState(() {
                _currentFormattedAddress = '正在解析地址...';
              });
              await _mapController?.reGeocode(lat, lng);

              // 等待地址解析完成
              await Future.delayed(const Duration(milliseconds: 800));
            } catch (e) {
              debugPrint('ReGeocode error for center point: $e');
              setState(() {
                _currentFormattedAddress =
                    '纬度: ${lat.toStringAsFixed(6)}, 经度: ${lng.toStringAsFixed(6)}';
              });
            }
          }

          // 最终确认位置
          if (mounted) {
            _confirmLocation();
          }
        } else {
          setState(() {
            _isLoadingLocation = false;
            _isConfirming = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('无法获取地图中心位置坐标'),
              duration: Duration(seconds: 2),
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      } else {
        setState(() {
          _isLoadingLocation = false;
          _isConfirming = false;
        });
        debugPrint('Center location result is null or empty'); // Debug print
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('无法获取地图中心位置数据'),
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error getting center point: $e');
      setState(() {
        _isLoadingLocation = false;
        _isConfirming = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('获取中心位置失败: $e'),
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
    } finally {
      // 确保重启定时器
      if (mounted && !_isConfirming) {
        Future.delayed(const Duration(milliseconds: 100), () {
          if (mounted && !_isConfirming) {
            _startMapCenterChecking();
          }
        });
      }
    }
  }

  void _confirmLocation() {
    debugPrint(
        'Confirming location: lat=$_currentLatitude, lng=$_currentLongitude, address=$_currentFormattedAddress');

    setState(() {
      _isLoadingLocation = false;
      _isConfirming = false;
    });

    Navigator.pop(
      context,
      LocationResult(
        latitude: _currentLatitude,
        longitude: _currentLongitude,
        formattedAddress: _currentFormattedAddress,
        province: _currentProvince,
        city: _currentCity,
        county: _currentCounty,
        useCurrentLocation: _useCurrentLocation,
      ),
    );
  }
}

// 自定义绘制三角形的画笔
class TrianglePainter extends CustomPainter {
  final Color color;

  TrianglePainter({this.color = const Color(0xFF667EEA)});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    path.moveTo(size.width / 2, size.height);
    path.lineTo(0, 0);
    path.lineTo(size.width, 0);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(TrianglePainter oldDelegate) => oldDelegate.color != color;
}
