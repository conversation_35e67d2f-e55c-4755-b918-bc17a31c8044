import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/auth/auth_state.dart';
import 'package:user_app/features/fishing_spots/models/spot_summary_vo.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/spot_moments_section.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class SpotCard extends StatefulWidget {
  final SpotSummaryVo spot;
  final VoidCallback onTap;

  const SpotCard({
    super.key,
    required this.spot,
    required this.onTap,
  });

  @override
  State<SpotCard> createState() => _SpotCardState();
}

class _SpotCardState extends State<SpotCard> {
  bool _isFavorite = false;
  bool _isLoadingFavorite = false;

  // 获取钓点ID
  int get spotId => widget.spot.id;

  // 获取钓点名称
  String get spotName => widget.spot.name;

  // 获取图片列表
  List<String> get images {
    return widget.spot.mainImage != null ? [widget.spot.mainImage!] : [];
  }

  // 获取评分
  double get rating => widget.spot.rating;

  // 获取最近动态数量
  int get recentMomentsCount => widget.spot.recentMomentsCount;

  @override
  void initState() {
    super.initState();
    _checkFavoriteStatus();
  }

  Future<void> _checkFavoriteStatus() async {
    // 可以在这里检查收藏状态
    // 由于 FishingSpotVo 没有 isFavorite 字段，需要单独查询
  }

  void _showLoginRequiredDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('需要登录'),
          content: const Text('收藏功能需要登录，登录后可以方便地管理您收藏的钓点'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
                context.navigateTo(AppRoutes.login);
              },
              child: const Text('去登录'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _toggleFavorite() async {
    if (_isLoadingFavorite) return;

    // 检查用户登录状态
    final authState = context.read<AuthState>();
    if (!authState.isLoggedIn) {
      _showLoginRequiredDialog();
      return;
    }

    // 用户已登录，执行收藏逻辑
    setState(() {
      _isLoadingFavorite = true;
    });

    try {
      final fishingSpotService = getIt<FishingSpotService>();

      if (_isFavorite) {
        await fishingSpotService.unfavoriteFishingSpot(spotId);
        setState(() {
          _isFavorite = false;
        });
      } else {
        await fishingSpotService.favoriteFishingSpot(spotId);
        setState(() {
          _isFavorite = true;
        });
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_isFavorite ? '已添加收藏' : '已取消收藏'),
            duration: MotionTokens.durationShort4,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('操作失败: $e'),
            backgroundColor: ColorTokens.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingFavorite = false;
        });
      }
    }
  }


  @override
  Widget build(BuildContext context) {
    final hasNewMoments = recentMomentsCount > 0;

    return Container(
      margin: const EdgeInsets.fromLTRB(
          SpacingTokens.space4, 0, SpacingTokens.space4, SpacingTokens.space4),
      child: Material(
        color: ColorTokens.surface.withValues(alpha: 0),
        child: InkWell(
          onTap: widget.onTap,
          borderRadius: ShapeTokens.borderRadiusLg,
          child: Container(
            decoration: BoxDecoration(
              color: ColorTokens.surface,
              borderRadius: ShapeTokens.borderRadiusLg,
              boxShadow: ElevationTokens.shadow2(context),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Image Section
                _SpotImageSection(
                  images: images,
                  spotName: spotName,
                  spotId: widget.spot.id,
                  hasNewMoments: hasNewMoments,
                  rating: rating,
                  recentMomentsCount: recentMomentsCount,
                ),

                // Content Section
                _SpotContentSection(
                  spot: widget.spot,
                  isFavorite: _isFavorite,
                  isLoadingFavorite: _isLoadingFavorite,
                  onFavoriteTap: _toggleFavorite,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _SpotImageSection extends StatelessWidget {
  final List<String> images;
  final String spotName;
  final int spotId;
  final bool hasNewMoments;
  final double rating;
  final int recentMomentsCount;

  const _SpotImageSection({
    required this.images,
    required this.spotName,
    required this.spotId,
    required this.hasNewMoments,
    required this.rating,
    required this.recentMomentsCount,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Image
        Container(
          height: 180,
          decoration: const BoxDecoration(
            borderRadius: ShapeTokens.borderRadiusTopLg,
            color: ColorTokens.surfaceVariant,
          ),
          child: ClipRRect(
            borderRadius: ShapeTokens.borderRadiusTopLg,
            child: images.isNotEmpty
                ? CachedNetworkImage(
                    imageUrl: images.first,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: ColorTokens.surfaceVariant,
                      child: const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            ColorTokens.outline,
                          ),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: ColorTokens.surfaceVariant,
                      child: const Icon(
                        Icons.image_not_supported,
                        size: 48,
                        color: ColorTokens.outline,
                      ),
                    ),
                  )
                : Container(
                    color: ColorTokens.surfaceVariant,
                    child: const Icon(
                      Icons.water,
                      size: 48,
                      color: ColorTokens.outline,
                    ),
                  ),
          ),
        ),

        // Gradient Overlay
        Positioned(
          bottom: 0,
          left: 0,
          right: 0,
          height: 80,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: ShapeTokens.borderRadiusTopLg,
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  ColorTokens.surface.withValues(alpha: 0),
                  ColorTokens.shadow.withValues(alpha: 0.5),
                ],
              ),
            ),
          ),
        ),

        // Badges
        Positioned(
          top: SpacingTokens.space3,
          left: SpacingTokens.space3,
          right: SpacingTokens.space3,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // New Moments Indicator - 强制显示用于调试
              if (hasNewMoments || recentMomentsCount > 0)
                GestureDetector(
                  onTap: () =>
                      _navigateToSpotMoments(context, spotId, spotName),
                  child: _Badge(
                    icon: Icons.whatshot,
                    label: hasNewMoments ? '新动态' : '调试:$recentMomentsCount',
                    gradient: LinearGradient(
                      colors: [
                        hasNewMoments ? ColorTokens.warning : ColorTokens.error,
                        hasNewMoments ? ColorTokens.warning : ColorTokens.error,
                      ],
                    ),
                  ),
                ),

              // Recent Moments Count - 修改条件：如果有最新动态就显示数量（至少显示1）
              if (hasNewMoments || recentMomentsCount > 0)
                GestureDetector(
                  onTap: () =>
                      _navigateToSpotMoments(context, spotId, spotName),
                  child: _Badge(
                    icon: Icons.forum,
                    label: '${recentMomentsCount > 0 ? recentMomentsCount : 1}',
                    backgroundColor: ColorTokens.surface.withValues(alpha: 0.9),
                    textColor: ColorTokens.onSurface,
                  ),
                ),
            ],
          ),
        ),

        // Rating
        if (rating > 0)
          Positioned(
            bottom: SpacingTokens.space3,
            left: SpacingTokens.space3,
            child: Container(
              padding: const EdgeInsets.symmetric(
                  horizontal: SpacingTokens.space2 + 2,
                  vertical: SpacingTokens.space1 + 2),
              decoration: BoxDecoration(
                color: ColorTokens.shadow.withValues(alpha: 0.6),
                borderRadius: ShapeTokens.borderRadiusLg,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.star,
                    size: 16,
                    color: ColorTokens.warning,
                  ),
                  SpacingTokens.horizontalSpaceXs,
                  Text(
                    rating.toStringAsFixed(1),
                    style: TypographyTokens.labelMedium.copyWith(
                      color: ColorTokens.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
}

class _SpotContentSection extends StatelessWidget {
  final SpotSummaryVo spot;
  final bool isFavorite;
  final bool isLoadingFavorite;
  final VoidCallback onFavoriteTap;

  const _SpotContentSection({
    required this.spot,
    required this.isFavorite,
    required this.isLoadingFavorite,
    required this.onFavoriteTap,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title and Favorite
          Row(
            children: [
              Expanded(
                child: Text(
                  spot.name,
                  style: TypographyTokens.titleMedium.copyWith(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SpacingTokens.horizontalSpaceSm,
              // Favorite Button
              GestureDetector(
                onTap: isLoadingFavorite ? null : onFavoriteTap,
                child: Container(
                  padding: SpacingTokens.paddingSm,
                  decoration: BoxDecoration(
                    color: isFavorite
                        ? ColorTokens.errorContainer
                        : ColorTokens.surfaceVariant,
                    shape: BoxShape.circle,
                  ),
                  child: isLoadingFavorite
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              isFavorite
                                  ? ColorTokens.error
                                  : ColorTokens.onSurfaceVariant,
                            ),
                          ),
                        )
                      : Icon(
                          isFavorite ? Icons.favorite : Icons.favorite_border,
                          size: 20,
                          color: isFavorite
                              ? ColorTokens.error
                              : ColorTokens.onSurfaceVariant,
                        ),
                ),
              ),
            ],
          ),

          SpacingTokens.verticalSpaceSm,

          // Address
          Row(
            children: [
              const Icon(
                Icons.location_on,
                size: 16,
                color: ColorTokens.onSurfaceVariant,
              ),
              SpacingTokens.horizontalSpaceXs,
              Expanded(
                child: Text(
                  spot.address,
                  style: TypographyTokens.bodyMedium.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          SpacingTokens.verticalSpaceMd,

          // 所有信息在一行中显示
          Row(
            children: [
              // 价格信息（突出显示）
              if (spot.priceText != null) ...[
                _buildPriceChip(spot.priceText!),
                const SizedBox(width: SpacingTokens.space1 + 2),
              ],

              // 签到人数
              _buildCompactStatChip(Icons.people_rounded,
                  spot.checkinCount.toString(), ColorTokens.secondary),

              const SizedBox(width: SpacingTokens.space1 + 2),

              // 评分
              if (spot.rating > 0) ...[
                _buildCompactStatChip(Icons.star_rounded,
                    spot.rating.toStringAsFixed(1), ColorTokens.warning),
                const SizedBox(width: SpacingTokens.space1 + 2),
              ],

              // 设施标签
              if (spot.hasFacilities) ...[
                _buildCompactFacilityChip(),
                const SizedBox(width: SpacingTokens.space1 + 2),
              ],

              // 鱼类信息（紧凑显示，使用Expanded防止溢出）
              if (spot.fishTypeNames.isNotEmpty)
                Expanded(
                  child: Row(
                    children: _buildCompactFishTypeChips(spot.fishTypeNames),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// 价格标签
  Widget _buildPriceChip(String priceText) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space2 + 2,
        vertical: SpacingTokens.space1,
      ),
      decoration: BoxDecoration(
        color: priceText == '免费'
            ? ColorTokens.successContainer
            : ColorTokens.primaryContainer,
        borderRadius: ShapeTokens.borderRadiusSm,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            priceText == '免费' ? Icons.free_breakfast : Icons.monetization_on,
            size: 12,
            color: priceText == '免费'
                ? ColorTokens.onSuccessContainer
                : ColorTokens.onPrimaryContainer,
          ),
          const SizedBox(width: SpacingTokens.space1 - 2),
          Text(
            priceText,
            style: TypographyTokens.labelSmall.copyWith(
              color: priceText == '免费'
                  ? ColorTokens.onSuccessContainer
                  : ColorTokens.onPrimaryContainer,
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
          ),
        ],
      ),
    );
  }

  /// 紧凑统计信息标签
  Widget _buildCompactStatChip(IconData icon, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space1 + 2,
        vertical: SpacingTokens.space1 - 2,
      ),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.12),
        borderRadius: ShapeTokens.borderRadiusXs,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 10,
            color: color,
          ),
          const SizedBox(width: SpacingTokens.space1 - 4),
          Text(
            value,
            style: TypographyTokens.labelSmall.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// 紧凑设施标签
  Widget _buildCompactFacilityChip() {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space1 + 2,
        vertical: SpacingTokens.space1 - 2,
      ),
      decoration: const BoxDecoration(
        color: ColorTokens.tertiaryContainer,
        borderRadius: ShapeTokens.borderRadiusXs,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.local_parking_rounded,
            size: 10,
            color: ColorTokens.onTertiaryContainer,
          ),
          const SizedBox(width: SpacingTokens.space1 - 4),
          Text(
            'P',
            style: TypographyTokens.labelSmall.copyWith(
              color: ColorTokens.onTertiaryContainer,
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// 紧凑鱼类标签列表（防止溢出）
  List<Widget> _buildCompactFishTypeChips(List<String> fishTypeNames) {
    final displayFishTypes = fishTypeNames.take(2).toList();
    final hasMore = fishTypeNames.length > 2;

    final List<Widget> chips = [];

    // 显示前两个鱼类
    for (int i = 0; i < displayFishTypes.length; i++) {
      if (i > 0) {
        chips.add(const SizedBox(width: SpacingTokens.space1));
      }
      chips.add(Flexible(
        child: _buildCompactFishTypeChip(displayFishTypes[i]),
      ));
    }

    // 如果还有更多，显示 +N
    if (hasMore) {
      chips
        ..add(const SizedBox(width: SpacingTokens.space1))
        ..add(_buildMoreFishTypeChip(fishTypeNames.length - 2));
    }

    return chips;
  }

  /// 单个鱼类标签
  Widget _buildCompactFishTypeChip(String fishType) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space1 + 2,
        vertical: SpacingTokens.space1 - 2,
      ),
      decoration: const BoxDecoration(
        color: ColorTokens.secondaryContainer,
        borderRadius: ShapeTokens.borderRadiusXs,
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(
            Icons.set_meal_rounded,
            size: 9,
            color: ColorTokens.onSecondaryContainer,
          ),
          const SizedBox(width: SpacingTokens.space1 - 4),
          Text(
            fishType,
            style: TypographyTokens.labelSmall.copyWith(
              color: ColorTokens.onSecondaryContainer,
              fontWeight: FontWeight.w500,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  /// 更多鱼类数量标签
  Widget _buildMoreFishTypeChip(int moreCount) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: SpacingTokens.space1 + 2,
        vertical: SpacingTokens.space1 - 2,
      ),
      decoration: const BoxDecoration(
        color: ColorTokens.surfaceVariant,
        borderRadius: ShapeTokens.borderRadiusXs,
      ),
      child: Text(
        '+$moreCount',
        style: TypographyTokens.labelSmall.copyWith(
          color: ColorTokens.onSurfaceVariant,
          fontWeight: FontWeight.w500,
          fontSize: 10,
        ),
      ),
    );
  }
}

class _Badge extends StatelessWidget {
  final IconData icon;
  final String label;
  final Gradient? gradient;
  final Color? backgroundColor;
  final Color? textColor;

  const _Badge({
    required this.icon,
    required this.label,
    this.gradient,
    this.backgroundColor,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
          horizontal: SpacingTokens.space3, vertical: SpacingTokens.space1 + 2),
      decoration: BoxDecoration(
        gradient: gradient,
        color: backgroundColor,
        borderRadius: ShapeTokens.borderRadiusLg,
        boxShadow: ElevationTokens.shadow1(context),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: textColor ?? ColorTokens.onPrimary,
          ),
          SpacingTokens.horizontalSpaceXs,
          Text(
            label,
            style: TypographyTokens.labelSmall.copyWith(
              color: textColor ?? ColorTokens.onPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}

// Helper function to navigate to spot moments
void _navigateToSpotMoments(BuildContext context, int spotId, String spotName) {
  // 使用新的模态管理系统
  context.openModal(
    child: DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) => SpotMomentsSection(
        fishingSpotId: spotId,
        fishingSpotName: spotName,
      ),
    ),
  );
}
