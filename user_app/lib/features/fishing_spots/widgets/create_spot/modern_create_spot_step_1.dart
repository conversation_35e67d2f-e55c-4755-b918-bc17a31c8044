import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/models/image/uploaded_image.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

/// 创建钓点第一步 - 图片上传和鱼种选择 (重构版本)
/// 
/// 这个组件将原来的大文件拆分为可重用的Design System组件，
/// 大幅减少了代码行数，提升了可维护性和复用性
class ModernImagesFishTypesStep extends StatelessWidget {
  final List<UploadedImage> spotImages;
  final List<FishType> availableFishTypes;
  final List<int> selectedFishTypeIds;
  final Future<void> Function() onAddImages;
  final void Function(int) onRemoveImage;
  final void Function(int) onToggleFishType;
  final void Function(String) onAddCustomFishType;
  final bool isLoadingFishTypes;
  
  // 可选配置参数
  final int maxImages;
  final String imagesSectionTitle;
  final String imagesSectionSubtitle;
  final String fishTypesSectionTitle;
  final String fishTypesSectionSubtitle;

  const ModernImagesFishTypesStep({
    super.key,
    required this.spotImages,
    required this.availableFishTypes,
    required this.selectedFishTypeIds,
    required this.onAddImages,
    required this.onRemoveImage,
    required this.onToggleFishType,
    required this.onAddCustomFishType,
    this.isLoadingFishTypes = false,
    this.maxImages = 9,
    this.imagesSectionTitle = '钓点图片',
    this.imagesSectionSubtitle = '上传真实的钓点照片，第一张为封面图',
    this.fishTypesSectionTitle = '可钓鱼种',
    this.fishTypesSectionSubtitle = '选择在此钓点可以钓到的鱼种',
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片上传部分
        DSImageUploadGrid(
          images: spotImages,
          onAddImages: onAddImages,
          onRemoveImage: onRemoveImage,
          maxImageCount: maxImages,
          title: imagesSectionTitle,
          subtitle: imagesSectionSubtitle,
          icon: Icons.photo_library_rounded,
        ),

        const SizedBox(height: SpacingTokens.space8),

        // 鱼种选择部分
        DSFishTypeSelector(
          availableFishTypes: availableFishTypes,
          selectedFishTypeIds: selectedFishTypeIds,
          onToggleFishType: onToggleFishType,
          onAddCustomFishType: onAddCustomFishType,
          isLoading: isLoadingFishTypes,
          title: fishTypesSectionTitle,
          subtitle: fishTypesSectionSubtitle,
          icon: Icons.set_meal_rounded,
        ),
      ],
    );
  }
}