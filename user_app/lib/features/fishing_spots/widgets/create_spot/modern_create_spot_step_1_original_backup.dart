import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/models/image/uploaded_image.dart';

class ModernImagesFishTypesStep extends StatelessWidget {
  final List<UploadedImage> spotImages;
  final List<FishType> availableFishTypes;
  final List<int> selectedFishTypeIds;
  final Future<void> Function() onAddImages;
  final void Function(int) onRemoveImage;
  final void Function(int) onToggleFishType;
  final void Function(String) onAddCustomFishType;
  final bool isLoadingFishTypes;

  const ModernImagesFishTypesStep({
    super.key,
    required this.spotImages,
    required this.availableFishTypes,
    required this.selectedFishTypeIds,
    required this.onAddImages,
    required this.onRemoveImage,
    required this.onToggleFishType,
    required this.onAddCustomFishType,
    required this.isLoadingFishTypes,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 图片上传部分
        _buildImageSection(context),

        const SizedBox(height: SpacingTokens.space8),

        // 鱼种选择部分
        _buildFishTypeSection(context),
      ],
    );
  }

  Widget _buildImageSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          context,
          title: '钓点图片',
          subtitle: '上传真实的钓点照片',
          icon: Icons.photo_library_rounded,
        ),

        SpacingTokens.verticalSpaceMd,

        // 图片网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount: spotImages.length + 1,
          itemBuilder: (context, index) {
            if (index == spotImages.length) {
              return _buildAddImageButton(context);
            }
            return _buildImageItem(context, index);
          },
        ),

        if (spotImages.isNotEmpty) ...[
          SpacingTokens.verticalSpaceSm,
          Center(
            child: Text(
              '${spotImages.length}/9 张图片',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildAddImageButton(BuildContext context) {
    return GestureDetector(
      onTap: spotImages.length < 9
          ? () {
              HapticFeedback.lightImpact();
              onAddImages();
            }
          : null,
      child: Container(
        decoration: BoxDecoration(
          color: spotImages.length < 9
              ? Theme.of(context)
                  .colorScheme
                  .primaryContainer
                  .withValues(alpha: 0.3)
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: ShapeTokens.borderRadiusLg,
          border: Border.all(
            color: spotImages.length < 9
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.5)
                : Theme.of(context).colorScheme.outline,
            width: 2,
            style: BorderStyle.solid,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_rounded,
              size: 32,
              color: spotImages.length < 9
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SpacingTokens.verticalSpaceSm,
            Text(
              spotImages.length < 9 ? '添加图片' : '已达上限',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: spotImages.length < 9
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w500,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageItem(BuildContext context, int index) {
    final uploadedImage = spotImages[index];
    // 使用文件路径和索引创建唯一键，确保每张图片都有独特标识
    final uniqueKey = 'image_${index}_${uploadedImage.file.path.hashCode}';

    return Stack(
      key: ValueKey(uniqueKey),
      children: [
        Container(
          decoration: BoxDecoration(
            borderRadius: ShapeTokens.borderRadiusLg,
            boxShadow: [
              BoxShadow(
                color:
                    Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
                blurRadius: SpacingTokens.space2,
                offset: const Offset(0, SpacingTokens.space1),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: ShapeTokens.borderRadiusLg,
            child: _buildImageContent(context, uploadedImage, uniqueKey),
          ),
        ),

        // 删除按钮
        Positioned(
          top: SpacingTokens.space2,
          right: SpacingTokens.space2,
          child: GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              onRemoveImage(index);
            },
            child: Container(
              padding: SpacingTokens.paddingXs,
              decoration: BoxDecoration(
                color:
                    Theme.of(context).colorScheme.error.withValues(alpha: 0.9),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context)
                        .colorScheme
                        .shadow
                        .withValues(alpha: 0.2),
                    blurRadius: SpacingTokens.space1,
                  ),
                ],
              ),
              child: Icon(
                Icons.close_rounded,
                size: 16,
                color: Theme.of(context).colorScheme.onError,
              ),
            ),
          ),
        ),

        // 主图标记
        if (index == 0)
          Positioned(
            bottom: SpacingTokens.space2,
            left: SpacingTokens.space2,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: SpacingTokens.space2,
                vertical: SpacingTokens.space1,
              ),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: ShapeTokens.borderRadiusSm,
              ),
              child: Text(
                '主图',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildFishTypeSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          context,
          title: '可钓鱼种',
          subtitle: '选择在此钓点可以钓到的鱼种',
          icon: Icons.set_meal_rounded,
        ),
        SpacingTokens.verticalSpaceMd,
        if (isLoadingFishTypes)
          const Center(
            child: CircularProgressIndicator(),
          )
        else if (availableFishTypes.isEmpty)
          _buildEmptyFishTypes(context)
        else
          _buildFishTypeGrid(),
      ],
    );
  }

  Widget _buildFishTypeGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: SpacingTokens.space3,
        mainAxisSpacing: SpacingTokens.space3,
        childAspectRatio: 2.5,
      ),
      itemCount: availableFishTypes.length + 1,
      // +1 for the add custom fish type button
      itemBuilder: (context, index) {
        if (index == availableFishTypes.length) {
          return _buildAddCustomFishTypeButton(context);
        }

        final fishType = availableFishTypes[index];
        final isSelected = selectedFishTypeIds.contains(fishType.id);

        return GestureDetector(
          onTap: () {
            HapticFeedback.selectionClick();
            onToggleFishType(fishType.id.toInt());
          },
          child: AnimatedContainer(
            duration: MotionTokens.durationShort4,
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surface,
              borderRadius: ShapeTokens.borderRadiusMd,
              border: Border.all(
                color: isSelected
                    ? Colors.transparent
                    : Theme.of(context).colorScheme.outline,
                width: 1.5,
              ),
              boxShadow: [
                BoxShadow(
                  color: isSelected
                      ? Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.3)
                      : Theme.of(context)
                          .colorScheme
                          .shadow
                          .withValues(alpha: 0.05),
                  blurRadius:
                      isSelected ? SpacingTokens.space3 : SpacingTokens.space2,
                  offset: const Offset(0, SpacingTokens.space1),
                ),
              ],
            ),
            child: Center(
              child: Text(
                fishType.name,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimary
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAddCustomFishTypeButton(BuildContext context) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _showAddCustomFishTypeDialog(context);
      },
      child: CustomPaint(
        painter: DashedBorderPainter(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
        child: Container(
          decoration: BoxDecoration(
            color: Theme.of(context)
                .colorScheme
                .primaryContainer
                .withValues(alpha: 0.3),
            borderRadius: ShapeTokens.borderRadiusMd,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.add_rounded,
                size: 20,
                color: Theme.of(context).colorScheme.primary,
              ),
              SpacingTokens.verticalSpaceXs,
              Text(
                '自定义',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w600,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showAddCustomFishTypeDialog(BuildContext context) {
    final TextEditingController controller = TextEditingController();

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: ShapeTokens.dialogShape,
          title: Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: ShapeTokens.borderRadiusSm,
                ),
                child: Icon(
                  Icons.set_meal_rounded,
                  color: Theme.of(context).colorScheme.onPrimary,
                  size: 20,
                ),
              ),
              SpacingTokens.horizontalSpaceSm,
              Text(
                '添加自定义鱼种',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '请输入鱼种名称',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
              SpacingTokens.verticalSpaceSm,
              TextField(
                controller: controller,
                autofocus: true,
                decoration: InputDecoration(
                  hintText: '例如：草鱼、鲤鱼、鲫鱼',
                  border: OutlineInputBorder(
                    borderRadius: ShapeTokens.borderRadiusMd,
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline,
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: ShapeTokens.borderRadiusMd,
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: SpacingTokens.space4,
                    vertical: SpacingTokens.space3,
                  ),
                ),
                maxLength: 20,
                textInputAction: TextInputAction.done,
                onSubmitted: (value) {
                  if (value.trim().isNotEmpty) {
                    Navigator.of(context).pop();
                    onAddCustomFishType(value.trim());
                  }
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                '取消',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary,
                borderRadius: ShapeTokens.borderRadiusSm,
              ),
              child: TextButton(
                onPressed: () {
                  final fishName = controller.text.trim();
                  if (fishName.isNotEmpty) {
                    Navigator.of(context).pop();
                    onAddCustomFishType(fishName);
                  }
                },
                child: Text(
                  '添加',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyFishTypes(BuildContext context) {
    return Container(
      padding: SpacingTokens.paddingXl,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        borderRadius: ShapeTokens.borderRadiusLg,
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.info_outline_rounded,
              size: 48,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SpacingTokens.verticalSpaceMd,
            Text(
              '暂无可选鱼种',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return Row(
      children: [
        Container(
          padding:
              const EdgeInsets.all(SpacingTokens.space2 + SpacingTokens.space1),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary,
            borderRadius: ShapeTokens.borderRadiusMd,
          ),
          child: Icon(
            icon,
            color: Theme.of(context).colorScheme.onPrimary,
            size: 24,
          ),
        ),
        SpacingTokens.horizontalSpaceMd,
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              Text(
                subtitle,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建图片内容显示
  Widget _buildImageContent(BuildContext context, UploadedImage uploadedImage,
      [String? keyPrefix]) {
    final baseKey = keyPrefix ?? 'img_${uploadedImage.file.path.hashCode}';
    // 如果有远程URL，使用CachedNetworkImage
    if (uploadedImage.url != null && uploadedImage.url!.startsWith('http')) {
      return CachedNetworkImage(
        key: ValueKey('${baseKey}_remote'),
        imageUrl: uploadedImage.url!,
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildLoadingPlaceholder(context),
        errorWidget: (context, url, error) => _buildErrorPlaceholder(context),
      );
    }

    // 对于本地文件，根据平台选择显示方式
    if (kIsWeb) {
      // Web平台：使用Image.memory从bytes加载
      return FutureBuilder<Uint8List>(
        key: ValueKey('${baseKey}_web'),
        future: uploadedImage.file.readAsBytes(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildLoadingPlaceholder(context);
          } else if (snapshot.hasError) {
            return _buildErrorPlaceholder(context);
          } else if (snapshot.hasData) {
            return Image.memory(
              key: ValueKey('${baseKey}_memory'),
              snapshot.data!,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              errorBuilder: (context, error, stackTrace) =>
                  _buildErrorPlaceholder(context),
            );
          } else {
            return _buildErrorPlaceholder(context);
          }
        },
      );
    } else {
      // 移动平台：使用Image.file
      return Image.file(
        key: ValueKey('${baseKey}_file'),
        File(uploadedImage.file.path),
        width: double.infinity,
        height: double.infinity,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) =>
            _buildErrorPlaceholder(context),
      );
    }
  }

  /// 构建加载占位符
  Widget _buildLoadingPlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Center(
        child: CircularProgressIndicator(
          strokeWidth: 2,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  /// 构建错误占位符
  Widget _buildErrorPlaceholder(BuildContext context) {
    return Container(
      color: Theme.of(context).colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.broken_image_rounded,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    );
  }
}

class DashedBorderPainter extends CustomPainter {
  final Color color;

  const DashedBorderPainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    const dashWidth = SpacingTokens.space1 + 1;
    const dashSpace = SpacingTokens.space1 - 1;
    // Create rounded rectangle path
    final path = Path()
      ..addRRect(RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(ShapeTokens.radiusMd),
      ));

    // Draw dashed path
    _drawDashedPath(canvas, path, paint, dashWidth, dashSpace);
  }

  void _drawDashedPath(Canvas canvas, Path path, Paint paint, double dashWidth,
      double dashSpace) {
    final pathMetrics = path.computeMetrics();
    for (final pathMetric in pathMetrics) {
      double distance = 0.0;
      while (distance < pathMetric.length) {
        final nextDistance = distance + dashWidth;
        final segment = pathMetric.extractPath(
          distance,
          nextDistance > pathMetric.length ? pathMetric.length : nextDistance,
        );
        canvas.drawPath(segment, paint);
        distance = nextDistance + dashSpace;
      }
    }
  }

  @override
  bool shouldRepaint(DashedBorderPainter oldDelegate) =>
      oldDelegate.color != color;
}
