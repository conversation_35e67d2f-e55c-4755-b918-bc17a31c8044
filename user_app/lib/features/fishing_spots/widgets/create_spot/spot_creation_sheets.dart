import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/create_spot_models.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

/// 添加价格表单组件
class AddPriceSheet extends StatefulWidget {
  final List<FishType> availableFishTypes;
  final Function(String, double, int?, FishType?, String?) onAdd;

  const AddPriceSheet({
    Key? key,
    required this.availableFishTypes,
    required this.onAdd,
  }) : super(key: key);

  @override
  State<AddPriceSheet> createState() => _AddPriceSheetState();
}

class _AddPriceSheetState extends State<AddPriceSheet> {
  final _priceController = TextEditingController();
  final _hoursController = TextEditingController();
  final _descriptionController = TextEditingController();

  String _selectedPriceType = '天票';
  FishType? _selectedFishType;

  final List<String> _priceTypes = ['天票', '夜钓', '小时', '斤钓'];

  @override
  void dispose() {
    _priceController.dispose();
    _hoursController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 拖动指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: ColorTokens.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // 标题栏
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '添加收费方式',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          const Divider(),
          // 表单内容
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 收费类型选择
                  Text(
                    '收费类型',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _priceTypes.map((type) {
                      final isSelected = _selectedPriceType == type;
                      return ChoiceChip(
                        label: Text(type),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedPriceType = type;
                              // 清空特定字段
                              if (type != '小时') {
                                _hoursController.clear();
                              }
                              if (type != '斤钓') {
                                _selectedFishType = null;
                              }
                            });
                          }
                        },
                        selectedColor: Theme.of(context).colorScheme.primary,
                        labelStyle: TextStyle(
                          color: isSelected ? Theme.of(context).colorScheme.onPrimary : null,
                          fontWeight: isSelected ? FontWeight.w600 : null,
                        ),
                      );
                    }).toList(),
                  ),

                  const SizedBox(height: SpacingTokens.space6),

                  // 价格输入
                  TextField(
                    controller: _priceController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: '价格（元）',
                      prefixText: '¥ ',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                    ),
                  ),

                  // 小时钓特有字段
                  if (_selectedPriceType == '小时') ...[
                    const SizedBox(height: SpacingTokens.space4),
                    TextField(
                      controller: _hoursController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: '时长（小时）',
                        hintText: '请输入钓鱼时长',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 14,
                        ),
                      ),
                    ),
                  ],

                  // 斤钓特有字段
                  if (_selectedPriceType == '斤钓') ...[
                    const SizedBox(height: SpacingTokens.space4),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: ColorTokens.outline),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButtonFormField<FishType>(
                        value: _selectedFishType,
                        decoration: const InputDecoration(
                          labelText: '鱼种',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 14,
                          ),
                        ),
                        items: widget.availableFishTypes
                            .map<DropdownMenuItem<FishType>>((fishType) {
                          return DropdownMenuItem<FishType>(
                            value: fishType,
                            child: Text(fishType.name ?? ''),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedFishType = value;
                          });
                        },
                      ),
                    ),
                  ],

                  const SizedBox(height: SpacingTokens.space4),

                  // 备注说明
                  TextField(
                    controller: _descriptionController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: '备注说明（可选）',
                      hintText: '如：包含午餐、可垂钓时间段等',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // 添加按钮
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _handleAdd,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('添加收费方式'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleAdd() {
    final priceText = _priceController.text.trim();
    if (priceText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入价格')),
      );
      return;
    }

    final price = double.tryParse(priceText);
    if (price == null || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的价格')),
      );
      return;
    }

    // 验证小时钓的时长
    int? hours;
    if (_selectedPriceType == '小时') {
      final hoursText = _hoursController.text.trim();
      if (hoursText.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请输入时长')),
        );
        return;
      }
      hours = int.tryParse(hoursText);
      if (hours == null || hours <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请输入有效的时长')),
        );
        return;
      }
    }

    // 验证斤钓的鱼种
    if (_selectedPriceType == '斤钓' && _selectedFishType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择鱼种')),
      );
      return;
    }

    widget.onAdd(
      _selectedPriceType,
      price,
      hours,
      _selectedFishType,
      _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
    );
    Navigator.pop(context);
  }
}

/// 编辑价格表单组件
class EditPriceSheet extends StatefulWidget {
  final SpotPrice price;
  final List<FishType> availableFishTypes;
  final Function(String, double, int?, FishType?, String?) onUpdate;

  const EditPriceSheet({
    Key? key,
    required this.price,
    required this.availableFishTypes,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<EditPriceSheet> createState() => _EditPriceSheetState();
}

class _EditPriceSheetState extends State<EditPriceSheet> {
  late final TextEditingController _priceController;
  late final TextEditingController _hoursController;
  late final TextEditingController _descriptionController;

  late String _selectedPriceType;
  FishType? _selectedFishType;

  final List<String> _priceTypes = ['天票', '夜钓', '小时', '斤钓'];

  @override
  void initState() {
    super.initState();
    _priceController = TextEditingController(text: widget.price.price?.toString() ?? '');
    _hoursController = TextEditingController(text: widget.price.hours?.toString() ?? '');
    _descriptionController = TextEditingController(text: widget.price.description);
    _selectedPriceType = widget.price.priceTypeName;
    _selectedFishType = widget.availableFishTypes
        .where((f) => f.id == widget.price.fishTypeId)
        .firstOrNull;
  }

  @override
  void dispose() {
    _priceController.dispose();
    _hoursController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: ColorTokens.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '编辑价格',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          const Divider(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '收费类型',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children: _priceTypes.map((type) {
                      final isSelected = _selectedPriceType == type;
                      return ChoiceChip(
                        label: Text(type),
                        selected: isSelected,
                        onSelected: (selected) {
                          if (selected) {
                            setState(() {
                              _selectedPriceType = type;
                              if (type != '小时') {
                                _hoursController.clear();
                              }
                              if (type != '斤钓') {
                                _selectedFishType = null;
                              }
                            });
                          }
                        },
                        selectedColor: Theme.of(context).colorScheme.primary,
                        labelStyle: TextStyle(
                          color: isSelected ? Theme.of(context).colorScheme.onPrimary : null,
                          fontWeight: isSelected ? FontWeight.w600 : null,
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: SpacingTokens.space6),
                  TextField(
                    controller: _priceController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: '价格（元）',
                      prefixText: '¥ ',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                    ),
                  ),
                  if (_selectedPriceType == '小时') ...[
                    const SizedBox(height: SpacingTokens.space4),
                    TextField(
                      controller: _hoursController,
                      keyboardType: TextInputType.number,
                      decoration: InputDecoration(
                        labelText: '时长（小时）',
                        hintText: '请输入钓鱼时长',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        contentPadding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 14,
                        ),
                      ),
                    ),
                  ],
                  if (_selectedPriceType == '斤钓') ...[
                    const SizedBox(height: SpacingTokens.space4),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: ColorTokens.outline),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: DropdownButtonFormField<FishType>(
                        value: _selectedFishType,
                        decoration: const InputDecoration(
                          labelText: '鱼种',
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 14,
                          ),
                        ),
                        items: widget.availableFishTypes
                            .map<DropdownMenuItem<FishType>>((fishType) {
                          return DropdownMenuItem<FishType>(
                            value: fishType,
                            child: Text(fishType.name ?? ''),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _selectedFishType = value;
                          });
                        },
                      ),
                    ),
                  ],
                  const SizedBox(height: SpacingTokens.space4),
                  TextField(
                    controller: _descriptionController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: '备注说明（可选）',
                      hintText: '如：包含午餐、可垂钓时间段等',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _handleUpdate,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('更新价格'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleUpdate() {
    final priceText = _priceController.text.trim();
    if (priceText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入价格')),
      );
      return;
    }

    final price = double.tryParse(priceText);
    if (price == null || price <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入有效的价格')),
      );
      return;
    }

    int? hours;
    if (_selectedPriceType == '小时') {
      final hoursText = _hoursController.text.trim();
      if (hoursText.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请输入时长')),
        );
        return;
      }
      hours = int.tryParse(hoursText);
      if (hours == null || hours <= 0) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('请输入有效的时长')),
        );
        return;
      }
    }

    if (_selectedPriceType == '斤钓' && _selectedFishType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请选择鱼种')),
      );
      return;
    }

    widget.onUpdate(
      _selectedPriceType,
      price,
      hours,
      _selectedFishType,
      _descriptionController.text.trim().isEmpty
          ? null
          : _descriptionController.text.trim(),
    );
    Navigator.pop(context);
  }
}

/// 设施编辑组件
class EditFacilitySheet extends StatefulWidget {
  final SpotFacility facility;
  final Function(String, String, String, String) onUpdate;

  const EditFacilitySheet({
    Key? key,
    required this.facility,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<EditFacilitySheet> createState() => _EditFacilitySheetState();
}

class _EditFacilitySheetState extends State<EditFacilitySheet> {
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;

  late String _selectedIcon;
  final List<Map<String, String>> _iconOptions = [
    {'name': 'parking', 'label': '停车场', 'icon': 'local_parking'},
    {'name': 'restroom', 'label': '洗手间', 'icon': 'wc'},
    {'name': 'grill', 'label': '烧烤架', 'icon': 'outdoor_grill'},
    {'name': 'boat', 'label': '租船', 'icon': 'directions_boat'},
    {'name': 'store', 'label': '小卖部', 'icon': 'store'},
    {'name': 'restaurant', 'label': '餐厅', 'icon': 'restaurant'},
  ];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.facility.name);
    _descriptionController = TextEditingController(text: widget.facility.description);
    _selectedIcon = widget.facility.icon;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // 拖动指示器
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: ColorTokens.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          // 标题栏
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '编辑设施',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          const Divider(),
          // 表单内容
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 设施名称
                  TextField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: '设施名称',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                    ),
                  ),
                  const SizedBox(height: SpacingTokens.space4),

                  // 图标选择
                  Text(
                    '选择图标',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 12,
                    runSpacing: 12,
                    children: _iconOptions.map((option) {
                      final isSelected = _selectedIcon == option['name'];
                      return GestureDetector(
                        onTap: () {
                          setState(() {
                            _selectedIcon = option['name']!;
                          });
                        },
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                                : ColorTokens.surfaceVariant,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: isSelected 
                                  ? Theme.of(context).colorScheme.primary
                                  : ColorTokens.outline,
                              width: isSelected ? 2 : 1,
                            ),
                          ),
                          child: Column(
                            children: [
                              Icon(
                                _getIconData(option['icon']!),
                                color: isSelected 
                                    ? Theme.of(context).colorScheme.primary
                                    : ColorTokens.onSurfaceVariant,
                                size: 24,
                              ),
                              const SizedBox(height: 4),
                              Text(
                                option['label']!,
                                style: TextStyle(
                                  fontSize: 12,
                                  color: isSelected 
                                      ? Theme.of(context).colorScheme.primary
                                      : ColorTokens.onSurfaceVariant,
                                  fontWeight: isSelected ? FontWeight.w600 : null,
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                  const SizedBox(height: SpacingTokens.space4),

                  // 设施描述
                  TextField(
                    controller: _descriptionController,
                    maxLines: 3,
                    decoration: InputDecoration(
                      labelText: '设施描述（可选）',
                      hintText: '详细描述设施的特点和使用说明',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.all(16),
                    ),
                  ),

                  const SizedBox(height: 32),

                  // 更新按钮
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _handleUpdate,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('更新设施'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'local_parking':
        return Icons.local_parking;
      case 'wc':
        return Icons.wc;
      case 'outdoor_grill':
        return Icons.outdoor_grill;
      case 'directions_boat':
        return Icons.directions_boat;
      case 'store':
        return Icons.store;
      case 'restaurant':
        return Icons.restaurant;
      default:
        return Icons.build_circle;
    }
  }

  void _handleUpdate() {
    final name = _nameController.text.trim();
    if (name.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('请输入设施名称')),
      );
      return;
    }

    widget.onUpdate(
      name,
      _selectedIcon,
      _descriptionController.text.trim(),
      '', // No id field available in SpotFacility
    );
    Navigator.pop(context);
  }
}

/// 批量价格编辑组件
class BulkPriceEditSheet extends StatefulWidget {
  final List<SpotPrice> prices;
  final VoidCallback onUpdate;

  const BulkPriceEditSheet({
    Key? key,
    required this.prices,
    required this.onUpdate,
  }) : super(key: key);

  @override
  State<BulkPriceEditSheet> createState() => _BulkPriceEditSheetState();
}

class _BulkPriceEditSheetState extends State<BulkPriceEditSheet> {
  final TextEditingController _percentageController = TextEditingController();
  String _adjustmentType = 'percentage'; // percentage, fixed
  String _operation = 'increase'; // increase, decrease

  @override
  void dispose() {
    _percentageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.6,
      decoration: const BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: ColorTokens.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '批量调整价格',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
          ),
          const Divider(),
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '调整方式',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('按百分比'),
                          value: 'percentage',
                          groupValue: _adjustmentType,
                          onChanged: (value) {
                            setState(() {
                              _adjustmentType = value!;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('按固定金额'),
                          value: 'fixed',
                          groupValue: _adjustmentType,
                          onChanged: (value) {
                            setState(() {
                              _adjustmentType = value!;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: SpacingTokens.space6),
                  Text(
                    '操作类型',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('增加'),
                          value: 'increase',
                          groupValue: _operation,
                          onChanged: (value) {
                            setState(() {
                              _operation = value!;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                      Expanded(
                        child: RadioListTile<String>(
                          title: const Text('减少'),
                          value: 'decrease',
                          groupValue: _operation,
                          onChanged: (value) {
                            setState(() {
                              _operation = value!;
                            });
                          },
                          contentPadding: EdgeInsets.zero,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: SpacingTokens.space6),
                  TextField(
                    controller: _percentageController,
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      labelText: _adjustmentType == 'percentage' ? '百分比' : '金额（元）',
                      suffixText: _adjustmentType == 'percentage' ? '%' : '元',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                    ),
                  ),
                  const SizedBox(height: SpacingTokens.space6),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: ColorTokens.info.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline,
                                color: ColorTokens.info, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              '预览调整结果',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                color: ColorTokens.info,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 12),
                        ...widget.prices.take(3).map((price) {
                          final preview = _calculateNewPrice(price.price?.toDouble() ?? 0);
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 4),
                            child: Text(
                              '${price.priceTypeName}: ¥${price.price?.toStringAsFixed(0)} → ¥${preview.toStringAsFixed(0)}',
                              style: TextStyle(
                                fontSize: 13,
                                color: ColorTokens.info,
                              ),
                            ),
                          );
                        }),
                        if (widget.prices.length > 3)
                          Text(
                            '还有 ${widget.prices.length - 3} 个价格将被调整...',
                            style: TextStyle(
                              fontSize: 13,
                              color: ColorTokens.info,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 32),
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: ElevatedButton(
                      onPressed: _handleBulkUpdate,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text('应用调整'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  double _calculateNewPrice(double originalPrice) {
    final valueText = _percentageController.text.trim();
    if (valueText.isEmpty) return originalPrice;

    final value = double.tryParse(valueText) ?? 0;

    if (_adjustmentType == 'percentage') {
      final multiplier = _operation == 'increase' ? (100 + value) / 100 : (100 - value) / 100;
      return originalPrice * multiplier;
    } else {
      return _operation == 'increase' ? originalPrice + value : originalPrice - value;
    }
  }

  void _handleBulkUpdate() {
    final valueText = _percentageController.text.trim();
    if (valueText.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请输入${_adjustmentType == 'percentage' ? '百分比' : '金额'}')),
      );
      return;
    }

    final value = double.tryParse(valueText);
    if (value == null || value <= 0) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('请输入有效的${_adjustmentType == 'percentage' ? '百分比' : '金额'}')),
      );
      return;
    }

    // 批量更新价格
    for (final price in widget.prices) {
      final originalPrice = price.price?.toDouble() ?? 0;
      final newPrice = _calculateNewPrice(originalPrice);

      // 确保价格不小于1元
      price.price = newPrice < 1 ? 1 : newPrice;
    }

    widget.onUpdate();
  }
}