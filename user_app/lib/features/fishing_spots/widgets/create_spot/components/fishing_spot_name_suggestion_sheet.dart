import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

/// 钓点名称建议底部弹窗
/// 
/// 显示智能推荐的钓点名称列表，支持搜索和自定义添加
class FishingSpotNameSuggestionSheet extends StatefulWidget {
  final List<String> suggestions;
  final void Function(String) onNameSelected;
  final String? currentName;

  const FishingSpotNameSuggestionSheet({
    super.key,
    required this.suggestions,
    required this.onNameSelected,
    this.currentName,
  });

  @override
  State<FishingSpotNameSuggestionSheet> createState() => 
      _FishingSpotNameSuggestionSheetState();
}

class _FishingSpotNameSuggestionSheetState 
    extends State<FishingSpotNameSuggestionSheet> {
  late List<String> filteredSuggestions;
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    filteredSuggestions = List.from(widget.suggestions);
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }

  void _filterSuggestions(String query) {
    setState(() {
      if (query.isEmpty) {
        filteredSuggestions = List.from(widget.suggestions);
      } else {
        filteredSuggestions = widget.suggestions
            .where((name) => name.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return DSBottomSheet(
      title: '智能推荐名称',
      titleIcon: Icons.auto_awesome_rounded,
      height: MediaQuery.of(context).size.height * 0.8,
      children: [
        _buildSearchField(),
        const SizedBox(height: SpacingTokens.space4),
        _buildSuggestionsList(),
      ],
    );
  }

  Widget _buildSearchField() {
    return DSSearchField(
      controller: searchController,
      hintText: '搜索推荐名称...',
      onChanged: _filterSuggestions,
      onClear: () {
        searchController.clear();
        _filterSuggestions('');
      },
    );
  }

  Widget _buildSuggestionsList() {
    if (filteredSuggestions.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // 推荐类别标题
        _buildCategoryHeader('基于位置推荐'),
        
        // 推荐列表
        ...filteredSuggestions.asMap().entries.map((entry) {
          final index = entry.key;
          final suggestion = entry.value;
          return _buildSuggestionTile(
            suggestion,
            index < 3 ? '热门推荐' : '智能推荐',
            index < 3 ? Icons.local_fire_department_rounded : Icons.lightbulb_rounded,
          );
        }).toList(),
        
        const SizedBox(height: SpacingTokens.space6),
        
        // 自定义输入提示
        _buildCustomInputHint(),
      ],
    );
  }

  Widget _buildCategoryHeader(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(
        vertical: SpacingTokens.space2,
        horizontal: SpacingTokens.space1,
      ),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildSuggestionTile(String name, String category, IconData categoryIcon) {
    final isSelected = name == widget.currentName;
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: SpacingTokens.space3),
      decoration: BoxDecoration(
        color: isSelected 
            ? theme.colorScheme.primaryContainer.withValues(alpha: 0.5)
            : theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: isSelected
              ? theme.colorScheme.primary.withValues(alpha: 0.5)
              : theme.colorScheme.outline.withValues(alpha: 0.2),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            widget.onNameSelected(name);
            Navigator.pop(context);
          },
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space4),
            child: Row(
              children: [
                // 前置图标容器
                Container(
                  padding: const EdgeInsets.all(SpacingTokens.space2),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? theme.colorScheme.primary
                        : theme.colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(SpacingTokens.space2),
                  ),
                  child: Icon(
                    categoryIcon,
                    size: SpacingTokens.space4,
                    color: isSelected
                        ? theme.colorScheme.onPrimary
                        : theme.colorScheme.onPrimaryContainer,
                  ),
                ),
                
                const SizedBox(width: SpacingTokens.space3),
                
                // 名称和类别信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected 
                              ? theme.colorScheme.primary
                              : theme.colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: SpacingTokens.space1),
                      Row(
                        children: [
                          Icon(
                            Icons.category_rounded,
                            size: SpacingTokens.space3,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: SpacingTokens.space1),
                          Text(
                            category,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 选中状态图标
                if (isSelected)
                  Icon(
                    Icons.check_circle_rounded,
                    color: theme.colorScheme.primary,
                    size: SpacingTokens.space5,
                  )
                else
                  Icon(
                    Icons.arrow_forward_ios_rounded,
                    size: SpacingTokens.space4,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        children: [
          Icon(
            Icons.search_off_rounded,
            size: SpacingTokens.space12,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.6),
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            '没有找到匹配的推荐',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            '尝试修改搜索关键词或使用自定义名称',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCustomInputHint() {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline_rounded,
            color: theme.colorScheme.primary,
            size: SpacingTokens.space5,
          ),
          const SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '没有满意的推荐？',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: SpacingTokens.space1),
                Text(
                  '您可以关闭此面板，在名称输入框中输入自定义名称',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 显示钓点名称建议弹窗的便捷方法
Future<String?> showFishingSpotNameSuggestions({
  required BuildContext context,
  required List<String> suggestions,
  String? currentName,
}) {
  return showDSBottomSheet<String>(
    context: context,
    builder: (context) => FishingSpotNameSuggestionSheet(
      suggestions: suggestions,
      currentName: currentName,
      onNameSelected: (name) => Navigator.pop(context, name),
    ),
  );
}