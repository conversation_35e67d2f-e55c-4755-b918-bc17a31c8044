import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

/// 钓点位置选择卡片组件
/// 
/// 用于显示和选择钓点位置信息，支持坐标显示和地图交互
class FishingSpotLocationCard extends StatelessWidget {
  final double latitude;
  final double longitude;
  final String formattedAddress;
  final bool useCurrentLocation;
  final VoidCallback onShowMapModal;

  const FishingSpotLocationCard({
    super.key,
    required this.latitude,
    required this.longitude,
    required this.formattedAddress,
    required this.useCurrentLocation,
    required this.onShowMapModal,
  });

  bool get hasLocation => latitude != 0 && longitude != 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: hasLocation
            ? theme.colorScheme.primaryContainer
            : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space5),
        border: Border.all(
          color: hasLocation
              ? theme.colorScheme.primary.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: hasLocation
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: SpacingTokens.space5,
            offset: const Offset(0, SpacingTokens.space2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            onShowMapModal();
          },
          borderRadius: BorderRadius.circular(SpacingTokens.space5),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space5),
            child: Column(
              children: [
                _buildLocationHeader(context),
                if (hasLocation) ...[
                  const SizedBox(height: SpacingTokens.space4),
                  _buildCoordinateDisplay(context),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLocationHeader(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        // 位置图标
        Container(
          width: SpacingTokens.space12 + SpacingTokens.space2,
          height: SpacingTokens.space12 + SpacingTokens.space2,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(SpacingTokens.space4),
            boxShadow: [
              BoxShadow(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: SpacingTokens.space3,
                offset: const Offset(0, SpacingTokens.space1),
              ),
            ],
          ),
          child: Icon(
            Icons.location_on_rounded,
            color: theme.colorScheme.onPrimary,
            size: SpacingTokens.space6 + SpacingTokens.space1,
          ),
        ),
        
        const SizedBox(width: SpacingTokens.space4),
        
        // 位置信息
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                hasLocation ? '已选择位置' : '选择钓点位置',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: SpacingTokens.space1),
              Text(
                hasLocation ? formattedAddress : '点击选择钓点的准确位置',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
        
        // 箭头图标
        Icon(
          Icons.arrow_forward_ios_rounded,
          color: theme.colorScheme.onSurfaceVariant,
          size: SpacingTokens.space5,
        ),
      ],
    );
  }

  Widget _buildCoordinateDisplay(BuildContext context) {
    return DSCoordinateDisplay(
      latitude: latitude,
      longitude: longitude,
      precision: 6,
      padding: const EdgeInsets.all(SpacingTokens.space3),
    );
  }
}

/// 紧凑版位置选择卡片
class CompactFishingSpotLocationCard extends StatelessWidget {
  final double latitude;
  final double longitude;
  final String formattedAddress;
  final VoidCallback onShowMapModal;
  final EdgeInsetsGeometry? padding;

  const CompactFishingSpotLocationCard({
    super.key,
    required this.latitude,
    required this.longitude,
    required this.formattedAddress,
    required this.onShowMapModal,
    this.padding,
  });

  bool get hasLocation => latitude != 0 && longitude != 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: padding ?? const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            onShowMapModal();
          },
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Row(
            children: [
              Icon(
                Icons.location_on_rounded,
                color: hasLocation 
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant,
                size: SpacingTokens.space5,
              ),
              
              const SizedBox(width: SpacingTokens.space3),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      hasLocation ? '位置已选择' : '选择位置',
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                    if (hasLocation) ...[
                      const SizedBox(height: SpacingTokens.space1),
                      Text(
                        formattedAddress,
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              Icon(
                Icons.chevron_right_rounded,
                color: theme.colorScheme.onSurfaceVariant,
                size: SpacingTokens.space5,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 位置状态指示器
class LocationStatusIndicator extends StatelessWidget {
  final double latitude;
  final double longitude;
  final bool useCurrentLocation;

  const LocationStatusIndicator({
    super.key,
    required this.latitude,
    required this.longitude,
    required this.useCurrentLocation,
  });

  bool get hasLocation => latitude != 0 && longitude != 0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (!hasLocation) {
      return DSStatusDisplay(
        status: '未选择位置',
        statusColor: theme.colorScheme.error,
        statusIcon: Icons.location_off_rounded,
        description: '请选择钓点位置',
      );
    }

    return DSStatusDisplay(
      status: useCurrentLocation ? '当前位置' : '自定义位置',
      statusColor: theme.colorScheme.primary,
      statusIcon: useCurrentLocation 
          ? Icons.my_location_rounded 
          : Icons.place_rounded,
      description: '位置已确认',
    );
  }
}