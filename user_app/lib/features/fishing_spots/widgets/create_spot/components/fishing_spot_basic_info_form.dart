import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

/// 钓点基本信息表单组件
/// 
/// 包含钓点名称、详细地址、描述等基本信息的输入表单
class FishingSpotBasicInfoForm extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController addressController;
  final TextEditingController descriptionController;
  final String Function() getSuggestedName;
  final List<String> Function() getSuggestedNames;
  final VoidCallback? onNameSuggestionTap;

  const FishingSpotBasicInfoForm({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.addressController,
    required this.descriptionController,
    required this.getSuggestedName,
    required this.getSuggestedNames,
    this.onNameSuggestionTap,
  });

  @override
  Widget build(BuildContext context) {
    return Form(
      key: form<PERSON><PERSON>,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基本信息标题
          DSSectionTitle(
            title: '基本信息',
            decoratorColor: Theme.of(context).colorScheme.primary,
          ),
          
          const SizedBox(height: SpacingTokens.space4),

          // 钓点名称输入框
          DSTextField(
            controller: nameController,
            label: '钓点名称',
            hintText: '给钓点起个响亮的名字',
            prefixIcon: Icons.edit_rounded,
            suffixIcon: IconButton(
              icon: const Icon(Icons.auto_awesome_rounded),
              tooltip: '智能推荐',
              onPressed: onNameSuggestionTap ?? 
                  () => _showNameSuggestions(context),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入钓点名称';
              }
              if (value.length < 2) {
                return '钓点名称至少需要2个字符';
              }
              if (value.length > 50) {
                return '钓点名称不能超过50个字符';
              }
              return null;
            },
          ),

          const SizedBox(height: SpacingTokens.space5),

          // 详细地址输入框
          DSTextArea(
            controller: addressController,
            label: '详细地址',
            hintText: '补充详细的地址信息',
            minLines: 2,
            maxLines: 3,
            maxLength: 200,
            showCounter: true,
          ),

          const SizedBox(height: SpacingTokens.space5),

          // 钓点描述输入框
          DSTextArea(
            controller: descriptionController,
            label: '钓点描述',
            hintText: '介绍一下这个钓点的特色...',
            minLines: 3,
            maxLines: 6,
            maxLength: 500,
            showCounter: true,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return '请输入钓点描述';
              }
              if (value.length < 10) {
                return '描述至少需要10个字符';
              }
              return null;
            },
          ),
        ],
      ),
    );
  }

  /// 显示名称推荐选项
  void _showNameSuggestions(BuildContext context) {
    final suggestions = getSuggestedNames();
    if (suggestions.isEmpty) return;

    showDSBottomSheet(
      context: context,
      builder: (context) => DSBottomSheet(
        title: '智能推荐名称',
        titleIcon: Icons.auto_awesome_rounded,
        children: [
          ...suggestions.map((suggestion) => 
            FishingSpotNameSuggestionTile(
              name: suggestion,
              description: '基于当前位置智能推荐',
              onTap: () {
                nameController.text = suggestion;
                Navigator.pop(context);
              },
            ),
          ).toList(),
        ],
      ),
    );
  }
}

/// 钓点名称建议瓦片组件
class FishingSpotNameSuggestionTile extends StatelessWidget {
  final String name;
  final String? description;
  final VoidCallback onTap;
  final IconData? leadingIcon;

  const FishingSpotNameSuggestionTile({
    super.key,
    required this.name,
    this.description,
    required this.onTap,
    this.leadingIcon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: SpacingTokens.space2),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space4),
            child: Row(
              children: [
                // 前置图标
                CircleAvatar(
                  radius: SpacingTokens.space4,
                  backgroundColor: theme.colorScheme.primaryContainer,
                  child: Icon(
                    leadingIcon ?? Icons.location_on_rounded,
                    color: theme.colorScheme.onPrimaryContainer,
                    size: SpacingTokens.space4,
                  ),
                ),
                
                const SizedBox(width: SpacingTokens.space3),
                
                // 名称和描述
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        name,
                        style: theme.textTheme.bodyLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (description != null) ...[
                        const SizedBox(height: SpacingTokens.space1),
                        Text(
                          description!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                
                // 尾随箭头
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: SpacingTokens.space4,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 表单验证助手类
class FishingSpotFormValidator {
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入钓点名称';
    }
    if (value.length < 2) {
      return '钓点名称至少需要2个字符';
    }
    if (value.length > 50) {
      return '钓点名称不能超过50个字符';
    }
    // 检查是否包含特殊字符
    final RegExp nameRegex = RegExp(r'^[a-zA-Z0-9\u4e00-\u9fa5\s\-_（）()]+$');
    if (!nameRegex.hasMatch(value)) {
      return '钓点名称包含不允许的字符';
    }
    return null;
  }

  static String? validateAddress(String? value) {
    if (value != null && value.isNotEmpty) {
      if (value.length > 200) {
        return '地址不能超过200个字符';
      }
    }
    return null;
  }

  static String? validateDescription(String? value) {
    if (value == null || value.isEmpty) {
      return '请输入钓点描述';
    }
    if (value.length < 10) {
      return '描述至少需要10个字符';
    }
    if (value.length > 500) {
      return '描述不能超过500个字符';
    }
    return null;
  }

  /// 验证整个表单
  static bool validateForm(GlobalKey<FormState> formKey) {
    return formKey.currentState?.validate() ?? false;
  }

  /// 保存表单数据
  static void saveForm(GlobalKey<FormState> formKey) {
    formKey.currentState?.save();
  }
}