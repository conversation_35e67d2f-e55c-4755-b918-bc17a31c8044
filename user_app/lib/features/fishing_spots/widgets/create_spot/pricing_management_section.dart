import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/create_spot_models.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';

/// 价格管理相关的Widget组件
class PricingManagementSection extends StatelessWidget {
  final bool isPaid;
  final List<SpotPrice> prices;
  final TextEditingController priceController;
  final List<FishType> availableFishTypes;
  final ValueChanged<bool> onIsPaidChanged;
  final void Function({
    required int priceTypeId,
    required String priceTypeName,
    double? price,
    int? hours,
    int? fishTypeId,
    String? fishTypeName,
    String? description,
  }) onAddPrice;
  final void Function(int index) onRemovePrice;
  final void Function(int index) onEditPrice;
  final void Function(int index) onDuplicatePrice;

  const PricingManagementSection({
    Key? key,
    required this.isPaid,
    required this.prices,
    required this.priceController,
    required this.availableFishTypes,
    required this.onIsPaidChanged,
    required this.onAddPrice,
    required this.onRemovePrice,
    required this.onEditPrice,
    required this.onDuplicatePrice,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          title: '收费设置',
          subtitle: '设置钓点的收费标准和价格',
          icon: Icons.attach_money,
          context: context,
        ),
        const SizedBox(height: SpacingTokens.space4),
        _buildPaidToggle(context),
        if (isPaid) ...[
          const SizedBox(height: SpacingTokens.space5),
          _buildPriceList(),
          const SizedBox(height: SpacingTokens.space4),
          _buildAddPriceButton(),
          const SizedBox(height: SpacingTokens.space4),
          _buildPriceSummary(),
        ],
      ],
    );
  }

  Widget _buildSectionHeader({
    required String title,
    required String subtitle,
    required IconData icon,
    required BuildContext context,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
            Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondary,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.onSecondary,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaidToggle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isPaid 
                  ? Theme.of(context).colorScheme.secondary 
                  : Theme.of(context).colorScheme.surfaceVariant,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              isPaid ? Icons.paid : Icons.money_off,
              color: isPaid 
                  ? Theme.of(context).colorScheme.onSecondary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '收费钓点',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isPaid ? '此钓点需要付费使用' : '免费开放钓点',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: isPaid,
            onChanged: onIsPaidChanged,
            activeColor: Theme.of(context).colorScheme.secondary,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceList() {
    if (prices.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: ColorTokens.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: ColorTokens.outline,
            style: BorderStyle.solid,
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              Icons.price_change,
              size: 48,
              color: ColorTokens.onSurfaceVariant,
            ),
            const SizedBox(height: SpacingTokens.space3),
            Text(
              '暂无价格设置',
              style: TextStyle(
                fontSize: 16,
                color: ColorTokens.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: SpacingTokens.space2),
            Text(
              '添加价格信息，让钓友了解收费标准',
              style: TextStyle(
                color: ColorTokens.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(Icons.price_check, color: ColorTokens.secondary),
                const SizedBox(width: 8),
                Text(
                  '价格列表 (${prices.length})',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ColorTokens.onSurface,
                  ),
                ),
                const Spacer(),
                TextButton.icon(
                  onPressed: _showPriceTemplates,
                  icon: const Icon(Icons.template_add, size: 16),
                  label: const Text('模板'),
                  style: TextButton.styleFrom(
                    foregroundColor: ColorTokens.secondary,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: prices.length,
            itemBuilder: (context, index) {
              final price = prices[index];
              return _buildPriceListItem(price, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildPriceListItem(SpotPrice price, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: ColorTokens.secondaryContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: ColorTokens.secondary,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getPriceTypeIcon(price.priceTypeName),
            color: ColorTokens.onSecondary,
            size: 20,
          ),
        ),
        title: Text(
          price.priceTypeName,
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('¥${price.price?.toStringAsFixed(2) ?? '0.00'}'),
            if (price.hours != null) 
              Text('${price.hours}小时'),
            if (price.fishTypeName != null)
              Text('鱼种: ${price.fishTypeName}'),
            if (price.description.isNotEmpty)
              Text(
                price.description,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: ColorTokens.onSurfaceVariant,
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'edit':
                onEditPrice(index);
                break;
              case 'duplicate':
                onDuplicatePrice(index);
                break;
              case 'delete':
                onRemovePrice(index);
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: ListTile(
                leading: Icon(Icons.edit),
                title: Text('编辑'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'duplicate',
              child: ListTile(
                leading: Icon(Icons.copy),
                title: Text('复制'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: ColorTokens.error),
                title: Text('删除', style: TextStyle(color: ColorTokens.error)),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddPriceButton() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _showAddPriceDialog,
                  icon: const Icon(Icons.add),
                  label: const Text('添加价格'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorTokens.secondary,
                    foregroundColor: ColorTokens.onSecondary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: SpacingTokens.space2),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _showBulkPriceEdit,
                  icon: const Icon(Icons.edit_note),
                  label: const Text('批量编辑'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: ColorTokens.secondary,
                    side: BorderSide(color: ColorTokens.secondary),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space2),
          TextButton.icon(
            onPressed: _showPriceTemplates,
            icon: const Icon(Icons.auto_awesome, size: 16),
            label: const Text('使用价格模板'),
            style: TextButton.styleFrom(
              foregroundColor: ColorTokens.secondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceSummary() {
    if (prices.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalPrices = prices.length;
    final minPrice = prices
        .where((p) => p.price != null)
        .map((p) => p.price!)
        .fold<double?>(null, (min, price) => min == null || price < min ? price : min);
    final maxPrice = prices
        .where((p) => p.price != null)
        .map((p) => p.price!)
        .fold<double?>(null, (max, price) => max == null || price > max ? price : max);
    final avgPrice = prices
        .where((p) => p.price != null)
        .map((p) => p.price!)
        .fold<double>(0, (sum, price) => sum + price) / prices.where((p) => p.price != null).length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            ColorTokens.secondaryContainer,
            ColorTokens.tertiaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: ColorTokens.secondary),
              const SizedBox(width: 8),
              Text(
                '价格统计',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ColorTokens.onSecondaryContainer,
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space4),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '价格数量',
                  totalPrices.toString(),
                  Icons.format_list_numbered,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '最低价格',
                  minPrice != null ? '¥${minPrice.toStringAsFixed(2)}' : '暂无',
                  Icons.trending_down,
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space2),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  '最高价格',
                  maxPrice != null ? '¥${maxPrice.toStringAsFixed(2)}' : '暂无',
                  Icons.trending_up,
                ),
              ),
              Expanded(
                child: _buildStatItem(
                  '平均价格',
                  avgPrice.isNaN ? '暂无' : '¥${avgPrice.toStringAsFixed(2)}',
                  Icons.bar_chart,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: ColorTokens.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, size: 20, color: ColorTokens.secondary),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getPriceTypeIcon(String priceType) {
    switch (priceType.toLowerCase()) {
      case '按小时':
      case '小时':
        return Icons.schedule;
      case '按天':
      case '日票':
        return Icons.today;
      case '按月':
      case '月票':
        return Icons.calendar_month;
      case '按年':
      case '年票':
        return Icons.calendar_today;
      case '按鱼种':
        return Icons.set_meal;
      default:
        return Icons.attach_money;
    }
  }

  void _showAddPriceDialog() {
    // TODO: 实现添加价格对话框
  }

  void _showPriceTemplates() {
    // TODO: 实现价格模板选择
  }

  void _showBulkPriceEdit() {
    // TODO: 实现批量价格编辑
  }
}