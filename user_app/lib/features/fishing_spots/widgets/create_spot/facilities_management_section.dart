import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/create_spot_models.dart';

/// 设施管理相关的Widget组件
class FacilitiesManagementSection extends StatelessWidget {
  final bool hasFacilities;
  final List<SpotFacility> facilities;
  final ValueChanged<bool> onHasFacilitiesChanged;
  final void Function(int index) onToggleFacility;
  final VoidCallback onAddNewFacility;
  final void Function(int index) onEditFacility;
  final void Function(int index) onDuplicateFacility;
  final void Function(int index) onToggleFacilityImportance;

  const FacilitiesManagementSection({
    Key? key,
    required this.hasFacilities,
    required this.facilities,
    required this.onHasFacilitiesChanged,
    required this.onToggleFacility,
    required this.onAddNewFacility,
    required this.onEditFacility,
    required this.onDuplicateFacility,
    required this.onToggleFacilityImportance,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          title: '钓点设施',
          subtitle: '添加钓点的基础设施信息',
          icon: Icons.build_circle_outlined,
          context: context,
        ),
        const SizedBox(height: SpacingTokens.space4),
        _buildFacilitiesToggle(context),
        if (hasFacilities) ...[
          const SizedBox(height: SpacingTokens.space5),
          _buildSearchBar(),
          const SizedBox(height: SpacingTokens.space4),
          _buildBatchOperations(),
          const SizedBox(height: SpacingTokens.space4),
          _buildQuickAddSection(),
          const SizedBox(height: SpacingTokens.space4),
          _buildCustomAddSection(),
          const SizedBox(height: SpacingTokens.space4),
          _buildAddedFacilitiesList(),
        ],
      ],
    );
  }

  Widget _buildSectionHeader({
    required String title,
    required String subtitle,
    required IconData icon,
    required BuildContext context,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.05),
            Theme.of(context).colorScheme.secondary.withOpacity(0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.onPrimary,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFacilitiesToggle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '提供设施服务',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '钓点是否提供相关设施服务',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: hasFacilities,
            onChanged: onHasFacilitiesChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: '搜索设施...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () {
              // TODO: 实现筛选功能
            },
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: ColorTokens.surface,
        ),
      ),
    );
  }

  Widget _buildBatchOperations() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorTokens.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '批量操作',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
          const SizedBox(height: SpacingTokens.space3),
          Row(
            children: [
              Expanded(
                child: _buildBatchButton(
                  'assets/icons/category_fishing.svg',
                  '钓鱼相关',
                  () => _addCategoryFacilities('fishing'),
                ),
              ),
              const SizedBox(width: SpacingTokens.space2),
              Expanded(
                child: _buildBatchButton(
                  'assets/icons/category_food.svg',
                  '餐饮服务',
                  () => _addCategoryFacilities('food'),
                ),
              ),
              const SizedBox(width: SpacingTokens.space2),
              Expanded(
                child: _buildBatchButton(
                  'assets/icons/category_rest.svg',
                  '休息设施',
                  () => _addCategoryFacilities('rest'),
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space3),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _showBulkEditFacilities(),
                  icon: const Icon(Icons.edit, size: 18),
                  label: const Text('批量编辑'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorTokens.primary,
                    foregroundColor: ColorTokens.onPrimary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: SpacingTokens.space2),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _clearAllFacilities,
                  icon: const Icon(Icons.clear_all, size: 18),
                  label: const Text('清空所有'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: ColorTokens.error,
                    foregroundColor: ColorTokens.onError,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBatchButton(String iconPath, String label, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: ColorTokens.outline),
        ),
        child: Column(
          children: [
            // TODO: 使用SVG图标
            Icon(Icons.category, size: 24, color: ColorTokens.primary),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: ColorTokens.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickAddSection() {
    final predefinedFacilities = [
      {'name': '钓台', 'icon': 'fishing_platform', 'category': 'fishing'},
      {'name': '遮阳棚', 'icon': 'shade', 'category': 'comfort'},
      {'name': '洗手间', 'icon': 'restroom', 'category': 'basic'},
      {'name': '停车场', 'icon': 'parking', 'category': 'basic'},
      {'name': '小卖部', 'icon': 'shop', 'category': 'food'},
      {'name': '餐厅', 'icon': 'restaurant', 'category': 'food'},
    ];

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '常用设施快速添加',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
          const SizedBox(height: SpacingTokens.space4),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: predefinedFacilities
                .map((facility) => _buildFacilityChip(facility, false))
                .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFacilityChip(Map<String, String> facility, bool isAdded) {
    return FilterChip(
      label: Text(facility['name']!),
      selected: isAdded,
      onSelected: (selected) {
        // TODO: 实现设施添加/移除逻辑
      },
      avatar: Icon(
        Icons.add_circle_outline,
        size: 18,
      ),
      backgroundColor: ColorTokens.surfaceVariant,
      selectedColor: ColorTokens.primaryContainer,
      checkmarkColor: ColorTokens.primary,
    );
  }

  Widget _buildCustomAddSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.add_circle, color: ColorTokens.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                '自定义设施',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ColorTokens.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space4),
          ElevatedButton.icon(
            onPressed: onAddNewFacility,
            icon: const Icon(Icons.add),
            label: const Text('添加新设施'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              foregroundColor: ColorTokens.onPrimary,
              minimumSize: const Size(double.infinity, 48),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAddedFacilitiesList() {
    if (facilities.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: ColorTokens.surfaceVariant,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: Column(
            children: [
              Icon(
                Icons.construction,
                size: 48,
                color: ColorTokens.onSurfaceVariant,
              ),
              const SizedBox(height: SpacingTokens.space3),
              Text(
                '暂无设施',
                style: TextStyle(
                  fontSize: 16,
                  color: ColorTokens.onSurfaceVariant,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: SpacingTokens.space2),
              Text(
                '添加钓点设施，让钓友了解更多信息',
                style: TextStyle(
                  color: ColorTokens.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  '已添加设施 (${facilities.length})',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: ColorTokens.onSurface,
                  ),
                ),
                const Spacer(),
                Text(
                  '长按可编辑',
                  style: TextStyle(
                    fontSize: 12,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 1),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: facilities.length,
            itemBuilder: (context, index) {
              final facility = facilities[index];
              return _buildFacilityListItem(facility, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFacilityListItem(SpotFacility facility, int index) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: facility.isImportant ? ColorTokens.primaryContainer : ColorTokens.surfaceVariant,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: facility.isImportant ? ColorTokens.primary : ColorTokens.outline,
          child: Icon(
            _getIconFromString(facility.icon),
            color: facility.isImportant ? ColorTokens.onPrimary : ColorTokens.onSurfaceVariant,
            size: 20,
          ),
        ),
        title: Text(
          facility.name,
          style: TextStyle(
            fontWeight: facility.isImportant ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        subtitle: facility.description.isNotEmpty
            ? Text(
                facility.description,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )
            : null,
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (facility.isImportant)
              Icon(
                Icons.star,
                color: ColorTokens.primary,
                size: 16,
              ),
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'edit':
                    onEditFacility(index);
                    break;
                  case 'duplicate':
                    onDuplicateFacility(index);
                    break;
                  case 'toggle_importance':
                    onToggleFacilityImportance(index);
                    break;
                  case 'delete':
                    onToggleFacility(index);
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: ListTile(
                    leading: Icon(Icons.edit),
                    title: Text('编辑'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'duplicate',
                  child: ListTile(
                    leading: Icon(Icons.copy),
                    title: Text('复制'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                PopupMenuItem(
                  value: 'toggle_importance',
                  child: ListTile(
                    leading: Icon(facility.isImportant ? Icons.star_outline : Icons.star),
                    title: Text(facility.isImportant ? '取消重要' : '标为重要'),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: ListTile(
                    leading: Icon(Icons.delete, color: ColorTokens.error),
                    title: Text('删除', style: TextStyle(color: ColorTokens.error)),
                    contentPadding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  IconData _getIconFromString(String iconName) {
    switch (iconName) {
      case 'fishing_platform':
        return Icons.deck;
      case 'shade':
        return Icons.beach_access;
      case 'restroom':
        return Icons.wc;
      case 'parking':
        return Icons.local_parking;
      case 'shop':
        return Icons.store;
      case 'restaurant':
        return Icons.restaurant;
      default:
        return Icons.build_circle;
    }
  }

  void _addCategoryFacilities(String category) {
    // TODO: 实现分类设施批量添加
  }

  void _clearAllFacilities() {
    // TODO: 实现清空所有设施
  }

  void _showBulkEditFacilities() {
    // TODO: 实现批量编辑设施
  }
}