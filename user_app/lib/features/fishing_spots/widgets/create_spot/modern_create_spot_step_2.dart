import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/create_spot_models.dart';
import 'package:user_app/features/fishing_spots/models/fish_type.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/facilities_management_section.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/pricing_management_section.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/certification_management_section.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/spot_creation_sheets.dart';

class ModernFacilitiesPriceStep extends StatefulWidget {
  final bool hasFacilities;
  final bool isPaid;
  final bool isOfficial;
  final List<SpotFacility> facilities;
  final List<SpotPrice> prices;
  final TextEditingController priceController;
  final List<FishType> availableFishTypes;
  final String spotVisibility;
  final List<CertificationDocument> certificationDocuments;
  final bool isUploadingDocuments;

  final ValueChanged<bool> onHasFacilitiesChanged;
  final void Function(int index) onToggleFacility;
  final VoidCallback onAddNewFacility;
  final ValueChanged<bool> onIsPaidChanged;
  final void Function({
    required int priceTypeId,
    required String priceTypeName,
    double? price,
    int? hours,
    int? fishTypeId,
    String? fishTypeName,
    String? description,
  }) onAddPrice;
  final void Function(int index) onRemovePrice;
  final ValueChanged<bool> onIsOfficialChanged;
  final VoidCallback onUploadDocuments;
  final ValueChanged<String> onVisibilityChanged;
  final void Function(int index) onRemoveDocument;
  final void Function(Map<String, dynamic>) onCertificationInfoChanged;

  const ModernFacilitiesPriceStep({
    super.key,
    required this.hasFacilities,
    required this.isPaid,
    required this.isOfficial,
    required this.facilities,
    required this.prices,
    required this.priceController,
    required this.onHasFacilitiesChanged,
    required this.onToggleFacility,
    required this.onAddNewFacility,
    required this.onIsPaidChanged,
    required this.onAddPrice,
    required this.onRemovePrice,
    required this.onIsOfficialChanged,
    required this.onUploadDocuments,
    required this.spotVisibility,
    required this.onVisibilityChanged,
    required this.certificationDocuments,
    required this.isUploadingDocuments,
    required this.onRemoveDocument,
    required this.onCertificationInfoChanged,
    this.availableFishTypes = const [],
  });

  @override
  State<ModernFacilitiesPriceStep> createState() =>
      _ModernFacilitiesPriceStepState();
}

class _ModernFacilitiesPriceStepState extends State<ModernFacilitiesPriceStep> {

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildMainHeader(),
        const SizedBox(height: SpacingTokens.space6),

        // 设施管理部分
        FacilitiesManagementSection(
          hasFacilities: widget.hasFacilities,
          facilities: widget.facilities,
          onHasFacilitiesChanged: widget.onHasFacilitiesChanged,
          onToggleFacility: widget.onToggleFacility,
          onAddNewFacility: _handleAddNewFacility,
          onEditFacility: _handleEditFacility,
          onDuplicateFacility: _handleDuplicateFacility,
          onToggleFacilityImportance: _handleToggleFacilityImportance,
        ),

        const SizedBox(height: 32),

        // 价格管理部分
        PricingManagementSection(
          isPaid: widget.isPaid,
          prices: widget.prices,
          priceController: widget.priceController,
          availableFishTypes: widget.availableFishTypes,
          onIsPaidChanged: widget.onIsPaidChanged,
          onAddPrice: _handleAddPrice,
          onRemovePrice: widget.onRemovePrice,
          onEditPrice: _handleEditPrice,
          onDuplicatePrice: _handleDuplicatePrice,
        ),

        const SizedBox(height: 32),

        // 认证管理部分
        CertificationManagementSection(
          isOfficial: widget.isOfficial,
          isPaid: widget.isPaid,
          certificationDocuments: widget.certificationDocuments,
          onIsOfficialChanged: widget.onIsOfficialChanged,
          onCertificationInfoChanged: widget.onCertificationInfoChanged,
          onUploadDocuments: widget.onUploadDocuments,
          onRemoveDocument: widget.onRemoveDocument,
        ),
      ],
    );
  }

  Widget _buildMainHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.08),
            Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              Icons.business_rounded,
              color: Theme.of(context).colorScheme.onPrimary,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '设施和价格',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '设置钓点设施、收费标准和认证信息',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 处理添加新设施
  void _handleAddNewFacility() {
    widget.onAddNewFacility();
  }

  // 处理编辑设施
  void _handleEditFacility(int index) {
    final facility = widget.facilities[index];
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EditFacilitySheet(
        facility: facility,
        onUpdate: (name, icon, description, id) {
          setState(() {
            facility.name = name;
            facility.icon = icon;
            facility.description = description;
          });
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('设施已更新'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.green,
            ),
          );
        },
      ),
    );
  }

  // 处理复制设施
  void _handleDuplicateFacility(int index) {
    final originalFacility = widget.facilities[index];
    final newFacility = SpotFacility(
      name: '${originalFacility.name} (副本)',
      icon: originalFacility.icon,
      description: originalFacility.description,
      isImportant: originalFacility.isImportant,
    );
    
    setState(() {
      widget.facilities.add(newFacility);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('设施已复制'),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.green,
      ),
    );
  }

  // 处理切换设施重要性
  void _handleToggleFacilityImportance(int index) {
    final facility = widget.facilities[index];
    final newImportanceValue = !(facility.isImportant ?? false);
    setState(() {
      facility.isImportant = newImportanceValue;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(newImportanceValue ? '已标记为重要设施' : '已取消重要标记'),
        behavior: SnackBarBehavior.floating,
        backgroundColor: newImportanceValue ? Colors.orange : Colors.blue,
      ),
    );
  }

  // 处理添加价格
  void _handleAddPrice({
    required int priceTypeId,
    required String priceTypeName,
    double? price,
    int? hours,
    int? fishTypeId,
    String? fishTypeName,
    String? description,
  }) {
    widget.onAddPrice(
      priceTypeId: priceTypeId,
      priceTypeName: priceTypeName,
      price: price,
      hours: hours,
      fishTypeId: fishTypeId,
      fishTypeName: fishTypeName,
      description: description,
    );
  }

  // 处理编辑价格
  void _handleEditPrice(int index) {
    final price = widget.prices[index];
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EditPriceSheet(
        price: price,
        availableFishTypes: widget.availableFishTypes,
        onUpdate: (priceType, priceValue, hours, fishType, description) {
          setState(() {
            price.priceTypeName = priceType;
            price.price = priceValue;
            price.hours = hours?.toInt(); // Convert num? to int?
            price.fishTypeId = fishType?.id.toInt();
            price.fishTypeName = fishType?.name;
            price.description = description ?? '';
          });
          Navigator.pop(context);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('价格已更新'),
              behavior: SnackBarBehavior.floating,
              backgroundColor: Colors.green,
            ),
          );
        },
      ),
    );
  }

  // 处理复制价格
  void _handleDuplicatePrice(int index) {
    final originalPrice = widget.prices[index];
    final newPrice = SpotPrice(
      priceType: originalPrice.priceType,
      priceTypeName: '${originalPrice.priceTypeName} (副本)',
      price: originalPrice.price,
      priceTypeId: originalPrice.priceTypeId,
      hours: originalPrice.hours,
      fishTypeId: originalPrice.fishTypeId,
      fishTypeName: originalPrice.fishTypeName,
      description: originalPrice.description,
    );
    
    setState(() {
      widget.prices.add(newPrice);
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('价格已复制'),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.green,
      ),
    );
  }

}