import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/components/components.dart';

/// 创建钓点第0步 - 基本信息和位置选择 (重构版本)
/// 
/// 这个组件将原来的大文件（435行）重构为模块化的Design System组件，
/// 大幅减少了代码行数，提升了可维护性和复用性
class ModernBasicInfoLocationStep extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final TextEditingController nameController;
  final TextEditingController addressController;
  final TextEditingController descriptionController;
  final double latitude;
  final double longitude;
  final String formattedAddress;
  final bool useCurrentLocation;
  final VoidCallback onShowMapModal;
  final String Function() getSuggestedName;
  final List<String> Function() getSuggestedNames;

  const ModernBasicInfoLocationStep({
    super.key,
    required this.formKey,
    required this.nameController,
    required this.addressController,
    required this.descriptionController,
    required this.latitude,
    required this.longitude,
    required this.formattedAddress,
    required this.useCurrentLocation,
    required this.onShowMapModal,
    required this.getSuggestedName,
    required this.getSuggestedNames,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 位置选择卡片
        FishingSpotLocationCard(
          latitude: latitude,
          longitude: longitude,
          formattedAddress: formattedAddress,
          useCurrentLocation: useCurrentLocation,
          onShowMapModal: onShowMapModal,
        ),

        const SizedBox(height: SpacingTokens.space6),

        // 基本信息表单
        FishingSpotBasicInfoForm(
          formKey: formKey,
          nameController: nameController,
          addressController: addressController,
          descriptionController: descriptionController,
          getSuggestedName: getSuggestedName,
          getSuggestedNames: getSuggestedNames,
          onNameSuggestionTap: () => _showNameSuggestions(context),
        ),
      ],
    );
  }

  /// 显示名称推荐选项
  void _showNameSuggestions(BuildContext context) {
    final suggestions = getSuggestedNames();
    if (suggestions.isEmpty) return;

    showFishingSpotNameSuggestions(
      context: context,
      suggestions: suggestions,
      currentName: nameController.text,
    ).then((selectedName) {
      if (selectedName != null) {
        nameController.text = selectedName;
      }
    });
  }
}