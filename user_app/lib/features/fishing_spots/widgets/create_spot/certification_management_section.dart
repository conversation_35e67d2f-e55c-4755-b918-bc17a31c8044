import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/create_spot/create_spot_models.dart';

/// 认证管理相关的Widget组件
class CertificationManagementSection extends StatelessWidget {
  final bool isOfficial;
  final bool isPaid;
  final List<CertificationDocument> certificationDocuments;
  final ValueChanged<bool> onIsOfficialChanged;
  final ValueChanged<Map<String, dynamic>> onCertificationInfoChanged;
  final VoidCallback onUploadDocuments;
  final void Function(int index) onRemoveDocument;

  const CertificationManagementSection({
    Key? key,
    required this.isOfficial,
    required this.isPaid,
    required this.certificationDocuments,
    required this.onIsOfficialChanged,
    required this.onCertificationInfoChanged,
    required this.onUploadDocuments,
    required this.onRemoveDocument,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          title: '官方认证',
          subtitle: '申请官方钓点认证',
          icon: Icons.verified_rounded,
          context: context,
        ),
        const SizedBox(height: SpacingTokens.space4),
        _buildCertificationToggle(context),
        if (isOfficial && certificationDocuments.isNotEmpty) ...[
          const SizedBox(height: SpacingTokens.space4),
          _buildUploadedDocuments(context),
        ] else if (isOfficial && certificationDocuments.isEmpty) ...[
          const SizedBox(height: SpacingTokens.space4),
          _buildCertificationPrompt(context),
        ],
      ],
    );
  }

  Widget _buildSectionHeader({
    required String title,
    required String subtitle,
    required IconData icon,
    required BuildContext context,
  }) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.05),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.1),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 56,
            height: 56,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.tertiary,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.3),
                  blurRadius: 12,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Icon(
              icon,
              color: Theme.of(context).colorScheme.onTertiary,
              size: 28,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCertificationToggle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 48,
            height: 48,
            decoration: BoxDecoration(
              color: isOfficial && certificationDocuments.isNotEmpty
                  ? Theme.of(context).colorScheme.tertiary
                  : Theme.of(context).colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              isOfficial && certificationDocuments.isNotEmpty
                  ? Icons.verified
                  : Icons.admin_panel_settings,
              color: isOfficial && certificationDocuments.isNotEmpty
                  ? Theme.of(context).colorScheme.onTertiary
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '申请官方认证',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  isOfficial && certificationDocuments.isNotEmpty
                      ? '已提交认证申请，通过认证后将获得官方标识'
                      : '通过认证后将获得官方标识，仅支持收费钓点',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: isOfficial && certificationDocuments.isNotEmpty,
            onChanged: (value) => _handleCertificationToggle(value, context),
            activeColor: Theme.of(context).colorScheme.tertiary,
          ),
        ],
      ),
    );
  }

  Widget _buildCertificationPrompt(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorTokens.warning.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ColorTokens.warning.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: ColorTokens.warning, size: 20),
              const SizedBox(width: 8),
              Text(
                '需要上传认证文件',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ColorTokens.warning,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '申请官方认证需要上传营业执照等相关资质文件。请先填写认证信息并上传证明文件。',
            style: TextStyle(
              fontSize: 14,
              color: ColorTokens.warning,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showCertificationDialog(context),
              icon: const Icon(Icons.upload_file),
              label: const Text('填写认证信息'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorTokens.warning,
                foregroundColor: ColorTokens.onWarning,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUploadedDocuments(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: ColorTokens.success.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ColorTokens.success.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.check_circle, color: ColorTokens.success, size: 20),
              const SizedBox(width: 8),
              Text(
                '已上传认证资料',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: ColorTokens.success,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...certificationDocuments
              .map((doc) => Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Icon(Icons.description,
                            size: 18, color: ColorTokens.onSurfaceVariant),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            doc.file.name ?? '营业执照',
                            style: TextStyle(color: ColorTokens.onSurfaceVariant),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, size: 18),
                          onPressed: () => onRemoveDocument(
                            certificationDocuments.indexOf(doc),
                          ),
                          padding: EdgeInsets.zero,
                          constraints: const BoxConstraints(),
                        ),
                      ],
                    ),
                  )),
          const SizedBox(height: SpacingTokens.space2),
          TextButton.icon(
            onPressed: () => _showCertificationDialog(context),
            icon: const Icon(Icons.edit, size: 18),
            label: const Text('修改认证信息'),
          ),
        ],
      ),
    );
  }

  void _handleCertificationToggle(bool value, BuildContext context) {
    if (value && isPaid) {
      // 如果开启官方认证且是收费钓点，显示认证申请表单
      _showCertificationDialog(context);
    } else if (!isPaid && value) {
      // 如果不是收费钓点，提示需要先设置为收费
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('官方认证仅支持收费钓点，请先设置收费信息'),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    } else {
      // 关闭官方认证时，同时清空认证信息
      if (!value) {
        onIsOfficialChanged(false);
      }
    }
  }

  void _showCertificationDialog(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => CertificationApplicationSheet(
        onSubmit: (certificationData) {
          // 处理认证申请，保存认证信息
          onCertificationInfoChanged(certificationData);
          Navigator.pop(context);

          // 直接调用上传文档方法
          onUploadDocuments();
        },
      ),
    );
  }
}

/// 认证申请表单组件
class CertificationApplicationSheet extends StatefulWidget {
  final Function(Map<String, dynamic>) onSubmit;

  const CertificationApplicationSheet({
    Key? key,
    required this.onSubmit,
  }) : super(key: key);

  @override
  State<CertificationApplicationSheet> createState() =>
      _CertificationApplicationSheetState();
}

class _CertificationApplicationSheetState
    extends State<CertificationApplicationSheet> {
  final _formKey = GlobalKey<FormState>();
  final _businessNameController = TextEditingController();
  final _contactNameController = TextEditingController();
  final _contactPhoneController = TextEditingController();
  final _licenseNoController = TextEditingController();
  final _legalPersonController = TextEditingController();

  String _businessType = 'individual';

  @override
  void dispose() {
    _businessNameController.dispose();
    _contactNameController.dispose();
    _contactPhoneController.dispose();
    _licenseNoController.dispose();
    _legalPersonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.9,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: ColorTokens.surface,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // 拖动指示器
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: ColorTokens.outline,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              // 标题栏
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '申请官方认证',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              const Divider(),
              // 表单内容
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(16),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 提示信息
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: ColorTokens.info.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.info_outline,
                                  color: ColorTokens.info, size: 20),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  '官方认证需要提供营业执照等相关资质，审核通过后将获得官方认证标识',
                                  style: TextStyle(
                                    color: ColorTokens.info,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: SpacingTokens.space6),

                        // 经营类型
                        _buildSectionTitle('经营类型'),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _buildRadioOption(
                                title: '个体工商户',
                                value: 'individual',
                                groupValue: _businessType,
                                onChanged: (value) {
                                  setState(() {
                                    _businessType = value!;
                                  });
                                },
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildRadioOption(
                                title: '公司',
                                value: 'company',
                                groupValue: _businessType,
                                onChanged: (value) {
                                  setState(() {
                                    _businessType = value!;
                                  });
                                },
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: SpacingTokens.space6),

                        // 基本信息
                        _buildSectionTitle('基本信息'),
                        const SizedBox(height: 12),
                        _buildTextField(
                          controller: _businessNameController,
                          label: '经营主体名称',
                          hint: '请输入营业执照上的名称',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return '请输入经营主体名称';
                            }
                            return null;
                          },
                        ),
                        const SizedBox(height: SpacingTokens.space4),
                        Row(
                          children: [
                            Expanded(
                              child: _buildTextField(
                                controller: _contactNameController,
                                label: '联系人姓名',
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入联系人姓名';
                                  }
                                  return null;
                                },
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildTextField(
                                controller: _contactPhoneController,
                                label: '联系电话',
                                keyboardType: TextInputType.phone,
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return '请输入联系电话';
                                  }
                                  if (!RegExp(r'^\d{11}$').hasMatch(value)) {
                                    return '请输入正确的手机号';
                                  }
                                  return null;
                                },
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: SpacingTokens.space6),

                        // 营业执照信息
                        _buildSectionTitle('营业执照信息', required: true),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: _buildTextField(
                                controller: _licenseNoController,
                                label: '营业执照号',
                                hint: '请输入营业执照号码',
                              ),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: _buildTextField(
                                controller: _legalPersonController,
                                label: '法人代表',
                                hint: '请输入法人姓名',
                              ),
                            ),
                          ],
                        ),

                        const SizedBox(height: 32),

                        // 提交按钮
                        SizedBox(
                          width: double.infinity,
                          height: 48,
                          child: ElevatedButton(
                            onPressed: _handleSubmit,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: Theme.of(context).colorScheme.onPrimary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text('确认并上传证件'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title, {bool required = false}) {
    return Row(
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (required) ...[
          const SizedBox(width: 4),
          Text(
            '*',
            style: TextStyle(color: ColorTokens.error),
          ),
        ],
      ],
    );
  }

  Widget _buildRadioOption({
    required String title,
    required String value,
    required String groupValue,
    required ValueChanged<String?> onChanged,
  }) {
    return InkWell(
      onTap: () => onChanged(value),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          border: Border.all(
            color: value == groupValue
                ? Theme.of(context).colorScheme.primary
                : ColorTokens.outline,
            width: value == groupValue ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(12),
          color: value == groupValue
              ? Theme.of(context).colorScheme.primary.withOpacity(0.05)
              : null,
        ),
        child: Row(
          children: [
            Radio<String>(
              value: value,
              groupValue: groupValue,
              onChanged: onChanged,
              activeColor: Theme.of(context).colorScheme.primary,
            ),
            Text(
              title,
              style: TextStyle(
                fontWeight: value == groupValue ? FontWeight.w600 : null,
                color: value == groupValue 
                    ? Theme.of(context).colorScheme.primary 
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    TextInputType? keyboardType,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      ),
    );
  }

  void _handleSubmit() {
    if (_formKey.currentState!.validate()) {
      final data = {
        'businessType': _businessType,
        'businessName': _businessNameController.text,
        'contactName': _contactNameController.text,
        'contactPhone': _contactPhoneController.text,
        'businessLicenseNo': _licenseNoController.text,
        'legalPerson': _legalPersonController.text,
      };

      widget.onSubmit(data);
    }
  }
}