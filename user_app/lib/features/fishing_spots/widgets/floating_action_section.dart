import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class FloatingActionSection extends StatelessWidget {
  final AnimationController fabAnimationController;
  final bool isMapView;
  final bool isVisible;
  final VoidCallback onAddSpot;
  final VoidCallback onToggleView;

  const FloatingActionSection({
    super.key,
    required this.fabAnimationController,
    required this.isMapView,
    required this.isVisible,
    required this.onAddSpot,
    required this.onToggleView,
  });

  @override
  Widget build(BuildContext context) {
    final fabScaleAnimation = CurvedAnimation(
      parent: fabAnimationController,
      curve: Curves.elasticOut,
    );

    return Positioned(
      right: SpacingTokens.space4,
      bottom: SpacingTokens.space20,
      child: AnimatedSlide(
        duration: MotionTokens.durationFast,
        offset: isVisible ? Offset.zero : const Offset(2.0, 0),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
          duration: MotionTokens.durationFast,
          opacity: isVisible ? 1.0 : 0.0,
          child: Column(
            children: [
              // Add Spot Button with Smart Login Verification
              ScaleTransition(
                scale: fabScaleAnimation,
                child: FloatingActionButton(
                  onPressed: onAddSpot,
                  heroTag: 'add_spot',
                  backgroundColor: ColorTokens.primary,
                  foregroundColor: ColorTokens.onPrimary,
                  elevation: ElevationTokens.level3,
                  child: const Icon(Icons.add_location_alt, size: 28),
                ),
              ),
              const SizedBox(height: SpacingTokens.space3),
              // View Switch Button
              ScaleTransition(
                scale: fabAnimationController,
                child: FloatingActionButton(
                  onPressed: onToggleView,
                  heroTag: 'toggle_view',
                  backgroundColor: ColorTokens.surface,
                  foregroundColor: ColorTokens.primary,
                  elevation: ElevationTokens.level2,
                  child: AnimatedSwitcher(
                    duration: MotionTokens.durationMedium,
                    child: Icon(
                      isMapView ? Icons.list : Icons.map,
                      key: ValueKey(isMapView),
                      color: ColorTokens.primary,
                      size: 28,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
