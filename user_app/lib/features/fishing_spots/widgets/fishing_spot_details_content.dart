import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_2d_amap/flutter_2d_amap.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/features/fishing_spots/widgets/checkin_button.dart';
import 'package:user_app/features/fishing_spots/widgets/facility_item.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/shared/widgets/gesture_enhancements.dart';
import 'package:user_app/widgets/user_circle_avatar.dart';

class FishingSpotDetailsContent extends StatefulWidget {
  final FishingSpotVo spot;
  final ScrollController scrollController;
  final VoidCallback? onNavigationPressed;
  final VoidCallback? onViewDynamicPressed;
  final Function(User)? onCreatorTap;
  final Function(int newCheckinCount)? onCheckinSuccess;
  final Future<void> Function()? onRefresh;

  const FishingSpotDetailsContent({
    super.key,
    required this.spot,
    required this.scrollController,
    this.onNavigationPressed,
    this.onViewDynamicPressed,
    this.onCreatorTap,
    this.onCheckinSuccess,
    this.onRefresh,
  });

  @override
  State<FishingSpotDetailsContent> createState() =>
      _FishingSpotDetailsContentState();
}

class _FishingSpotDetailsContentState extends State<FishingSpotDetailsContent> {
  bool _isLoading = false;
  bool _hasError = false;
  String? _errorMessage;

  Future<void> _handleRefresh() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      if (widget.onRefresh != null) {
        await widget.onRefresh!();
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  void _handleRetry() {
    _handleRefresh();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return StateIndicator(
      isLoading: _isLoading,
      hasError: _hasError,
      errorMessage: _errorMessage,
      onRetry: _handleRetry,
      useSkeleton: true,
      loadingMessage: '加载钓点详情...',
      child: ElasticScrollView(
        enablePullToRefresh: widget.onRefresh != null,
        onRefresh: _handleRefresh,
        refreshText: '下拉刷新钓点信息',
        child: SwipeToDismissContainer(
          onDismiss: () => Navigator.pop(context),
          child: CustomScrollView(
            controller: widget.scrollController,
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      horizontal: SpacingTokens.space4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Drag handle
                      Center(
                        child: Container(
                          width: 40,
                          height: 4,
                          margin: const EdgeInsets.symmetric(
                              vertical: SpacingTokens.space3),
                          decoration: BoxDecoration(
                            color: ColorTokens.outlineVariant,
                            borderRadius: BorderRadius.circular(2),
                          ),
                        ),
                      ),

                      // Header with name and verification badge
                      Padding(
                        padding:
                            const EdgeInsets.only(bottom: SpacingTokens.space4),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              widget.spot.name,
                              style: Theme.of(context)
                                  .textTheme
                                  .headlineMedium
                                  ?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    letterSpacing: -0.5,
                                    color: ColorTokens.onSurface,
                                  ),
                            ),
                            const SizedBox(height: SpacingTokens.space2),
                            if (widget.spot.isOfficial ||
                                widget.spot.verificationLevel > 0)
                              Row(
                                children: [
                                  if (widget.spot.isOfficial)
                                    _buildFlatBadge(
                                      icon: Icons.verified,
                                      label: '官方认证',
                                      color: colorScheme.primary,
                                    )
                                  else if (widget.spot.verificationLevel > 0)
                                    _buildFlatBadge(
                                      icon: Icons.thumb_up,
                                      label:
                                          '用户推荐 ${widget.spot.verificationLevel}/3',
                                      color: ColorTokens.tertiary,
                                    ),
                                ],
                              ),
                          ],
                        ),
                      ),

                      // Creator information (只在有完整creator对象时显示)
                      if (widget.spot.creator != null)
                        _buildCreatorSection(widget.spot.creator!)
                      else if (widget.spot.createdBy != null && kDebugMode)
                        // 在debug模式下显示创建者ID
                        Padding(
                          padding: const EdgeInsets.only(
                              bottom: SpacingTokens.space4),
                          child: Container(
                            padding: const EdgeInsets.all(SpacingTokens.space3),
                            decoration: BoxDecoration(
                              color: ColorTokens.tertiaryContainer,
                              borderRadius:
                                  BorderRadius.circular(SpacingTokens.space2),
                              border: Border.all(
                                color: ColorTokens.outlineVariant,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.info,
                                  size: 16,
                                  color: ColorTokens.onTertiaryContainer,
                                ),
                                const SizedBox(width: SpacingTokens.space2),
                                Expanded(
                                  child: Text(
                                    '创建者ID: ${widget.spot.createdBy} (需要后端API支持)',
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall
                                        ?.copyWith(
                                          color:
                                              ColorTokens.onTertiaryContainer,
                                        ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                      // Address section
                      Padding(
                        padding:
                            const EdgeInsets.only(bottom: SpacingTokens.space4),
                        child: Row(
                          children: [
                            Icon(
                              Icons.location_on_rounded,
                              size: 16,
                              color: ColorTokens.onSurfaceVariant,
                            ),
                            const SizedBox(width: SpacingTokens.space2),
                            Expanded(
                              child: Text(
                                widget.spot.address,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium
                                    ?.copyWith(
                                      color: ColorTokens.onSurfaceVariant,
                                      height: 1.3,
                                    ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Images gallery
                      if (widget.spot.images != null &&
                          widget.spot.images!.isNotEmpty)
                        _buildFlatImageGallery(widget.spot.images!),

                      const SizedBox(height: SpacingTokens.space4),

                      // Description
                      if (widget.spot.description != null &&
                          widget.spot.description!.isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(
                              bottom: SpacingTokens.space4),
                          child: Text(
                            widget.spot.description!,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium
                                ?.copyWith(
                                  color: ColorTokens.onSurface,
                                  height: 1.4,
                                ),
                          ),
                        ),

                      // Fish types section
                      if (widget.spot.fishTypeList.isNotEmpty ||
                          widget.spot.extraFishTypesList.isNotEmpty)
                        _buildFlatDetailsSection(
                          context: context,
                          title: '鱼类品种',
                          icon: Icons.catching_pokemon,
                          color: ColorTokens.primary,
                          child: _buildFlatChipsWrap(
                            items: [
                              ...widget.spot.fishTypeList
                                  .map((fish) => fish.name),
                              ...widget.spot.extraFishTypesList,
                            ],
                            color: ColorTokens.primary,
                          ),
                        ),

                      // Best seasons section
                      _buildFlatDetailsSection(
                        context: context,
                        title: '最佳季节',
                        icon: Icons.calendar_today_rounded,
                        color: ColorTokens.secondary,
                        child: _buildFlatChipsWrap(
                          items: widget.spot.seasons,
                          color: ColorTokens.secondary,
                          currentValue: _getCurrentSeason(),
                        ),
                      ),

                      // Facilities section
                      if (widget.spot.hasFacilities)
                        _buildFlatDetailsSection(
                          context: context,
                          title: '场地设施',
                          icon: Icons.restaurant_rounded,
                          color: ColorTokens.tertiary,
                          child:
                              _buildFlatFacilitiesContent(context, widget.spot),
                        ),

                      // Price section
                      _buildFlatDetailsSection(
                        context: context,
                        title: '价格信息',
                        icon: Icons.payments_rounded,
                        color: ColorTokens.secondary,
                        child: _buildFlatPricingContent(context, widget.spot),
                      ),

                      // Stats section
                      _buildFlatDetailsSection(
                        context: context,
                        title: '数据统计',
                        icon: Icons.bar_chart_rounded,
                        color: ColorTokens.primary,
                        child: Container(
                          padding: const EdgeInsets.all(SpacingTokens.space3),
                          decoration: BoxDecoration(
                            color: ColorTokens.surfaceVariant,
                            borderRadius:
                                BorderRadius.circular(SpacingTokens.space3),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              _buildFlatStat(
                                context: context,
                                icon: Icons.remove_red_eye_rounded,
                                value: widget.spot.visitorCount.toString(),
                                label: '访问',
                                color: ColorTokens.primary,
                              ),
                              Container(
                                height: 32,
                                width: 1,
                                color: ColorTokens.outlineVariant,
                              ),
                              _buildFlatStat(
                                context: context,
                                icon: Icons.person_rounded,
                                value: widget.spot.checkinCount.toString(),
                                label: '签到',
                                color: ColorTokens.primary,
                              ),
                              Container(
                                height: 32,
                                width: 1,
                                color: ColorTokens.outlineVariant,
                              ),
                              _buildFlatStat(
                                context: context,
                                icon: Icons.star_rounded,
                                value: widget.spot.rating.toStringAsFixed(1),
                                label: '评分',
                                color: ColorTokens.tertiary,
                              ),
                            ],
                          ),
                        ),
                      ),

                      const SizedBox(height: SpacingTokens.space5),

                      // Action buttons
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton.icon(
                              onPressed: () {
                                Navigator.pop(context);
                                if (widget.spot.latitude != 0 &&
                                    widget.spot.longitude != 0) {
                                  final lngLat = LngLat(widget.spot.longitude,
                                      widget.spot.latitude);
                                  context.navigateTo(AppRoutes.routePlanning,
                                      extra: RoutePlanningRouteData(
                                        address: widget.spot.address,
                                        destination: lngLat,
                                      ));
                                }
                              },
                              icon: const Icon(Icons.directions_rounded,
                                  size: 18),
                              label: const Text('导航'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: ColorTokens.primary,
                                foregroundColor: ColorTokens.onPrimary,
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(
                                      SpacingTokens.space2),
                                ),
                                padding: const EdgeInsets.symmetric(
                                    vertical: SpacingTokens.space3),
                                elevation: 0,
                              ),
                            ),
                          ),
                          if (widget.onViewDynamicPressed != null) ...[
                            const SizedBox(width: SpacingTokens.space3),
                            Expanded(
                              child: OutlinedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  widget.onViewDynamicPressed?.call();
                                },
                                icon: const Icon(Icons.article_outlined,
                                    size: 18),
                                label: const Text('查看动态'),
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: ColorTokens.primary,
                                  side: BorderSide(
                                    color: ColorTokens.outline,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        SpacingTokens.space2),
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                      vertical: SpacingTokens.space3),
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),

                      const SizedBox(height: SpacingTokens.space3),

                      CheckinButton(
                        spotId: widget.spot.id,
                        spotName: widget.spot.name,
                        style: CheckinButtonStyle.outlined,
                        fullWidth: true,
                        onCheckinSuccess: (newCheckinCount) {
                          Navigator.pop(context);
                          widget.onCheckinSuccess?.call(newCheckinCount);
                        },
                      ),

                      const SizedBox(height: SpacingTokens.space4),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Show full screen image viewer
  void _showImageViewer(
      BuildContext context, List<String> images, int initialIndex) {
    showDialog(
      context: context,
      barrierColor: Colors.black87,
      builder: (context) => ImageViewerDialog(
        images: images,
        initialIndex: initialIndex,
      ),
    );
  }

  // Enhanced image gallery with indicators and better UX
  Widget _buildFlatImageGallery(List<String> images) {
    return Column(
      children: [
        SizedBox(
          height: 220,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(SpacingTokens.space3),
            child: PageView.builder(
              itemCount: images.length,
              itemBuilder: (context, index) {
                return Container(
                  margin:
                      EdgeInsets.symmetric(horizontal: SpacingTokens.space1),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(SpacingTokens.space3),
                    boxShadow: [
                      BoxShadow(
                        color: ColorTokens.shadow.withOpacity(0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(SpacingTokens.space3),
                    child: Stack(
                      children: [
                        Image.network(
                          images[index],
                          width: double.infinity,
                          height: double.infinity,
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return Container(
                              color: ColorTokens.surfaceVariant,
                              child: const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              ),
                            );
                          },
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              color: ColorTokens.surfaceVariant,
                              child: Center(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.image_not_supported_rounded,
                                      color: ColorTokens.onSurfaceVariant,
                                      size: 32,
                                    ),
                                    const SizedBox(
                                        height: SpacingTokens.space1),
                                    Text(
                                      '图片加载失败',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: ColorTokens.onSurfaceVariant,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                        // Image count indicator
                        if (images.length > 1)
                          Positioned(
                            top: SpacingTokens.space2,
                            right: SpacingTokens.space2,
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: SpacingTokens.space2,
                                vertical: SpacingTokens.space1,
                              ),
                              decoration: BoxDecoration(
                                color: ColorTokens.scrim.withOpacity(0.6),
                                borderRadius:
                                    BorderRadius.circular(SpacingTokens.space4),
                              ),
                              child: Text(
                                '${index + 1}/${images.length}',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ),
        if (images.length > 1) ...[
          const SizedBox(height: SpacingTokens.space2),
          Text(
            '左右滑动查看更多图片',
            style: TextStyle(
              fontSize: 12,
              color: ColorTokens.onSurfaceVariant,
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ],
    );
  }

  // Enhanced section header with better visual hierarchy
  Widget _buildFlatDetailsSection({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Color color,
    required Widget child,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: SpacingTokens.space5),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space4),
        border: Border.all(
          color: ColorTokens.outlineVariant,
          width: 0.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header with enhanced styling
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: SpacingTokens.space4,
              vertical: SpacingTokens.space3,
            ),
            decoration: BoxDecoration(
              color: color.withOpacity(0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(SpacingTokens.space4),
                topRight: Radius.circular(SpacingTokens.space4),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(SpacingTokens.space2),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.15),
                    borderRadius: BorderRadius.circular(SpacingTokens.space2),
                  ),
                  child: Icon(
                    icon,
                    size: 18,
                    color: color,
                  ),
                ),
                const SizedBox(width: SpacingTokens.space3),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: ColorTokens.onSurface,
                        letterSpacing: -0.2,
                      ),
                ),
              ],
            ),
          ),
          // Section content
          Padding(
            padding: EdgeInsets.fromLTRB(
              SpacingTokens.space4,
              SpacingTokens.space3,
              SpacingTokens.space4,
              SpacingTokens.space4,
            ),
            child: child,
          ),
        ],
      ),
    );
  }

  // Enhanced chip wrapper with better visual design
  Widget _buildFlatChipsWrap({
    required List<String> items,
    required Color color,
    String? currentValue,
  }) {
    if (items.isEmpty) {
      return Container(
        padding: EdgeInsets.all(SpacingTokens.space3),
        decoration: BoxDecoration(
          color: ColorTokens.surfaceVariant.withOpacity(0.5),
          borderRadius: BorderRadius.circular(SpacingTokens.space2),
          border: Border.all(
            color: ColorTokens.outlineVariant,
            style: BorderStyle.solid,
            width: 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.info_outline,
              size: 16,
              color: ColorTokens.onSurfaceVariant,
            ),
            const SizedBox(width: SpacingTokens.space2),
            Text(
              '暂无信息',
              style: TextStyle(
                fontSize: 12,
                color: ColorTokens.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      );
    }

    return Wrap(
      spacing: SpacingTokens.space2,
      runSpacing: SpacingTokens.space2,
      children: items.map((item) {
        final bool isHighlighted = currentValue != null && item == currentValue;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: EdgeInsets.symmetric(
            horizontal: SpacingTokens.space3,
            vertical: SpacingTokens.space2,
          ),
          decoration: BoxDecoration(
            color: isHighlighted
                ? color.withOpacity(0.2)
                : ColorTokens.surfaceVariant,
            borderRadius: BorderRadius.circular(SpacingTokens.space4),
            border: Border.all(
              color: isHighlighted
                  ? color.withOpacity(0.5)
                  : ColorTokens.outlineVariant,
              width: isHighlighted ? 1.5 : 1,
            ),
            boxShadow: isHighlighted
                ? [
                    BoxShadow(
                      color: color.withOpacity(0.1),
                      blurRadius: 4,
                      offset: const Offset(0, 1),
                    ),
                  ]
                : null,
          ),
          child: Text(
            item,
            style: TextStyle(
              fontSize: 13,
              fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w500,
              color: isHighlighted ? color : ColorTokens.onSurfaceVariant,
              letterSpacing: -0.1,
            ),
          ),
        );
      }).toList(),
    );
  }

  // Flat pricing section
  Widget _buildFlatPricingContent(BuildContext context, FishingSpotVo spot) {
    if (!spot.isPaid) {
      return Container(
        padding: const EdgeInsets.all(SpacingTokens.space3),
        decoration: BoxDecoration(
          color: ColorTokens.tertiaryContainer,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(SpacingTokens.space2),
              decoration: BoxDecoration(
                color: ColorTokens.surface,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.money_off_rounded,
                color: ColorTokens.tertiary,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '免费钓场',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ColorTokens.tertiary,
                  ),
                ),
                Text(
                  '无需支付任何费用',
                  style: TextStyle(
                    fontSize: 12,
                    color: ColorTokens.tertiary,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    // Simple price display if no detailed prices available
    if (spot.prices.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(SpacingTokens.space3),
        decoration: BoxDecoration(
          color: ColorTokens.secondaryContainer,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: ColorTokens.surface,
                borderRadius: BorderRadius.circular(6),
              ),
              child: Icon(
                Icons.payments_rounded,
                color: ColorTokens.secondary,
                size: 16,
              ),
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '收费钓场',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ColorTokens.secondary,
                  ),
                ),
                Text(
                  '¥${spot.price?.toStringAsFixed(0) ?? "未知"}/天',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: ColorTokens.secondary,
                  ),
                ),
              ],
            ),
          ],
        ),
      );
    }

    // Detailed price display when prices list is available
    return Column(
      children: spot.prices.map((priceItem) {
        String title = priceItem.priceTypeName;
        if (priceItem.fishType != null) {
          title += ' (${priceItem.fishType?.name})';
        }
        final String durationText =
            priceItem.hours != null ? '${priceItem.hours}小时' : '每天';

        return Container(
          margin: EdgeInsets.only(bottom: 8),
          padding: EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: ColorTokens.surfaceContainer,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      durationText,
                      style: TextStyle(
                        color: ColorTokens.onSurfaceVariant,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: ColorTokens.secondaryContainer,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  '¥${priceItem.price.toStringAsFixed(0)}',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: ColorTokens.secondary,
                  ),
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // Flat facilities section
  Widget _buildFlatFacilitiesContent(BuildContext context, FishingSpotVo spot) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: ColorTokens.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Wrap(
        spacing: 12,
        runSpacing: 12,
        children: spot.facilities.isNotEmpty
            ? spot.facilities.map((facility) {
                return _buildFlatFacilityItem(
                  context: context,
                  icon: FacilityItem.getIconFromString(facility.icon),
                  label: facility.name,
                  color: ColorTokens.tertiary,
                );
              }).toList()
            : [
                _buildFlatFacilityItem(
                  context: context,
                  icon: Icons.wc_rounded,
                  label: '卫生间',
                  color: ColorTokens.tertiary,
                ),
                _buildFlatFacilityItem(
                  context: context,
                  icon: Icons.local_parking_rounded,
                  label: '停车场',
                  color: ColorTokens.tertiary,
                ),
              ],
      ),
    );
  }

  // Helper method to get current season
  String _getCurrentSeason() {
    final month = DateTime.now().month;
    if (month >= 3 && month <= 5) {
      return '春';
    } else if (month >= 6 && month <= 8) {
      return '夏';
    } else if (month >= 9 && month <= 11) {
      return '秋';
    } else {
      return '冬';
    }
  }

  // Flat badge widget
  Widget _buildFlatBadge({
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: color,
          ),
          const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  // Flat stat widget
  Widget _buildFlatStat({
    required BuildContext context,
    required IconData icon,
    required String value,
    required String label,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(
          icon,
          size: 16,
          color: color,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: ColorTokens.onSurface,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: ColorTokens.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  // Flat facility item widget
  Widget _buildFlatFacilityItem({
    required BuildContext context,
    required IconData icon,
    required String label,
    required Color color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: SpacingTokens.paddingSm,
          decoration: BoxDecoration(
            color: color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            size: 16,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 11,
            color: ColorTokens.onSurfaceVariant,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // Build creator section
  Widget _buildCreatorSection(User creator) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () {
              debugPrint(
                  '👤 [FishingSpotDetailsContent] 用户头像被点击: ${creator.name} (ID: ${creator.id})');
              if (widget.onCreatorTap != null) {
                widget.onCreatorTap!(creator);
              } else {
                debugPrint('⚠️ [FishingSpotDetailsContent] onCreatorTap 回调为空');
              }
            },
            child: UserCircleAvatar(
              avatarUrl: creator.avatarUrl,
              radius: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      creator.name ?? '匿名用户',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 6),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: ColorTokens.primaryContainer,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '发布者',
                        style: TextStyle(
                          fontSize: 10,
                          color: ColorTokens.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
                if (creator.introduce != null && creator.introduce!.isNotEmpty)
                  Text(
                    creator.introduce!,
                    style: TextStyle(
                      fontSize: 12,
                      color: ColorTokens.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
          Icon(
            Icons.chevron_right,
            size: 16,
            color: ColorTokens.outlineVariant,
          ),
        ],
      ),
    );
  }
}

/// 全屏图片查看器对话框
class ImageViewerDialog extends StatefulWidget {
  final List<String> images;
  final int initialIndex;

  const ImageViewerDialog({
    super.key,
    required this.images,
    required this.initialIndex,
  });

  @override
  State<ImageViewerDialog> createState() => _ImageViewerDialogState();
}

class _ImageViewerDialogState extends State<ImageViewerDialog>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog.fullscreen(
      backgroundColor: Colors.black,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Stack(
          children: [
            // 图片轮播
            PageView.builder(
              controller: _pageController,
              itemCount: widget.images.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
                HapticFeedback.selectionClick();
              },
              itemBuilder: (context, index) {
                return InteractiveViewer(
                  minScale: 0.5,
                  maxScale: 4.0,
                  child: Center(
                    child: Hero(
                      tag: 'image_$index',
                      child: Image.network(
                        widget.images[index],
                        fit: BoxFit.contain,
                        loadingBuilder: (context, child, loadingProgress) {
                          if (loadingProgress == null) return child;
                          return Center(
                            child: CircularProgressIndicator(
                              value: loadingProgress.expectedTotalBytes != null
                                  ? loadingProgress.cumulativeBytesLoaded /
                                      (loadingProgress.expectedTotalBytes ?? 1)
                                  : null,
                              color: ColorTokens.primary,
                            ),
                          );
                        },
                        errorBuilder: (context, error, stackTrace) {
                          return Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.broken_image_rounded,
                                  size: 64,
                                  color: Colors.white54,
                                ),
                                const SizedBox(height: SpacingTokens.space2),
                                const Text(
                                  '图片加载失败',
                                  style: TextStyle(
                                    color: Colors.white54,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                );
              },
            ),

            // 顶部工具栏
            Positioned(
              top: MediaQuery.of(context).padding.top + SpacingTokens.space2,
              left: SpacingTokens.space4,
              right: SpacingTokens.space4,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // 关闭按钮
                  Material(
                    color: Colors.black45,
                    borderRadius: BorderRadius.circular(SpacingTokens.space6),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(SpacingTokens.space6),
                      onTap: () {
                        HapticFeedback.selectionClick();
                        _animationController.reverse().then((_) {
                          Navigator.pop(context);
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.all(SpacingTokens.space3),
                        child: const Icon(
                          Icons.close_rounded,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                  // 分享按钮
                  Material(
                    color: Colors.black45,
                    borderRadius: BorderRadius.circular(SpacingTokens.space6),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(SpacingTokens.space6),
                      onTap: () {
                        HapticFeedback.mediumImpact();
                        // TODO: 实现分享功能
                      },
                      child: Container(
                        padding: EdgeInsets.all(SpacingTokens.space3),
                        child: const Icon(
                          Icons.share_rounded,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 底部指示器
            if (widget.images.length > 1)
              Positioned(
                bottom: MediaQuery.of(context).padding.bottom +
                    SpacingTokens.space4,
                left: 0,
                right: 0,
                child: Center(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: SpacingTokens.space4,
                      vertical: SpacingTokens.space2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(SpacingTokens.space6),
                    ),
                    child: Text(
                      '${_currentIndex + 1} / ${widget.images.length}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
