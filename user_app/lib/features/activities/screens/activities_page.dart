import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/activities/widgets/activity_action_button.dart';
import 'package:user_app/features/activities/widgets/participants_widget.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/locations/models/region.dart';
import 'package:user_app/features/locations/view_models/region_view_model.dart';
import 'package:user_app/features/locations/widgets/region_selector.dart';
import 'package:user_app/shared/widgets/login_required_dialog.dart';

import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

class ActivitiesPage extends StatefulWidget {
  const ActivitiesPage({super.key});

  @override
  State<ActivitiesPage> createState() => _ActivitiesPageState();
}

class _ActivitiesPageState extends State<ActivitiesPage>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  String _selectedCategory = 'all';
  late AnimationController _animationController;

  // 地区选择相关
  Region? _selectedProvince;
  Region? _selectedCity;
  String _currentLocationText = '全国';

  // 搜索相关

  final TextEditingController _searchController = TextEditingController();
  bool _isSearchExpanded = false;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animationController.forward();

    // 加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadInitialData();
    });
  }

  Future<void> _loadInitialData() async {
    final viewModel = context.read<FishingPlanViewModel>();
    final regionViewModel = context.read<RegionViewModel>();
    final authViewModel = context.read<AuthViewModel>();

    // 检查用户认证状态
    if (authViewModel.isUserLoggedIn()) {
      await authViewModel.checkAndRefreshAuthStatus();
    }

    // 预加载省份数据
    regionViewModel.loadProvinces();

    // 根据当前地区筛选加载数据
    await viewModel.loadUpcomingPlansWithLocation(
      refresh: true,
      province: _selectedProvince?.shortName,
      city: _selectedCity?.name,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.surfaceContainer,
      body: RefreshIndicator(
        onRefresh: _handleRefresh,
        color: ColorTokens.primary,
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            // 使用统一的 DSSliverAppBar 组件
            DSSliverAppBar(
              title: '钓鱼活动',
              expandedHeight: 120,
              floating: true,
              snap: true,
              pinned: true,
              automaticallyImplyLeading: false,
              actions: [
                DSAppBarAction(
                  icon: _isSearchExpanded ? Icons.close : Icons.search,
                  onPressed: _toggleSearch,
                  tooltip: _isSearchExpanded ? '关闭搜索' : '搜索活动',
                ),
                DSAppBarAction(
                  icon: Icons.location_on_outlined,
                  onPressed: _showRegionSelector,
                  tooltip: '选择地区',
                ),
                DSAppBarAction(
                  icon: Icons.add,
                  onPressed: _navigateToCreateActivity,
                  tooltip: '创建活动',
                ),
                const SizedBox(width: SpacingTokens.space2),
              ],
              flexibleSpace: FlexibleSpaceBar(
                background: Container(
                  color: ColorTokens.surface,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // 搜索框 - 可展开/折叠
                      if (_isSearchExpanded) _buildSearchBar(),

                      // 快速筛选标签 - 直接进入主题
                      Container(
                        height: SpacingTokens.space10,
                        margin:
                            const EdgeInsets.only(bottom: SpacingTokens.space2),
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(
                              horizontal: SpacingTokens.space4),
                          children: [
                            // 地区标签
                            _buildLocationChip(),
                            const SizedBox(width: SpacingTokens.space2),
                            _buildFilterChip(
                                '全部', 'all', _selectedCategory == 'all'),
                            _buildFilterChip(
                                '今日', 'today', _selectedCategory == 'today',
                                hasNew: true),
                            _buildFilterChip('本周末', 'weekend',
                                _selectedCategory == 'weekend'),
                            _buildFilterChip(
                                '海钓', 'sea', _selectedCategory == 'sea'),
                            _buildFilterChip(
                                '黑坑', 'pond', _selectedCategory == 'pond'),
                            _buildFilterChip(
                                '路亚', 'lure', _selectedCategory == 'lure'),
                            _buildFilterChip('附近5km', 'nearby',
                                _selectedCategory == 'nearby'),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // 直接展示活动列表 - 使用真实数据
            Consumer<FishingPlanViewModel>(
              builder: (context, viewModel, child) {
                if (viewModel.isLoadingUpcoming) {
                  return SliverToBoxAdapter(
                    child: Container(
                      padding: const EdgeInsets.all(
                          SpacingTokens.space12 + SpacingTokens.space1),
                      child: Column(
                        children: [
                          const CircularProgressIndicator(
                            color: ColorTokens.primary,
                          ),
                          const SizedBox(height: SpacingTokens.space4),
                          Text(
                            '正在加载精彩活动...',
                            style: TypographyTokens.bodyMedium.copyWith(
                              color: ColorTokens.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }

                if (viewModel.errorUpcoming != null) {
                  return SliverToBoxAdapter(
                    child: _buildErrorState(viewModel.errorUpcoming!),
                  );
                }

                final activities =
                    _getFilteredActivities(viewModel.upcomingPlans);

                if (activities.isEmpty) {
                  return SliverToBoxAdapter(
                    child: _buildEmptyState(),
                  );
                }

                return SliverList(
                  delegate: SliverChildListDelegate([
                    // 活动统计信息栏
                    _buildActivityStatsBar(activities, viewModel.upcomingPlans),

                    // 活动列表
                    ...List.generate(activities.length, (index) {
                      final activity = activities[index];
                      if (index == 0 &&
                          (activity.isHot ||
                              activity.currentParticipants >
                                  activity.maxParticipants * 0.8)) {
                        // 第一个卡片：热门活动
                        return _buildHotActivityCard(activity);
                      }
                      return _buildEnhancedActivityCard(activity, index);
                    }),

                    // 加载更多按钮
                    if (viewModel.hasMoreData || viewModel.isLoadingMore)
                      _buildLoadMoreButton(viewModel),
                  ]),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  // 筛选活动数据
  List<FishingPlan> _getFilteredActivities(List<FishingPlan> allActivities) {
    List<FishingPlan> filteredList = allActivities;

    // 首先按搜索关键词筛选
    if (_searchQuery.isNotEmpty) {
      final String query = _searchQuery.toLowerCase();
      filteredList = filteredList.where((activity) {
        return activity.title.toLowerCase().contains(query) ||
            activity.location.toLowerCase().contains(query) ||
            activity.description.toLowerCase().contains(query) ||
            (activity.activityType?.toLowerCase().contains(query) ?? false);
      }).toList();
    }

    // 然后按分类筛选
    if (_selectedCategory == 'all') {
      return filteredList;
    }

    final now = DateTime.now();
    return filteredList.where((activity) {
      switch (_selectedCategory) {
        case 'today':
          return _isSameDay(activity.planDate, now);
        case 'weekend':
          return _isWeekend(activity.planDate);
        case 'sea':
          return activity.activityType?.contains('海钓') == true ||
              activity.title.contains('海钓') ||
              activity.description.contains('海钓');
        case 'pond':
          return activity.activityType?.contains('黑坑') == true ||
              activity.title.contains('黑坑') ||
              activity.description.contains('黑坑');
        case 'lure':
          return activity.activityType?.contains('路亚') == true ||
              activity.title.contains('路亚') ||
              activity.description.contains('路亚');
        case 'nearby':
          return activity.distance != null && activity.distance! <= 5.0;
        default:
          return true;
      }
    }).toList();
  }

  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  bool _isWeekend(DateTime date) {
    return date.weekday == DateTime.saturday || date.weekday == DateTime.sunday;
  }

  Widget _buildErrorState(String error) {
    String friendlyMessage = '加载失败了';
    IconData errorIcon = Icons.error_outline;

    if (error.contains('网络')) {
      friendlyMessage = '网络连接异常';
      errorIcon = Icons.wifi_off;
    } else if (error.contains('超时')) {
      friendlyMessage = '网络请求超时';
      errorIcon = Icons.timer_off;
    } else if (error.contains('401') || error.contains('403')) {
      friendlyMessage = '登录已过期，请重新登录';
      errorIcon = Icons.lock_outline;
    } else if (error.contains('500')) {
      friendlyMessage = '服务器暂时繁忙';
      errorIcon = Icons.dns_outlined;
    }

    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        children: [
          Icon(
            errorIcon,
            size: 64,
            color: ColorTokens.error,
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            friendlyMessage,
            style: TypographyTokens.headlineSmall.copyWith(
              color: ColorTokens.onError,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            '请检查网络连接后重试',
            style: TypographyTokens.bodyMedium.copyWith(
              color: ColorTokens.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          if (kDebugMode) ...[
            const SizedBox(height: SpacingTokens.space2),
            Text(
              error,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: SpacingTokens.space4),
          ElevatedButton(
            onPressed: _loadInitialData,
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    String title;
    String subtitle;
    IconData icon;

    if (_searchQuery.isNotEmpty) {
      title = '未找到相关活动';
      subtitle = '尝试更换搜索关键词或筛选条件';
      icon = Icons.search_off;
    } else if (_selectedCategory != 'all') {
      title = '暂无此类活动';
      subtitle = '该分类下暂时没有活动，试试其他分类';
      icon = Icons.filter_list_off;
    } else if (_selectedProvince != null || _selectedCity != null) {
      title = '该地区暂无活动';
      subtitle = '尝试选择其他地区或创建新活动';
      icon = Icons.location_off;
    } else {
      title = '暂无钓鱼活动';
      subtitle = '成为第一个发起活动的钓友吧！';
      icon = Icons.event_note;
    }

    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        children: [
          Icon(
            icon,
            size: 64,
            color: ColorTokens.onSurfaceVariant,
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            title,
            style: TypographyTokens.headlineSmall,
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            subtitle,
            style: TypographyTokens.bodyMedium.copyWith(
              color: ColorTokens.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: SpacingTokens.space4),
          ElevatedButton(
            onPressed: _navigateToCreateActivity,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              foregroundColor: ColorTokens.onPrimary,
            ),
            child: const Text('创建活动'),
          ),
        ],
      ),
    );
  }

  // 筛选标签
  Widget _buildFilterChip(String label, String value, bool isSelected,
      {bool hasNew = false}) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedCategory = value;
        });
        HapticFeedback.lightImpact();
      },
      child: Container(
        margin: const EdgeInsets.only(right: SpacingTokens.space2),
        padding: const EdgeInsets.symmetric(
            horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
        decoration: BoxDecoration(
          color: isSelected ? ColorTokens.primary : ColorTokens.surface,
          borderRadius: ShapeTokens.borderRadiusFull,
          border: Border.all(
            color: isSelected ? ColorTokens.primary : ColorTokens.outline,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? ColorTokens.onPrimary
                        : ColorTokens.onSurfaceVariant,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
            ),
            if (hasNew) ...[
              const SizedBox(width: SpacingTokens.space1),
              Container(
                width: SpacingTokens.space2 - 2,
                height: SpacingTokens.space2 - 2,
                decoration: const BoxDecoration(
                  color: ColorTokens.error,
                  shape: BoxShape.circle,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  // 热门活动卡片 - 特殊展示
  Widget _buildHotActivityCard(FishingPlan activity) {
    final remainingSlots =
        activity.maxParticipants - activity.currentParticipants;
    return GestureDetector(
      onTap: () => _navigateToActivityDetail(activity.id),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
        decoration: BoxDecoration(
          borderRadius: ShapeTokens.borderRadiusLg,
          color: ColorTokens.primaryContainer,
          border: Border.all(color: ColorTokens.primary, width: 2),
          boxShadow: [
            BoxShadow(
              color: ColorTokens.primary.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Stack(
          children: [
            // 装饰元素
            Positioned(
              right: -30,
              top: -30,
              child: Container(
                width: 150,
                height: 150,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Theme.of(context)
                      .colorScheme
                      .onSurface
                      .withValues(alpha: 0.1),
                ),
              ),
            ),

            Padding(
              padding: const EdgeInsets.all(SpacingTokens.space5),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 10, vertical: 4),
                        decoration: BoxDecoration(
                          color: ColorTokens.surface.withValues(alpha: 0.2),
                          borderRadius: ShapeTokens.borderRadiusMd,
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.local_fire_department,
                                color: ColorTokens.onPrimary, size: 16),
                            const SizedBox(width: SpacingTokens.space1),
                            Text(
                              '今日热门',
                              style: TypographyTokens.labelSmall.copyWith(
                                color: ColorTokens.onPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Spacer(),
                      Text(
                        remainingSlots > 0 ? '仅剩$remainingSlots个名额' : '名额已满',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: ColorTokens.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                    ],
                  ),

                  const SizedBox(height: SpacingTokens.space4),

                  Text(
                    activity.title,
                    style: TypographyTokens.headlineMedium.copyWith(
                      color: ColorTokens.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: SpacingTokens.space2),

                  Text(
                    activity.description.length > 50
                        ? '${activity.description.substring(0, 50)}...'
                        : activity.description,
                    style: TypographyTokens.bodyMedium.copyWith(
                      color: ColorTokens.onPrimary.withValues(alpha: 0.9),
                    ),
                  ),

                  const SizedBox(height: SpacingTokens.space5),

                  // 参与者头像
                  Row(
                    children: [
                      _buildAvatarStack(),
                      const SizedBox(width: SpacingTokens.space3),
                      Text(
                        '${activity.currentParticipants}位钓友已参加',
                        style: TypographyTokens.bodySmall.copyWith(
                          color: ColorTokens.onPrimary.withValues(alpha: 0.9),
                        ),
                      ),
                      const Spacer(),
                      ElevatedButton(
                        onPressed: _canInteractWithActivity(activity)
                            ? () => _handleJoinActivity(activity)
                            : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: ColorTokens.surface,
                          foregroundColor: ColorTokens.primary,
                          shape: const RoundedRectangleBorder(
                            borderRadius: ShapeTokens.borderRadiusXl,
                          ),
                        ),
                        child: Text(
                          _getParticipationText(activity),
                          style: TypographyTokens.labelMedium
                              .copyWith(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 增强版活动卡片 - 更吸引人
  Widget _buildEnhancedActivityCard(FishingPlan activity, int index) {
    final DateFormat timeFormat = DateFormat('MM月dd日 HH:mm');

    return GestureDetector(
      onTap: () => _navigateToActivityDetail(activity.id),
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
        decoration: BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: ShapeTokens.borderRadiusLg,
          boxShadow: [
            BoxShadow(
              color: ColorTokens.shadow.withValues(alpha: 0.1),
              blurRadius: ElevationTokens.level2,
              offset: const Offset(0, SpacingTokens.space1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 图片区域 - 视觉吸引力
            Stack(
              children: [
                Container(
                  width: double.infinity,
                  height: 180,
                  decoration: BoxDecoration(
                    borderRadius:
                        const BorderRadius.vertical(top: Radius.circular(16)),
                    color: _getActivityBackgroundColor(index),
                  ),
                  child: Stack(
                    children: [
                      // 钓点图片或默认背景
                      Positioned.fill(
                        child: ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(16)),
                          child: _buildActivityImage(activity, index),
                        ),
                      ),

                      // 渐变遮罩
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: const BorderRadius.vertical(
                                top: Radius.circular(16)),
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Theme.of(context)
                                    .colorScheme
                                    .surface
                                    .withValues(alpha: 0),
                                ColorTokens.shadow.withValues(alpha: 0.5),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // 活动类型标签
                      Positioned(
                        top: 12,
                        left: 12,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 10, vertical: 6),
                              decoration: const BoxDecoration(
                                color: ColorTokens.surface,
                                borderRadius: ShapeTokens.borderRadiusMd,
                              ),
                              child: Text(
                                activity.activityType ?? '钓鱼',
                                style: Theme.of(context)
                                    .textTheme
                                    .labelSmall
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ),
                            if (activity.isOfficialActivity) ...[
                              const SizedBox(
                                  width: SpacingTokens.space1 +
                                      SpacingTokens.space0),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: SpacingTokens.space2,
                                    vertical: SpacingTokens.space1),
                                decoration: const BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      ColorTokens.tertiary,
                                      ColorTokens.tertiaryContainer
                                    ],
                                  ),
                                  borderRadius: ShapeTokens.borderRadiusSm,
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.verified,
                                      size: 12,
                                      color: Theme.of(context)
                                          .colorScheme
                                          .onTertiaryContainer,
                                    ),
                                    const SizedBox(
                                        width: SpacingTokens.space0 + 2),
                                    Text(
                                      '官方',
                                      style: Theme.of(context)
                                          .textTheme
                                          .labelSmall
                                          ?.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: Theme.of(context)
                                                .colorScheme
                                                .onTertiaryContainer,
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),

                      // 价格标签
                      Positioned(
                        top: 12,
                        right: 12,
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 6),
                          decoration: BoxDecoration(
                            color: (activity.price == null ||
                                    activity.price == '免费')
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.tertiary,
                            borderRadius: ShapeTokens.borderRadiusSm,
                          ),
                          child: Text(
                            activity.price ?? '免费',
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall
                                ?.copyWith(
                                  color:
                                      Theme.of(context).colorScheme.onPrimary,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ),
                      ),

                      // 底部信息 - 移除以避免与图片重叠，改为在内容区域显示
                    ],
                  ),
                ),

                // 热门标记
                if (activity.isHot)
                  Positioned(
                    top: 12,
                    left: 100,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: SpacingTokens.space2,
                          vertical: SpacingTokens.space1),
                      decoration: const BoxDecoration(
                        color: ColorTokens.error,
                        borderRadius: ShapeTokens.borderRadiusMd,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.local_fire_department,
                              color: ColorTokens.onError, size: 12),
                          const SizedBox(width: SpacingTokens.space0 + 2),
                          Text(
                            '热门',
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall
                                ?.copyWith(
                                  color: ColorTokens.onError,
                                  fontWeight: FontWeight.bold,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),

            // 内容区域
            Padding(
              padding: const EdgeInsets.all(SpacingTokens.space4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题和组织者
                  Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              activity.title,
                              style: TypographyTokens.headlineSmall.copyWith(
                                fontWeight: FontWeight.bold,
                                color: ColorTokens.onSurface,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: SpacingTokens.space1),
                            Row(
                              children: [
                                Stack(
                                  children: [
                                    CircleAvatar(
                                      radius: 12,
                                      backgroundColor:
                                          activity.isOfficialActivity
                                              ? ColorTokens.tertiary
                                                  .withValues(alpha: 0.2)
                                              : ColorTokens.primary
                                                  .withValues(alpha: 0.1),
                                      child: activity.isOfficialActivity
                                          ? const Icon(
                                              Icons.business,
                                              size: 14,
                                              color: ColorTokens.tertiary,
                                            )
                                          : Text(
                                              activity.ownerName.isNotEmpty
                                                  ? activity.ownerName[0]
                                                  : 'U',
                                              style: TypographyTokens.labelSmall
                                                  .copyWith(
                                                color: ColorTokens.primary,
                                                fontWeight: FontWeight.bold,
                                              ),
                                            ),
                                    ),
                                    if (activity.isOfficialActivity)
                                      Positioned(
                                        right: -2,
                                        top: -2,
                                        child: Container(
                                          width: 10,
                                          height: 10,
                                          decoration: BoxDecoration(
                                            color: ColorTokens.tertiary,
                                            shape: BoxShape.circle,
                                            border: Border.all(
                                              color: ColorTokens.surface,
                                              width: 1,
                                            ),
                                          ),
                                          child: const Icon(
                                            Icons.verified,
                                            size: 6,
                                            color: ColorTokens.surface,
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                                const SizedBox(
                                    width: SpacingTokens.space1 +
                                        SpacingTokens.space0),
                                Flexible(
                                  child: Text(
                                    activity.ownerName,
                                    style: TypographyTokens.bodySmall.copyWith(
                                      color: activity.isOfficialActivity
                                          ? ColorTokens.tertiary
                                          : ColorTokens.onSurfaceVariant,
                                      fontWeight: activity.isOfficialActivity
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                const SizedBox(width: SpacingTokens.space2),
                                const Icon(Icons.verified,
                                    size: 14, color: ColorTokens.primary),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: SpacingTokens.space2),

                  // 位置和时间信息 - 从图片区域移到这里，避免被图片遮挡
                  Row(
                    children: [
                      const Icon(Icons.location_on,
                          color: ColorTokens.primary, size: 16),
                      const SizedBox(width: SpacingTokens.space1),
                      Expanded(
                        child: Text(
                          activity.location,
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: ColorTokens.onSurface,
                                    fontWeight: FontWeight.w500,
                                  ),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      const SizedBox(width: SpacingTokens.space2),
                      const Icon(Icons.access_time,
                          color: ColorTokens.primary, size: 16),
                      const SizedBox(width: SpacingTokens.space1),
                      Text(
                        timeFormat.format(activity.planDate),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: ColorTokens.onSurface,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                    ],
                  ),

                  const SizedBox(height: SpacingTokens.space3),

                  // 标签
                  Wrap(
                    spacing: 6,
                    runSpacing: 6,
                    children: [
                      if (activity.activityType != null)
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: ColorTokens.secondary.withValues(alpha: 0.1),
                            borderRadius: ShapeTokens.borderRadiusSm,
                          ),
                          child: Text(
                            activity.activityType!,
                            style: TypographyTokens.labelSmall.copyWith(
                              color: ColorTokens.secondary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      if (activity.price != null && activity.price != '免费')
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: ColorTokens.primary.withValues(alpha: 0.1),
                            borderRadius: ShapeTokens.borderRadiusSm,
                          ),
                          child: Text(
                            activity.price!,
                            style: TypographyTokens.labelSmall.copyWith(
                              color: ColorTokens.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                    ],
                  ),

                  const SizedBox(height: SpacingTokens.space3),

                  // 参与信息和操作
                  Column(
                    children: [
                      // 第一行：参与者信息和主要操作
                      Row(
                        children: [
                          // 参与者头像组件
                          Expanded(
                            child: ParticipantsWidget(
                              activity: activity,
                              maxAvatars: 3,
                              avatarSize: 24,
                              onTap: () =>
                                  _navigateToActivityDetail(activity.id),
                            ),
                          ),

                          const SizedBox(width: SpacingTokens.space2),

                          // 快速参与按钮
                          ActivityActionButton(
                            activity: activity,
                            onJoin: () => _handleJoinActivity(activity),
                            onLeave: () => _handleLeaveActivity(activity),
                            showFullActions: false,
                          ),
                        ],
                      ),

                      const SizedBox(height: SpacingTokens.space2),

                      // 第二行：次要操作
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // 距离信息
                          if (activity.distance != null)
                            Text(
                              '距您 ${activity.distance!.toStringAsFixed(1)}km',
                              style: TypographyTokens.bodySmall.copyWith(
                                color: ColorTokens.onSurfaceVariant,
                              ),
                            )
                          else
                            const SizedBox.shrink(),

                          // 操作按钮组
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // 收藏按钮
                              IconButton(
                                onPressed: () =>
                                    _handleBookmarkActivity(activity),
                                icon: Icon(
                                  activity.isBookmarked
                                      ? Icons.bookmark
                                      : Icons.bookmark_border,
                                  size: 16,
                                  color: activity.isBookmarked
                                      ? ColorTokens.primary
                                      : ColorTokens.onSurfaceVariant,
                                ),
                                tooltip:
                                    activity.isBookmarked ? '取消收藏' : '收藏活动',
                                padding:
                                    const EdgeInsets.all(SpacingTokens.space1),
                                constraints: const BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                              ),

                              // 分享按钮
                              IconButton(
                                onPressed: () => _handleShareActivity(activity),
                                icon: const Icon(
                                  Icons.share,
                                  size: 16,
                                  color: ColorTokens.primary,
                                ),
                                tooltip: '分享活动',
                                padding:
                                    const EdgeInsets.all(SpacingTokens.space1),
                                constraints: const BoxConstraints(
                                  minWidth: 32,
                                  minHeight: 32,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 头像堆叠组件
  Widget _buildAvatarStack() {
    return SizedBox(
      width: 80,
      height: 32,
      child: Stack(
        children: List.generate(4, (index) {
          return Positioned(
            left: index * 18.0,
            child: Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: ColorTokens.surface, width: 2),
                color: Theme.of(context).colorScheme.primaryContainer,
              ),
              child: Center(
                child: Text(
                  '${index + 1}',
                  style: TypographyTokens.labelMedium.copyWith(
                    color: ColorTokens.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          );
        }),
      ),
    );
  }

  // 地区标签显示
  Widget _buildLocationChip() {
    return GestureDetector(
      onTap: _showRegionSelector,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space3, vertical: SpacingTokens.space2),
        decoration: BoxDecoration(
          color: ColorTokens.secondary.withValues(alpha: 0.1),
          borderRadius: ShapeTokens.borderRadiusLg,
          border: Border.all(color: ColorTokens.secondary),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.location_on,
              size: 14,
              color: ColorTokens.secondary,
            ),
            const SizedBox(width: SpacingTokens.space1),
            Text(
              _currentLocationText,
              style: TypographyTokens.labelMedium.copyWith(
                color: ColorTokens.secondary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(width: SpacingTokens.space0 + 2),
            const Icon(
              Icons.keyboard_arrow_down,
              size: 14,
              color: ColorTokens.secondary,
            ),
          ],
        ),
      ),
    );
  }

  // 显示地区选择器
  Future<void> _showRegionSelector() async {
    final result = await RegionSelectorModal.show(
      context,
      title: '选择地区',
      initialProvince: _selectedProvince,
      initialCity: _selectedCity,
      showDistricts: false, // 钓鱼活动只需要到城市级别
    );

    if (result != null) {
      final province = result['province'];
      final city = result['city'];

      if (province != _selectedProvince || city != _selectedCity) {
        setState(() {
          _selectedProvince = province;
          _selectedCity = city;

          // 更新显示文本
          if (province != null) {
            if (city != null) {
              _currentLocationText = '${province.shortName} ${city.name}';
            } else {
              _currentLocationText = province.shortName;
            }
          } else {
            _currentLocationText = '全国';
          }
        });

        // 重新加载数据
        await _loadInitialData();
      }
    }
  }

  // 处理加入活动
  Future<void> _handleJoinActivity(FishingPlan activity) async {
    final authViewModel = context.read<AuthViewModel>();

    if (!authViewModel.isUserLoggedIn()) {
      await LoginRequiredDialog.showForJoinActivity(context);
      return;
    }

    try {
      final viewModel = context.read<FishingPlanViewModel>();
      await viewModel.joinPlan(activity.id);

      if (mounted) {
        _showSnackBar('成功参加活动：${activity.title}', isError: false);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('参加活动失败：$e', isError: true);
      }
    }
  }

  Future<void> _handleLeaveActivity(FishingPlan activity) async {
    final authViewModel = context.read<AuthViewModel>();

    if (!authViewModel.isUserLoggedIn()) {
      await LoginRequiredDialog.showGeneral(context, action: '退出活动');
      return;
    }

    try {
      final viewModel = context.read<FishingPlanViewModel>();
      await viewModel.leavePlan(activity.id);

      if (mounted) {
        _showSnackBar('已退出活动：${activity.title}', isError: false);
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar('退出活动失败：$e', isError: true);
      }
    }
  }

  Future<void> _navigateToActivityDetail(int activityId) async {
    HapticFeedback.lightImpact();

    // 找到对应的活动对象
    final viewModel = context.read<FishingPlanViewModel>();
    final activity = viewModel.upcomingPlans.firstWhere(
      (plan) => plan.id == activityId,
      orElse: () => throw StateError('Activity not found'),
    );

    // 导航到活动详情页
    await context.navigateToWithResult(AppRoutes.activityDetail, extra: ActivityDetailRouteData(activity: activity));

    // 不需要刷新，因为使用了局部状态更新
    // ViewModel 会自动更新列表中的活动状态
  }

  // 活动统计信息栏
  Widget _buildActivityStatsBar(
      List<FishingPlan> filteredActivities, List<FishingPlan> allActivities) {
    final totalParticipants = filteredActivities.fold<int>(
        0, (sum, activity) => sum + activity.currentParticipants);
    final hotActivities =
        filteredActivities.where((activity) => activity.isHot).length;
    // Removed freeActivities - no longer displayed in simplified design

    return Container(
      margin: const EdgeInsets.symmetric(
          horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 简化的标题行 - 更直观的信息展示
          if (filteredActivities.isNotEmpty)
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: SpacingTokens.space2),
              child: Row(
                children: [
                  Text(
                    '共${filteredActivities.length}个活动',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: ColorTokens.onSurface,
                        ),
                  ),
                  if (totalParticipants > 0) ...[
                    Text(
                      ' • $totalParticipants人参与',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: ColorTokens.onSurfaceVariant,
                          ),
                    ),
                  ],
                  const Spacer(),
                  if (hotActivities > 0)
                    Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: SpacingTokens.space2,
                          vertical: SpacingTokens.space0 + 2),
                      decoration: const BoxDecoration(
                        color: ColorTokens.errorContainer,
                        borderRadius: ShapeTokens.borderRadiusFull,
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(Icons.local_fire_department,
                              size: 14, color: ColorTokens.error),
                          const SizedBox(width: SpacingTokens.space0 + 2),
                          Text(
                            '$hotActivities个热门',
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall
                                ?.copyWith(
                                  color: ColorTokens.error,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),

          // 筛选状态提示（如果有筛选条件）
          if (_searchQuery.isNotEmpty ||
              _selectedCategory != 'all' ||
              _currentLocationText != '全国') ...[
            const SizedBox(height: SpacingTokens.space2),
            Container(
              margin:
                  const EdgeInsets.symmetric(horizontal: SpacingTokens.space2),
              padding: const EdgeInsets.symmetric(
                  horizontal: SpacingTokens.space3,
                  vertical: SpacingTokens.space1),
              decoration: const BoxDecoration(
                color: ColorTokens.primaryContainer,
                borderRadius: ShapeTokens.borderRadiusMd,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.filter_list,
                      size: 16, color: ColorTokens.primary),
                  const SizedBox(width: SpacingTokens.space1),
                  Text(
                    '筛选结果：${filteredActivities.length}/${allActivities.length}',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: ColorTokens.primary,
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: SpacingTokens.space2),
        ],
      ),
    );
  }

  // Removed _buildStatItem method - no longer needed in simplified design

  // 下拉刷新
  Future<void> _handleRefresh() async {
    try {
      final viewModel = context.read<FishingPlanViewModel>();
      await viewModel.loadUpcomingPlansWithLocation(
        refresh: true,
        province: _selectedProvince?.shortName,
        city: _selectedCity?.name,
      );
    } catch (e) {
      debugPrint('刷新失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('刷新失败: $e'),
            backgroundColor: ColorTokens.error,
          ),
        );
      }
    }
  }

  // 搜索相关方法
  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
      if (!_isSearchExpanded) {
        _searchController.clear();
        _searchQuery = '';
      }
    });
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
      decoration: BoxDecoration(
        color: ColorTokens.surfaceContainerHighest,
        borderRadius: ShapeTokens.borderRadiusLg,
        border: Border.all(color: ColorTokens.outline),
      ),
      child: TextField(
        controller: _searchController,
        autofocus: true,
        decoration: const InputDecoration(
          hintText: '搜索活动标题、地点或类型...',
          prefixIcon: Icon(Icons.search, color: ColorTokens.onSurfaceVariant),
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space3),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
        onSubmitted: (value) {
          // 可以在这里添加搜索提交逻辑
        },
      ),
    );
  }

  // 导航到创建中心页面
  Future<void> _navigateToCreateActivity() async {
    final authViewModel = context.read<AuthViewModel>();

    // 检查用户是否已登录
    if (!authViewModel.isUserLoggedIn()) {
      await LoginRequiredDialog.showForCreateActivity(context);
      return;
    }

    // 导航到新的创建中心
    context.navigateTo('/create-center');
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(isError ? Icons.error : Icons.check_circle,
                color: isError ? ColorTokens.onError : ColorTokens.onSuccess),
            const SizedBox(width: SpacingTokens.space2),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: isError ? ColorTokens.error : ColorTokens.success,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  // 处理活动分享
  Future<void> _handleShareActivity(FishingPlan activity) async {
    try {
      // 构建分享内容
      final shareText = _buildShareText(activity);

      // 显示分享选项对话框
      final result = await showModalBottomSheet<String>(
        context: context,
        backgroundColor:
            Theme.of(context).colorScheme.surface.withValues(alpha: 0),
        builder: (context) => _buildShareBottomSheet(activity, shareText),
      );

      if (result != null) {
        switch (result) {
          case 'text':
            await Share.share(shareText);
            break;
          case 'link':
            // 如果有活动链接，分享链接
            final activityLink = _buildActivityLink(activity);
            await Share.share('$shareText\n\n查看详情: $activityLink');
            break;
          case 'image':
            // 分享活动海报（如果有的话）
            await _shareActivityImage(activity);
            break;
        }

        _showSnackBar('分享成功！');
      }
    } catch (e) {
      debugPrint('Error sharing activity: $e');
      _showSnackBar('分享失败，请稍后重试', isError: true);
    }
  }

  // 构建分享文本
  String _buildShareText(FishingPlan activity) {
    final dateFormat = DateFormat('MM月dd日 HH:mm');
    final formattedDate = dateFormat.format(activity.planDate);

    return '''🎣 ${activity.title}

📍 地点：${activity.location}
📅 时间：$formattedDate
👥 参与人数：${activity.currentParticipants}/${activity.maxParticipants}人
💰 费用：${activity.price ?? '免费'}

${activity.description.isNotEmpty ? '活动介绍：\n${activity.description}\n\n' : ''}组织者：${activity.ownerName}

快来一起钓鱼吧！🐟''';
  }

  // 构建活动链接
  String _buildActivityLink(FishingPlan activity) {
    // 这里应该是实际的深链接或网页链接
    return 'https://fishing-app.com/activity/${activity.id}';
  }

  // 分享活动图片
  Future<void> _shareActivityImage(FishingPlan activity) async {
    // TODO: 实现生成活动海报并分享
    // 可以使用 screenshot 包来生成卡片截图
    _showSnackBar('图片分享功能开发中');
  }

  // 分享选项底部弹窗
  Widget _buildShareBottomSheet(FishingPlan activity, String shareText) {
    return Container(
      decoration: const BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusTopLg,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 拖拽指示器
          Container(
            margin: const EdgeInsets.only(top: SpacingTokens.space2),
            width: SpacingTokens.space10,
            height: SpacingTokens.space1,
            decoration: const BoxDecoration(
              color: ColorTokens.outline,
              borderRadius: ShapeTokens.borderRadiusXs,
            ),
          ),

          const SizedBox(height: SpacingTokens.space5),

          // 标题
          Text(
            '分享活动',
            style: TypographyTokens.headlineSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),

          const SizedBox(height: SpacingTokens.space5),

          // 分享选项
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildShareOption(
                  icon: Icons.text_fields,
                  label: '文字分享',
                  onTap: () => Navigator.pop(context, 'text'),
                ),
                _buildShareOption(
                  icon: Icons.link,
                  label: '链接分享',
                  onTap: () => Navigator.pop(context, 'link'),
                ),
                _buildShareOption(
                  icon: Icons.image,
                  label: '海报分享',
                  onTap: () => Navigator.pop(context, 'image'),
                ),
              ],
            ),
          ),

          const SizedBox(height: SpacingTokens.space8),

          // 取消按钮
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: const RoundedRectangleBorder(
                    borderRadius: ShapeTokens.borderRadiusMd,
                    side: BorderSide(color: ColorTokens.outline),
                  ),
                ),
                child: Text(
                  '取消',
                  style: TypographyTokens.labelLarge.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ),
            ),
          ),

          const SizedBox(height: SpacingTokens.space5),
        ],
      ),
    );
  }

  // 处理活动收藏
  Future<void> _handleBookmarkActivity(FishingPlan activity) async {
    final authViewModel = context.read<AuthViewModel>();

    if (!authViewModel.isUserLoggedIn()) {
      await LoginRequiredDialog.showForBookmarkActivity(context);
      return;
    }

    try {
      final viewModel = context.read<FishingPlanViewModel>();

      if (activity.isBookmarked) {
        // 取消收藏
        await viewModel.removeBookmark(activity.id);
        _showSnackBar('取消收藏成功');
      } else {
        // 添加收藏
        await viewModel.addBookmark(activity.id);
        _showSnackBar('收藏成功');
      }
    } catch (e) {
      debugPrint('Error handling bookmark: $e');
      _showSnackBar('操作失败，请稍后重试', isError: true);
    }
  }

  // 分享选项按钮
  Widget _buildShareOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: ColorTokens.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: ColorTokens.primary,
              size: 28,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            label,
            style: TypographyTokens.labelMedium.copyWith(
              color: ColorTokens.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  // 加载更多按钮
  Widget _buildLoadMoreButton(FishingPlanViewModel viewModel) {
    return Container(
      margin: const EdgeInsets.all(SpacingTokens.space4),
      child: viewModel.isLoadingMore
          ? const Center(
              child: Padding(
                padding: EdgeInsets.all(SpacingTokens.space4),
                child: CircularProgressIndicator(
                  valueColor:
                      AlwaysStoppedAnimation<Color>(ColorTokens.primary),
                ),
              ),
            )
          : ElevatedButton(
              onPressed: viewModel.hasMoreData
                  ? () => viewModel.loadMoreUpcomingPlans()
                  : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorTokens.primary,
                foregroundColor: ColorTokens.onPrimary,
                padding:
                    const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                shape: const RoundedRectangleBorder(
                  borderRadius: ShapeTokens.borderRadiusMd,
                ),
                elevation: 2,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.refresh, size: 18),
                  const SizedBox(width: SpacingTokens.space2),
                  Text(
                    viewModel.hasMoreData ? '加载更多' : '没有更多数据',
                    style: TypographyTokens.labelLarge.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  // 构建活动图片
  Widget _buildActivityImage(FishingPlan activity, int index) {
    // 优先使用spotImages（如果后端提供）
    if (activity.spotImages != null && activity.spotImages!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: activity.spotImages!.first,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildDefaultBackground(index),
        errorWidget: (context, url, error) => _buildDefaultBackground(index),
      );
    }

    // 使用美观的默认背景
    return _buildDefaultBackground(index);
  }

  // 构建简化的默认背景
  Widget _buildDefaultBackground(int index) {
    return Container(
      decoration: BoxDecoration(
        color: _getActivityBackgroundColor(index),
      ),
      child: Center(
        child: Icon(
          _getActivityIcon(index),
          size: 60,
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
        ),
      ),
    );
  }

  // 获取简化的背景色
  Color _getActivityBackgroundColor(int index) {
    final colorScheme = Theme.of(context).colorScheme;

    final colors = [
      colorScheme.primaryContainer, // 主色容器
      colorScheme.secondaryContainer, // 次色容器
      colorScheme.tertiaryContainer, // 第三色容器
      colorScheme.surfaceContainerHigh, // 高对比表面
      colorScheme.primaryContainer.withValues(alpha: 0.7), // 主色淡化
      colorScheme.secondaryContainer.withValues(alpha: 0.7), // 次色淡化
      colorScheme.tertiaryContainer.withValues(alpha: 0.7), // 第三色淡化
      colorScheme.surfaceContainerHighest, // 最高对比表面
    ];

    return colors[index % colors.length];
  }

  // 获取活动图标
  IconData _getActivityIcon(int index) {
    final icons = [
      Icons.water,
      Icons.waves,
      Icons.anchor,
      Icons.sailing,
      Icons.pool,
      Icons.water_drop,
      Icons.terrain,
      Icons.landscape,
    ];

    return icons[index % icons.length];
  }

  String _getParticipationText(FishingPlan activity) {
    if (activity.hasJoined) {
      return '已参加';
    }

    if (activity.isOfficialActivity) {
      // 官方活动特殊处理
      if (!activity.isUpcoming) {
        return '活动结束';
      }
      if (activity.isFull) {
        return '名额已满';
      }
      return '立即报名';
    }

    if (activity.isOwner) {
      return '发起人';
    }

    if (!activity.isUpcoming) {
      return '活动结束';
    }

    if (activity.isFull) {
      return '名额已满';
    }

    return '立即参加';
  }

  bool _canInteractWithActivity(FishingPlan activity) {
    // 官方活动可以参与（如果满足条件）
    if (activity.isOfficialActivity) {
      return activity.isUpcoming && !activity.isFull && !activity.hasJoined;
    }

    // 用户活动：不是发起人且可以参与
    return !activity.isOwner && activity.canJoin;
  }
}
