import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/shared/widgets/login_required_dialog.dart';

class ActivityDetailPage extends StatefulWidget {
  final FishingPlan activity;

  const ActivityDetailPage({
    super.key,
    required this.activity,
  });

  @override
  State<ActivityDetailPage> createState() => _ActivityDetailPageState();
}

class _ActivityDetailPageState extends State<ActivityDetailPage> {
  late FishingPlan _activity;

  @override
  void initState() {
    super.initState();
    _activity = widget.activity;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          _buildSliverAppBar(),
          SliverToBoxAdapter(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildActivityHeader(),
                _buildActivityInfo(),
                _buildDescriptionSection(),
                _buildLocationSection(),
                _buildParticipantsSection(),
                _buildOrganizerSection(),
                const SizedBox(height: SpacingTokens.space20), // 为底部按钮留出空间
              ],
            ),
          ),
        ],
      ),
      bottomNavigationBar: _buildBottomActionBar(),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 250,
      pinned: true,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back),
        onPressed: () => Navigator.of(context).pop(),
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: _buildActivityImage(),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.favorite_border),
          onPressed: _handleFavorite,
        ),
        IconButton(
          icon: const Icon(Icons.share),
          onPressed: _handleShare,
        ),
      ],
    );
  }

  Widget _buildActivityImage() {
    // 使用钓点图片或默认渐变背景
    if (_activity.spotImages != null && _activity.spotImages!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: _activity.spotImages!.first,
        fit: BoxFit.cover,
        placeholder: (context, url) => _buildDefaultBackground(),
        errorWidget: (context, url, error) => _buildDefaultBackground(),
      );
    }
    return _buildDefaultBackground();
  }

  Widget _buildDefaultBackground() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ColorTokens.primary,
            ColorTokens.primaryContainer,
          ],
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              ColorTokens.transparent,
              ColorTokens.scrim.withValues(alpha: 0.3),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildActivityHeader() {
    return Padding(
      padding: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: ColorTokens.primary.withValues(alpha: 0.1),
                  borderRadius: ShapeTokens.borderRadiusXs,
                ),
                child: Text(
                  _activity.activityType ?? '钓鱼',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: ColorTokens.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              if (_activity.isOfficialActivity) ...[
                SpacingTokens.horizontalSpaceSm,
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 4),
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [ColorTokens.warning, ColorTokens.primary],
                    ),
                    borderRadius: ShapeTokens.borderRadiusXs,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.verified,
                          size: 10, color: ColorTokens.onPrimary),
                      const SizedBox(width: SpacingTokens.space1 / 2),
                      Text(
                        '官方',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: ColorTokens.onPrimary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              const Spacer(),
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: (_activity.price == null || _activity.price == '免费')
                      ? ColorTokens.success
                      : ColorTokens.warning,
                  borderRadius: ShapeTokens.borderRadiusXs,
                ),
                child: Text(
                  _activity.price ?? '免费',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: ColorTokens.onPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SpacingTokens.verticalSpaceSm,
          Text(
            _activity.title,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityInfo() {
    final dateFormat = DateFormat('MM月dd日 EEEE', 'zh_CN');
    final timeFormat = DateFormat('HH:mm');

    return Container(
      margin: SpacingTokens.paddingHorizontalMd,
      padding: SpacingTokens.paddingMd,
      decoration: const BoxDecoration(
        color: ColorTokens.surfaceContainerLowest,
        borderRadius: ShapeTokens.borderRadiusMd,
      ),
      child: Column(
        children: [
          _buildInfoRow(
            Icons.access_time,
            '时间',
            '${dateFormat.format(_activity.planDate)} ${timeFormat.format(_activity.planDate)}',
          ),
          SpacingTokens.verticalSpaceSm,
          _buildInfoRow(
            Icons.location_on,
            '地点',
            _activity.location,
          ),
          SpacingTokens.verticalSpaceSm,
          _buildInfoRow(
            Icons.group,
            '人数',
            '${_activity.currentParticipants}/${_activity.maxParticipants}人',
          ),
          if (_activity.weather != null && _activity.weather!.isNotEmpty) ...[
            SpacingTokens.verticalSpaceSm,
            _buildInfoRow(
              Icons.wb_sunny,
              '天气',
              '${_activity.weather} ${_activity.temperature ?? ''}',
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(icon, size: 20, color: ColorTokens.primary),
        SpacingTokens.horizontalSpaceSm,
        SizedBox(
          width: 50,
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ColorTokens.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: ColorTokens.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildDescriptionSection() {
    if (_activity.description.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '活动介绍',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
          SpacingTokens.verticalSpaceXs,
          Text(
            _activity.description,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              height: 1.5,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLocationSection() {
    return Padding(
      padding: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '活动地点',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
          SpacingTokens.verticalSpaceXs,
          Container(
            width: double.infinity,
            height: 120,
            decoration: const BoxDecoration(
              color: ColorTokens.surfaceContainerHigh,
              borderRadius: ShapeTokens.borderRadiusSm,
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.map,
                      size: 32, color: ColorTokens.onSurfaceVariant),
                  SpacingTokens.verticalSpaceXs,
                  Text(
                    '地图功能开发中',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: ColorTokens.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ),
          SpacingTokens.verticalSpaceXs,
          Text(
            _activity.location,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: ColorTokens.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParticipantsSection() {
    return Padding(
      padding: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '参与者 (${_activity.currentParticipants})',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              if (_activity.currentParticipants > 0)
                TextButton(
                  onPressed: () {
                    // TODO: 显示完整参与者列表
                  },
                  child: const Text('查看全部'),
                ),
            ],
          ),
          SpacingTokens.verticalSpaceXs,
          // TODO: 显示参与者头像列表
          Text(
            '暂无参与者',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOrganizerSection() {
    return Padding(
      padding: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '组织者',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
          SpacingTokens.verticalSpaceSm,
          Row(
            children: [
              Stack(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: _activity.isOfficialActivity
                        ? ColorTokens.warning.withValues(alpha: 0.2)
                        : ColorTokens.primary.withValues(alpha: 0.1),
                    child: _activity.isOfficialActivity
                        ? const Icon(
                            Icons.business,
                            size: 24,
                            color: ColorTokens.primary,
                          )
                        : Text(
                            _activity.ownerName.isNotEmpty
                                ? _activity.ownerName[0]
                                : 'U',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: ColorTokens.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                  ),
                  if (_activity.isOfficialActivity)
                    Positioned(
                      right: -2,
                      top: -2,
                      child: Container(
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: ColorTokens.warning,
                          shape: BoxShape.circle,
                          border:
                              Border.all(color: ColorTokens.surface, width: 2),
                        ),
                        child: const Icon(
                          Icons.verified,
                          size: 10,
                          color: ColorTokens.onWarning,
                        ),
                      ),
                    ),
                ],
              ),
              SpacingTokens.horizontalSpaceSm,
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _activity.ownerName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: _activity.isOfficialActivity
                            ? ColorTokens.primary
                            : ColorTokens.onSurface,
                      ),
                    ),
                    if (_activity.contactInfo != null &&
                        _activity.contactInfo!.isNotEmpty)
                      Text(
                        _activity.contactInfo!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: ColorTokens.onSurfaceVariant,
                        ),
                      ),
                  ],
                ),
              ),
              if (!_activity.isOfficialActivity && !_activity.isOwner)
                OutlinedButton(
                  onPressed: () {
                    // TODO: 关注/私信功能
                  },
                  child: const Text('关注'),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        boxShadow: ElevationTokens.elevation2,
      ),
      child: SafeArea(
        child: Row(
          children: [
            // 收藏按钮
            Container(
              width: 48,
              height: 48,
              margin: const EdgeInsets.only(right: SpacingTokens.space3),
              decoration: BoxDecoration(
                border: Border.all(color: ColorTokens.outline),
                borderRadius: BorderRadius.circular(24),
              ),
              child: IconButton(
                icon: Icon(
                  _activity.isBookmarked
                      ? Icons.bookmark
                      : Icons.bookmark_border,
                  color: _activity.isBookmarked
                      ? ColorTokens.primary
                      : ColorTokens.onSurfaceVariant,
                ),
                onPressed: _handleBookmark,
              ),
            ),
            // 主要操作按钮
            Expanded(
              child: SizedBox(
                height: 48,
                child: ElevatedButton(
                  onPressed:
                      _canInteractWithActivity() ? _handleJoinActivity : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _getButtonColor(),
                    foregroundColor: ColorTokens.onPrimary,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(24),
                    ),
                    elevation: 0,
                  ),
                  child: Text(
                    _getButtonText(),
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: ColorTokens.onPrimary,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool _canInteractWithActivity() {
    if (_activity.isOfficialActivity) {
      return _activity.isUpcoming && !_activity.isFull && !_activity.hasJoined;
    }
    return !_activity.isOwner && _activity.canJoin;
  }

  Color _getButtonColor() {
    if (_activity.hasJoined) return ColorTokens.surfaceContainerHighest;
    if (_activity.isOwner) return ColorTokens.success;
    if (!_activity.isUpcoming) return ColorTokens.surfaceContainerHighest;
    if (_activity.isFull) return ColorTokens.surfaceContainerHighest;
    return ColorTokens.primary;
  }

  String _getButtonText() {
    if (_activity.hasJoined) return '已参加';
    if (_activity.isOwner) return '我发起的';
    if (!_activity.isUpcoming) return '活动结束';
    if (_activity.isFull) return '名额已满';

    return _activity.isOfficialActivity ? '立即报名' : '立即参加';
  }

  Future<void> _handleJoinActivity() async {
    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);

    if (!authViewModel.isUserLoggedIn()) {
      await LoginRequiredDialog.showGeneral(context, action: '参与活动');
      return;
    }

    // 显示确认对话框
    final confirmed = await _showJoinConfirmDialog();
    if (!confirmed) return;

    try {
      final planViewModel =
          Provider.of<FishingPlanViewModel>(context, listen: false);
      await planViewModel.joinPlan(_activity.id);

      // 只更新本地详情页状态（ViewModel 会自动更新列表状态）
      setState(() {
        _activity = _activity.copyWith(
          hasJoined: true,
          currentParticipants: _activity.currentParticipants + 1,
        );
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('参与成功！'),
          backgroundColor: ColorTokens.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('参与失败：$e'),
          backgroundColor: ColorTokens.error,
        ),
      );
    }
  }

  Future<bool> _showJoinConfirmDialog() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            shape: const RoundedRectangleBorder(
                borderRadius: ShapeTokens.borderRadiusLg),
            title: const Text('确认参与'),
            content: Text('确定要参与「${_activity.title}」吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: ColorTokens.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8)),
                ),
                child: const Text('确认参与'),
              ),
            ],
          ),
        ) ??
        false;
  }

  Future<void> _handleFavorite() async {
    HapticFeedback.lightImpact();

    final authViewModel = Provider.of<AuthViewModel>(context, listen: false);
    if (!authViewModel.isUserLoggedIn()) {
      await LoginRequiredDialog.showGeneral(context, action: '收藏活动');
      return;
    }

    try {
      final planViewModel =
          Provider.of<FishingPlanViewModel>(context, listen: false);

      if (_activity.isBookmarked) {
        await planViewModel.removeBookmark(_activity.id);
      } else {
        await planViewModel.addBookmark(_activity.id);
      }

      setState(() {
        _activity = _activity.copyWith(isBookmarked: !_activity.isBookmarked);
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(_activity.isBookmarked ? '收藏成功' : '取消收藏成功'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('操作失败：$e'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Future<void> _handleBookmark() async {
    // 使用相同的收藏逻辑
    _handleFavorite();
  }

  void _handleShare() {
    final shareText = '''
🎣 ${_activity.title}

📍 地点：${_activity.location}
📅 时间：${DateFormat('MM月dd日 HH:mm', 'zh_CN').format(_activity.planDate)}
👥 人数：${_activity.currentParticipants}/${_activity.maxParticipants}人
💰 费用：${_activity.price ?? '免费'}

${_activity.description.isNotEmpty ? '${_activity.description}\n\n' : ''}组织者：${_activity.ownerName}

快来一起钓鱼吧！🐟''';

    Share.share(shareText);
  }
}

// 为FishingPlan添加copyWith方法的扩展
extension FishingPlanExtension on FishingPlan {
  FishingPlan copyWith({
    bool? hasJoined,
    int? currentParticipants,
    bool? isBookmarked,
  }) {
    return FishingPlan(
      id: id,
      title: title,
      location: location,
      planDate: planDate,
      timeRange: timeRange,
      currentParticipants: currentParticipants ?? this.currentParticipants,
      maxParticipants: maxParticipants,
      weather: weather,
      temperature: temperature,
      description: description,
      isOwner: isOwner,
      status: status,
      ownerId: ownerId,
      owner: owner,
      createdAt: createdAt,
      updatedAt: updatedAt,
      spotId: spotId,
      contactInfo: contactInfo,
      isPublic: isPublic,
      participants: participants,
      hasJoined: hasJoined ?? this.hasJoined,
      distance: distance,
      activityType: activityType,
      price: price,
      isHot: isHot,
      isFollowingOwner: isFollowingOwner,
      isBookmarked: isBookmarked ?? this.isBookmarked,
      friendsParticipated: friendsParticipated,
      participantAvatars: participantAvatars,
      spotImages: spotImages,
      source: source,
    );
  }
}
