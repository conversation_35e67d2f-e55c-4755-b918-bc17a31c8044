import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/di/injection.dart';

import '../view_models/create_activity_view_model.dart';
import '../widgets/create_activity/basic_info_widget.dart';
import '../widgets/create_activity/details_input_widget.dart';
import '../widgets/create_activity/location_selector_widget.dart';
import '../widgets/create_activity/participants_settings_widget.dart';
import '../widgets/create_activity/time_selector_widget.dart';

/// 创建钓鱼活动页面
class CreateActivityPage extends StatelessWidget {
  const CreateActivityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => CreateActivityViewModel(
        dio: getIt<Dio>(),
      )..initializeData(),
      child: const _CreateActivityView(),
    );
  }
}

class _CreateActivityView extends StatelessWidget {
  const _CreateActivityView();

  @override
  Widget build(BuildContext context) {
    return Consumer<CreateActivityViewModel>(
      builder: (context, viewModel, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          appBar: AppBar(
            title: const Text('创建钓鱼活动'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            elevation: 0,
          ),
          body: viewModel.isLoading
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  padding: SpacingTokens.paddingMd,
                  child: Form(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.stretch,
                      children: [
                        if (viewModel.errorMessage != null) ...[
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color:
                                  Theme.of(context).colorScheme.errorContainer,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .error
                                      .withValues(alpha: 0.3)),
                            ),
                            child: Row(
                              children: [
                                Icon(Icons.error_outline,
                                    color: Theme.of(context).colorScheme.error),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    viewModel.errorMessage!,
                                    style: TextStyle(
                                        color: Theme.of(context)
                                            .colorScheme
                                            .onErrorContainer),
                                  ),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.close),
                                  onPressed: viewModel.clearError,
                                  color: Theme.of(context).colorScheme.error,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: SpacingTokens.space4),
                        ],
                        _buildSection(
                          '基本信息',
                          BasicInfoWidget(
                            titleController: viewModel.titleController,
                            descriptionController:
                                viewModel.descriptionController,
                          ),
                        ),
                        _buildSection(
                          '活动地点',
                          LocationSelectorWidget(
                            selectedFishingSpot: viewModel.selectedFishingSpot,
                            selectedSpotData: viewModel.selectedSpotData,
                            onSpotSelection: () =>
                                _selectFishingSpot(context, viewModel),
                          ),
                        ),
                        _buildSection(
                          '活动时间',
                          TimeSelectorWidget(
                            selectedDate: viewModel.selectedDate,
                            selectedTime: viewModel.selectedTime,
                            selectedTimeSlot: viewModel.selectedTimeSlot,
                            timeSlots: viewModel.timeSlots,
                            onDateSelection: () =>
                                _selectDate(context, viewModel),
                            onTimeSelection: () =>
                                _selectTime(context, viewModel),
                            onTimeSlotChanged: viewModel.setSelectedTimeSlot,
                            getTomorrowDate: viewModel.getTomorrowDate,
                            getNextWeekendDate: viewModel.getNextWeekendDate,
                            formatTime: viewModel.formatTime,
                          ),
                        ),
                        _buildSection(
                          '参与设置',
                          ParticipantsSettingsWidget(
                            maxParticipantsController:
                                viewModel.maxParticipantsController,
                            selectedDifficulty: viewModel.selectedDifficulty,
                            difficultyLevels: viewModel.difficultyLevels,
                            onDifficultyChanged:
                                viewModel.setSelectedDifficulty,
                          ),
                        ),
                        _buildSection(
                          '详细信息',
                          DetailsInputWidget(
                            contactInfoController:
                                viewModel.contactInfoController,
                            equipmentController: viewModel.equipmentController,
                          ),
                        ),
                        const SizedBox(height: 32),
                        SizedBox(
                          height: 50,
                          child: ElevatedButton(
                            onPressed:
                                viewModel.isSubmitting || !viewModel.isFormValid
                                    ? null
                                    : () => _submitActivity(context, viewModel),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: ColorTokens.primary,
                              foregroundColor:
                                  Theme.of(context).colorScheme.onPrimary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 0,
                            ),
                            child: viewModel.isSubmitting
                                ? SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Theme.of(context)
                                              .colorScheme
                                              .onPrimary),
                                    ),
                                  )
                                : Text(
                                    '创建活动',
                                    style: Theme.of(context)
                                        .textTheme
                                        .labelLarge
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                          ),
                        ),
                        const SizedBox(height: SpacingTokens.space4),
                      ],
                    ),
                  ),
                ),
        );
      },
    );
  }

  Widget _buildSection(String title, Widget child) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
        ),
        const SizedBox(height: 12),
        Container(
          padding: SpacingTokens.paddingMd,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context)
                    .colorScheme
                    .shadow
                    .withValues(alpha: 0.05),
                blurRadius: 10,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: child,
        ),
        const SizedBox(height: SpacingTokens.space5),
      ],
    );
  }

  Future<void> _selectDate(
      BuildContext context, CreateActivityViewModel viewModel) async {
    final picked = await showDatePicker(
      context: context,
      initialDate:
          viewModel.selectedDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      viewModel.setSelectedDate(picked);
    }
  }

  Future<void> _selectTime(
      BuildContext context, CreateActivityViewModel viewModel) async {
    final picked = await showTimePicker(
      context: context,
      initialTime:
          viewModel.selectedTime ?? const TimeOfDay(hour: 9, minute: 0),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: Theme.of(context).colorScheme.primary,
                ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null) {
      viewModel.setSelectedTime(picked);
    }
  }

  Future<void> _selectFishingSpot(
      BuildContext context, CreateActivityViewModel viewModel) async {
    // TODO: Implement fishing spot selection
    // This would typically show a modal or navigate to a spot selection page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('钓点选择功能开发中')),
    );
  }

  Future<void> _submitActivity(
      BuildContext context, CreateActivityViewModel viewModel) async {
    final success = await viewModel.submitActivity();

    if (success && context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('活动创建成功！'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
      Navigator.of(context).pop();
    } else if (context.mounted && viewModel.errorMessage != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(viewModel.errorMessage!),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }
}
