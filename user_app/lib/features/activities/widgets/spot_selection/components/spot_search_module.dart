import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';
import 'package:user_app/features/activities/services/spot_search_index_service.dart';

/// 钓点搜索模块组件
/// 
/// 包含搜索输入、搜索建议、搜索结果等功能
class SpotSearchModule extends StatefulWidget {
  final Function(String) onSearch;
  final Function(SpotRecommendationData) onSpotSelected;
  final List<SpotRecommendationData> searchResults;
  final List<String> searchSuggestions;
  final bool isSearching;
  final bool showSuggestions;
  final String searchQuery;

  const SpotSearchModule({
    super.key,
    required this.onSearch,
    required this.onSpotSelected,
    required this.searchResults,
    required this.searchSuggestions,
    required this.isSearching,
    required this.showSuggestions,
    required this.searchQuery,
  });

  @override
  State<SpotSearchModule> createState() => _SpotSearchModuleState();
}

class _SpotSearchModuleState extends State<SpotSearchModule> {
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _searchController.text = widget.searchQuery;
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  void _onSearchChanged() {
    widget.onSearch(_searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 搜索输入框
        _buildSearchField(),
        
        // 搜索建议
        if (widget.showSuggestions && widget.searchSuggestions.isNotEmpty)
          _buildSearchSuggestions(),
        
        // 搜索结果
        if (widget.searchQuery.isNotEmpty)
          Expanded(child: _buildSearchResults()),
      ],
    );
  }

  Widget _buildSearchField() {
    return Container(
      margin: const EdgeInsets.all(SpacingTokens.space4),
      child: DSSearchField(
        controller: _searchController,
        hintText: '搜索钓点名称、位置或特色...',
        onChanged: (value) => widget.onSearch(value),
        onClear: () {
          _searchController.clear();
          widget.onSearch('');
        },
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: SpacingTokens.space2,
            offset: const Offset(0, SpacingTokens.space1),
          ),
        ],
      ),
      child: Column(
        children: widget.searchSuggestions.take(5).map((suggestion) {
          return SpotSearchSuggestionTile(
            suggestion: suggestion,
            query: widget.searchQuery,
            onTap: () {
              _searchController.text = suggestion;
              widget.onSearch(suggestion);
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildSearchResults() {
    if (widget.isSearching) {
      return const SpotSearchLoadingState();
    }

    if (widget.searchResults.isEmpty) {
      return SpotSearchEmptyState(
        query: widget.searchQuery,
        onClearSearch: () {
          _searchController.clear();
          widget.onSearch('');
        },
      );
    }

    return SpotSearchResultsList(
      results: widget.searchResults,
      query: widget.searchQuery,
      onSpotSelected: widget.onSpotSelected,
    );
  }
}

/// 搜索建议瓦片组件
class SpotSearchSuggestionTile extends StatelessWidget {
  final String suggestion;
  final String query;
  final VoidCallback onTap;

  const SpotSearchSuggestionTile({
    super.key,
    required this.suggestion,
    required this.query,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(SpacingTokens.space3),
          child: Row(
            children: [
              Icon(
                Icons.search_rounded,
                size: SpacingTokens.space4,
                color: theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: SpacingTokens.space3),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                    ),
                    children: _buildHighlightedText(suggestion, query, theme),
                  ),
                ),
              ),
              Icon(
                Icons.north_west_rounded,
                size: SpacingTokens.space4,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ],
          ),
        ),
      ),
    );
  }

  List<TextSpan> _buildHighlightedText(String text, String query, ThemeData theme) {
    if (query.isEmpty) {
      return [TextSpan(text: text)];
    }

    final List<TextSpan> spans = [];
    final String lowerText = text.toLowerCase();
    final String lowerQuery = query.toLowerCase();
    int start = 0;

    while (true) {
      final int index = lowerText.indexOf(lowerQuery, start);
      if (index == -1) {
        if (start < text.length) {
          spans.add(TextSpan(text: text.substring(start)));
        }
        break;
      }

      if (index > start) {
        spans.add(TextSpan(text: text.substring(start, index)));
      }

      spans.add(TextSpan(
        text: text.substring(index, index + query.length),
        style: TextStyle(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ));

      start = index + query.length;
    }

    return spans;
  }
}

/// 搜索加载状态
class SpotSearchLoadingState extends StatelessWidget {
  const SpotSearchLoadingState({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            '搜索中...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}

/// 搜索空状态
class SpotSearchEmptyState extends StatelessWidget {
  final String query;
  final VoidCallback onClearSearch;

  const SpotSearchEmptyState({
    super.key,
    required this.query,
    required this.onClearSearch,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off_rounded,
            size: SpacingTokens.space16,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            '没有找到相关钓点',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            '试试搜索 "${query.length > 20 ? '${query.substring(0, 20)}...' : query}"',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: SpacingTokens.space6),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextButton.icon(
                onPressed: onClearSearch,
                icon: const Icon(Icons.clear_rounded),
                label: const Text('清空搜索'),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

/// 搜索结果列表
class SpotSearchResultsList extends StatelessWidget {
  final List<SpotRecommendationData> results;
  final String query;
  final Function(SpotRecommendationData) onSpotSelected;

  const SpotSearchResultsList({
    super.key,
    required this.results,
    required this.query,
    required this.onSpotSelected,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      itemCount: results.length,
      itemBuilder: (context, index) {
        final spot = results[index];
        return Container(
          margin: const EdgeInsets.only(bottom: SpacingTokens.space3),
          child: SpotSearchResultCard(
            spot: spot,
            query: query,
            onTap: () => onSpotSelected(spot),
          ),
        );
      },
    );
  }
}

/// 搜索结果卡片
class SpotSearchResultCard extends StatelessWidget {
  final SpotRecommendationData spot;
  final String query;
  final VoidCallback onTap;

  const SpotSearchResultCard({
    super.key,
    required this.spot,
    required this.query,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: SpacingTokens.space2,
            offset: const Offset(0, SpacingTokens.space1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space4),
            child: Row(
              children: [
                // 钓点图片
                Container(
                  width: SpacingTokens.space12,
                  height: SpacingTokens.space12,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(SpacingTokens.space2),
                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  ),
                  child: spot.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(SpacingTokens.space2),
                          child: DSImageDisplay.network(
                            spot.imageUrl!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Icon(
                          Icons.place_rounded,
                          color: theme.colorScheme.primary,
                          size: SpacingTokens.space6,
                        ),
                ),
                
                const SizedBox(width: SpacingTokens.space3),
                
                // 钓点信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 名称
                      RichText(
                        text: TextSpan(
                          style: theme.textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                          children: _buildHighlightedText(
                            spot.name ?? '未知钓点',
                            query,
                            theme,
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: SpacingTokens.space1),
                      
                      // 位置
                      if (spot.address != null) ...[
                        Text(
                          spot.address!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                        const SizedBox(height: SpacingTokens.space1),
                      ],
                      
                      // 距离和评分
                      Row(
                        children: [
                          if (spot.distance != null) ...[
                            Icon(
                              Icons.navigation_rounded,
                              size: SpacingTokens.space3,
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            const SizedBox(width: SpacingTokens.space1),
                            Text(
                              '${(spot.distance! / 1000).toStringAsFixed(1)}km',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                          
                          if (spot.distance != null && spot.rating != null)
                            const SizedBox(width: SpacingTokens.space3),
                          
                          if (spot.rating != null) ...[
                            Icon(
                              Icons.star_rounded,
                              size: SpacingTokens.space3,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: SpacingTokens.space1),
                            Text(
                              spot.rating!.toStringAsFixed(1),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 箭头
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: SpacingTokens.space4,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<TextSpan> _buildHighlightedText(String text, String query, ThemeData theme) {
    if (query.isEmpty) {
      return [TextSpan(text: text)];
    }

    final List<TextSpan> spans = [];
    final String lowerText = text.toLowerCase();
    final String lowerQuery = query.toLowerCase();
    int start = 0;

    while (true) {
      final int index = lowerText.indexOf(lowerQuery, start);
      if (index == -1) {
        if (start < text.length) {
          spans.add(TextSpan(text: text.substring(start)));
        }
        break;
      }

      if (index > start) {
        spans.add(TextSpan(text: text.substring(start, index)));
      }

      spans.add(TextSpan(
        text: text.substring(index, index + query.length),
        style: TextStyle(
          color: theme.colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ));

      start = index + query.length;
    }

    return spans;
  }
}

// 临时数据类定义（应该从其他地方导入）
class SpotRecommendationData {
  final String? name;
  final String? address;
  final String? imageUrl;
  final double? distance;
  final double? rating;
  final Map<String, dynamic> rawData;

  SpotRecommendationData({
    this.name,
    this.address,
    this.imageUrl,
    this.distance,
    this.rating,
    required this.rawData,
  });
}