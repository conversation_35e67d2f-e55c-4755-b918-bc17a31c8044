import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';
import 'spot_search_module.dart'; // 临时导入数据类型

/// 钓点收藏和历史模块组件
/// 
/// 包含收藏钓点和选择历史功能
class SpotFavoritesModule extends StatelessWidget {
  final List<SpotRecommendationData> favoriteSpots;
  final List<SpotRecommendationData> historySpots;
  final bool isLoading;
  final String? error;
  final Function(SpotRecommendationData) onSpotSelected;
  final Function(SpotRecommendationData) onToggleFavorite;
  final VoidCallback? onClearHistory;
  final VoidCallback? onRefresh;

  const SpotFavoritesModule({
    super.key,
    required this.favoriteSpots,
    required this.historySpots,
    required this.isLoading,
    this.error,
    required this.onSpotSelected,
    required this.onToggleFavorite,
    this.onClearHistory,
    this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const SpotFavoritesLoadingState();
    }

    if (error != null) {
      return SpotFavoritesErrorState(
        error: error!,
        onRetry: onRefresh,
      );
    }

    return Column(
      children: [
        // 标签页切换
        _buildTabBar(context),
        
        const SizedBox(height: SpacingTokens.space4),
        
        // 内容区域
        Expanded(
          child: _buildTabContent(context),
        ),
      ],
    );
  }

  Widget _buildTabBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      child: Row(
        children: [
          Expanded(
            child: _TabButton(
              label: '收藏',
              count: favoriteSpots.length,
              icon: Icons.favorite_rounded,
              isActive: true, // 这里应该有状态管理
              onTap: () {
                // 切换到收藏标签
              },
            ),
          ),
          const SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: _TabButton(
              label: '历史',
              count: historySpots.length,
              icon: Icons.history_rounded,
              isActive: false, // 这里应该有状态管理
              onTap: () {
                // 切换到历史标签
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabContent(BuildContext context) {
    // 这里应该根据当前选中的标签显示对应内容
    // 为了简化，这里同时显示两个区域
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 收藏区域
          if (favoriteSpots.isNotEmpty) ...[
            _buildFavoriteSection(context),
            const SizedBox(height: SpacingTokens.space6),
          ],
          
          // 历史区域
          if (historySpots.isNotEmpty) ...[
            _buildHistorySection(context),
          ],
          
          // 空状态
          if (favoriteSpots.isEmpty && historySpots.isEmpty)
            const SpotFavoritesEmptyState(),
        ],
      ),
    );
  }

  Widget _buildFavoriteSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DSSectionTitle(
          title: '收藏钓点',
          trailing: favoriteSpots.length > 3
              ? TextButton(
                  onPressed: () {
                    // 查看全部收藏
                  },
                  child: const Text('查看全部'),
                )
              : null,
        ),
        
        const SizedBox(height: SpacingTokens.space3),
        
        SpotFavoritesList(
          spots: favoriteSpots.take(3).toList(),
          onSpotSelected: onSpotSelected,
          onToggleFavorite: onToggleFavorite,
        ),
      ],
    );
  }

  Widget _buildHistorySection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DSSectionTitle(
          title: '选择历史',
          trailing: onClearHistory != null
              ? TextButton(
                  onPressed: onClearHistory,
                  child: Text(
                    '清空历史',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                )
              : null,
        ),
        
        const SizedBox(height: SpacingTokens.space3),
        
        SpotHistoryList(
          spots: historySpots.take(5).toList(),
          onSpotSelected: onSpotSelected,
        ),
      ],
    );
  }
}

/// 标签按钮组件
class _TabButton extends StatelessWidget {
  final String label;
  final int count;
  final IconData icon;
  final bool isActive;
  final VoidCallback onTap;

  const _TabButton({
    required this.label,
    required this.count,
    required this.icon,
    required this.isActive,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: isActive
            ? theme.colorScheme.primaryContainer
            : theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: isActive
              ? theme.colorScheme.primary.withValues(alpha: 0.3)
              : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space3),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  icon,
                  size: SpacingTokens.space4,
                  color: isActive
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: SpacingTokens.space2),
                Text(
                  label,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: isActive
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurfaceVariant,
                    fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  ),
                ),
                if (count > 0) ...[
                  const SizedBox(width: SpacingTokens.space2),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: SpacingTokens.space2,
                      vertical: SpacingTokens.space1,
                    ),
                    decoration: BoxDecoration(
                      color: isActive
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                      borderRadius: BorderRadius.circular(SpacingTokens.space2),
                    ),
                    child: Text(
                      count > 99 ? '99+' : count.toString(),
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: isActive
                            ? theme.colorScheme.onPrimary
                            : theme.colorScheme.surface,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 收藏钓点列表
class SpotFavoritesList extends StatelessWidget {
  final List<SpotRecommendationData> spots;
  final Function(SpotRecommendationData) onSpotSelected;
  final Function(SpotRecommendationData) onToggleFavorite;

  const SpotFavoritesList({
    super.key,
    required this.spots,
    required this.onSpotSelected,
    required this.onToggleFavorite,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: spots.length,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: SpacingTokens.space3),
          child: SpotFavoriteCard(
            spot: spots[index],
            onTap: () => onSpotSelected(spots[index]),
            onToggleFavorite: () => onToggleFavorite(spots[index]),
          ),
        );
      },
    );
  }
}

/// 历史钓点列表
class SpotHistoryList extends StatelessWidget {
  final List<SpotRecommendationData> spots;
  final Function(SpotRecommendationData) onSpotSelected;

  const SpotHistoryList({
    super.key,
    required this.spots,
    required this.onSpotSelected,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: spots.length,
      itemBuilder: (context, index) {
        final spot = spots[index];
        return Container(
          margin: const EdgeInsets.only(bottom: SpacingTokens.space2),
          child: SpotHistoryCard(
            spot: spot,
            onTap: () => onSpotSelected(spot),
            // 可以添加最后使用时间显示
            lastUsed: DateTime.now().subtract(Duration(days: index + 1)),
          ),
        );
      },
    );
  }
}

/// 收藏钓点卡片
class SpotFavoriteCard extends StatelessWidget {
  final SpotRecommendationData spot;
  final VoidCallback onTap;
  final VoidCallback onToggleFavorite;

  const SpotFavoriteCard({
    super.key,
    required this.spot,
    required this.onTap,
    required this.onToggleFavorite,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: SpacingTokens.space2,
            offset: const Offset(0, SpacingTokens.space1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space4),
            child: Row(
              children: [
                // 钓点图片
                Container(
                  width: SpacingTokens.space12,
                  height: SpacingTokens.space12,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(SpacingTokens.space2),
                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  ),
                  child: spot.imageUrl != null
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(SpacingTokens.space2),
                          child: DSImageDisplay.network(
                            spot.imageUrl!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Icon(
                          Icons.place_rounded,
                          color: theme.colorScheme.primary,
                          size: SpacingTokens.space6,
                        ),
                ),
                
                const SizedBox(width: SpacingTokens.space3),
                
                // 钓点信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 名称
                      Text(
                        spot.name ?? '未知钓点',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      if (spot.address != null) ...[
                        const SizedBox(height: SpacingTokens.space1),
                        Text(
                          spot.address!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                      
                      const SizedBox(height: SpacingTokens.space2),
                      
                      // 距离和评分
                      Row(
                        children: [
                          if (spot.distance != null) ...[
                            Icon(
                              Icons.navigation_rounded,
                              size: SpacingTokens.space3,
                              color: theme.colorScheme.primary,
                            ),
                            const SizedBox(width: SpacingTokens.space1),
                            Text(
                              '${(spot.distance! / 1000).toStringAsFixed(1)}km',
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.primary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                          
                          if (spot.distance != null && spot.rating != null)
                            const SizedBox(width: SpacingTokens.space3),
                          
                          if (spot.rating != null) ...[
                            Icon(
                              Icons.star_rounded,
                              size: SpacingTokens.space3,
                              color: Colors.amber,
                            ),
                            const SizedBox(width: SpacingTokens.space1),
                            Text(
                              spot.rating!.toStringAsFixed(1),
                              style: theme.textTheme.bodySmall?.copyWith(
                                color: theme.colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 收藏按钮
                IconButton(
                  onPressed: onToggleFavorite,
                  icon: Icon(
                    Icons.favorite_rounded,
                    color: Colors.red,
                    size: SpacingTokens.space5,
                  ),
                  tooltip: '取消收藏',
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 历史钓点卡片
class SpotHistoryCard extends StatelessWidget {
  final SpotRecommendationData spot;
  final VoidCallback onTap;
  final DateTime lastUsed;

  const SpotHistoryCard({
    super.key,
    required this.spot,
    required this.onTap,
    required this.lastUsed,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space3),
            child: Row(
              children: [
                // 历史图标
                Container(
                  padding: const EdgeInsets.all(SpacingTokens.space2),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.secondaryContainer,
                    borderRadius: BorderRadius.circular(SpacingTokens.space2),
                  ),
                  child: Icon(
                    Icons.history_rounded,
                    size: SpacingTokens.space4,
                    color: theme.colorScheme.onSecondaryContainer,
                  ),
                ),
                
                const SizedBox(width: SpacingTokens.space3),
                
                // 钓点信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        spot.name ?? '未知钓点',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: SpacingTokens.space1),
                      
                      Text(
                        _formatLastUsed(lastUsed),
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // 距离
                if (spot.distance != null) ...[
                  Text(
                    '${(spot.distance! / 1000).toStringAsFixed(1)}km',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(width: SpacingTokens.space2),
                ],
                
                // 箭头
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: SpacingTokens.space3,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  String _formatLastUsed(DateTime lastUsed) {
    final now = DateTime.now();
    final difference = now.difference(lastUsed);

    if (difference.inMinutes < 1) {
      return '刚刚使用';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${lastUsed.month}月${lastUsed.day}日';
    }
  }
}

/// 收藏和历史加载状态
class SpotFavoritesLoadingState extends StatelessWidget {
  const SpotFavoritesLoadingState({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 标签页骨架
        Container(
          margin: const EdgeInsets.all(SpacingTokens.space4),
          child: Row(
            children: [
              Expanded(child: _buildTabSkeleton(context)),
              const SizedBox(width: SpacingTokens.space3),
              Expanded(child: _buildTabSkeleton(context)),
            ],
          ),
        ),
        
        const SizedBox(height: SpacingTokens.space4),
        
        // 内容骨架
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
            itemCount: 3,
            itemBuilder: (context, index) => Container(
              margin: const EdgeInsets.only(bottom: SpacingTokens.space3),
              child: _buildCardSkeleton(context),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTabSkeleton(BuildContext context) {
    return Container(
      height: SpacingTokens.space12,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
      ),
    );
  }

  Widget _buildCardSkeleton(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          // 图片骨架
          Container(
            width: SpacingTokens.space12,
            height: SpacingTokens.space12,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(SpacingTokens.space2),
            ),
          ),
          
          const SizedBox(width: SpacingTokens.space3),
          
          // 文字骨架
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: SpacingTokens.space4,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(SpacingTokens.space1),
                  ),
                ),
                const SizedBox(height: SpacingTokens.space2),
                Container(
                  height: SpacingTokens.space3,
                  width: double.infinity * 0.6,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(SpacingTokens.space1),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// 收藏和历史错误状态
class SpotFavoritesErrorState extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const SpotFavoritesErrorState({
    super.key,
    required this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: SpacingTokens.space16,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            '加载失败',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            error,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: SpacingTokens.space6),
            FilledButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
}

/// 收藏和历史空状态
class SpotFavoritesEmptyState extends StatelessWidget {
  const SpotFavoritesEmptyState({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bookmark_border_rounded,
            size: SpacingTokens.space16,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            '暂无收藏和历史',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            '收藏喜欢的钓点或选择钓点后\n这里就会显示相关记录',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}