import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';
import 'spot_search_module.dart'; // 临时导入，实际应该从服务层导入

/// 钓点推荐模块组件
/// 
/// 包含智能推荐、热门钓点、最近使用等推荐功能
class SpotRecommendationModule extends StatelessWidget {
  final List<SpotRecommendationData> recommendedSpots;
  final List<SpotRecommendationData> recentSpots;
  final bool isLoading;
  final String? error;
  final Function(SpotRecommendationData) onSpotSelected;
  final VoidCallback? onRefresh;
  final VoidCallback? onViewAll;

  const SpotRecommendationModule({
    super.key,
    required this.recommendedSpots,
    required this.recentSpots,
    required this.isLoading,
    this.error,
    required this.onSpotSelected,
    this.onRefresh,
    this.onViewAll,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const SpotRecommendationLoadingState();
    }

    if (error != null) {
      return SpotRecommendationErrorState(
        error: error!,
        onRetry: onRefresh,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 智能推荐区域
        if (recommendedSpots.isNotEmpty) ...[
          _buildSmartRecommendations(context),
          const SizedBox(height: SpacingTokens.space6),
        ],

        // 最近使用区域
        if (recentSpots.isNotEmpty) ...[
          _buildRecentSpots(context),
          const SizedBox(height: SpacingTokens.space6),
        ],

        // 空状态
        if (recommendedSpots.isEmpty && recentSpots.isEmpty)
          const SpotRecommendationEmptyState(),
      ],
    );
  }

  Widget _buildSmartRecommendations(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 区域标题
        DSSectionTitle(
          title: '智能推荐',
          trailing: onViewAll != null
              ? TextButton(
                  onPressed: onViewAll,
                  child: const Text('查看更多'),
                )
              : null,
        ),

        const SizedBox(height: SpacingTokens.space3),

        // 推荐列表
        SpotRecommendationGrid(
          spots: recommendedSpots.take(6).toList(),
          onSpotSelected: onSpotSelected,
        ),
      ],
    );
  }

  Widget _buildRecentSpots(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 区域标题
        const DSSectionTitle(
          title: '最近使用',
          decoratorColor: Colors.orange,
        ),

        const SizedBox(height: SpacingTokens.space3),

        // 最近列表
        SpotRecentList(
          spots: recentSpots.take(3).toList(),
          onSpotSelected: onSpotSelected,
        ),
      ],
    );
  }
}

/// 推荐加载状态
class SpotRecommendationLoadingState extends StatelessWidget {
  const SpotRecommendationLoadingState({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 智能推荐骨架屏
        const DSSectionTitle(title: '智能推荐'),
        const SizedBox(height: SpacingTokens.space3),
        _buildSkeletonGrid(),
        
        const SizedBox(height: SpacingTokens.space6),
        
        // 最近使用骨架屏
        const DSSectionTitle(title: '最近使用'),
        const SizedBox(height: SpacingTokens.space3),
        _buildSkeletonList(),
      ],
    );
  }

  Widget _buildSkeletonGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: SpacingTokens.space3,
        crossAxisSpacing: SpacingTokens.space3,
        childAspectRatio: 1.2,
      ),
      itemCount: 4,
      itemBuilder: (context, index) => const _SkeletonSpotCard(),
    );
  }

  Widget _buildSkeletonList() {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      itemCount: 3,
      itemBuilder: (context, index) => Container(
        margin: const EdgeInsets.only(bottom: SpacingTokens.space3),
        child: const _SkeletonSpotListItem(),
      ),
    );
  }
}

/// 推荐错误状态
class SpotRecommendationErrorState extends StatelessWidget {
  final String error;
  final VoidCallback? onRetry;

  const SpotRecommendationErrorState({
    super.key,
    required this.error,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline_rounded,
            size: SpacingTokens.space16,
            color: theme.colorScheme.error,
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            '加载失败',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.error,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            error,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          if (onRetry != null) ...[
            const SizedBox(height: SpacingTokens.space6),
            FilledButton.icon(
              onPressed: onRetry,
              icon: const Icon(Icons.refresh_rounded),
              label: const Text('重试'),
            ),
          ],
        ],
      ),
    );
  }
}

/// 推荐空状态
class SpotRecommendationEmptyState extends StatelessWidget {
  const SpotRecommendationEmptyState({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space8),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.explore_off_rounded,
            size: SpacingTokens.space16,
            color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.5),
          ),
          const SizedBox(height: SpacingTokens.space4),
          Text(
            '暂无推荐钓点',
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            '系统还在学习您的偏好\n请稍后再试或手动搜索钓点',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}

/// 推荐钓点网格
class SpotRecommendationGrid extends StatelessWidget {
  final List<SpotRecommendationData> spots;
  final Function(SpotRecommendationData) onSpotSelected;

  const SpotRecommendationGrid({
    super.key,
    required this.spots,
    required this.onSpotSelected,
  });

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: SpacingTokens.space3,
        crossAxisSpacing: SpacingTokens.space3,
        childAspectRatio: 1.2,
      ),
      itemCount: spots.length,
      itemBuilder: (context, index) {
        return SpotRecommendationCard(
          spot: spots[index],
          onTap: () => onSpotSelected(spots[index]),
        );
      },
    );
  }
}

/// 最近使用列表
class SpotRecentList extends StatelessWidget {
  final List<SpotRecommendationData> spots;
  final Function(SpotRecommendationData) onSpotSelected;

  const SpotRecentList({
    super.key,
    required this.spots,
    required this.onSpotSelected,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      itemCount: spots.length,
      itemBuilder: (context, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: SpacingTokens.space3),
          child: SpotRecentCard(
            spot: spots[index],
            onTap: () => onSpotSelected(spots[index]),
          ),
        );
      },
    );
  }
}

/// 推荐钓点卡片
class SpotRecommendationCard extends StatelessWidget {
  final SpotRecommendationData spot;
  final VoidCallback onTap;

  const SpotRecommendationCard({
    super.key,
    required this.spot,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: SpacingTokens.space2,
            offset: const Offset(0, SpacingTokens.space1),
          ),
        ],
      ),
      child: Material(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 钓点图片
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(SpacingTokens.space3),
                    ),
                    color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  ),
                  child: spot.imageUrl != null
                      ? ClipRRect(
                          borderRadius: const BorderRadius.vertical(
                            top: Radius.circular(SpacingTokens.space3),
                          ),
                          child: DSImageDisplay.network(
                            spot.imageUrl!,
                            fit: BoxFit.cover,
                          ),
                        )
                      : Icon(
                          Icons.place_rounded,
                          color: theme.colorScheme.primary,
                          size: SpacingTokens.space8,
                        ),
                ),
              ),
              
              // 钓点信息
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(SpacingTokens.space3),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 名称
                      Text(
                        spot.name ?? '未知钓点',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: SpacingTokens.space1),
                      
                      // 位置和评分
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (spot.address != null) ...[
                              Text(
                                spot.address!,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: theme.colorScheme.onSurfaceVariant,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                            ],
                            
                            // 距离和评分
                            Row(
                              children: [
                                if (spot.distance != null) ...[
                                  Icon(
                                    Icons.navigation_rounded,
                                    size: SpacingTokens.space3,
                                    color: theme.colorScheme.primary,
                                  ),
                                  const SizedBox(width: SpacingTokens.space1),
                                  Flexible(
                                    child: Text(
                                      '${(spot.distance! / 1000).toStringAsFixed(1)}km',
                                      style: theme.textTheme.bodySmall?.copyWith(
                                        color: theme.colorScheme.primary,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ],
                                
                                if (spot.distance != null && spot.rating != null)
                                  const SizedBox(width: SpacingTokens.space2),
                                
                                if (spot.rating != null) ...[
                                  Icon(
                                    Icons.star_rounded,
                                    size: SpacingTokens.space3,
                                    color: Colors.amber,
                                  ),
                                  const SizedBox(width: SpacingTokens.space1),
                                  Text(
                                    spot.rating!.toStringAsFixed(1),
                                    style: theme.textTheme.bodySmall?.copyWith(
                                      color: theme.colorScheme.onSurfaceVariant,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 最近使用卡片
class SpotRecentCard extends StatelessWidget {
  final SpotRecommendationData spot;
  final VoidCallback onTap;

  const SpotRecentCard({
    super.key,
    required this.spot,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(SpacingTokens.space3),
          child: Padding(
            padding: const EdgeInsets.all(SpacingTokens.space4),
            child: Row(
              children: [
                // 最近使用标识
                Container(
                  padding: const EdgeInsets.all(SpacingTokens.space2),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(SpacingTokens.space2),
                  ),
                  child: Icon(
                    Icons.history_rounded,
                    color: Colors.orange,
                    size: SpacingTokens.space4,
                  ),
                ),
                
                const SizedBox(width: SpacingTokens.space3),
                
                // 钓点信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        spot.name ?? '未知钓点',
                        style: theme.textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (spot.address != null) ...[
                        const SizedBox(height: SpacingTokens.space1),
                        Text(
                          spot.address!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
                
                // 箭头
                Icon(
                  Icons.arrow_forward_ios_rounded,
                  size: SpacingTokens.space4,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

/// 骨架屏 - 钓点卡片
class _SkeletonSpotCard extends StatelessWidget {
  const _SkeletonSpotCard();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 图片骨架
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(SpacingTokens.space3),
                ),
                color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
              ),
            ),
          ),
          
          // 文字骨架
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.all(SpacingTokens.space3),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: SpacingTokens.space4,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(SpacingTokens.space1),
                    ),
                  ),
                  const SizedBox(height: SpacingTokens.space2),
                  Container(
                    height: SpacingTokens.space3,
                    width: double.infinity * 0.7,
                    decoration: BoxDecoration(
                      color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(SpacingTokens.space1),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 骨架屏 - 列表项
class _SkeletonSpotListItem extends StatelessWidget {
  const _SkeletonSpotListItem();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(SpacingTokens.space3),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          // 图标骨架
          Container(
            width: SpacingTokens.space10,
            height: SpacingTokens.space10,
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(SpacingTokens.space2),
            ),
          ),
          
          const SizedBox(width: SpacingTokens.space3),
          
          // 文字骨架
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  height: SpacingTokens.space4,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(SpacingTokens.space1),
                  ),
                ),
                const SizedBox(height: SpacingTokens.space2),
                Container(
                  height: SpacingTokens.space3,
                  width: double.infinity * 0.6,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.surfaceVariant.withValues(alpha: 0.5),
                    borderRadius: BorderRadius.circular(SpacingTokens.space1),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}