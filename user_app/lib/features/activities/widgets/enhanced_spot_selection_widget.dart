import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/services/mobile_interaction_service.dart';
import 'package:user_app/core/widgets/mobile_components.dart';
import 'package:user_app/features/activities/services/real_spot_recommendation_service.dart';
import 'package:user_app/features/activities/services/spot_favorite_history_service.dart';
import 'package:user_app/features/activities/services/spot_filter_sort_service.dart';
import 'package:user_app/features/activities/services/spot_discovery_history_service.dart';
import 'package:user_app/features/activities/services/spot_search_index_service.dart';
import 'package:user_app/features/activities/widgets/offline_data_manager_widget.dart';
import 'package:user_app/features/activities/widgets/spot_discovery_history_widget.dart';
import 'package:user_app/features/activities/widgets/social_feed_widget.dart';
import 'package:user_app/features/activities/widgets/spot_filter_panel_widget.dart';
import 'package:user_app/features/activities/widgets/spot_quick_preview_widget.dart';
import 'package:user_app/features/activities/widgets/spot_selection_map_widget.dart';
import 'package:user_app/features/activities/widgets/spot_sort_selector_widget.dart';
import 'package:user_app/features/fishing_spots/services/fishing_spot_service.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 增强的钓点选择组件
/// 集成智能推荐、最近使用、搜索筛选等功能
class EnhancedSpotSelectionWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onSpotSelected;
  final String? activityType;
  final List<String>? selectedMethods;
  final List<String>? selectedFishes;
  final String? feeType;

 const EnhancedSpotSelectionWidget({
    super.key,
    required this.onSpotSelected,
    this.activityType,
    this.selectedMethods,
    this.selectedFishes,
    this.feeType,
  });

  @override
  State<EnhancedSpotSelectionWidget> createState() =>
      _EnhancedSpotSelectionWidgetState();
}

class _EnhancedSpotSelectionWidgetState
    extends State<EnhancedSpotSelectionWidget> {
  Map<String, dynamic>? _selectedSpotData;
  bool _isLoadingRecommendations = false;
  List<SpotRecommendationData> _recommendedSpots = [];
  List<SpotRecommendationData> _recentSpots = [];
  RealSpotRecommendationService? _recommendationService;
  SpotDiscoveryHistoryService? _discoveryHistoryService;
  SpotSearchIndexService? _searchIndexService;
  String? _error;

  // 搜索相关状态
  List<SpotRecommendationData> _searchResults = [];
  bool _isSearching = false;
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();
  List<String> _searchSuggestions = [];
  bool _showSuggestions = false;

  // 收藏和历史相关状态
  List<SpotRecommendationData> _favoriteSpots = [];
  List<SpotRecommendationData> _historySpots = [];
  bool _isLoadingFavorites = false;
  bool _showFavorites = false;
  bool _showHistory = false;

  // 筛选和排序相关状态
  SpotFilterCriteria _currentFilter = const SpotFilterCriteria();
  SpotSortCriteria _currentSort = const SpotSortCriteria();
  FilterOptions? _filterOptions;
  List<SpotRecommendationData> _filteredRecommendations = [];
  bool _hasActiveFilters = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeService();
      // 如果有费用类型偏好，自动设置筛选
      if (widget.feeType == '免费') {
        _currentFilter = _currentFilter.copyWith(freeOnly: true, paidOnly: false);
      }
    });
  }

  void _initializeService() {
    try {
      final fishingSpotService = context.read<FishingSpotService>();
      _recommendationService =
          RealSpotRecommendationService(fishingSpotService);
      _discoveryHistoryService = SpotDiscoveryHistoryService();
      _discoveryHistoryService!.initialize();
      _searchIndexService = SpotSearchIndexService();
      _searchIndexService!.initialize();
      _loadSpotData();
      _loadFavoritesAndHistory();
    } catch (e) {
      setState(() {
        _error = '服务初始化失败: $e';
      });
    }
  }

  @override
  void didUpdateWidget(EnhancedSpotSelectionWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当活动类型、钓法或费用类型变化时，重新加载推荐
    if (widget.activityType != oldWidget.activityType ||
        widget.selectedMethods != oldWidget.selectedMethods ||
        widget.selectedFishes != oldWidget.selectedFishes ||
        widget.feeType != oldWidget.feeType) {
      // 如果费用类型是免费，自动设置免费筛选
      if (widget.feeType == '免费' && !_currentFilter.freeOnly) {
        setState(() {
          _currentFilter = _currentFilter.copyWith(freeOnly: true, paidOnly: false);
        });
      }
      _loadSpotData();
    }
  }

  /// 加载收藏和历史数据
  Future<void> _loadFavoritesAndHistory() async {
    setState(() {
      _isLoadingFavorites = true;
    });

    try {
      final service = SpotFavoriteHistoryService.instance;
      final futures = await Future.wait([
        service.getFavoriteSpots(),
        service.getSelectionHistory(),
      ]);

      setState(() {
        _favoriteSpots = futures[0];
        _historySpots = futures[1];
        _isLoadingFavorites = false;
      });
    } catch (e) {
      debugPrint('加载收藏和历史失败: $e');
      setState(() {
        _isLoadingFavorites = false;
      });
    }
  }

  Future<void> _loadSpotData() async {
    if (_recommendationService == null) return;

    setState(() {
      _isLoadingRecommendations = true;
      _error = null;
    });

    try {
      // 获取用户当前位置
      Position? position =
          await RealSpotRecommendationService.getCurrentLocation();

      position ??= Position(
        latitude: 39.9042,
        longitude: 116.4074,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        heading: 0,
        speed: 0,
        speedAccuracy: 0,
        altitudeAccuracy: 0,
        headingAccuracy: 0,
      );

      // 并行加载推荐钓点和最近使用的钓点
      final results = await Future.wait([
        _recommendationService!.getSmartRecommendations(
          userLatitude: position.latitude,
          userLongitude: position.longitude,
          activityType: widget.activityType,
          selectedMethods: widget.selectedMethods,
          selectedFishes: widget.selectedFishes,
          limit: 8,
        ),
        _recommendationService!.getRecentUsedSpots(),
      ]);

      setState(() {
        _recommendedSpots = results[0];
        _recentSpots = results[1];
        _isLoadingRecommendations = false;
      });

      // 更新筛选选项并应用当前筛选
      _updateFilterOptionsAndApply();
      
      // 构建搜索索引
      if (_searchIndexService != null && _recommendedSpots.isNotEmpty) {
        await _searchIndexService!.buildSearchIndex(_recommendedSpots);
      }
    } catch (e) {
      setState(() {
        _error = '加载钓点数据失败: $e';
        _isLoadingRecommendations = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: 
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusLg,
        boxShadow: [
          BoxShadow(
            color: ColorTokens.onSurface.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(SpacingTokens.space5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 移除header，因为在创建活动页面已经有标题了
            if (_error != null)
              _buildErrorState()
            else if (_selectedSpotData != null)
              _buildSelectedSpot()
            else ...[
              if (_isLoadingRecommendations)
                _buildLoadingState()
              else ...[
                // 添加标签页切换
                _buildTabSelector(),
               SizedBox(height: SpacingTokens.space4),
                // 筛选和排序控制
                if (!_showFavorites && !_showHistory) ...[
                  _buildFilterSortControls(),
                 SizedBox(height: SpacingTokens.space4),
                ],
                // 根据选择的标签显示不同内容
                if (_showFavorites)
                  _buildFavoriteSpots()
                else if (_showHistory)
                  _buildHistorySpots()
                else ...[
                  if (_hasActiveFilters && _filteredRecommendations.isNotEmpty)
                    _buildFilteredRecommendations()
                  else if (!_hasActiveFilters && _recommendedSpots.isNotEmpty)
                    _buildSmartRecommendations(),
                  if (_recentSpots.isNotEmpty) ...[
                   SizedBox(height: SpacingTokens.space4),
                    _buildRecentSpots(),
                  ],
                ],
               SizedBox(height: SpacingTokens.space4),
                _buildActionButtons(),
              ],
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: const [
        Container(
          padding: SpacingTokens.paddingSm,
          decoration: 
            color: ColorTokens.error.withValues(alpha: 0.1),
            borderRadius: ShapeTokens.borderRadiusMd,
          ),
          child:
             Icons.location_on, color: ColorTokens.error, size: 20),
        ),
        SizedBox(SizedBox(width: SpacingTokens.space3),
        Text(
          '活动地点',
          style: TypographyTokens.titleLarge.copyWith(
            color: ColorTokens.onSurface,
          ),
        ),
        if (_selectedSpotData != null) ...[
         ),
          TextButton(
            onPressed: () {
              setState(() {
                _selectedSpotData = null;
              });
            },
            child: '重新选择'),
          ),
        ],
      ],
    );
  }

  Widget _buildSelectedSpot() {
    final spot = _selectedSpotData!;
    return Column(
      children: const [
        Container(
          padding: SpacingTokens.paddingMd,
          decoration: 
            color: ColorTokens.error.withValues(alpha: 0.1),
            borderRadius: ShapeTokens.borderRadiusMd,
            border: Border.all(color: ColorTokens.error),
          ),
          child: Column(
            children: const [
              Row(
                children: const [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: const [
                        Row(
                          children: const [
                           
                              Icons.check_circle,
                              color: ColorTokens.error,
                              size: 20,
                            ),
                            SizedBox(SizedBox(width: SpacingTokens.space2),
                            Expanded(
                              child: Text(
                                spot['name'],
                                style: TypographyTokens.titleMedium.copyWith(
                                ),
                              ),
                            ),
                          ],
                        ),
                        if (spot['address'] != null) ...[
                          SizedBox(SizedBox(height: SpacingTokens.space1),
                          Text(
                            spot['address'],
                            style: TypographyTokens.bodySmall.copyWith(
                              color: ColorTokens.onSurfaceVariant,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(SizedBox(height: SpacingTokens.space3),
              // 更换钓点按钮
              SizedBox(
                SizedBox(width: double.infinity,
                child: OutlinedButton(
                  onPressed: () {
                    setState(() {
                      _selectedSpotData = null;
                    });
                  },
                  style: OutlinedButton.styleFrom(
                    foregroundColor: ColorTokens.error,
                    side: const BorderSide(color: ColorTokens.error),
                    shape: const RoundedRectangleBorder(
                      borderRadius: ShapeTokens.borderRadiusMd,
                    ),
                  ),
                  child: '更换钓点'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return 
      SizedBox(height: 120,
      child: MobileLoadingIndicator(
        message: '正在为您智能推荐钓点...',
        size: 24,
      ),
    );
  }

  Widget _buildSmartRecommendations() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        Row(
          children: const [
           Icons.psychology, size: 18, color: ColorTokens.warning),
           SizedBox(width: 6),
            Text(
              '智能推荐',
              style: TypographyTokens.labelLarge.copyWith(
                color: ColorTokens.tertiary,
              ),
            ),
           SizedBox(width: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: 
                color: ColorTokens.warning.withValues(alpha: 0.1),
                borderRadius: ShapeTokens.borderRadiusMd,
              ),
              child: Text(
                '基于您的选择',
                style: TypographyTokens.labelSmall.copyWith(
                  color: ColorTokens.tertiary,
                ),
              ),
            ),
          ],
        ),
       SizedBox(height: 12),
        // 改为垂直列表，使用 Column 包裹
        ...List.generate(
          _recommendedSpots.length > 3 ? 3 : _recommendedSpots.length, // 默认显示前3个
          (index) => Padding(
            padding: EdgeInsets.only(bottom: 12),
            child: _buildVerticalRecommendationCard(_recommendedSpots[index]),
          ),
        ),
        // 如果有更多钓点，显示"查看全部"按钮
        if (_recommendedSpots.length > 3)
          Center(
            child: TextButton(
              onPressed: _showAllRecommendations,
              child: Text(
                '查看全部 ${_recommendedSpots.length} 个推荐',
                style: 
                  color: ColorTokens.warning,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildRecommendationCard(SpotRecommendationData spot) {
    return Container(
      SizedBox(width: 200,
      margin: EdgeInsets.only(right: SpacingTokens.space3),
      child: Card(
        elevation: 2,
        child: InkWell(
          onTap: () async {
            await MobileInteractionService.lightImpact();
            _showSpotPreview(spot);
          },
          onLongPress: () async {
            await MobileInteractionService.longPressFeedback();
            _selectSpot(spot.toMap());
          },
          borderRadius: ShapeTokens.borderRadiusMd,
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                // 钓点名称和推荐分数
                Row(
                  children: const [
                    Expanded(
                      child: Text(
                        spot.name,
                        style: TypographyTokens.titleSmall.copyWith(
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 6, vertical: 2),
                      decoration: 
                        color: _getScoreColor(spot.recommendationScore.round()),
                        borderRadius: ShapeTokens.borderRadiusMd,
                      ),
                      child: Text(
                        '${spot.recommendationScore.round()}分',
                        style: TypographyTokens.labelSmall.copyWith(
                          color: ColorTokens.surface,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(SizedBox(height: SpacingTokens.space1),

                // 推荐理由
                if (spot.recommendationReason.isNotEmpty)
                  Text(
                    spot.recommendationReason,
                    style: TypographyTokens.labelSmall.copyWith(
                      color: ColorTokens.primary,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

               ),

                // 底部信息
                Row(
                  children: const [
                   Icons.location_on, size: 12, color: ColorTokens.onSurfaceVariant),
                    Text(
                      '${spot.distance.toStringAsFixed(1)}km',
                      style: TextStyle(fontSize: 11),
                    ),
                   ),
                    if (spot.rating > 0) ...[
                     Icons.star, size: 12, color: ColorTokens.warning),
                      Text(
                        spot.rating.toStringAsFixed(1),
                        style: TextStyle(fontSize: 11),
                      ),
                    ],
                  ],
                ),

                // 价格信息和热门标签
                Row(
                  children: const [
                    Text(
                      spot.isPaid ? (spot.priceRange ?? '收费') : '免费',
                      style: TextStyle(
                        color:
                            spot.isPaid ? ColorTokens.success[600] : ColorTokens.info[600],
                        fontWeight: FontWeight.w500,
                        TextStyle(fontSize: 11,
                      ),
                    ),
                    if (spot.isPopularToday) ...[
                     ),
                     Icons.local_fire_department,
                          size: 12, color: ColorTokens.error),
                      Text('热门', style: TypographyTokens.labelSmall.copyWith(TextStyle(fontSize: 10)),
                    ],
                  ],
                ),

                // 预览提示
               SizedBox(height: 4),
                Row(
                  children: const [
                    Icon(Icons.visibility, size: 10, color: ColorTokens.onSurfaceVariant[500]),
                    SizedBox(SizedBox(width: SpacingTokens.space1),
                    Text(
                      '点击预览',
                      style: TextStyle(
                        TextStyle(fontSize: 9,
                        color: ColorTokens.onSurfaceVariant[500],
                      ),
                    ),
                   ),
                    Icon(Icons.touch_app, size: 10, color: ColorTokens.onSurfaceVariant),
                    SizedBox(SizedBox(width: SpacingTokens.space1),
                    Text(
                      '长按选择',
                      style: TextStyle(
                        TextStyle(fontSize: 9,
                        color: ColorTokens.onSurfaceVariant[500],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建垂直布局的推荐卡片
  Widget _buildVerticalRecommendationCard(SpotRecommendationData spot) {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () async {
          await MobileInteractionService.lightImpact();
          _showSpotPreview(spot);
        },
        borderRadius: ShapeTokens.borderRadiusMd,
        child: Padding(
          padding: SpacingTokens.paddingMd,
          child: Row(
            children: const [
              // 左侧主要信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    // 钓点名称和标签
                    Row(
                      children: const [
                        Expanded(
                          child: Text(
                            spot.name,
                            style: 
                              fontWeight: FontWeight.w600,
                              TextStyle(fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (spot.isPopularToday) ...[
                         SizedBox(width: 8),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: 
                              color: ColorTokens.error.withValues(alpha: 0.1),
                              borderRadius: ShapeTokens.borderRadiusXs,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: const [
                                Icon(Icons.local_fire_department, size: 12, color: ColorTokens.error),
                               SizedBox(width: 2),
                                Text('热门', style: TextStyle(TextStyle(fontSize: 10, color: ColorTokens.error)),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                    SizedBox(SizedBox(height: SpacingTokens.space2),
                    
                    // 推荐理由
                    if (spot.recommendationReason.isNotEmpty) ...[
                      Text(
                        spot.recommendationReason,
                        style: TextStyle(
                          color: ColorTokens.primary,
                          TextStyle(fontSize: 13,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(SizedBox(height: SpacingTokens.space2),
                    ],
                    
                    // 距离、评分、价格等信息
                    Row(
                      children: const [
                        Icon(Icons.location_on, size: 14, color: ColorTokens.onSurfaceVariant),
                       SizedBox(width: 4),
                        Text(
                          '${spot.distance.toStringAsFixed(1)}km',
                          style: TextStyle(TextStyle(fontSize: 12, color: ColorTokens.onSurfaceVariant[600]),
                        ),
                        SizedBox(SizedBox(width: SpacingTokens.space3),
                        if (spot.rating > 0) ...[
                         Icons.star, size: 14, color: ColorTokens.warning),
                         SizedBox(width: 4),
                          Text(
                            spot.rating.toStringAsFixed(1),
                            style: TextStyle(TextStyle(fontSize: 12, color: ColorTokens.onSurfaceVariant[600]),
                          ),
                          SizedBox(SizedBox(width: SpacingTokens.space3),
                        ],
                       ), // 添加弹性空间
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: 
                            color: spot.isPaid 
                                ? ColorTokens.success.withValues(alpha: 0.1)
                                : ColorTokens.info.withValues(alpha: 0.1),
                            borderRadius: ShapeTokens.borderRadiusXs,
                          ),
                          child: Text(
                            spot.isPaid ? (spot.priceRange ?? '收费') : '免费',
                            style: TextStyle(
                              color: spot.isPaid ? ColorTokens.primary : ColorTokens.secondary,
                              TextStyle(fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // 右侧操作区域
              SizedBox(SizedBox(width: SpacingTokens.space4),
              Column(
                children: const [
                  // 推荐分数
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space3, vertical: SpacingTokens.space1),
                    decoration: 
                      color: _getScoreColor(spot.recommendationScore.round()),
                      borderRadius: ShapeTokens.borderRadiusMd,
                    ),
                    child: Column(
                      children: const [
                        Text(
                          '${spot.recommendationScore.round()}',
                          style: 
                            color: ColorTokens.surface,
                            TextStyle(fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                       
                          '推荐分',
                          style: TextStyle(
                            color: ColorTokens.surface,
                            TextStyle(fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(SizedBox(height: SpacingTokens.space2),
                  // 选择按钮
                  ElevatedButton(
                    onPressed: () async {
                      await MobileInteractionService.mediumImpact();
                      _selectSpot(spot.toMap());
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
                      shape: const RoundedRectangleBorder(
                        borderRadius: ShapeTokens.borderRadiusMd,
                      ),
                    ),
                    child: 
                      '选择',
                      style: TextStyle(TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示所有推荐钓点
  void _showAllRecommendations() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        SizedBox(height: MediaQuery.of(context).size.height * 0.8,
        decoration: 
          color: ColorTokens.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: const [
            // 标题栏
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: 
                border: const Border(
                  bottom: const BorderSide(color: ColorTokens.outline),
                ),
              ),
              child: Row(
                children: const [
                 Icons.psychology, color: ColorTokens.tertiary),
                 SizedBox(width: 8),
                 
                    '所有推荐钓点',
                    style: TextStyle(
                      TextStyle(fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                 ),
                  IconButton(
                    icon: Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // 钓点列表
            Expanded(
              child: ListView.builder(
                padding: SpacingTokens.paddingMd,
                itemCount: _recommendedSpots.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: SpacingTokens.space3),
                    child: _buildVerticalRecommendationCard(_recommendedSpots[index]),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示所有筛选结果
  void _showAllFilteredRecommendations() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        SizedBox(height: MediaQuery.of(context).size.height * 0.8,
        decoration: 
          color: ColorTokens.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: const [
            // 标题栏
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: 
                border: const Border(
                  bottom: const BorderSide(color: ColorTokens.outline),
                ),
              ),
              child: Row(
                children: const [
                 Icons.filter_list, color: ColorTokens.tertiary),
                 SizedBox(width: 8),
                 
                    '所有筛选结果',
                    style: TextStyle(
                      TextStyle(fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                 ),
                  IconButton(
                    icon: Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // 钓点列表
            Expanded(
              child: ListView.builder(
                padding: SpacingTokens.paddingMd,
                itemCount: _filteredRecommendations.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: SpacingTokens.space3),
                    child: _buildVerticalRecommendationCard(_filteredRecommendations[index]),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建垂直布局的收藏钓点卡片
  Widget _buildVerticalFavoriteCard(SpotRecommendationData spot) {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () async {
          await MobileInteractionService.lightImpact();
          _showSpotPreview(spot);
        },
        borderRadius: ShapeTokens.borderRadiusMd,
        child: Padding(
          padding: SpacingTokens.paddingMd,
          child: Row(
            children: const [
              // 左侧主要信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    // 钓点名称和收藏标识
                    Row(
                      children: const [
                       Icons.favorite, size: 16, color: ColorTokens.error),
                       SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            spot.name,
                            style: 
                              fontWeight: FontWeight.w600,
                              TextStyle(fontSize: 16,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        if (spot.isPopularToday) ...[
                         SizedBox(width: 8),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: 
                              color: ColorTokens.error.withValues(alpha: 0.1),
                              borderRadius: ShapeTokens.borderRadiusXs,
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: const [
                                Icon(Icons.local_fire_department, size: 12, color: ColorTokens.error),
                               SizedBox(width: 2),
                                Text('热门', style: TextStyle(TextStyle(fontSize: 10, color: ColorTokens.error)),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                    SizedBox(SizedBox(height: SpacingTokens.space2),
                    
                    // 距离、评分、价格等信息
                    Row(
                      children: const [
                        Icon(Icons.location_on, size: 14, color: ColorTokens.onSurfaceVariant),
                       SizedBox(width: 4),
                        Text(
                          '${spot.distance.toStringAsFixed(1)}km',
                          style: TextStyle(TextStyle(fontSize: 12, color: ColorTokens.onSurfaceVariant[600]),
                        ),
                        SizedBox(SizedBox(width: SpacingTokens.space3),
                        if (spot.rating > 0) ...[
                         Icons.star, size: 14, color: ColorTokens.warning),
                         SizedBox(width: 4),
                          Text(
                            spot.rating.toStringAsFixed(1),
                            style: TextStyle(TextStyle(fontSize: 12, color: ColorTokens.onSurfaceVariant[600]),
                          ),
                          SizedBox(SizedBox(width: SpacingTokens.space3),
                        ],
                       ), // 添加弹性空间
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: 
                            color: spot.isPaid 
                                ? ColorTokens.success.withValues(alpha: 0.1)
                                : ColorTokens.info.withValues(alpha: 0.1),
                            borderRadius: ShapeTokens.borderRadiusXs,
                          ),
                          child: Text(
                            spot.isPaid ? (spot.priceRange ?? '收费') : '免费',
                            style: TextStyle(
                              color: spot.isPaid ? ColorTokens.primary : ColorTokens.secondary,
                              TextStyle(fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // 右侧操作按钮
              SizedBox(SizedBox(width: SpacingTokens.space4),
              ElevatedButton(
                onPressed: () async {
                  await MobileInteractionService.mediumImpact();
                  _selectSpot(spot.toMap());
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  shape: const RoundedRectangleBorder(
                    borderRadius: ShapeTokens.borderRadiusMd,
                  ),
                ),
                child: 
                  '选择',
                  style: TextStyle(TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建垂直布局的历史钓点卡片
  Widget _buildVerticalHistoryCard(SpotRecommendationData spot) {
    return Card(
      elevation: 1,
      margin: EdgeInsets.zero,
      child: InkWell(
        onTap: () async {
          await MobileInteractionService.lightImpact();
          _selectSpot(spot.toMap());
        },
        borderRadius: ShapeTokens.borderRadiusMd,
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Row(
            children: const [
              // 历史图标
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: 
                  color: ColorTokens.onSurfaceVariant.withValues(alpha: 0.1),
                  borderRadius: ShapeTokens.borderRadiusMd,
                ),
                child: Icon(
                  Icons.history,
                  size: 16,
                  color: ColorTokens.onSurfaceVariant[600],
                ),
              ),
              SizedBox(SizedBox(width: SpacingTokens.space3),
              
              // 钓点信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    Text(
                      spot.name,
                      style: 
                        fontWeight: FontWeight.w600,
                        TextStyle(fontSize: 14,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                   SizedBox(height: 4),
                    Row(
                      children: const [
                        Icon(Icons.location_on, size: 12, color: ColorTokens.onSurfaceVariant[600]),
                       SizedBox(width: 4),
                        Text(
                          '${spot.distance.toStringAsFixed(1)}km',
                          style: TextStyle(TextStyle(fontSize: 11, color: ColorTokens.onSurfaceVariant[600]),
                        ),
                        SizedBox(SizedBox(width: SpacingTokens.space3),
                        Text(
                          spot.isPaid ? (spot.priceRange ?? '收费') : '免费',
                          style: TextStyle(
                            color: spot.isPaid ? ColorTokens.success[600] : ColorTokens.info[600],
                            TextStyle(fontSize: 11,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              
              // 选择按钮
              TextButton(
                onPressed: () async {
                  await MobileInteractionService.lightImpact();
                  _selectSpot(spot.toMap());
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                ),
                child: 
                  '选择',
                  style: TextStyle(TextStyle(fontSize: 12),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示所有收藏钓点
  void _showAllFavorites() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        SizedBox(height: MediaQuery.of(context).size.height * 0.8,
        decoration: 
          color: ColorTokens.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: const [
            // 标题栏
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: 
                border: const Border(
                  bottom: const BorderSide(color: ColorTokens.outline),
                ),
              ),
              child: Row(
                children: const [
                 Icons.favorite, color: ColorTokens.error),
                 SizedBox(width: 8),
                 
                    '我的收藏',
                    style: TextStyle(
                      TextStyle(fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                 ),
                  IconButton(
                    icon: Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // 收藏列表
            Expanded(
              child: ListView.builder(
                padding: SpacingTokens.paddingMd,
                itemCount: _favoriteSpots.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: SpacingTokens.space3),
                    child: _buildVerticalFavoriteCard(_favoriteSpots[index]),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示所有历史钓点
  void _showAllHistory() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        SizedBox(height: MediaQuery.of(context).size.height * 0.8,
        decoration: 
          color: ColorTokens.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
        ),
        child: Column(
          children: const [
            // 标题栏
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: 
                border: const Border(
                  bottom: const BorderSide(color: ColorTokens.outline),
                ),
              ),
              child: Row(
                children: const [
                  Icon(Icons.history, color: ColorTokens.onSurfaceVariant[600]),
                 SizedBox(width: 8),
                 
                    '选择历史',
                    style: TextStyle(
                      TextStyle(fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                 ),
                  TextButton(
                    onPressed: () {
                      _clearHistory();
                      Navigator.of(context).pop();
                    },
                    child: '清空'),
                  ),
                  IconButton(
                    icon: Icons.close),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                ],
              ),
            ),
            // 历史列表
            Expanded(
              child: ListView.builder(
                padding: SpacingTokens.paddingMd,
                itemCount: _historySpots.length,
                itemBuilder: (context, index) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: 8),
                    child: _buildVerticalHistoryCard(_historySpots[index]),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentSpots() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        Row(
          children: const [
            Icon(Icons.history, size: 18, color: ColorTokens.onSurfaceVariant[600]),
           SizedBox(width: 6),
            Text(
              '最近使用',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: ColorTokens.onSurfaceVariant[700],
              ),
            ),
          ],
        ),
       SizedBox(height: SpacingTokens.space2),
        SizedBox(
          SizedBox(height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _recentSpots.length,
            itemBuilder: (context, index) {
              final spot = _recentSpots[index];
              return Container(
                margin: EdgeInsets.only(right: 8),
                child: ActionChip(
                  onPressed: () => _selectSpot(spot.toMap()),
                  avatar: Icons.location_on, size: 16),
                  label: Text(
                    spot.name,
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildErrorState() {
    return Container(
      padding: SpacingTokens.paddingMd,
      decoration: 
        color: ColorTokens.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: ColorTokens.error.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: const [
          Row(
            children: const [
              Icon(Icons.error_outline, color: ColorTokens.error[600], size: 20),
             SizedBox(width: 8),
              Text(
                '加载失败',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: ColorTokens.error[600],
                ),
              ),
            ],
          ),
         SizedBox(height: SpacingTokens.space2),
          Text(
            _error!,
            style: TextStyle(
              color: ColorTokens.error[700],
              TextStyle(fontSize: 14,
            ),
          ),
         SizedBox(height: 12),
          ElevatedButton.icon(
            onPressed: _loadSpotData,
            icon: Icons.refresh, size: 16),
            label: '重新加载'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.error[600],
              foregroundColor: ColorTokens.onPrimary,
              padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: const [
        // 智能推荐按钮 - 主要操作
        if (_hasActiveFilters || 
            widget.selectedMethods != null || 
            widget.selectedFishes != null ||
            widget.feeType != null) ...[
          SizedBox(
            SizedBox(width: double.infinity,
            child: MobileOptimizedButton(
              onPressed: _performSmartRecommendation,
              backgroundColor: ColorTokens.warning,
              foregroundColor: ColorTokens.onPrimary,
              tooltip: '根据您的偏好智能推荐钓点',
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: const [
                 Icons.psychology, size: 20),
                 SizedBox(width: 8),
                 
                    '基于偏好智能推荐',
                    style: TextStyle(fontWeight: FontWeight.bold, TextStyle(fontSize: 16),
                  ),
                 SizedBox(width: 8),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: 
                      color: ColorTokens.onPrimary.withValues(alpha: 0.3),
                      borderRadius: ShapeTokens.borderRadiusMd,
                    ),
                    child: 
                      'AI',
                      style: TextStyle(TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
                    ),
                  ),
                ],
              ),
            ),
          ),
         SizedBox(height: SpacingTokens.space4),
        ],
        
        // 简化的操作按钮 - 只保留最常用的
        Row(
          children: const [
            Expanded(
              child: MobileOptimizedButton(
                onPressed: _showSpotSearchModal,
                backgroundColor: ColorTokens.surface,
                foregroundColor: ColorTokens.primary,
                child: 
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.search, size: 18),
                   SizedBox(width: 4),
                    Text('搜索钓点'),
                  ],
                ),
              ),
            ),
           SizedBox(width: 8),
            Expanded(
              child: MobileOptimizedButton(
                onPressed: _showMapPicker,
                backgroundColor: ColorTokens.surface,
                foregroundColor: ColorTokens.primary,
                child: 
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Icon(Icons.map, size: 18),
                   SizedBox(width: 4),
                    Text('地图选择'),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Color _getScoreColor(int score) {
    if (score >= 90) return ColorTokens.error;
    if (score >= 80) return ColorTokens.warning;
    if (score >= 70) return ColorTokens.info;
    return ColorTokens.onSurfaceVariant;
  }

  Future<void> _selectSpot(Map<String, dynamic> spot) async {
    // 添加成功操作反馈
    await MobileInteractionService.successFeedback();

    setState(() {
      _selectedSpotData = spot;
    });

    // 添加到选择历史
    try {
      final spotData = SpotRecommendationData(
        spotId: spot['spotId'] ?? 0,
        name: spot['name'] ?? '',
        address: spot['address'] ?? '',
        latitude: (spot['latitude'] ?? 0.0).toDouble(),
        longitude: (spot['longitude'] ?? 0.0).toDouble(),
        distance: (spot['distance'] ?? 0.0).toDouble(),
        recommendationScore: (spot['recommendationScore'] ?? 0.0).toDouble(),
        recommendationReason: spot['reason'] ?? '',
        matchedFeatures: List<String>.from(spot['features'] ?? []),
        rating: (spot['rating'] ?? 0.0).toDouble(),
        isPaid: spot['isPaid'] ?? false,
        priceRange: spot['priceRange'],
        facilities: List<String>.from(spot['facilities'] ?? []),
        fishTypes: List<String>.from(spot['fishTypes'] ?? []),
        isPopularToday: spot['isPopularToday'] ?? false,
      );

      await SpotFavoriteHistoryService.instance.addToHistory(spotData);
      
      // 记录发现历史
      if (_discoveryHistoryService != null) {
        await _discoveryHistoryService!.recordDiscovery(
          spot: spotData,
          source: DiscoverySource.recommendation, // 可以根据实际来源调整
          context: {
            'activityType': widget.activityType,
            'selectedMethods': widget.selectedMethods,
            'selectedFishes': widget.selectedFishes,
          },
        );
      }
      
      // 刷新历史数据
      _loadFavoritesAndHistory();
    } catch (e) {
      debugPrint('添加到历史失败: $e');
    }

    widget.onSpotSelected(spot);
  }

  void _showSpotSearchModal() {
    // 重置搜索状态
    _searchController.clear();
    _searchResults.clear();
    _searchQuery = '';
    _isSearching = false;
    _searchSuggestions.clear();
    _showSuggestions = false;

    MobileBottomSheet.show(
      context: context,
      title: '搜索钓点',
      child: _buildSearchContent(),
    );
  }

  Widget _buildSearchContent() {
    return Column(
      children: const [
        Padding(
          padding: SpacingTokens.paddingMd,
          child: Column(
            children: const [
              MobileOptimizedSearchBar(
                hintText: '搜索钓点名称或地址',
                controller: _searchController,
                autofocus: true,
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                  _performSearch(value);
                  _updateSearchSuggestions(value);
                },
                onClear: () {
                  setState(() {
                    _searchQuery = '';
                    _searchResults.clear();
                    _searchSuggestions.clear();
                    _showSuggestions = false;
                  });
                },
              ),
              
              // 搜索建议
              if (_showSuggestions && _searchSuggestions.isNotEmpty) ...[ 
               SizedBox(height: SpacingTokens.space2),
                _buildSearchSuggestions(),
              ],
              
              // 搜索统计按钮
              if (_searchQuery.isEmpty) ...[ 
               SizedBox(height: SpacingTokens.space2),
                Row(
                  children: const [
                    Expanded(
                      child: MobileOptimizedButton(
                        onPressed: _showSearchStats,
                        backgroundColor: ColorTokens.onSurfaceVariant[100],
                        foregroundColor: ColorTokens.onSurfaceVariant[700],
                        child: 
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: const [
                            Icon(Icons.analytics, size: 16),
                           SizedBox(width: 4),
                            Text('搜索统计', style: TextStyle(TextStyle(fontSize: 12)),
                          ],
                        ),
                      ),
                    ),
                   SizedBox(width: 8),
                    Expanded(
                      child: MobileOptimizedButton(
                        onPressed: _showPopularSearches,
                        backgroundColor: ColorTokens.onSurfaceVariant[100],
                        foregroundColor: ColorTokens.onSurfaceVariant[700],
                        child: 
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: const [
                            Icon(Icons.trending_up, size: 16),
                           SizedBox(width: 4),
                            Text('热门搜索', style: TextStyle(TextStyle(fontSize: 12)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
        Expanded(
          child: _buildSearchResults(),
        ),
      ],
    );
  }

  Widget _buildSearchResults() {
    if (_searchQuery.isEmpty) {
      return _buildSearchPlaceholder();
    }

    if (_isSearching) {
      return const MobileLoadingIndicator(
        message: '正在搜索钓点...',
      );
    }

    if (_searchResults.isEmpty) {
      return _buildNoResultsWidget();
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final spot = _searchResults[index];
        return _buildRealSearchResultCard(spot);
      },
    );
  }

  Widget _buildSearchPlaceholder() {
    return 
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          Icon(
            Icons.search,
            size: 64,
            color: ColorTokens.onSurfaceVariant,
          ),
         SizedBox(height: SpacingTokens.space4),
          Text(
            '输入钓点名称或地址进行搜索',
            style: TextStyle(
              TextStyle(fontSize: 16,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
         SizedBox(height: SpacingTokens.space2),
          Text(
            '例如："太湖"、"西湖"、"钓鱼基地"',
            style: TextStyle(
              TextStyle(fontSize: 14,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: const [
          Icon(
            Icons.location_off,
            size: 64,
            color: ColorTokens.onSurfaceVariant[400],
          ),
         SizedBox(height: SpacingTokens.space4),
          Text(
            '未找到相关钓点',
            style: TextStyle(
              TextStyle(fontSize: 18,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurfaceVariant[600],
            ),
          ),
         SizedBox(height: SpacingTokens.space2),
          Text(
            '试试其他搜索关键词',
            style: TextStyle(
              TextStyle(fontSize: 14,
              color: ColorTokens.onSurfaceVariant[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRealSearchResultCard(SpotRecommendationData spot) {
    return Card(
      margin: EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: spot.isPaid ? ColorTokens.success[100] : ColorTokens.info[100],
          child: Icon(
            spot.isPaid ? Icons.attach_money : Icons.star,
            color: spot.isPaid ? ColorTokens.success[600] : ColorTokens.info[600],
          ),
        ),
        title: Text(
          spot.name,
          style: 
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: const [
            Text(
              spot.address,
              style: TextStyle(fontSize: 12),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
           SizedBox(height: 4),
            Row(
              children: const [
                Text(
                  spot.isPaid ? (spot.priceRange ?? '收费') : '免费',
                  style: TextStyle(
                    color: spot.isPaid ? ColorTokens.success[600] : ColorTokens.info[600],
                    fontWeight: FontWeight.w500,
                    TextStyle(fontSize: 12,
                  ),
                ),
                if (spot.rating > 0) ...[
                 ' · ', style: TextStyle(TextStyle(fontSize: 12)),
                  Icon(Icons.star, size: 12, color: ColorTokens.warning[600]),
                  Text(
                    spot.rating.toStringAsFixed(1),
                    style: TextStyle(fontSize: 12),
                  ),
                ],
               ' · ', style: TextStyle(TextStyle(fontSize: 12)),
                Text(
                  '${spot.distance.toStringAsFixed(1)}km',
                  style: TextStyle(fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: const [
            IconButton(
              onPressed: () => _showSpotPreview(spot),
              icon: Icons.visibility, size: 18),
              color: ColorTokens.primary,
              tooltip: '预览详情',
            ),
           Icons.arrow_forward_ios, size: 16),
          ],
        ),
        onTap: () {
          Navigator.of(context).pop();
          _selectSpot(spot.toMap());
        },
      ),
    );
  }

  /// 更新搜索建议
  Future<void> _updateSearchSuggestions(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchSuggestions.clear();
        _showSuggestions = false;
      });
      return;
    }

    if (_searchIndexService != null) {
      try {
        final suggestions = await _searchIndexService!.getSearchSuggestions(
          query: query,
          limit: 8,
        );
        
        setState(() {
          _searchSuggestions = suggestions;
          _showSuggestions = suggestions.isNotEmpty;
        });
      } catch (e) {
        debugPrint('获取搜索建议失败: $e');
      }
    }
  }

  /// 构建搜索建议列表
  Widget _buildSearchSuggestions() {
    return Container(
     constraints: BoxConstraints(maxHeight: 200),
      decoration: 
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusMd,
        border: Border.all(color: ColorTokens.onSurfaceVariant[300]!),
        boxShadow: [
          BoxShadow(
            color: ColorTokens.scrim.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: _searchSuggestions.length,
        itemBuilder: (context, index) {
          final suggestion = _searchSuggestions[index];
          return ListTile(
            dense: true,
            leading: Icons.search, size: 16, color: ColorTokens.onSurfaceVariant),
            title: Text(
              suggestion,
              style: TextStyle(fontSize: 14),
            ),
            onTap: () {
              _searchController.text = suggestion;
              setState(() {
                _searchQuery = suggestion;
                _showSuggestions = false;
              });
              _performSearch(suggestion);
            },
          );
        },
      ),
    );
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() {
        _searchResults.clear();
        _isSearching = false;
      });
      return;
    }

    if (query.length < 2) {
      return; // 至少需要2个字符才开始搜索
    }

    setState(() {
      _isSearching = true;
    });

    try {
      List<SpotRecommendationData> results = [];
      
      // 优先使用智能搜索索引
      if (_searchIndexService != null) {
        final searchResults = await _searchIndexService!.smartSearch(
          query: query,
          limit: 20,
          options: SearchOptions(
            maxDistance: 50.0, // 最大50km
            minRating: 0.0,
          ),
        );
        
        results = searchResults.map((result) => result.spot).toList();
      } else {
        // 回退到传统搜索
        Position? position =
            await RealSpotRecommendationService.getCurrentLocation();

        results = await _recommendationService?.searchSpots(
          query: query,
          latitude: position?.latitude,
          longitude: position?.longitude,
          pageSize: 20,
        ) ?? [];
      }

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
      
      // 记录搜索历史
      if (_discoveryHistoryService != null && results.isNotEmpty) {
        await _discoveryHistoryService!.recordSearch(
          query: query,
          results: results,
        );
      }
    } catch (e) {
      debugPrint('搜索钓点失败: $e');
      setState(() {
        _searchResults = [];
        _isSearching = false;
      });
    }
  }

  Future<void> _showMapPicker() async {
    if (_recommendationService == null) {
      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Text('地图服务未初始化，请稍后重试'),
          backgroundColor: ColorTokens.error,
        ),
      );
      return;
    }

    // 获取当前位置作为地图初始位置
    Position? currentPosition;
    try {
      currentPosition =
          await RealSpotRecommendationService.getCurrentLocation();
    } catch (e) {
      debugPrint('无法获取当前位置: $e');
    }

    if (!mounted) return;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => SpotSelectionMapWidget(
        recommendationService: _recommendationService!,
        onSpotSelected: (spotData) {
          setState(() {
            _selectedSpotData = spotData;
          });
          widget.onSpotSelected(spotData);
        },
        initialLatitude: currentPosition?.latitude,
        initialLongitude: currentPosition?.longitude,
      ),
    );
  }

  /// 显示钓点详情预览
  void _showSpotPreview(SpotRecommendationData spot) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        SizedBox(height: MediaQuery.of(context).size.height * 0.7,
        decoration: 
          color: ColorTokens.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: const [
            // 头部
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: 
                border: const Border(bottom: BorderSide(color: ColorTokens.onSurfaceVariant[200]!)),
              ),
              child: Row(
                children: const [
                 Icons.info_outline, color: ColorTokens.primary),
                 SizedBox(width: 8),
                 
                    '钓点详情预览',
                    style: TextStyle(
                      TextStyle(fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                 ),
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: '关闭'),
                  ),
                ],
              ),
            ),
            // 内容
            Expanded(
              child: SingleChildScrollView(
                padding: SpacingTokens.paddingMd,
                child: SpotQuickPreviewWidget(
                  spot: spot,
                  onSelect: () {
                    Navigator.of(context).pop();
                    _selectSpot(spot.toMap());
                  },
                  onFavoriteToggle: () {
                    // 刷新收藏状态
                    _loadFavoritesAndHistory();
                  },
                  showSelectButton: true,
                  isCompact: false,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建标签页选择器
  Widget _buildTabSelector() {
    final currentIndex = _showFavorites ? 1 : (_showHistory ? 2 : 0);

    return Column(
      children: const [
        // 移除快捷筛选标签，因为偏好已经在上层页面设置
        // 标签页切换
        MobileOptimizedTabBar(
          tabs: [
           const MobileTab(label: '推荐', semanticLabel: '智能推荐钓点'),
            MobileTab(
                label: '收藏 (${_favoriteSpots.length})', semanticLabel: '收藏的钓点'),
            MobileTab(
                label: '历史 (${_historySpots.length})', semanticLabel: '历史选择记录'),
          ],
          currentIndex: currentIndex,
          onTap: (index) {
            setState(() {
              _showFavorites = index == 1;
              _showHistory = index == 2;
            });
          },
        ),
      ],
    );
  }

  // 移除快捷筛选标签方法，因为偏好已经在上层页面设置

  // 移除快捷筛选标签相关方法

  /// 构建收藏钓点列表
  Widget _buildFavoriteSpots() {
    if (_isLoadingFavorites) {
      return 
        child: CircularProgressIndicator(),
      );
    }

    if (_favoriteSpots.isEmpty) {
      return _buildEmptyState(
        icon: Icons.favorite_border,
        title: '暂无收藏钓点',
        subtitle: '点击钓点卡片上的❤️图标来收藏喜欢的钓点',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        Row(
          children: const [
           Icons.favorite, size: 18, color: ColorTokens.error),
           SizedBox(width: 6),
           
              '我的收藏',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: ColorTokens.error,
              ),
            ),
           SizedBox(width: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: 
                color: ColorTokens.error.withValues(alpha: 0.1),
                borderRadius: ShapeTokens.borderRadiusMd,
              ),
              child: Text(
                '${_favoriteSpots.length} 个',
                style: 
                  TextStyle(fontSize: 10,
                  color: ColorTokens.error,
                ),
              ),
            ),
           ),
            TextButton(
              onPressed: _showFavoriteStats,
              child: '统计', style: TextStyle(TextStyle(fontSize: 12)),
            ),
          ],
        ),
       SizedBox(height: 12),
        // 改为垂直列表，显示前3个
        ...List.generate(
          _favoriteSpots.length > 3 ? 3 : _favoriteSpots.length,
          (index) => Padding(
            padding: EdgeInsets.only(bottom: 12),
            child: _buildVerticalFavoriteCard(_favoriteSpots[index]),
          ),
        ),
        // 如果有更多收藏，显示"查看全部"按钮
        if (_favoriteSpots.length > 3)
          Center(
            child: TextButton(
              onPressed: () => _showAllFavorites(),
              child: Text(
                '查看全部 ${_favoriteSpots.length} 个收藏',
                style: 
                  color: ColorTokens.error,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// 构建历史钓点列表
  Widget _buildHistorySpots() {
    if (_isLoadingFavorites) {
      return 
        child: CircularProgressIndicator(),
      );
    }

    if (_historySpots.isEmpty) {
      return _buildEmptyState(
        icon: Icons.history,
        title: '暂无选择历史',
        subtitle: '您选择过的钓点会自动记录在这里',
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        Row(
          children: const [
            Icon(Icons.history, size: 18, color: ColorTokens.onSurfaceVariant[600]),
           SizedBox(width: 6),
            Text(
              '选择历史',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: ColorTokens.onSurfaceVariant[700],
              ),
            ),
           SizedBox(width: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: 
                color: ColorTokens.onSurfaceVariant[200],
                borderRadius: ShapeTokens.borderRadiusMd,
              ),
              child: Text(
                '${_historySpots.length} 个',
                style: TextStyle(
                  TextStyle(fontSize: 10,
                  color: ColorTokens.onSurfaceVariant[600],
                ),
              ),
            ),
           ),
            TextButton(
              onPressed: _clearHistory,
              child: '清空', style: TextStyle(TextStyle(fontSize: 12)),
            ),
          ],
        ),
       SizedBox(height: 12),
        // 改为垂直列表，显示前5个历史记录
        ...List.generate(
          _historySpots.length > 5 ? 5 : _historySpots.length,
          (index) => Padding(
            padding: EdgeInsets.only(bottom: 8),
            child: _buildVerticalHistoryCard(_historySpots[index]),
          ),
        ),
        // 如果有更多历史，显示"查看全部"按钮
        if (_historySpots.length > 5)
          Center(
            child: TextButton(
              onPressed: () => _showAllHistory(),
              child: Text(
                '查看全部 ${_historySpots.length} 条历史',
                style: TextStyle(
                  color: ColorTokens.onSurfaceVariant[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// 构建收藏钓点卡片
  Widget _buildFavoriteCard(SpotRecommendationData spot) {
    return Container(
      SizedBox(width: 200,
      margin: EdgeInsets.only(right: SpacingTokens.space3),
      child: Card(
        elevation: 2,
        child: InkWell(
          onTap: () async {
            await MobileInteractionService.lightImpact();
            _showSpotPreview(spot);
          },
          onLongPress: () async {
            await MobileInteractionService.longPressFeedback();
            _selectSpot(spot.toMap());
          },
          borderRadius: ShapeTokens.borderRadiusMd,
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: const [
                // 钓点名称和取消收藏按钮
                Row(
                  children: const [
                    Expanded(
                      child: Text(
                        spot.name,
                        style: TypographyTokens.titleSmall.copyWith(
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    GestureDetector(
                      onTap: () => _toggleFavorite(spot),
                      child: 
                        Icons.favorite,
                        color: ColorTokens.error,
                        size: 18,
                      ),
                    ),
                  ],
                ),
                SizedBox(SizedBox(height: SpacingTokens.space1),

                // 地址
                Text(
                  spot.address,
                  style: TextStyle(
                    color: ColorTokens.onSurfaceVariant[600],
                    TextStyle(fontSize: 11,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),

               ),

                // 底部信息
                Row(
                  children: const [
                   Icons.location_on, size: 12, color: ColorTokens.onSurfaceVariant),
                    Text(
                      '${spot.distance.toStringAsFixed(1)}km',
                      style: TextStyle(fontSize: 11),
                    ),
                   ),
                    if (spot.rating > 0) ...[
                     Icons.star, size: 12, color: ColorTokens.warning),
                      Text(
                        spot.rating.toStringAsFixed(1),
                        style: TextStyle(fontSize: 11),
                      ),
                    ],
                  ],
                ),

                // 价格信息
                Row(
                  children: const [
                    Text(
                      spot.isPaid ? (spot.priceRange ?? '收费') : '免费',
                      style: TextStyle(
                        color:
                            spot.isPaid ? ColorTokens.success[600] : ColorTokens.info[600],
                        fontWeight: FontWeight.w500,
                        TextStyle(fontSize: 11,
                      ),
                    ),
                    if (spot.isPopularToday) ...[
                     ),
                     Icons.local_fire_department,
                          size: 12, color: ColorTokens.error),
                      Text('热门', style: TypographyTokens.labelSmall.copyWith(TextStyle(fontSize: 10)),
                    ],
                  ],
                ),

                // 预览提示
               SizedBox(height: 4),
                Row(
                  children: const [
                    Icon(Icons.visibility, size: 10, color: ColorTokens.onSurfaceVariant[500]),
                    SizedBox(SizedBox(width: SpacingTokens.space1),
                    Text(
                      '点击预览',
                      style: TextStyle(
                        TextStyle(fontSize: 9,
                        color: ColorTokens.onSurfaceVariant[500],
                      ),
                    ),
                   ),
                    Icon(Icons.touch_app, size: 10, color: ColorTokens.onSurfaceVariant),
                    SizedBox(SizedBox(width: SpacingTokens.space1),
                    Text(
                      '长按选择',
                      style: TextStyle(
                        TextStyle(fontSize: 9,
                        color: ColorTokens.onSurfaceVariant[500],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return SizedBox(
      SizedBox(height: 120,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: const [
            Icon(
              icon,
              size: 48,
              color: ColorTokens.onSurfaceVariant[400],
            ),
           SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                TextStyle(fontSize: 16,
                fontWeight: FontWeight.w600,
                color: ColorTokens.onSurfaceVariant[600],
              ),
            ),
           SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                TextStyle(fontSize: 12,
                color: ColorTokens.onSurfaceVariant[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 切换收藏状态
  Future<void> _toggleFavorite(SpotRecommendationData spot) async {
    try {
      final service = SpotFavoriteHistoryService.instance;
      final isFavorite = await service.isFavorite(spot.spotId);

      if (isFavorite) {
        await service.removeFromFavorites(spot.spotId);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已从收藏中移除 ${spot.name}'),
            backgroundColor: ColorTokens.onSurfaceVariant[600],
          ),
        );
      } else {
        await service.addToFavorites(spot);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已收藏 ${spot.name}'),
            backgroundColor: ColorTokens.error[600],
          ),
        );
      }

      // 刷新收藏数据
      _loadFavoritesAndHistory();
    } catch (e) {
      debugPrint('切换收藏状态失败: $e');
    }
  }

  /// 显示收藏统计
  Future<void> _showFavoriteStats() async {
    try {
      final stats =
          await SpotFavoriteHistoryService.instance.getFavoriteStats();

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: '收藏统计'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              Text('总收藏: ${stats['total']}个'),
              Text('免费钓点: ${stats['free']}个'),
              Text('收费钓点: ${stats['paid']}个'),
              if (stats['avgRating']! > 0)
                Text('平均评分: ${(stats['avgRating']! / 10).toStringAsFixed(1)}分'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: '确定'),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('获取收藏统计失败: $e');
    }
  }

  /// 清空历史记录
  Future<void> _clearHistory() async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: '确认清空'),
        content: '确定要清空所有选择历史吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: '取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: '确定'),
          ),
        ],
      ),
    );

    if (confirm == true) {
      await SpotFavoriteHistoryService.instance.clearHistory();
      _loadFavoritesAndHistory();

      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Text('已清空选择历史'),
        ),
      );
    }
  }

  /// 更新筛选选项并应用当前筛选
  void _updateFilterOptionsAndApply() {
    if (_recommendedSpots.isEmpty) return;

    // 生成筛选选项
    _filterOptions = SpotFilterSortService.getFilterOptions(_recommendedSpots);

    // 应用当前筛选和排序
    _applyFilterAndSort();
  }

  /// 应用筛选和排序
  void _applyFilterAndSort() {
    if (_recommendedSpots.isEmpty) return;

    // 应用筛选和排序
    final filtered = SpotFilterSortService.filterAndSort(
      _recommendedSpots,
      filter: _currentFilter,
      sort: _currentSort,
    );

    setState(() {
      _filteredRecommendations = filtered;
      _hasActiveFilters = _currentFilter.hasAnyFilter;
    });
  }

  /// 构建筛选和排序控制
  Widget _buildFilterSortControls() {
    final filterCount = _currentFilter.activeFilterCount;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        // 移除筛选提示，因为偏好已经在上层页面设置
        // 筛选和排序按钮
        Row(
          children: const [
            // 筛选按钮
            Expanded(
              child: MobileOptimizedButton(
                onPressed: _filterOptions != null ? _showFilterPanel : null,
                backgroundColor: filterCount > 0
                    ? ColorTokens.primary.withValues(alpha: 0.1)
                    : ColorTokens.onSurfaceVariant[100],
                foregroundColor:
                    filterCount > 0 ? ColorTokens.primary : ColorTokens.onSurfaceVariant[600],
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                   Icons.filter_list, size: 18),
                   SizedBox(width: 4),
                    Text(
                      filterCount > 0 ? '筛选($filterCount)' : '筛选',
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(SizedBox(width: SpacingTokens.space3),
            // 排序按钮
            Expanded(
              child: MobileOptimizedButton(
                onPressed: _showSortSelector,
                backgroundColor: ColorTokens.onSurfaceVariant[100],
                foregroundColor: ColorTokens.onSurfaceVariant[600],
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                   Icons.sort, size: 18),
                   SizedBox(width: 4),
                    Text(
                      _currentSort.primarySort.label,
                      style: TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建筛选后的推荐结果
  Widget _buildFilteredRecommendations() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: const [
        Row(
          children: const [
           Icons.psychology, size: 18, color: ColorTokens.warning),
           SizedBox(width: 6),
           
              '筛选结果',
              style: TypographyTokens.labelLarge.copyWith(
                color: ColorTokens.tertiary,
              ),
            ),
           SizedBox(width: 8),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: 
                color: ColorTokens.warning.withValues(alpha: 0.1),
                borderRadius: ShapeTokens.borderRadiusMd,
              ),
              child: Text(
                '${_filteredRecommendations.length}个钓点',
                style: 
                  TextStyle(fontSize: 10,
                  color: ColorTokens.warning,
                ),
              ),
            ),
           ),
            TextButton(
              onPressed: _clearFilters,
              child: 
                '清除筛选',
                style: TextStyle(TextStyle(fontSize: 12),
              ),
            ),
          ],
        ),
       SizedBox(height: 12),
        // 改为垂直列表，显示前3个结果
        ...List.generate(
          _filteredRecommendations.length > 3 ? 3 : _filteredRecommendations.length,
          (index) => Padding(
            padding: EdgeInsets.only(bottom: 12),
            child: _buildVerticalRecommendationCard(_filteredRecommendations[index]),
          ),
        ),
        // 如果有更多结果，显示"查看全部"按钮
        if (_filteredRecommendations.length > 3)
          Center(
            child: TextButton(
              onPressed: () => _showAllFilteredRecommendations(),
              child: Text(
                '查看全部 ${_filteredRecommendations.length} 个结果',
                style: 
                  color: ColorTokens.warning,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
      ],
    );
  }

  /// 显示筛选面板
  void _showFilterPanel() {
    if (_filterOptions == null) return;

    MobileBottomSheet.show(
      context: context,
      child: SpotFilterPanelWidget(
        filterOptions: _filterOptions!,
        currentFilter: _currentFilter,
        onFilterChanged: (filter) {
          setState(() {
            _currentFilter = filter;
          });
          _applyFilterAndSort();
          Navigator.of(context).pop();
        },
        onReset: () {
          setState(() {
            _currentFilter = const SpotFilterCriteria();
          });
          _applyFilterAndSort();
          Navigator.of(context).pop();
        },
      ),
    );
  }

  /// 显示排序选择器
  void _showSortSelector() {
    MobileBottomSheet.show(
      context: context,
      child: SpotSortSelectorWidget(
        currentSort: _currentSort,
        onSortChanged: (sort) {
          setState(() {
            _currentSort = sort;
          });
          _applyFilterAndSort();
        },
      ),
    );
  }

  /// 清除筛选
  void _clearFilters() {
    setState(() {
      _currentFilter = const SpotFilterCriteria();
    });
    _applyFilterAndSort();
  }

  /// 显示离线数据管理器
  void _showOfflineDataManager() {
    MobileBottomSheet.show(
      context: context,
      child: const OfflineDataManagerWidget(),
    );
  }

  /// 显示钓点发现历史
  void _showDiscoveryHistory() {
    MobileBottomSheet.show(
      context: context,
      child: const SpotDiscoveryHistoryWidget(),
    );
  }

  /// 显示社交动态
  void _showSocialFeed() {
    MobileBottomSheet.show(
      context: context,
      child: const SocialFeedWidget(),
    );
  }

  /// 显示搜索统计
  Future<void> _showSearchStats() async {
    if (_searchIndexService == null) return;
    
    try {
      final stats = await _searchIndexService!.getSearchStats();
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: 
            children: const [
              Icon(Icons.analytics, color: ColorTokens.primary),
             SizedBox(width: 8),
              Text('搜索统计'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: const [
              _buildStatRow('总搜索次数', '${stats.totalSearches}次'),
              _buildStatRow('独特查询', '${stats.uniqueQueries}个'),
              _buildStatRow('平均结果数', '${stats.averageResultsCount.toStringAsFixed(1)}个'),
              if (stats.topQueries.isNotEmpty) ...[ 
                SizedBox(SizedBox(height: SpacingTokens.space3),
               
                  '热门搜索词',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
               SizedBox(height: 4),
                Wrap(
                  spacing: 4,
                  runSpacing: 4,
                  children: stats.topQueries.take(5).map((query) => 
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: 
                        color: ColorTokens.error.withValues(alpha: 0.1),
                        borderRadius: ShapeTokens.borderRadiusMd,
                      ),
                      child: Text(
                        query,
                        style: 
                          TextStyle(fontSize: 12,
                          color: ColorTokens.primary,
                        ),
                      ),
                    ),
                  ).toList(),
                ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: '关闭'),
            ),
            TextButton(
              onPressed: () async {
                await _searchIndexService!.clearSearchStats();
                Navigator.of(context).pop();
                ScaffoldMessenger.of(context).showSnackBar(
                 const SnackBar(content: Text('搜索统计已清除')),
                );
              },
              child: '清除统计'),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('获取搜索统计失败: $e');
    }
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: const [
          Text(label, style: TextStyle(color: ColorTokens.onSurfaceVariant[600])),
          Text(value, style: fontWeight: FontWeight.w600)),
        ],
      ),
    );
  }

  /// 显示热门搜索
  Future<void> _showPopularSearches() async {
    if (_searchIndexService == null) return;
    
    try {
      final popularSearches = await _searchIndexService!.getPopularSearches(20);
      
      if (popularSearches.isEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(content: Text('暂无热门搜索记录')),
        );
        return;
      }
      
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: 
            children: const [
              Icon(Icons.trending_up, color: ColorTokens.primary),
             SizedBox(width: 8),
              Text('热门搜索'),
            ],
          ),
          content: SizedBox(
            SizedBox(width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: popularSearches.length,
              itemBuilder: (context, index) {
                final search = popularSearches[index];
                return ListTile(
                  dense: true,
                  leading: CircleAvatar(
                    radius: 12,
                    backgroundColor: ColorTokens.primary,
                    child: Text(
                      '${index + 1}',
                      style: 
                        color: ColorTokens.surface,
                        TextStyle(fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(search),
                  trailing: Icons.search, size: 16),
                  onTap: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop(); // 关闭搜索界面
                    _searchController.text = search;
                    setState(() {
                      _searchQuery = search;
                    });
                    _performSearch(search);
                    _showSpotSearchModal(); // 重新打开搜索界面
                  },
                );
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: '关闭'),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('获取热门搜索失败: $e');
    }
  }

  /// 执行智能推荐
  Future<void> _performSmartRecommendation() async {
    if (_recommendationService == null) return;

    setState(() {
      _isLoadingRecommendations = true;
    });

    try {
      // 获取用户当前位置
      Position? position = await RealSpotRecommendationService.getCurrentLocation();
      
      position ??= Position(
        latitude: 39.9042,
        longitude: 116.4074,
        timestamp: DateTime.now(),
        accuracy: 0,
        altitude: 0,
        heading: 0,
        speed: 0,
        speedAccuracy: 0,
        altitudeAccuracy: 0,
        headingAccuracy: 0,
      );

      // 基于筛选条件获取智能推荐
      final recommendations = await _recommendationService!.getSmartRecommendations(
        userLatitude: position.latitude,
        userLongitude: position.longitude,
        activityType: widget.activityType,
        selectedMethods: widget.selectedMethods,
        selectedFishes: widget.selectedFishes,
        limit: 30, // 获取更多数据以便筛选
      );

      // 如果有费用类型偏好，确保筛选条件正确
      SpotFilterCriteria filterToApply = _currentFilter;
      if (widget.feeType == '免费' && !filterToApply.freeOnly) {
        filterToApply = filterToApply.copyWith(freeOnly: true, paidOnly: false);
      }

      // 应用筛选条件
      final filteredSpots = SpotFilterSortService.filterSpots(recommendations, filterToApply);
      
      // 应用排序
      final sortedSpots = SpotFilterSortService.sortSpots(filteredSpots, _currentSort);

      setState(() {
        _recommendedSpots = sortedSpots.take(20).toList(); // 限制显示数量
        _filteredRecommendations = sortedSpots;
        _isLoadingRecommendations = false;
      });

      // 显示推荐结果提示
      if (sortedSpots.isNotEmpty) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: const [
               Icons.psychology, color: ColorTokens.onPrimary, size: 20),
               SizedBox(width: 8),
                Text('已为您推荐 ${sortedSpots.length} 个符合条件的钓点'),
              ],
            ),
            backgroundColor: ColorTokens.warning,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Text('暂无符合当前筛选条件的钓点，请调整筛选条件'),
            backgroundColor: ColorTokens.onSurfaceVariant,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      setState(() {
        _isLoadingRecommendations = false;
      });
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('智能推荐失败: $e'),
          backgroundColor: ColorTokens.error,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
