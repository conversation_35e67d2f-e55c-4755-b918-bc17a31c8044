import 'package:flutter/material.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/models/fishing_plan/public_event.dart';
import 'package:user_app/features/public_events/services/public_event_service.dart';
import 'package:user_app/features/public_events/widgets/event_analytics_widget.dart';
import 'package:user_app/features/public_events/widgets/event_social_widget.dart';
import 'package:user_app/features/public_events/widgets/event_weather_widget.dart';
import 'package:user_app/features/public_events/screens/event_live_page.dart';
import 'package:user_app/features/public_events/screens/public_event_detail_page.dart';
import 'package:user_app/features/public_events/screens/create_public_event_page.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 活动组织者控制台
class EventOrganizerDashboard extends StatefulWidget {
 const EventOrganizerDashboard({super.key});

  @override
  State<EventOrganizerDashboard> createState() => _EventOrganizerDashboardState();
}

class _EventOrganizerDashboardState extends State<EventOrganizerDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final PublicEventService _eventService = PublicEventService();

  List<PublicEvent> _myEvents = [];
  List<PublicEvent> _activeEvents = [];
  List<PublicEvent> _upcomingEvents = [];
  List<PublicEvent> _pastEvents = [];
  
  final bool _isLoading = true;
  String? _error;
  
  // 统计数据
  final int _totalEvents = 0;
  final int _totalParticipants = 0;
  final double _averageRating = 0.0;
  final double _completionRate = 0.0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOrganizerData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadOrganizerData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      await _eventService.initialize();
      
      // 获取我创建的所有活动
      _myEvents = await _eventService.getMyCreatedEvents();
      
      // 分类活动
      _categorizeEvents();
      
      // 计算统计数据
      _calculateStats();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _categorizeEvents() {
    final now = DateTime.now();
    
    _activeEvents = _myEvents.where((event) {
      final eventStart = event.eventTime;
      final eventEnd = event.eventTime.add(const Duration(hours: 8)); // 假设活动持续8小时
      return now.isAfter(eventStart) && now.isBefore(eventEnd);
    }).toList();
    
    _upcomingEvents = _myEvents.where((event) {
      return event.eventTime.isAfter(now);
    }).toList();
    
    _pastEvents = _myEvents.where((event) {
      final eventEnd = event.eventTime.add(const Duration(hours: 8));
      return eventEnd.isBefore(now);
    }).toList();
  }

  void _calculateStats() {
    _totalEvents = _myEvents.length;
    _totalParticipants = _myEvents.fold(0, (sum, event) => sum + event.currentParticipants);
    
    // 计算平均评分（模拟数据）
    _averageRating = _myEvents.isEmpty ? 0.0 : 4.2;
    
    // 计算完成率（模拟数据）
    _completionRate = _pastEvents.isEmpty ? 0.0 : 0.85;
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('组织者控制台'),
          backgroundColor: ColorTokens.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
        ),
        body: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: ColorTokens.primary),
             const SizedBox(height: SpacingTokens.space4),
              Text('加载中...'),
            ],
          ),
        ),
      );
    }

    if (_error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('组织者控制台'),
          backgroundColor: ColorTokens.primary,
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 64, color: Theme.of(context).colorScheme.onSurfaceVariant),
             const SizedBox(height: SpacingTokens.space4),
              Text('加载失败', style: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant)),
             const SizedBox(height: SpacingTokens.space2),
              Text(_error!),
             const SizedBox(height: SpacingTokens.space4),
              ElevatedButton(
                onPressed: _loadOrganizerData,
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: _buildAppBar(),
      body: Column(
        children: [
          _buildStatsOverview(),
          _buildQuickActions(),
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildActiveEventsTab(),
                _buildUpcomingEventsTab(),
                _buildPastEventsTab(),
                _buildAnalyticsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _createNewEvent,
        backgroundColor: ColorTokens.primary,
        child: const Icon(Icons.add, color: Theme.of(context).colorScheme.onPrimary),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title: const Text('组织者控制台'),
      backgroundColor: ColorTokens.primary,
      foregroundColor: Theme.of(context).colorScheme.onPrimary,
      elevation: 0,
      actions: [
        IconButton(
          onPressed: () {
            // TODO: 显示通知
          },
          icon: const Icon(Icons.notifications_outlined),
        ),
        PopupMenuButton<String>(
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
           const PopupMenuItem(
              value: 'settings',
              child: Row(
                children: [
                  Icon(Icons.settings, size: 18),
                 const SizedBox(width: 8),
                  Text('设置'),
                ],
              ),
            ),
           const PopupMenuItem(
              value: 'help',
              child: Row(
                children: [
                  Icon(Icons.help_outline, size: 18),
                 const SizedBox(width: 8),
                  Text('帮助'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatsOverview() {
    return Container(
      margin: SpacingTokens.paddingMd,
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [ColorTokens.primary, Color(0xFFFF9F80)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ColorTokens.primary.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         const Text(
            '我的活动概览',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
         const SizedBox(height: SpacingTokens.space4),
          Row(
            children: [
              Expanded(
                child: _buildStatItem('总活动数', _totalEvents.toString(), Icons.event),
              ),
              Expanded(
                child: _buildStatItem('总参与者', _totalParticipants.toString(), Icons.people),
              ),
            ],
          ),
         const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildStatItem('平均评分', _averageRating.toStringAsFixed(1), Icons.star),
              ),
              Expanded(
                child: _buildStatItem('完成率', '${(_completionRate * 100).toStringAsFixed(0)}%', Icons.check_circle),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 16, color: Theme.of(context).colorScheme.onPrimary70),
           const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onPrimary70,
              ),
            ),
          ],
        ),
       const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onPrimary,
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _buildActionButton(
              '创建活动',
              Icons.add_circle_outline,
              _createNewEvent,
              Colors.blue,
            ),
          ),
         const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              '活动模板',
              Icons.bookmark_outline,
              _manageTemplates,
              Colors.green,
            ),
          ),
         const SizedBox(width: 12),
          Expanded(
            child: _buildActionButton(
              '消息通知',
              Icons.notifications_outlined,
              _viewNotifications,
              Colors.orange,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, VoidCallback onTap, Color color) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, size: 24, color: color),
           const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: ColorTokens.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Theme.of(context).colorScheme.onPrimary,
        unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
        labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        tabs: [
          Tab(text: '进行中 (${_activeEvents.length})'),
          Tab(text: '即将开始 (${_upcomingEvents.length})'),
          Tab(text: '已结束 (${_pastEvents.length})'),
         const Tab(text: '数据分析'),
        ],
      ),
    );
  }

  Widget _buildActiveEventsTab() {
    if (_activeEvents.isEmpty) {
      return _buildEmptyState('暂无进行中的活动', Icons.event_busy);
    }

    return RefreshIndicator(
      onRefresh: _loadOrganizerData,
      child: ListView.builder(
        padding: SpacingTokens.paddingMd,
        itemCount: _activeEvents.length,
        itemBuilder: (context, index) {
          return _buildEventCard(_activeEvents[index], EventStatus.active);
        },
      ),
    );
  }

  Widget _buildUpcomingEventsTab() {
    if (_upcomingEvents.isEmpty) {
      return _buildEmptyState('暂无即将开始的活动', Icons.schedule);
    }

    return RefreshIndicator(
      onRefresh: _loadOrganizerData,
      child: ListView.builder(
        padding: SpacingTokens.paddingMd,
        itemCount: _upcomingEvents.length,
        itemBuilder: (context, index) {
          return _buildEventCard(_upcomingEvents[index], EventStatus.upcoming);
        },
      ),
    );
  }

  Widget _buildPastEventsTab() {
    if (_pastEvents.isEmpty) {
      return _buildEmptyState('暂无已结束的活动', Icons.history);
    }

    return RefreshIndicator(
      onRefresh: _loadOrganizerData,
      child: ListView.builder(
        padding: SpacingTokens.paddingMd,
        itemCount: _pastEvents.length,
        itemBuilder: (context, index) {
          return _buildEventCard(_pastEvents[index], EventStatus.past);
        },
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    // 构造模拟的分析数据
    final analyticsData = EventAnalyticsData(
      totalEvents: _totalEvents,
      totalParticipants: _totalParticipants,
      averageRating: _averageRating,
      completionRate: _completionRate,
      eventTypeStats: [
        EventTypeStats(
          type: EventType.free,
          count: _myEvents.where((e) => e.eventType == EventType.free).length,
          totalParticipants: _myEvents
              .where((e) => e.eventType == EventType.free)
              .fold(0, (sum, e) => sum + e.currentParticipants),
          averageRating: 4.3,
        ),
        EventTypeStats(
          type: EventType.paid,
          count: _myEvents.where((e) => e.eventType == EventType.paid).length,
          totalParticipants: _myEvents
              .where((e) => e.eventType == EventType.paid)
              .fold(0, (sum, e) => sum + e.currentParticipants),
          averageRating: 4.1,
        ),
      ],
      monthlyStats: _generateMonthlyStats(),
      popularLocations: _generatePopularLocations(),
      tagAnalytics: [],
      demographics: const ParticipantDemographics(
        ageGroups: {'18-25': 30, '26-35': 45, '36-45': 20, '45+': 5},
        experienceLevels: {'新手': 40, '进阶': 35, '专家': 25},
        genderDistribution: {'男': 65, '女': 35},
        repeatParticipantRate: 0.72,
      ),
      topPerformingEvents: _generateTopPerformingEvents(),
    );

    return SingleChildScrollView(
      padding: SpacingTokens.paddingMd,
      child: EventAnalyticsWidget(
        data: analyticsData,
        onEventTapped: (eventId) {
          final event = _myEvents.where((e) => e.id == eventId).firstOrNull;
          if (event != null) {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => PublicEventDetailPage(eventId: eventId),
              ),
            );
          }
        },
      ),
    );
  }

  Widget _buildEmptyState(String message, IconData icon) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, size: 64, color: Theme.of(context).colorScheme.onSurfaceVariant),
         const SizedBox(height: SpacingTokens.space4),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          ElevatedButton(
            onPressed: _createNewEvent,
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text(
              '创建新活动',
              style: TextStyle(color: Theme.of(context).colorScheme.onPrimary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventCard(PublicEvent event, EventStatus status) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.onPrimary,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // 状态条
          Container(
            width: double.infinity,
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: _getStatusColor(status).withValues(alpha: 0.1),
              borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
            ),
            child: Row(
              children: [
                Icon(
                  _getStatusIcon(status),
                  size: 16,
                  color: _getStatusColor(status),
                ),
               const SizedBox(width: 8),
                Text(
                  _getStatusText(status),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: _getStatusColor(status),
                  ),
                ),
               const Spacer(),
                if (status == EventStatus.active)
                  GestureDetector(
                    onTap: () => _goToLivePage(event),
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 6,
                            height: 6,
                            decoration: const BoxDecoration(
                              color: Theme.of(context).colorScheme.onPrimary,
                              shape: BoxShape.circle,
                            ),
                          ),
                         const SizedBox(width: 4),
                         const Text(
                            'LIVE',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          
          // 活动内容
          Padding(
            padding: SpacingTokens.paddingMd,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            event.title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                         const SizedBox(height: 4),
                          Text(
                            event.location,
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: event.eventType == EventType.free 
                            ? Colors.green[100] 
                            : Colors.orange[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        event.eventType.displayName,
                        style: TextStyle(
                          fontSize: 10,
                          color: event.eventType == EventType.free 
                              ? Colors.green[700] 
                              : Colors.orange[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
               const SizedBox(height: 12),
                
                // 时间和参与者信息
                Row(
                  children: [
                    Icon(Icons.schedule, size: 14, color: Theme.of(context).colorScheme.onSurfaceVariant),
                   const SizedBox(width: 4),
                    Text(
                      DateFormat('yyyy-MM-dd HH:mm').format(event.eventTime),
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                   const Spacer(),
                    Icon(Icons.people, size: 14, color: Theme.of(context).colorScheme.onSurfaceVariant),
                   const SizedBox(width: 4),
                    Text(
                      '${event.currentParticipants}/${event.maxParticipants}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
                
               const SizedBox(height: SpacingTokens.space4),
                
                // 操作按钮
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: () => _viewEventDetail(event),
                        style: OutlinedButton.styleFrom(
                          side: const BorderSide(color: ColorTokens.primary),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          '查看详情',
                          style: TextStyle(
                            color: ColorTokens.primary,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                   const SizedBox(width: 12),
                    if (status == EventStatus.upcoming)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _editEvent(event),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: ColorTokens.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            '编辑',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    if (status == EventStatus.active)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _goToLivePage(event),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            '实时管理',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                    if (status == EventStatus.past)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _viewEventReport(event),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            '查看报告',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.onPrimary,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<MonthlyStats> _generateMonthlyStats() {
    final List<MonthlyStats> stats = [];
    final now = DateTime.now();
    
    for (int i = 5; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final eventsInMonth = _myEvents.where((event) {
        return event.eventTime.year == month.year && 
               event.eventTime.month == month.month;
      }).length;
      
      stats.add(MonthlyStats(
        month: month,
        eventsCount: eventsInMonth,
        participantsCount: eventsInMonth * 12, // 模拟数据
        revenue: eventsInMonth * 800.0, // 模拟数据
      ));
    }
    
    return stats;
  }

  List<PopularLocation> _generatePopularLocations() {
    final locationCounts = <String, int>{};
    
    for (final event in _myEvents) {
      locationCounts[event.location] = (locationCounts[event.location] ?? 0) + 1;
    }
    
    final sortedLocations = locationCounts.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    return sortedLocations.take(5).map((entry) {
      return PopularLocation(
        name: entry.key,
        eventsCount: entry.value,
        successRate: 0.85 + (entry.value * 0.02), // 模拟成功率
      );
    }).toList();
  }

  List<EventPerformanceMetric> _generateTopPerformingEvents() {
    return _pastEvents.take(5).map((event) {
      return EventPerformanceMetric(
        eventId: event.id,
        eventTitle: event.title,
        participantsCount: event.currentParticipants,
        rating: 4.0 + (event.currentParticipants / 100), // 模拟评分
        profitability: 75.0 + (event.currentParticipants * 2), // 模拟盈利率
        eventDate: event.eventTime,
      );
    }).toList();
  }

  Color _getStatusColor(EventStatus status) {
    switch (status) {
      case EventStatus.active:
        return Colors.red;
      case EventStatus.upcoming:
        return Colors.blue;
      case EventStatus.past:
        return Colors.grey;
    }
  }

  IconData _getStatusIcon(EventStatus status) {
    switch (status) {
      case EventStatus.active:
        return Icons.play_circle_filled;
      case EventStatus.upcoming:
        return Icons.schedule;
      case EventStatus.past:
        return Icons.check_circle;
    }
  }

  String _getStatusText(EventStatus status) {
    switch (status) {
      case EventStatus.active:
        return '进行中';
      case EventStatus.upcoming:
        return '即将开始';
      case EventStatus.past:
        return '已结束';
    }
  }

  void _createNewEvent() {
    context.navigateToWithResult(AppRoutes.createPublicEvent).then((result) {
      if (result == true) {
        _loadOrganizerData();
      }
    });
  }

  void _manageTemplates() {
    // TODO: 实现模板管理功能
    ScaffoldMessenger.of(context).showSnackBar(
     const SnackBar(content: Text('模板管理功能开发中...')),
    );
  }

  void _viewNotifications() {
    // TODO: 实现通知查看功能
    ScaffoldMessenger.of(context).showSnackBar(
     const SnackBar(content: Text('通知功能开发中...')),
    );
  }

  void _viewEventDetail(PublicEvent event) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PublicEventDetailPage(eventId: event.id),
      ),
    );
  }

  void _editEvent(PublicEvent event) {
    // TODO: 实现编辑活动功能
    ScaffoldMessenger.of(context).showSnackBar(
     const SnackBar(content: Text('编辑功能开发中...')),
    );
  }

  void _goToLivePage(PublicEvent event) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => EventLivePage(eventId: event.id),
      ),
    );
  }

  void _viewEventReport(PublicEvent event) {
    // TODO: 实现活动报告功能
    ScaffoldMessenger.of(context).showSnackBar(
     const SnackBar(content: Text('活动报告功能开发中...')),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'settings':
        // TODO: 设置页面
        break;
      case 'help':
        // TODO: 帮助页面
        break;
    }
  }
}

enum EventStatus {
  active,
  upcoming,
  past,
}