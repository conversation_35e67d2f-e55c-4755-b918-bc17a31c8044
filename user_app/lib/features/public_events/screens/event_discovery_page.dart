import 'package:flutter/material.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/public_events/services/event_recommendation_service.dart';
import 'package:user_app/features/public_events/services/event_notification_service.dart';
import 'package:user_app/models/fishing_plan/public_event.dart';
import 'package:user_app/models/fishing_plan/event_notification.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/features/public_events/widgets/public_event_card.dart';
import 'package:user_app/features/public_events/screens/public_event_detail_page.dart';
import 'package:user_app/features/public_events/screens/event_search_page.dart';
import 'package:user_app/features/public_events/screens/event_notifications_page.dart';
import 'package:intl/intl.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 活动发现页面 - 个性化推荐中心
class EventDiscoveryPage extends StatefulWidget {
 const EventDiscoveryPage({super.key});

  @override
  State<EventDiscoveryPage> createState() => _EventDiscoveryPageState();
}

class _EventDiscoveryPageState extends State<EventDiscoveryPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final EventRecommendationService _recommendationService = EventRecommendationService();
  final EventNotificationService _notificationService = EventNotificationService();

  List<RecommendationResult> _personalizedRecommendations = [];
  List<RecommendationResult> _trendingRecommendations = [];
  List<RecommendationResult> _friendsRecommendations = [];
  List<EventNotification> _recentNotifications = [];
  final int _unreadNotificationCount = 0;

  final bool _isLoading = true;
  String? _error;
  double? _userLatitude;
  double? _userLongitude;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadDiscoveryData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDiscoveryData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // 初始化服务
      await Future.wait([
        _recommendationService.initialize(),
        _notificationService.initialize(),
      ]);

      // TODO: 获取用户位置
      // final location = await _getCurrentLocation();
      // _userLatitude = location?.latitude;
      // _userLongitude = location?.longitude;

      // 并行加载推荐数据
      final results = await Future.wait([
        _recommendationService.getPersonalizedRecommendations(
          limit: 10,
          latitude: _userLatitude,
          longitude: _userLongitude,
        ),
        _recommendationService.getTrendingRecommendations(limit: 8),
        _recommendationService.getFriendsRecommendations(limit: 6),
        _notificationService.getUserNotifications(pageSize: 5, isRead: false),
        _notificationService.getUnreadCount(),
      ]);

      setState(() {
        _personalizedRecommendations = results[0] as List<RecommendationResult>;
        _trendingRecommendations = results[1] as List<RecommendationResult>;
        _friendsRecommendations = results[2] as List<RecommendationResult>;
        _recentNotifications = results[3] as List<EventNotification>;
        _unreadNotificationCount = results[4] as int;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const UnifiedPageScaffold(
        title: '发现活动',
        body: UnifiedLoadingState(message: '为您推荐精彩活动...'),
      );
    }

    if (_error != null) {
      return UnifiedPageScaffold(
        title: '发现活动',
        body: UnifiedEmptyState(
          icon: Icons.error_outline,
          title: '加载失败',
          subtitle: _error!,
          action: ElevatedButton(
            onPressed: _loadDiscoveryData,
            child: const Text('重试'),
          ),
        ),
      );
    }

    return UnifiedPageScaffold(
      title: '发现活动',
      actions: [
        // 通知按钮
        Stack(
          children: [
            IconButton(
              onPressed: _navigateToNotifications,
              icon: const Icon(Icons.notifications_outlined),
            ),
            if (_unreadNotificationCount > 0)
              Positioned(
                right: 6,
                top: 6,
                child: Container(
                  padding: EdgeInsets.all(4),
                  decoration: const BoxDecoration(
                    color: Theme.of(context).colorScheme.error,
                    shape: BoxShape.circle,
                  ),
                 constraints: const BoxConstraints(
                    minWidth: 16,
                    minHeight: 16,
                  ),
                  child: Text(
                    _unreadNotificationCount > 99 
                        ? '99+' 
                        : _unreadNotificationCount.toString(),
                    style: const TextStyle(
                      color: Theme.of(context).colorScheme.surface,
                      // 使用 Theme.of(context).textTheme
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
          ],
        ),
        // 搜索按钮
        IconButton(
          onPressed: _navigateToSearch,
          icon: const Icon(Icons.search),
        ),
      ],
      body: RefreshIndicator(
        onRefresh: _loadDiscoveryData,
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 通知快览卡片
              if (_recentNotifications.isNotEmpty)
                _buildNotificationQuickView(),

              // 个性化推荐
              _buildRecommendationSection(
                '为您推荐',
                '基于您的兴趣爱好精心挑选',
                Icons.favorite,
                _personalizedRecommendations,
                isPersonalized: true,
              ),

              // 热门活动
              _buildRecommendationSection(
                '热门活动',
                '大家都在参与的精彩活动',
                Icons.local_fire_department,
                _trendingRecommendations,
                showTrending: true,
              ),

              // 朋友参与
              if (_friendsRecommendations.isNotEmpty)
                _buildRecommendationSection(
                  '朋友参与',
                  '您的朋友也在关注这些活动',
                  Icons.people,
                  _friendsRecommendations,
                  showSocial: true,
                ),

             const SizedBox(height: 80), // 为底部导航栏留出空间
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationQuickView() {
    return Container(
      margin: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
             const Icon(
                Icons.notifications_active,
                color: ColorTokens.primary,
                size: 20,
              ),
             const SizedBox(width: 8),
             const Text(
                '最新消息',
                style: TextStyle(
                  // 使用 Theme.of(context).textTheme
                  fontWeight: FontWeight.bold,
                ),
              ),
             const Spacer(),
              TextButton(
                onPressed: _navigateToNotifications,
                child: const Text('查看全部'),
              ),
            ],
          ),
         const SizedBox(height: 12),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _recentNotifications.length,
              itemBuilder: (context, index) {
                final notification = _recentNotifications[index];
                return _buildNotificationCard(notification);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(EventNotification notification) {
    final typeColor = Color(
      int.parse(notification.type.colorHex.substring(1), radix: 16),
    );

    return Container(
      width: 280,
      margin: EdgeInsets.only(right: 12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: typeColor.withValues(alpha: 0.3)),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: ColorTokens.transparent,
        child: InkWell(
          onTap: () => _onNotificationTap(notification),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(6),
                      decoration: BoxDecoration(
                        color: typeColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Icon(
                        _getNotificationIcon(notification.type.iconName),
                        color: typeColor,
                        size: 16,
                      ),
                    ),
                   const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        notification.type.displayName,
                        style: TextStyle(
                          // 使用 Theme.of(context).textTheme
                          color: typeColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    Text(
                      notification.getTimeAgo(),
                      style: TextStyle(
                        // 使用 Theme.of(context).textTheme
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
               const SizedBox(height: SpacingTokens.space2),
                Text(
                  notification.title,
                  style: const TextStyle(
                    // 使用 Theme.of(context).textTheme
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
               const SizedBox(height: 4),
                Text(
                  notification.content,
                  style: TextStyle(
                    // 使用 Theme.of(context).textTheme
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendationSection(
    String title,
    String subtitle,
    IconData icon,
    List<RecommendationResult> recommendations, {
    final bool isPersonalized = false,
    final bool showTrending = false,
    final bool showSocial = false,
  }) {
    if (recommendations.isEmpty) return const SizedBox.shrink();

    return Container(
      margin: EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              children: [
                Icon(icon, color: ColorTokens.primary, size: 24),
               const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          // 使用 Theme.of(context).textTheme
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        subtitle,
                        style: TextStyle(
                          // 使用 Theme.of(context).textTheme
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
         const SizedBox(height: SpacingTokens.space4),
          SizedBox(
            height: isPersonalized ? 320 : 280,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              padding: EdgeInsets.symmetric(horizontal: 16),
              itemCount: recommendations.length,
              itemBuilder: (context, index) {
                final recommendation = recommendations[index];
                return Container(
                  width: 280,
                  margin: EdgeInsets.only(right: 16),
                  child: _buildRecommendationCard(
                    recommendation,
                    isPersonalized: isPersonalized,
                    showTrending: showTrending,
                    showSocial: showSocial,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(
    RecommendationResult recommendation, {
    final bool isPersonalized = false,
    final bool showTrending = false,
    final bool showSocial = false,
  }) {
    final event = recommendation.event;

    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.08),
            blurRadius: 15,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Material(
        color: ColorTokens.transparent,
        child: InkWell(
          onTap: () => _onEventTap(event),
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 事件图片和徽章
              Stack(
                children: [
                  Container(
                    height: 160,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                      image: event.images.isNotEmpty
                          ? DecorationImage(
                              image: NetworkImage(event.images.first),
                              fit: BoxFit.cover,
                            )
                          : null,
                      color: event.images.isEmpty ? Theme.of(context).colorScheme.surfaceContainerHigh : null,
                    ),
                    child: event.images.isEmpty
                        ? Center(
                            child: Icon(
                              Icons.event,
                              size: 48,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          )
                        : null,
                  ),
                  // 推荐徽章
                  if (showTrending)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.error[400],
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.local_fire_department, size: 12, color: Theme.of(context).colorScheme.surface),
                           const SizedBox(width: 4),
                            Text(
                              '热门',
                              style: TextStyle(
                                // 使用 Theme.of(context).textTheme
                                color: Theme.of(context).colorScheme.surface,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  if (showSocial)
                    Positioned(
                      top: 12,
                      left: 12,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.people, size: 12, color: Theme.of(context).colorScheme.surface),
                           const SizedBox(width: 4),
                            Text(
                              '朋友参与',
                              style: TextStyle(
                                // 使用 Theme.of(context).textTheme
                                color: Theme.of(context).colorScheme.surface,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  // 推荐得分
                  if (isPersonalized && recommendation.score > 0.8)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.tertiary,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.recommend, size: 12, color: Theme.of(context).colorScheme.surface),
                           const SizedBox(width: 4),
                            Text(
                              '推荐',
                              style: TextStyle(
                                // 使用 Theme.of(context).textTheme
                                color: Theme.of(context).colorScheme.surface,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
              
              // 活动信息
              Expanded(
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 活动标题
                      Text(
                        event.title,
                        style: const TextStyle(
                          // 使用 Theme.of(context).textTheme
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                     const SizedBox(height: SpacingTokens.space2),
                      
                      // 时间和地点
                      Row(
                        children: [
                          Icon(Icons.schedule, size: 14, color: Theme.of(context).colorScheme.onSurfaceVariant),
                         const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              DateFormat('MM月dd日 HH:mm').format(event.eventTime),
                              style: TextStyle(
                                // 使用 Theme.of(context).textTheme
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ),
                        ],
                      ),
                     const SizedBox(height: 4),
                      Row(
                        children: [
                          Icon(Icons.location_on, size: 14, color: Theme.of(context).colorScheme.onSurfaceVariant),
                         const SizedBox(width: 4),
                          Expanded(
                            child: Text(
                              event.location,
                              style: TextStyle(
                                // 使用 Theme.of(context).textTheme
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),

                     const Spacer(),

                      // 推荐理由（仅个性化推荐显示）
                      if (isPersonalized && recommendation.reasons.isNotEmpty) ...[ 
                       const SizedBox(height: SpacingTokens.space2),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: ColorTokens.primary.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            recommendation.reasons.first,
                            style: const TextStyle(
                              fontSize: 11,
                              color: ColorTokens.primary,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],

                     const SizedBox(height: SpacingTokens.space2),

                      // 报名状态
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: _getEventTypeColor(event.eventType).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              event.eventType.displayName,
                              style: TextStyle(
                                // 使用 Theme.of(context).textTheme
                                color: _getEventTypeColor(event.eventType),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                         const Spacer(),
                          Text(
                            '${event.currentParticipants}/${event.maxParticipants}人',
                            style: TextStyle(
                              // 使用 Theme.of(context).textTheme
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getEventTypeColor(EventType type) {
    switch (type) {
      case EventType.free:
        return ColorTokens.success;
      case EventType.paid:
        return ColorTokens.warning;
      case EventType.competition:
        return ColorTokens.tertiary;
      case EventType.workshop:
        return ColorTokens.info;
    }
  }

  IconData _getNotificationIcon(String iconName) {
    switch (iconName) {
      case 'schedule':
        return Icons.schedule;
      case 'check_circle':
        return Icons.check_circle;
      case 'cancel':
        return Icons.cancel;
      case 'edit':
        return Icons.edit;
      case 'event_busy':
        return Icons.event_busy;
      case 'event_available':
        return Icons.event_available;
      case 'group':
        return Icons.group;
      case 'trending_up':
        return Icons.trending_up;
      default:
        return Icons.notifications;
    }
  }

  void _onEventTap(PublicEvent event) {
    // 记录用户点击行为
    _recommendationService.recordUserAction(event.id, 'view');

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PublicEventDetailPage(eventId: event.id),
      ),
    ).then((_) {
      // 返回后刷新推荐
      _loadDiscoveryData();
    });
  }

  void _onNotificationTap(EventNotification notification) {
    // 标记为已读
    if (!notification.isRead) {
      _notificationService.markAsRead(notification.id);
    }

    // 如果有关联活动，跳转到活动详情
    if (notification.eventId != null) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PublicEventDetailPage(eventId: notification.eventId!),
        ),
      );
    }
  }

  void _navigateToNotifications() {
    context.navigateToWithResult(AppRoutes.eventNotifications).then((_) {
      // 返回后刷新通知数量
      _loadDiscoveryData();
    });
  }

  void _navigateToSearch() {
    context.navigateTo(AppRoutes.eventSearch);
  }
}