import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/models/fishing_plan/public_event.dart';
import 'package:user_app/features/public_events/services/public_event_service.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/features/public_events/widgets/public_event_card.dart';
import 'package:user_app/features/public_events/widgets/event_filter_widget.dart';
import 'package:user_app/features/public_events/screens/create_public_event_page.dart';
import 'package:user_app/features/public_events/screens/public_event_detail_page.dart';
import 'package:user_app/features/public_events/screens/organizer_dashboard_page.dart';
import 'package:user_app/features/public_events/screens/event_search_page.dart';
import 'package:user_app/features/public_events/screens/my_event_registrations_page.dart';
import 'package:user_app/utils/permission_guard.dart';
import 'package:user_app/models/user/user_role.dart';
import 'package:intl/intl.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 公开活动页面
class PublicEventsPage extends StatefulWidget {
 const PublicEventsPage({super.key});

  @override
  State<PublicEventsPage> createState() => _PublicEventsPageState();
}

class _PublicEventsPageState extends State<PublicEventsPage>
    with SingleTickerProviderStateMixin, PermissionAwareMixin {
  late TabController _tabController;
  final PublicEventService _eventService = PublicEventService();
  final ScrollController _scrollController = ScrollController();
  
  List<PublicEvent> _allEvents = [];
  List<PublicEvent> _hotEvents = [];
  List<PublicEvent> _recommendedEvents = [];
  
  final bool _isLoading = true;
  final bool _isLoadingMore = false;
  String? _error;
  
  // 过滤条件
  EventType? _selectedEventType;
  List<EventTag> _selectedTags = [];
  String? _selectedLocation;
  String? _searchKeyword;
  
  final int _currentPage = 1;
  static const int _pageSize = 20;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_onScroll);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _tabController.index == 0) {
        _loadMoreEvents();
      }
    }
  }

  Future<void> _loadInitialData() async {
    await Future.wait([
      _loadAllEvents(refresh: true),
      _loadHotEvents(),
      _loadRecommendedEvents(),
    ]);
  }

  Future<void> _loadAllEvents({bool refresh = false}) async {
    try {
      if (refresh) {
        setState(() {
          _isLoading = true;
          _error = null;
          _currentPage = 1;
        });
      }

      await _eventService.initialize();
      final events = await _eventService.getPublicEvents(
        page: refresh ? 1 : _currentPage,
        pageSize: _pageSize,
        eventType: _selectedEventType,
        tags: _selectedTags.isNotEmpty ? _selectedTags : null,
        location: _selectedLocation,
        keyword: _searchKeyword,
      );

      setState(() {
        if (refresh) {
          _allEvents = events;
        } else {
          _allEvents.addAll(events);
        }
        _isLoading = false;
        _isLoadingMore = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
        _isLoadingMore = false;
      });
    }
  }

  Future<void> _loadMoreEvents() async {
    if (_isLoadingMore) return;
    
    setState(() {
      _isLoadingMore = true;
      _currentPage++;
    });
    
    await _loadAllEvents();
  }

  Future<void> _loadHotEvents() async {
    try {
      final events = await _eventService.getHotEvents();
      setState(() {
        _hotEvents = events;
      });
    } catch (e) {
      debugPrint('Error loading hot events: $e');
    }
  }

  Future<void> _loadRecommendedEvents() async {
    try {
      final events = await _eventService.getRecommendedEvents();
      setState(() {
        _recommendedEvents = events;
      });
    } catch (e) {
      debugPrint('Error loading recommended events: $e');
    }
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EventFilterWidget(
        selectedEventType: _selectedEventType,
        selectedTags: _selectedTags,
        selectedLocation: _selectedLocation,
        onFilterChanged: (eventType, tags, location) {
          setState(() {
            _selectedEventType = eventType;
            _selectedTags = tags;
            _selectedLocation = location;
          });
          _loadAllEvents(refresh: true);
        },
      ),
    );
  }

  void _navigateToSearchPage() {
    context.navigateTo(AppRoutes.eventSearch);
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '公开活动',
      actions: [
        IconButton(
          onPressed: _navigateToMyRegistrations,
          icon: const Icon(Icons.bookmark_outline),
          tooltip: '我的报名',
        ),
        PermissionGuard.organizerOnly(
          child: IconButton(
            onPressed: _navigateToOrganizerDashboard,
            icon: const Icon(Icons.dashboard),
            tooltip: '组织者控制台',
          ),
        ),
        IconButton(
          onPressed: _navigateToSearchPage,
          icon: const Icon(Icons.search),
        ),
        IconButton(
          onPressed: _showFilterDialog,
          icon: const Icon(Icons.filter_list),
        ),
      ],
      body: Column(
        children: [
          _buildTabBar(),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllEventsTab(),
                _buildHotEventsTab(),
                _buildRecommendedEventsTab(),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createEvent,
        label: const Text('创建活动'),
        icon: const Icon(Icons.add),
        backgroundColor: ColorTokens.primary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          color: ColorTokens.primary,
          borderRadius: BorderRadius.circular(12),
        ),
        labelColor: Colors.white,
        unselectedLabelColor: Colors.grey[600],
        labelStyle: const TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 14,
        ),
        tabs: const [
          Tab(text: '全部活动'),
          Tab(text: '热门活动'),
          Tab(text: '推荐活动'),
        ],
      ),
    );
  }

  Widget _buildAllEventsTab() {
    if (_isLoading) {
      return const UnifiedLoadingState(message: '加载中...');
    }

    if (_error != null) {
      return UnifiedEmptyState(
        icon: Icons.error_outline,
        title: '加载失败',
        subtitle: _error!,
        action: ElevatedButton(
          onPressed: () => _loadAllEvents(refresh: true),
          child: const Text('重试'),
        ),
      );
    }

    if (_allEvents.isEmpty) {
      return UnifiedEmptyState(
        icon: Icons.event_busy,
        title: '暂无活动',
        subtitle: _hasFilters() ? '没有符合条件的活动' : '还没有公开活动',
        action: _hasFilters() 
            ? TextButton(
                onPressed: _clearFilters,
                child: const Text('清除筛选条件'),
              )
            : null,
      );
    }

    return RefreshIndicator(
      onRefresh: () => _loadAllEvents(refresh: true),
      child: ListView.builder(
        controller: _scrollController,
        padding: SpacingTokens.paddingMd,
        itemCount: _allEvents.length + (_isLoadingMore ? 1 : 0),
        itemBuilder: (context, index) {
          if (index == _allEvents.length) {
            return const Center(
              child: Padding(
                padding: SpacingTokens.paddingMd,
                child: CircularProgressIndicator(),
              ),
            );
          }
          
          final event = _allEvents[index];
          return PublicEventCard(
            event: event,
            onTap: () => _navigateToEventDetail(event),
          );
        },
      ),
    );
  }

  Widget _buildHotEventsTab() {
    if (_hotEvents.isEmpty) {
      return const UnifiedEmptyState(
        icon: Icons.local_fire_department,
        title: '暂无热门活动',
        subtitle: '快来参与更多活动吧',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadHotEvents,
      child: ListView.builder(
        padding: SpacingTokens.paddingMd,
        itemCount: _hotEvents.length,
        itemBuilder: (context, index) {
          final event = _hotEvents[index];
          return PublicEventCard(
            event: event,
            onTap: () => _navigateToEventDetail(event),
            showHotBadge: true,
          );
        },
      ),
    );
  }

  Widget _buildRecommendedEventsTab() {
    if (_recommendedEvents.isEmpty) {
      return const UnifiedEmptyState(
        icon: Icons.recommend,
        title: '暂无推荐活动',
        subtitle: '为您推荐更多精彩活动',
      );
    }

    return RefreshIndicator(
      onRefresh: _loadRecommendedEvents,
      child: ListView.builder(
        padding: SpacingTokens.paddingMd,
        itemCount: _recommendedEvents.length,
        itemBuilder: (context, index) {
          final event = _recommendedEvents[index];
          return PublicEventCard(
            event: event,
            onTap: () => _navigateToEventDetail(event),
            showRecommendedBadge: true,
          );
        },
      ),
    );
  }

  bool _hasFilters() {
    return _selectedEventType != null || 
           _selectedTags.isNotEmpty || 
           _selectedLocation != null ||
           _searchKeyword != null;
  }

  void _clearFilters() {
    setState(() {
      _selectedEventType = null;
      _selectedTags.clear();
      _selectedLocation = null;
      _searchKeyword = null;
    });
    _loadAllEvents(refresh: true);
  }

  void _navigateToEventDetail(PublicEvent event) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PublicEventDetailPage(eventId: event.id),
      ),
    ).then((_) {
      // 从详情页返回后刷新数据
      _loadAllEvents(refresh: true);
    });
  }

  void _createEvent() {
    context.navigateTo(AppRoutes.createPublicEvent).then((created) {
      if (created == true) {
        // 创建成功后刷新列表
        _loadAllEvents(refresh: true);
      }
    });
  }

  void _navigateToOrganizerDashboard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const OrganizerDashboardPage(),
      ),
    );
  }

  void _navigateToMyRegistrations() {
    context.navigateTo(AppRoutes.myEventRegistrations);
  }
}