import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/chat/view_models/chat_view_model.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class StartChatButton extends StatelessWidget {
  final String targetUserId;
  final String? targetUserName;
  final String? targetUserAvatar;

 const StartChatButton({
    super.key,
    required this.targetUserId,
    this.targetUserName,
    this.targetUserAvatar,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<ChatViewModel>(
      builder: (context, chatViewModel, child) {
        return ElevatedButton.icon(
          onPressed: () => _startChat(context, chatViewModel),
          icon: const Icon(Icons.chat_bubble_outline),
          label: const Text('私信'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: ColorTokens.onPrimary,
            shape: const RoundedRectangleBorder(
              borderRadius: ShapeTokens.borderRadiusFull,
            ),
          ),
        );
      },
    );
  }

  Future<void> _startChat() async {
    try {
      // 确保IM已初始化
      if (!chatViewModel.isInitialized) {
        // 显示loading
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(color: Theme.of(context).colorScheme.primary),
                SpacingTokens.horizontalSpaceMd,
                Text('正在初始化聊天...', style: TypographyTokens.bodyLarge),
              ],
            ),
          ),
        );

        await chatViewModel.initialize();
        Navigator.of(context).pop(); // 关闭loading dialog
      }

      // 导航到聊天页面
      context.navigateTo('/chat/simple/c2c_$targetUserId', extra: {
        'conversationType': 1, // C2C
        'showName': targetUserName ?? '用户$targetUserId',
        'userID': targetUserId,
      });
    } catch (e) {
      // 关闭可能存在的loading dialog
      if (Navigator.canPop(context)) {
        Navigator.of(context).pop();
      }
      
      // 显示错误
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('无法开始聊天: $e', style: TypographyTokens.bodyLarge.copyWith(color: ColorTokens.onError)),
          backgroundColor: ColorTokens.error,
        ),
      );
    }
  }
}

/// 浮动聊天按钮组件，可以在其他页面使用
class FloatingChatButton extends StatelessWidget {
  final String targetUserId;
  final String? targetUserName;

 const FloatingChatButton({
    super.key,
    required this.targetUserId,
    this.targetUserName,
  });

  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () => _startChat(context),
      backgroundColor: Theme.of(context).colorScheme.primary,
      child: const Icon(Icons.chat, color: ColorTokens.onPrimary),
    );
  }

  void _startChat(BuildContext context) {
    context.navigateTo('/chat/simple/c2c_$targetUserId', extra: {
      'conversationType': 1,
      'showName': targetUserName ?? '用户$targetUserId',
      'userID': targetUserId,
    });
  }
}

/// 简单的聊天列表项组件
class ChatListItem extends StatelessWidget {
  final String conversationID;
  final String showName;
  final String? lastMessage;
  final String? avatarUrl;
  final int unreadCount;
  final DateTime? lastMessageTime;

 const ChatListItem({
    super.key,
    required this.conversationID,
    required this.showName,
    this.lastMessage,
    this.avatarUrl,
    this.unreadCount = 0,
    this.lastMessageTime,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: avatarUrl != null ? NetworkImage(avatarUrl!) : null,
        child: avatarUrl == null
            ? Text(showName.isNotEmpty ? showName[0] : '?')
            : null,
      ),
      title: Row(
        children: [
          Expanded(
            child: Text(
              showName,
              style: TypographyTokens.titleMedium.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (lastMessageTime != null)
            Text(
              _formatTime(lastMessageTime!),
              style: TypographyTokens.bodySmall.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
        ],
      ),
      subtitle: Row(
        children: [
          Expanded(
            child: Text(
              lastMessage ?? '暂无消息',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TypographyTokens.bodyMedium.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
          ),
          if (unreadCount > 0)
            Container(
              padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space2, vertical: SpacingTokens.space1),
              decoration: const BoxDecoration(
                color: ColorTokens.error,
                borderRadius: ShapeTokens.borderRadiusSm,
              ),
              child: Text(
                unreadCount > 99 ? '99+' : unreadCount.toString(),
                style: TypographyTokens.bodySmall.copyWith(
                  color: ColorTokens.onError,
                ),
              ),
            ),
        ],
      ),
      onTap: () {
        context.navigateTo('/chat/simple/$conversationID', extra: {
          'conversationType': conversationID.startsWith('c2c_') ? 1 : 2,
          'showName': showName,
        });
      },
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays > 0) {
      return '${difference.inDays}天前';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}小时前';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}分钟前';
    } else {
      return '刚刚';
    }
  }
}