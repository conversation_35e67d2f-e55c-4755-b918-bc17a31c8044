import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/features/search/SearchSystem/SearchSystem.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class FollowedUsersPage extends StatefulWidget {
 const FollowedUsersPage({super.key});

  @override
  State<FollowedUsersPage> createState() => _FollowedUsersPageState();
}

class _FollowedUsersPageState extends State<FollowedUsersPage>
    with TickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _pulseController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;

  // 列表项动画
  final List<AnimationController> _itemControllers = [];

  // 搜索功能
  String _searchQuery = '';


  // 示例数据
  final List<FeedItem> _feedItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();

    // 加载数据
    _loadFeedData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadFeedData() {
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _feedItems.addAll([
          FeedItem(
            id: 1,
            userName: '钓鱼达人小李',
            userAvatar: null,
            content: '今天在东湖钓场收获满满！连上了好几条大鲤鱼，最大的一条有3斤多！',
            images: ['fishing1.jpg', 'fishing2.jpg'],
            location: '东湖钓场',
            time: DateTime.now().subtract(const Duration(hours: 2)),
            likes: 23,
            comments: 5,
            isLiked: true,
          ),
          FeedItem(
            id: 2,
            userName: '老王爱钓鱼',
            userAvatar: null,
            content: '分享一个小技巧：早晨5-7点是鱼儿最活跃的时候，用红虫做饵效果特别好',
            images: [],
            location: null,
            time: DateTime.now().subtract(const Duration(hours: 5)),
            likes: 15,
            comments: 8,
            isLiked: false,
          ),
          FeedItem(
            id: 3,
            userName: '渔乐无穷',
            userAvatar: null,
            content: '周末组织钓友聚会，有兴趣的朋友可以报名参加！地点：西山水库',
            images: ['spot.jpg'],
            location: '西山水库',
            time: DateTime.now().subtract(const Duration(days: 1)),
            likes: 45,
            comments: 12,
            isLiked: false,
          ),
        ]);
        _isLoading = false;
      });
    });
  }

  AnimationController _createItemController(int index) {
    // 确保列表有足够的控制器
    while (index >= _itemControllers.length) {
      final controllerIndex = _itemControllers.length;
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (controllerIndex * 100)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  Future<void> _onRefresh() async {
    HapticFeedback.mediumImpact();
    await Future.delayed(const Duration(seconds: 2));
    // TODO: 刷新数据
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '钓友圈',
      hasTabBar: true,
      tabController: _tabController,
      tabs: const [
        Tab(text: '动态'),
        Tab(text: '话题'),
        Tab(text: '钓友'),
      ],
      onRefresh: _onRefresh,
      enableSearch: true,
      onSearchChanged: (value) {
        setState(() {
          _searchQuery = value;
        });
      },
      actions: [
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () {
            UnifiedSearchService().quickSearch(
              context,
              UnifiedSearchService.USER_SEARCH,
            );
          },
        ),
      ],
      showAnimations: true,
      gradientColors: const [
        Theme.of(context).colorScheme.primary,
        Theme.of(context).colorScheme.secondary,
      ],
      backgroundDecoration: _buildBackgroundDecoration(),
      expandedHeight: 200,
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildFeedList(),
          _buildTopicsList(),
          _buildUsersList(),
        ],
      ),
      floatingActionButton: ScaleTransition(
        scale: _scaleAnimation,
        child: FloatingActionButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            _showCreateOptions();
          },
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: const Icon(Icons.add, color: Theme.of(context).colorScheme.surface),
        ),
      ),
    );
  }

  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 脉冲圆圈装饰
        Positioned(
          top: -80,
          left: -80,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 200,
                  height: 200,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
                  ),
                ),
              );
            },
          ),
        ),
        // 动态图标装饰
        Positioned(
          top: 100,
          right: 30,
          child: AnimatedBuilder(
            animation: _fadeAnimation,
            builder: (context, child) {
              return Opacity(
                opacity: _fadeAnimation.value * 0.15,
                child: const Icon(
                  Icons.group,
                  size: 60,
                  color: Theme.of(context).colorScheme.surface,
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  Widget _buildFeedList() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(
          color: Theme.of(context).colorScheme.primary,
        ),
      );
    }

    final filteredItems = _feedItems.where((item) {
      if (_searchQuery.isEmpty) return true;
      final query = _searchQuery.toLowerCase();
      return item.userName.toLowerCase().contains(query) ||
             item.content.toLowerCase().contains(query) ||
             (item.location?.toLowerCase().contains(query) ?? false);
    }).toList();

    if (filteredItems.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.only(bottom: 80),
      itemCount: filteredItems.length,
      itemBuilder: (context, index) {
        return _buildFeedItem(filteredItems[index], index);
      },
    );
  }

  Widget _buildFeedItem(FeedItem item, int index) {
    final controller = _createItemController(index);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (controller.value * 0.2),
          child: Opacity(
            opacity: controller.value,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Theme.of(context).colorScheme.surface, Theme.of(context).colorScheme.surfaceContainer],
                ),
                borderRadius: BorderRadius.circular(24),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.05),
                    blurRadius: 15,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: InkWell(
                onTap: () => _viewFeedDetail(item),
                borderRadius: BorderRadius.circular(24),
                child: Padding(
                  padding: SpacingTokens.paddingLg,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 用户信息
                      _buildUserInfo(item),
                     const SizedBox(height: SpacingTokens.space4),
                      // 内容
                      Text(
                        item.content,
                        style: const TextStyle(
                          // 使用 Theme.of(context).textTheme
                          height: 1.5,
                          color: ColorTokens.onSurface,
                        ),
                      ),
                      // 图片
                      if (item.images.isNotEmpty) ...[
                       const SizedBox(height: 12),
                        _buildImageGrid(item.images),
                      ],
                      // 位置信息
                      if (item.location != null) ...[
                       const SizedBox(height: 12),
                        _buildLocationTag(item.location!),
                      ],
                     const SizedBox(height: SpacingTokens.space4),
                      // 互动栏
                      _buildInteractionBar(item),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildUserInfo(FeedItem item) {
    return Row(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
            ),
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                blurRadius: 8,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Center(
            child: Text(
              item.userName.substring(0, 1),
              style: const TextStyle(
                color: Theme.of(context).colorScheme.surface,
                // 使用 Theme.of(context).textTheme
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
       const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                item.userName,
                style: const TextStyle(
                  // 使用 Theme.of(context).textTheme
                  fontWeight: FontWeight.bold,
                  color: ColorTokens.onSurface,
                ),
              ),
             const SizedBox(height: 2),
              Text(
                _formatTime(item.time),
                style: const TextStyle(
                  // 使用 Theme.of(context).textTheme
                  color: ColorTokens.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          icon: Icon(Icons.more_horiz, color: Theme.of(context).colorScheme.onSurfaceVariant),
          onPressed: () => _showMoreOptions(item),
        ),
      ],
    );
  }

  Widget _buildImageGrid(List<String> images) {
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: Container(
            color: Theme.of(context).colorScheme.outline,
            child: const Center(
              child: Icon(Icons.image, size: 40, color: Theme.of(context).colorScheme.onSurfaceVariant),
            ),
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: images.length > 4 ? 4 : images.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Container(
            color: Theme.of(context).colorScheme.outline,
            child: Stack(
              fit: StackFit.expand,
              children: [
               const Center(
                  child: Icon(Icons.image, size: 30, color: Theme.of(context).colorScheme.onSurfaceVariant),
                ),
                if (index == 3 && images.length > 4)
                  Container(
                    color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.5),
                    child: Center(
                      child: Text(
                        '+${images.length - 4}',
                        style: const TextStyle(
                          color: Theme.of(context).colorScheme.surface,
                          // 使用 Theme.of(context).textTheme
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildLocationTag(String location) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
         const Icon(Icons.location_on, size: 16, color: Theme.of(context).colorScheme.primary),
         const SizedBox(width: 4),
          Text(
            location,
            style: const TextStyle(
              // 使用 Theme.of(context).textTheme
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionBar(FeedItem item) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            _buildInteractionButton(
              icon: item.isLiked ? Icons.favorite : Icons.favorite_border,
              label: item.likes.toString(),
              color: item.isLiked ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.onSurfaceVariant!,
              onTap: () => _toggleLike(item),
            ),
           const SizedBox(width: 24),
            _buildInteractionButton(
              icon: Icons.chat_bubble_outline,
              label: item.comments.toString(),
              color: Theme.of(context).colorScheme.onSurfaceVariant!,
              onTap: () => _viewComments(item),
            ),
          ],
        ),
        IconButton(
          icon: Icon(Icons.share, color: Theme.of(context).colorScheme.onSurfaceVariant),
          onPressed: () => _shareFeed(item),
        ),
      ],
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Row(
        children: [
          Icon(icon, size: 22, color: color),
         const SizedBox(width: 6),
          Text(
            label,
            style: TextStyle(
              color: color,
              // 使用 Theme.of(context).textTheme
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopicsList() {
    final topics = [
      {'name': '#新手入门', 'posts': 156, 'hot': true},
      {'name': '#钓具推荐', 'posts': 98, 'hot': false},
      {'name': '#钓点分享', 'posts': 234, 'hot': true},
      {'name': '#技巧交流', 'posts': 87, 'hot': false},
    ];

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 20),
      itemCount: topics.length,
      itemBuilder: (context, index) {
        final topic = topics[index];
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: ListTile(
            onTap: () {
              HapticFeedback.lightImpact();
              // TODO: 查看话题详情
            },
            contentPadding:
                EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: topic['hot'] as bool
                      ? [Theme.of(context).colorScheme.error, Theme.of(context).colorScheme.errorContainer]
                      : [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Text(
                  '#',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.surface,
                    // 使用 Theme.of(context).textTheme
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            title: Text(
              topic['name'] as String,
              style: const TextStyle(
                // 使用 Theme.of(context).textTheme
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
            subtitle: Text(
              '${topic['posts']}条动态',
              style: const TextStyle(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (topic['hot'] as bool)
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Text(
                      '热门',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.error,
                        // 使用 Theme.of(context).textTheme
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
               const SizedBox(width: 8),
                Icon(Icons.chevron_right, color: Theme.of(context).colorScheme.onSurfaceVariant),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUsersList() {
    final users = [
      {'name': '钓鱼达人小李', 'followers': 1234, 'isFollowing': true},
      {'name': '老王爱钓鱼', 'followers': 856, 'isFollowing': true},
      {'name': '渔乐无穷', 'followers': 2341, 'isFollowing': false},
      {'name': '钓友小张', 'followers': 567, 'isFollowing': true},
    ];

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: 20),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        return Container(
          margin: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.05),
                blurRadius: 10,
                offset: Offset(0, 4),
              ),
            ],
          ),
          child: ListTile(
            onTap: () {
              HapticFeedback.lightImpact();
              // TODO: 查看用户主页
            },
            contentPadding: SpacingTokens.paddingMd,
            leading: Container(
              width: 56,
              height: 56,
              decoration: const BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                ),
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  (user['name'] as String).substring(0, 1),
                  style: const TextStyle(
                    color: Theme.of(context).colorScheme.surface,
                    // 使用 Theme.of(context).textTheme
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
            title: Text(
              user['name'] as String,
              style: const TextStyle(
                // 使用 Theme.of(context).textTheme
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
            subtitle: Text(
              '${user['followers']}位粉丝',
              style: const TextStyle(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
            trailing: _buildFollowButton(user['isFollowing'] as bool),
          ),
        );
      },
    );
  }

  Widget _buildFollowButton(bool isFollowing) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // TODO: 切换关注状态
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          gradient: isFollowing
              ? LinearGradient(
                  colors: [Theme.of(context).colorScheme.outline!, Theme.of(context).colorScheme.onSurfaceVariant!],
                )
              : const LinearGradient(
                  colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                ),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          isFollowing ? '已关注' : '关注',
          style: TextStyle(
            color: isFollowing ? ColorTokens.onSurfaceVariant : Theme.of(context).colorScheme.surface,
            // 使用 Theme.of(context).textTheme
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return const UnifiedEmptyState(
      icon: Icons.group_outlined,
      title: '暂无动态',
      subtitle: '关注更多钓友，查看精彩动态',
    );
  }

  void _showCreateOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        padding: SpacingTokens.paddingXl,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
           const SizedBox(height: SpacingTokens.space5),
           const Text(
              '发布内容',
              style: TextStyle(
                // 使用 Theme.of(context).textTheme
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
           const SizedBox(height: SpacingTokens.space5),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildCreateOption(Icons.article, '发动态', () {
                  Navigator.pop(context);
                  context.navigateTo('/publish_moment');
                }),
                _buildCreateOption(Icons.tag, '发话题', () {
                  Navigator.pop(context);
                  // TODO: 发布话题
                }),
                _buildCreateOption(Icons.event, '发起活动', () {
                  Navigator.pop(context);
                  // TODO: 发起活动
                }),
              ],
            ),
           const SizedBox(height: SpacingTokens.space5),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateOption(IconData icon, String label, VoidCallback onTap) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Column(
        children: [
          Container(
            padding: SpacingTokens.paddingMd,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(icon, color: Theme.of(context).colorScheme.primary, size: 28),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            label,
            style: const TextStyle(
              color: ColorTokens.onSurfaceVariant,
              // 使用 Theme.of(context).textTheme
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }

  void _viewFeedDetail(FeedItem item) {
    // TODO: 查看动态详情
  }

  void _toggleLike(FeedItem item) {
    setState(() {
      item.isLiked = !item.isLiked;
      item.likes += item.isLiked ? 1 : -1;
    });
  }

  void _viewComments(FeedItem item) {
    // TODO: 查看评论
  }

  void _shareFeed(FeedItem item) {
    // TODO: 分享动态
  }

  void _showMoreOptions(FeedItem item) {
    // TODO: 显示更多选项
  }
}

// 动态数据模型
class FeedItem {
  final int id;
  final String userName;
  final String? userAvatar;
  final String content;
  final List<String> images;
  final String? location;
  final DateTime time;
  int likes;
  final int comments;
  bool isLiked;

  FeedItem({
    required this.id,
    required this.userName,
    this.userAvatar,
    required this.content,
    required this.images,
    this.location,
    required this.time,
    required this.likes,
    required this.comments,
    required this.isLiked,
  });
}
