import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:tencent_cloud_chat_sdk/enum/message_elem_type.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart'
    if (dart.library.html) 'package:tencent_cloud_chat_sdk/web/compatible_models/v2_tim_message.dart';
import 'package:user_app/features/chat/view_models/chat_view_model.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

class ChatListPage extends StatefulWidget {
 const ChatListPage({super.key});

  @override
  State<ChatListPage> createState() => _ChatListPageState();
}

class _ChatListPageState extends State<ChatListPage>
    with WidgetsBindingObserver {
  List<V2TimConversation> conversations = [];
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeChat();
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    try {
      final chatViewModel = context.read<ChatViewModel>();
      chatViewModel.removeConversationListener(_onConversationListUpdate);
    } catch (e) {
      debugPrint(
          '⚠️ [ChatListPage] Failed to remove conversation listener: $e');
    }
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      _loadConversations();
    }
  }

  Future<void> _initializeChat() async {
    final chatViewModel = context.read<ChatViewModel>();
    if (!chatViewModel.isInitialized) {
      await chatViewModel.initialize();
    }

    chatViewModel.addConversationListener(_onConversationListUpdate);
    await _loadConversations();
  }

  void _onConversationListUpdate(List<V2TimConversation> conversationList) {
    if (mounted) {
      setState(() {
        conversations = conversationList;
      });
    }
  }

  void _navigateToFriendsList() {
    // TODO: 实现导航到朋友列表页面
    context.navigateTo('/friends');
  }

  Future<void> _loadConversations() async {
    if (isLoading) return;

    setState(() {
      isLoading = true;
    });

    try {
      final chatViewModel = context.read<ChatViewModel>();
      final conversationList = await chatViewModel.getConversationList();

      if (mounted) {
        setState(() {
          conversations = conversationList;
        });
      }
    } catch (e) {
      debugPrint('❌ [ChatListPage] Failed to load conversations: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载会话列表失败: $e'),
            backgroundColor: ColorTokens.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.surfaceContainer,
      appBar: DSAppBar(
        title: '消息',
        showBackButton: false,
        automaticallyImplyLeading: false,
        centerTitle: false,
        actions: [
          DSAppBarAction(
            icon: Icons.people_outline,
            onPressed: _navigateToFriendsList,
            tooltip: '朋友列表',
          ),
          const SizedBox(width: SpacingTokens.space2),
        ],
      ),
      body: SafeArea(
        bottom: false,
        child: isLoading && conversations.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              ColorTokens.primary,
                            ),
                          ),
                          SpacingTokens.verticalSpaceMd,
                          Text(
                            '加载中...',
                            style: TypographyTokens.bodyLarge.copyWith(
                              color: ColorTokens.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    )
                  : conversations.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                             const Icon(
                                Icons.chat_bubble_outline,
                                size: 80,
                                color: ColorTokens.onSurfaceVariant,
                              ),
                              SpacingTokens.verticalSpaceMd,
                              Text(
                                '暂无聊天',
                                style: TypographyTokens.headlineSmall.copyWith(
                                  color: ColorTokens.onSurfaceVariant,
                                ),
                              ),
                              SpacingTokens.verticalSpaceSm,
                              Text(
                                '开始一段新的对话吧',
                                style: TypographyTokens.bodyMedium.copyWith(
                                  color: ColorTokens.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: _loadConversations,
                          color: ColorTokens.primary,
                          child: ListView.builder(
                            padding: SpacingTokens.paddingVerticalSm,
                            itemCount: conversations.length,
                            itemBuilder: (context, index) {
                              return _buildConversationItem(
                                  conversations[index]);
                            },
                          ),
                        ),
      ),
    );
  }

  Widget _buildConversationItem(V2TimConversation conversation) {
    final lastMessage = conversation.lastMessage;
    final timestamp = DateTime.fromMillisecondsSinceEpoch(
      (conversation.lastMessage?.timestamp ?? 0) * 1000,
    );

    return Container(
      margin: EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space1),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusLg,
        boxShadow: ElevationTokens.shadow1(context),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: ShapeTokens.borderRadiusLg,
          onTap: () => _startChat(conversation),
          child: Padding(
            padding: SpacingTokens.paddingMd,
            child: Row(
              children: [
                // 头像区域
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: const LinearGradient(
                      colors: const [
                        ColorTokens.primary,
                        ColorTokens.primaryContainer,
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: ElevationTokens.shadow2(context),
                  ),
                  child: conversation.faceUrl != null &&
                          conversation.faceUrl!.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(28),
                          child: Image.network(
                            conversation.faceUrl!,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) {
                              return _buildDefaultAvatar(conversation);
                            },
                          ),
                        )
                      : _buildDefaultAvatar(conversation),
                ),
                SpacingTokens.horizontalSpaceMd,

                // 会话信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              conversation.showName ?? '未知用户',
                              style: TypographyTokens.bodyLarge.copyWith(
                                fontWeight: FontWeight.w600,
                                color: ColorTokens.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Text(
                            _formatTime(timestamp),
                            style: TypographyTokens.labelMedium.copyWith(
                              color: ColorTokens.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                      SpacingTokens.verticalSpaceXs,
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              _getLastMessageContent(lastMessage) ?? '暂无消息',
                              style: TypographyTokens.bodyMedium.copyWith(
                                color: ColorTokens.onSurfaceVariant,
                                height: 1.4,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if ((conversation.unreadCount ?? 0) > 0) ...[
                            SpacingTokens.horizontalSpaceSm,
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: SpacingTokens.space2,
                                vertical: SpacingTokens.space1,
                              ),
                              decoration: const BoxDecoration(
                                color: ColorTokens.error,
                                borderRadius: ShapeTokens.borderRadiusXl,
                              ),
                              child: Text(
                                conversation.unreadCount! > 99
                                    ? '99+'
                                    : conversation.unreadCount.toString(),
                                style: TypographyTokens.labelSmall.copyWith(
                                  color: ColorTokens.onError,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDefaultAvatar(V2TimConversation conversation) {
    return Center(
      child: Text(
        (conversation.showName ?? '未知')[0].toUpperCase(),
        style: TypographyTokens.headlineSmall.copyWith(
          color: ColorTokens.onPrimary,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  String? _getLastMessageContent(V2TimMessage? message) {
    if (message == null) return null;

    switch (message.elemType) {
      case MessageElemType.V2TIM_ELEM_TYPE_TEXT:
        return message.textElem?.text;
      case MessageElemType.V2TIM_ELEM_TYPE_IMAGE:
        return '[图片]';
      case MessageElemType.V2TIM_ELEM_TYPE_SOUND:
        return '[语音]';
      case MessageElemType.V2TIM_ELEM_TYPE_VIDEO:
        return '[视频]';
      case MessageElemType.V2TIM_ELEM_TYPE_FILE:
        return '[文件]';
      case MessageElemType.V2TIM_ELEM_TYPE_FACE:
        return '[表情]';
      default:
        return '[消息]';
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inDays == 0) {
      // 今天
      return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      final weekdays = ['一', '二', '三', '四', '五', '六', '日'];
      return '周${weekdays[time.weekday - 1]}';
    } else {
      return '${time.month}/${time.day}';
    }
  }

  void _startChat(V2TimConversation conversation) {
    final chatViewModel = context.read<ChatViewModel>();
    chatViewModel.markConversationAsRead(conversation.conversationID ?? '');

    context.navigateTo(
      '/chat/simple/${conversation.conversationID}',
      extra: {
        'conversationType': conversation.type,
        'showName': conversation.showName ?? '未知用户',
        'userID': conversation.userID,
      },
    );
  }
}
