import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/community/view_models/community_view_model.dart';
import 'package:user_app/features/moments/screens/moment_detail_page.dart';
import 'package:user_app/features/search/SearchSystem/SearchSystem.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';
import 'package:user_app/widgets/moments_list.dart';

class CommunityPage extends StatefulWidget {
  const CommunityPage({super.key});

  @override
  State<CommunityPage> createState() => _CommunityPageState();
}

class _CommunityPageState extends State<CommunityPage>
    with SingleTickerProviderStateMixin {
  late CommunityViewModel _communityViewModel;
  late AnimationController _fabAnimationController;

  bool _previousAuthState = false;

  @override
  void initState() {
    super.initState();
    _communityViewModel = context.read<CommunityViewModel>();
    _fabAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _fabAnimationController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _communityViewModel.loadMoments(refresh: true);
      _previousAuthState = context.isUserLoggedIn;
    });
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    super.dispose();
  }

  final List<String> _filterOptions = ['全部', '钓获分享', '装备展示', '技巧分享', '问答求助'];

  @override
  Widget build(BuildContext context) {
    final currentAuthState = context.isUserLoggedIn;

    if (_previousAuthState != currentAuthState) {
      _previousAuthState = currentAuthState;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          _communityViewModel.loadMoments(refresh: true);
        }
      });
    }

    final communityViewModel = context.watch<CommunityViewModel>();
    final isLoading = communityViewModel.isLoading;
    final moments = communityViewModel.moments;
    final hasMore = communityViewModel.hasMore;
    final hasSelectedTags = communityViewModel.selectedTags.isNotEmpty;

    return Scaffold(
      backgroundColor: ColorTokens.surfaceContainer,
      body: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          DSSliverAppBar(
            title: '社区',
            floating: true,
            pinned: false,
            snap: false,
            automaticallyImplyLeading: false,
            actions: [
              DSAppBarAction(
                icon: communityViewModel.sortBy == 'latest'
                    ? Icons.schedule
                    : Icons.local_fire_department,
                onPressed: _showSortOptions,
                tooltip: '排序方式',
              ),
              DSAppBarAction(
                icon: Icons.search,
                onPressed: () => UnifiedSearchService().quickSearch(
                  context,
                  UnifiedSearchService.MOMENT_SEARCH,
                ),
                tooltip: '搜索',
              ),
              DSAppBarAction(
                icon: Icons.notifications_outlined,
                onPressed: _showNotifications,
                tooltip: '通知',
                // TODO: Replace with real logic e.g., notificationViewModel.hasUnread
                badge: Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: ColorTokens.error,
                    shape: BoxShape.circle,
                  ),
                ),
              ),
              const SizedBox(width: SpacingTokens.space2),
            ],
          ),
          SliverPersistentHeader(
            pinned: true,
            delegate: _OptimizedFilterBarDelegate(
              minHeight: 48.5, // 增加0.5px来容纳边框
              maxHeight: hasSelectedTags ? 84.5 : 48.5, // 两种情况都增加0.5px
              child: Container(
                decoration: const BoxDecoration(
                  color: ColorTokens.surface,
                  border: Border(
                    bottom: BorderSide(
                      color: ColorTokens.outlineVariant,
                      width: 0.5,
                    ),
                  ),
                ),
                child: Column(
                  children: [
                    Container(
                      height: 48,
                      padding: SpacingTokens.paddingHorizontalMd,
                      child: Row(
                        children: [
                          if (context.isUserLoggedIn)
                            GestureDetector(
                              onTap: () {
                                HapticFeedback.selectionClick();
                                final newValue =
                                    !communityViewModel.showOnlyFollowing;
                                communityViewModel
                                    .setShowOnlyFollowing(newValue);
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 6),
                                decoration: BoxDecoration(
                                  color: communityViewModel.showOnlyFollowing
                                      ? ColorTokens.primary
                                      : ColorTokens.surfaceContainerHighest,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      communityViewModel.showOnlyFollowing
                                          ? Icons.people
                                          : Icons.people_outline,
                                      size: 16,
                                      color:
                                          communityViewModel.showOnlyFollowing
                                              ? ColorTokens.onPrimary
                                              : ColorTokens.onSurfaceVariant,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      '关注',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color:
                                            communityViewModel.showOnlyFollowing
                                                ? ColorTokens.onPrimary
                                                : ColorTokens.onSurfaceVariant,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (context.isUserLoggedIn) const SizedBox(width: 8),
                          Expanded(
                            child: SingleChildScrollView(
                              scrollDirection: Axis.horizontal,
                              child: Row(
                                children: _filterOptions.map((filter) {
                                  final isSelected =
                                      communityViewModel.selectedMomentType ==
                                          filter;
                                  return Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: GestureDetector(
                                      onTap: () {
                                        HapticFeedback.selectionClick();
                                        communityViewModel
                                            .setMomentTypeFilter(filter);
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 16, vertical: 8),
                                        decoration: BoxDecoration(
                                          color: isSelected
                                              ? ColorTokens.primary
                                              : ColorTokens.transparent,
                                          borderRadius:
                                              BorderRadius.circular(20),
                                          border: Border.all(
                                            color: isSelected
                                                ? ColorTokens.primary
                                                : ColorTokens.outline,
                                            width: 1.5,
                                          ),
                                          boxShadow: isSelected
                                              ? [
                                                  BoxShadow(
                                                    color: ColorTokens.primary
                                                        .withAlpha(51),
                                                    blurRadius: 4,
                                                    offset: const Offset(0, 2),
                                                  ),
                                                ]
                                              : null,
                                        ),
                                        child: Text(
                                          filter,
                                          style: TextStyle(
                                            color: isSelected
                                                ? ColorTokens.onPrimary
                                                : ColorTokens.onSurfaceVariant,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }).toList(),
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          GestureDetector(
                            onTap: _showAdvancedFilters,
                            child: AnimatedContainer(
                              duration: const Duration(milliseconds: 200),
                              padding: SpacingTokens.paddingSm,
                              decoration: BoxDecoration(
                                color: hasSelectedTags
                                    ? ColorTokens.primaryContainer
                                    : ColorTokens.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(8),
                                boxShadow: hasSelectedTags
                                    ? [
                                        BoxShadow(
                                          color:
                                              ColorTokens.primary.withAlpha(26),
                                          blurRadius: 4,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]
                                    : null,
                              ),
                              child: Badge(
                                isLabelVisible: hasSelectedTags,
                                label: Text(communityViewModel
                                    .selectedTags.length
                                    .toString()),
                                child: Icon(
                                  Icons.filter_list,
                                  size: 20,
                                  color: hasSelectedTags
                                      ? ColorTokens.primary
                                      : ColorTokens.onSurfaceVariant,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (hasSelectedTags)
                      Container(
                        height: 36,
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: ListView(
                          scrollDirection: Axis.horizontal,
                          children: communityViewModel.selectedTags
                              .map((tag) => Container(
                                    margin: const EdgeInsets.only(right: 8),
                                    child: Chip(
                                      label: Text(tag),
                                      deleteIcon:
                                          const Icon(Icons.close, size: 16),
                                      onDeleted: () {
                                        communityViewModel.toggleTag(tag);
                                      },
                                      backgroundColor:
                                          ColorTokens.primaryContainer,
                                      deleteIconColor: ColorTokens.primary,
                                      labelStyle: const TextStyle(
                                        color: ColorTokens.primary,
                                        fontSize: 12,
                                      ),
                                      padding: EdgeInsets.zero,
                                      visualDensity: VisualDensity.compact,
                                    ),
                                  ))
                              .toList(),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
          const SliverToBoxAdapter(
            child: SizedBox(
                height: SpacingTokens.space4), // Standard header spacing
          ),
          SliverFillRemaining(
            child: MomentsList(
              moments: moments,
              isLoading: isLoading,
              hasMore: hasMore,
              onRefresh: () => communityViewModel.loadMoments(refresh: true),
              onLoadMore: () => communityViewModel.loadMoments(),
              onMomentTap: (moment) => _showMomentDetail(moment),
              onUserTap: _showUserProfile,
              onLikeTap: (momentId) => _toggleLike(momentId),
              showSpotInfo: true,
              emptyWidget: _buildEmptyWidget(),
              padding: const EdgeInsets.fromLTRB(SpacingTokens.space4, 0,
                  SpacingTokens.space4, SpacingTokens.space4),
            ),
          ),
        ],
      ),
      floatingActionButton: ScaleTransition(
        scale: _fabAnimationController,
        child: FloatingActionButton.extended(
          onPressed: _showPublishOptions,
          backgroundColor: ColorTokens.primary,
          foregroundColor: ColorTokens.onPrimary,
          icon: const Icon(Icons.edit),
          label: const Text('发布'),
        ),
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.bubble_chart_outlined,
              size: 80, color: ColorTokens.onSurfaceVariant),
          SpacingTokens.verticalSpaceMd,
          Text(
            _communityViewModel.showOnlyFollowing ? '还没有关注的人发布动态' : '暂无动态',
            style: TypographyTokens.bodyLarge
                .copyWith(color: ColorTokens.onSurfaceVariant),
          ),
          SpacingTokens.verticalSpaceSm,
          Text(
            _communityViewModel.showOnlyFollowing ? '去发现更多有趣的钓友吧' : '成为第一个分享的人',
            style: TypographyTokens.bodyMedium
                .copyWith(color: ColorTokens.onSurfaceVariant),
          ),
          SpacingTokens.verticalSpaceLg,
          OutlinedButton.icon(
            onPressed: _communityViewModel.showOnlyFollowing
                ? () => _communityViewModel.setShowOnlyFollowing(false)
                : _showPublishOptions,
            icon: Icon(_communityViewModel.showOnlyFollowing
                ? Icons.explore
                : Icons.add),
            label:
                Text(_communityViewModel.showOnlyFollowing ? '发现动态' : '发布动态'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    final currentSortBy = context.read<CommunityViewModel>().sortBy;
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorTokens.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: ColorTokens.surface,
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(ShapeTokens.radiusLg))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: ColorTokens.outline,
                      borderRadius: BorderRadius.circular(2)),
                ),
                Padding(
                  padding: SpacingTokens.paddingMd,
                  child: Text('排序方式',
                      style: TypographyTokens.titleMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: ColorTokens.onSurface)),
                ),
                ListTile(
                  leading: const Icon(Icons.schedule),
                  title: const Text('最新发布'),
                  trailing: currentSortBy == 'latest'
                      ? const Icon(Icons.check, color: ColorTokens.primary)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    _communityViewModel.setSortBy('latest');
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.local_fire_department),
                  title: const Text('最热门'),
                  subtitle: const Text('按互动量排序'),
                  trailing: currentSortBy == 'hottest'
                      ? const Icon(Icons.check, color: ColorTokens.primary)
                      : null,
                  onTap: () {
                    Navigator.pop(context);
                    _communityViewModel.setSortBy('hottest');
                  },
                ),
                SpacingTokens.verticalSpaceMd,
              ],
            ),
          ),
        );
      },
    );
  }

  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) {
        final List<String> localSelectedTags =
            List.from(_communityViewModel.selectedTags);
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.6,
              decoration: const BoxDecoration(
                  color: ColorTokens.surface,
                  borderRadius:
                      BorderRadius.vertical(top: Radius.circular(20))),
              child: Column(
                children: [
                  Container(
                    padding: SpacingTokens.paddingMd,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text('标签筛选',
                            style: TypographyTokens.titleMedium.copyWith(
                                fontWeight: FontWeight.w600,
                                color: ColorTokens.onSurface)),
                        TextButton(
                          onPressed: () =>
                              setModalState(() => localSelectedTags.clear()),
                          child: const Text('清除'),
                        ),
                      ],
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: [
                          '新手入门',
                          '路亚技巧',
                          '台钓',
                          '野钓',
                          '黑坑',
                          '饵料配方',
                          '装备推荐',
                          '钓点分享',
                          '冬季钓鱼',
                          '鲫鱼',
                          '鲤鱼',
                          '草鱼',
                          '鲢鳙',
                          '翘嘴',
                          '鲈鱼'
                        ].map((tag) {
                          final isSelected = localSelectedTags.contains(tag);
                          return FilterChip(
                            label: Text(tag),
                            selected: isSelected,
                            onSelected: (selected) {
                              setModalState(() {
                                if (selected) {
                                  localSelectedTags.add(tag);
                                } else {
                                  localSelectedTags.remove(tag);
                                }
                              });
                            },
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                  Container(
                    padding: SpacingTokens.paddingMd,
                    child: SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                        onPressed: () {
                          Navigator.pop(context);
                          _communityViewModel
                              .setSelectedTags(localSelectedTags);
                        },
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12)),
                        ),
                        child:
                            const Text('应用筛选', style: TextStyle(fontSize: 16)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  void _showPublishOptions() {
    context.executeWithLoginCheck(
      () => _performShowPublishOptions(),
      message: '您需要先登录才能发布动态',
    );
  }

  void _performShowPublishOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorTokens.transparent,
      builder: (context) {
        return Container(
          decoration: const BoxDecoration(
              color: ColorTokens.surface,
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(ShapeTokens.radiusLg))),
          child: SafeArea(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                      color: ColorTokens.outline,
                      borderRadius: BorderRadius.circular(2)),
                ),
                Padding(
                  padding: SpacingTokens.paddingMd,
                  child: Text('发布动态',
                      style: TypographyTokens.titleMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: ColorTokens.onSurface)),
                ),
                Padding(
                  padding: SpacingTokens.paddingMd,
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              icon: Icons.set_meal,
                              label: '钓获分享',
                              color: Colors.green,
                              onTap: () {
                                Navigator.pop(context);
                                context.navigateTo(AppRoutes.publishMoment,
                                    extra: '钓获分享');
                              }),
                          _buildPublishOption(
                              icon: Icons.backpack,
                              label: '装备展示',
                              color: Colors.blue,
                              onTap: () {
                                Navigator.pop(context);
                                context.navigateTo(AppRoutes.publishMoment,
                                    extra: '装备展示');
                              }),
                        ],
                      ),
                      SpacingTokens.verticalSpaceMd,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildPublishOption(
                              icon: Icons.tips_and_updates,
                              label: '技巧分享',
                              color: Colors.orange,
                              onTap: () {
                                Navigator.pop(context);
                                context.navigateTo(AppRoutes.publishMoment,
                                    extra: '技巧分享');
                              }),
                          _buildPublishOption(
                              icon: Icons.help_outline,
                              label: '问答求助',
                              color: Colors.purple,
                              onTap: () {
                                Navigator.pop(context);
                                context.navigateTo(AppRoutes.publishMoment,
                                    extra: '问答求助');
                              }),
                        ],
                      ),
                    ],
                  ),
                ),
                SpacingTokens.verticalSpaceMd,
              ],
            ),
          ),
        );
      },
    );
  }

  // [FIXED] - Adjusted dimensions to prevent overflow
  Widget _buildPublishOption(
      {required IconData icon,
      required String label,
      required Color color,
      required VoidCallback onTap}) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 56, // was 64
            height: 56, // was 64
            decoration: BoxDecoration(
                color: color.withAlpha(26), // use withAlpha
                borderRadius: BorderRadius.circular(16)),
            child: Icon(icon, color: color, size: 28), // was 32
          ),
          const SizedBox(height: 6), // was 8
          Text(label,
              style: TypographyTokens.labelMedium
                  .copyWith(color: ColorTokens.onSurfaceVariant)),
        ],
      ),
    );
  }

  void _showNotifications() {
    context.executeWithLoginCheck(
      () => _performShowNotifications(),
      message: '您需要先登录才能查看通知',
    );
  }

  void _performShowNotifications() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.8,
          decoration: const BoxDecoration(
              color: ColorTokens.surface,
              borderRadius: BorderRadius.vertical(
                  top: Radius.circular(ShapeTokens.radiusLg))),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                    color: ColorTokens.outline,
                    borderRadius: BorderRadius.circular(2)),
              ),
              Padding(
                padding: SpacingTokens.paddingMd,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text('通知',
                        style: TypographyTokens.titleMedium.copyWith(
                            fontWeight: FontWeight.w600,
                            color: ColorTokens.onSurface)),
                    TextButton(onPressed: () {}, child: const Text('全部已读')),
                  ],
                ),
              ),
              Expanded(
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(Icons.notifications_none,
                          size: 80, color: ColorTokens.surfaceContainerHighest),
                      SpacingTokens.verticalSpaceMd,
                      Text('暂无新通知',
                          style: TypographyTokens.titleMedium
                              .copyWith(color: ColorTokens.onSurfaceVariant)),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _showMomentDetail(MomentVo moment) async {
    final result = await Navigator.of(context).push<MomentVo>(
      MaterialPageRoute(
        builder: (context) => MomentDetailPage(
          momentId: moment.id!,
          initialMoment: moment,
        ),
      ),
    );

    if (result != null) {
      _communityViewModel.updateMoment(result);
    }
  }

  void _showUserProfile(user) {
    context.navigateTo('/user/profile/${user.id}', extra: user);
  }

  Future<void> _toggleLike(int momentId) async {
    context.executeWithLoginCheck(
      () => _performToggleLike(momentId),
      message: '您需要先登录才能点赞动态',
    );
  }

  Future<void> _performToggleLike(int momentId) async {
    try {
      await _communityViewModel.toggleLikeMoment(momentId);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('点赞失败: $e'),
              behavior: SnackBarBehavior.floating,
              duration: const Duration(seconds: 2)),
        );
      }
    }
  }
}

class _OptimizedFilterBarDelegate extends SliverPersistentHeaderDelegate {
  final Widget child;
  final double minHeight;
  final double maxHeight;

  _OptimizedFilterBarDelegate({
    required this.child,
    required this.minHeight,
    required this.maxHeight,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Material(
      elevation: overlapsContent ? 2 : 0,
      shadowColor: Colors.black.withAlpha(26),
      child: child,
    );
  }

  @override
  double get maxExtent => maxHeight;

  @override
  double get minExtent => minHeight;

  @override
  bool shouldRebuild(covariant _OptimizedFilterBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}
