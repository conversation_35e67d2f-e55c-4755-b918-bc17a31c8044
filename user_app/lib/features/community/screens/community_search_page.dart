import 'dart:async';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:shimmer/shimmer.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class CommunitySearchPage extends StatefulWidget {
 const CommunitySearchPage({super.key});

  @override
  State<CommunitySearchPage> createState() => _CommunitySearchPageState();
}

class _CommunitySearchPageState extends State<CommunitySearchPage>
    with TickerProviderStateMixin {
  // 搜索控制器
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  final ScrollController _scrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _bounceController;
  late AnimationController _rippleController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _bounceAnimation;
  late Animation<double> _rippleAnimation;

  // 列表项动画
  final List<AnimationController> _itemControllers = [];

  // 搜索状态
  final bool _isSearching = false;
  bool _isLoading = false;
  bool _hasSearched = false;
  String _searchQuery = '';
  String _selectedFilter = 'all'; // all, moment, user, spot

  // 搜索数据
  List<SearchResult> _searchResults = [];
  List<String> _searchHistory = [];
  List<String> _hotSearches = [];
  List<String> _suggestions = [];

  // 防抖计时器
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _bounceController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _rippleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: Offset(0, -0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    _bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _bounceController,
      curve: Curves.easeInOut,
    ));

    _rippleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _rippleController,
      curve: Curves.easeOut,
    ));

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
    _bounceController.repeat(reverse: true);

    // 加载初始数据
    _loadInitialData();

    // 自动聚焦搜索框
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _searchFocusNode.requestFocus();
    });
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _searchController.dispose();
    _searchFocusNode.dispose();
    _scrollController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _bounceController.dispose();
    _rippleController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadInitialData() {
    // 模拟加载搜索历史
    _searchHistory = [
      '路亚竿推荐',
      '黑坑钓鱼技巧',
      '野钓装备',
      '钓鱼地点',
    ];

    // 模拟加载热门搜索
    _hotSearches = [
      '新手入门装备',
      '路亚钓法教程',
      '黑坑偷驴技巧',
      '野钓选位经验',
      '饵料配方大全',
      '钓鱼天气预报',
      '渔获记录分享',
      '钓友交流群',
    ];
  }

  void _performSearch() {
    if (_searchController.text.trim().isEmpty) return;

    HapticFeedback.lightImpact();
    _searchQuery = _searchController.text.trim();

    setState(() {
      _isLoading = true;
      _hasSearched = true;
      _suggestions = [];
    });

    // 添加到搜索历史
    if (!_searchHistory.contains(_searchQuery)) {
      _searchHistory.insert(0, _searchQuery);
      if (_searchHistory.length > 10) {
        _searchHistory.removeLast();
      }
    }

    // 模拟搜索延迟
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _searchResults = _generateMockResults();
          _animateListItems();
        });
      }
    });
  }

  List<SearchResult> _generateMockResults() {
    // 根据筛选条件生成模拟结果
    if (_selectedFilter == 'all') {
      return [
        SearchResult(
          type: 'moment',
          title: '今天在东湖钓到大鲤鱼！',
          subtitle: '分享了钓鱼心得',
          author: '钓鱼达人',
          time: '2小时前',
          imageUrl: 'assets/images/fish1.jpg',
        ),
        SearchResult(
          type: 'user',
          title: '钓鱼高手小王',
          subtitle: '关注者 1.2k | 获赞 5.6k',
          author: '',
          time: '',
          imageUrl: 'assets/images/avatar1.jpg',
        ),
        SearchResult(
          type: 'spot',
          title: '东湖钓鱼场',
          subtitle: '距离您 5.2km | 评分 4.8',
          author: '',
          time: '',
          imageUrl: 'assets/images/spot1.jpg',
        ),
      ];
    } else {
      // 根据具体筛选返回对应类型的结果
      return [];
    }
  }

  void _animateListItems() {
    for (int i = 0; i < _searchResults.length; i++) {
      final controller = _createItemController(i);
      Future.delayed(Duration(milliseconds: i * 100), () {
        if (controller.status != AnimationStatus.forward) {
          controller.forward();
        }
      });
    }
  }

  AnimationController _createItemController(int index) {
    if (index < _itemControllers.length) {
      return _itemControllers[index];
    }

    final controller = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _itemControllers.add(controller);
    return controller;
  }

  void _updateSuggestions(String query) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        setState(() {
          if (query.isEmpty) {
            _suggestions = [];
          } else {
            // 模拟搜索建议
            final allSuggestions = [
              '路亚钓法技巧',
              '路亚竿推荐',
              '路亚饵选择',
              '黑坑钓鱼攻略',
              '野钓装备推荐',
              '钓鱼地点分享',
              '饵料配方大全',
            ];

            _suggestions =
                allSuggestions.where((s) => s.contains(query)).take(4).toList();
          }
        });
      }
    });
  }

  void _navigateToDetail(SearchResult result) {
    HapticFeedback.lightImpact();
    // 根据类型导航到不同页面
    switch (result.type) {
      case 'moment':
        context.navigateTo('/moment/detail/${result.id}');
        break;
      case 'user':
        context.navigateTo('/user/profile/${result.id}');
        break;
      case 'spot':
        context.navigateTo('/spot/detail/${result.id}');
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            _buildSearchBar(),
            Expanded(
              child: _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: const [
            Theme.of(context).colorScheme.primary,
            Theme.of(context).colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF4E8BF7).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // 搜索栏
          Row(
            children: [
              // 返回按钮
              IconButton(
                icon: Container(
                  padding: SpacingTokens.paddingSm,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.arrow_back_ios,
                      color: Colors.white, size: 20),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  Navigator.pop(context);
                },
              ),
              // 搜索输入框
              Expanded(
                child: Container(
                  height: 44,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(22),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: Offset(0, 4),
                      ),
                    ],
                  ),
                  child: TextField(
                    controller: _searchController,
                    focusNode: _searchFocusNode,
                    textInputAction: TextInputAction.search,
                    onSubmitted: (_) => _performSearch(),
                    onChanged: (value) {
                      setState(() {
                        _updateSuggestions(value);
                      });
                    },
                    decoration: InputDecoration(
                      hintText: '搜索动态、钓友、钓点...',
                      hintStyle: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 15,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.only(
                        left: 16,
                        right: 8,
                        top: 12,
                        bottom: 12,
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        color: Colors.grey[600],
                        size: 20,
                      ),
                      suffixIcon: _searchController.text.isNotEmpty
                          ? IconButton(
                              icon: Icon(
                                Icons.clear,
                                color: Colors.grey[600],
                                size: 20,
                              ),
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                _searchController.clear();
                                setState(() {
                                  _hasSearched = false;
                                  _searchResults = [];
                                  _suggestions = [];
                                });
                                _searchFocusNode.requestFocus();
                              },
                              padding: EdgeInsets.all(4),
                             constraints: const BoxConstraints(
                                minWidth: 40,
                                minHeight: 40,
                              ),
                            )
                          : null,
                    ),
                  ),
                ),
              ),
              // 搜索按钮
             const SizedBox(width: 8),
              ScaleTransition(
                scale: _bounceAnimation,
                child: TextButton(
                  onPressed: _performSearch,
                  style: TextButton.styleFrom(
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 16),
                  ),
                  child: const Text(
                    '搜索',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ],
          ),
          // 筛选标签
          if (_hasSearched) ...[
           const SizedBox(height: 12),
            _buildFilterTags(),
          ],
        ],
      ),
    );
  }

  Widget _buildFilterTags() {
    final filters = [
      {'key': 'all', 'label': '全部', 'icon': Icons.apps},
      {'key': 'moment', 'label': '动态', 'icon': Icons.article},
      {'key': 'user', 'label': '钓友', 'icon': Icons.person},
      {'key': 'spot', 'label': '钓点', 'icon': Icons.location_on},
    ];

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: filters.map((filter) {
          final isSelected = _selectedFilter == filter['key'];
          return Padding(
            padding: EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    filter['icon'] as IconData,
                    size: 16,
                    color: isSelected ? Colors.white : Colors.grey[600],
                  ),
                 const SizedBox(width: 4),
                  Text(filter['label'] as String),
                ],
              ),
              selected: isSelected,
              onSelected: (selected) {
                HapticFeedback.lightImpact();
                setState(() {
                  _selectedFilter = filter['key'] as String;
                });
                _performSearch();
              },
              backgroundColor: Colors.white,
              selectedColor: Color(0xFF4E8BF7),
              labelStyle: TextStyle(
                color: isSelected ? Colors.white : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              elevation: isSelected ? 4 : 0,
              pressElevation: 8,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildContent() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_hasSearched) {
      if (_searchResults.isEmpty) {
        return _buildEmptyResultState();
      }
      return _buildSearchResults();
    }

    return _buildInitialState();
  }

  Widget _buildInitialState() {
    return SingleChildScrollView(
      padding: EdgeInsets.only(top: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 搜索建议
          if (_suggestions.isNotEmpty) ...[
            _buildSuggestionsSection(),
           const SizedBox(height: SpacingTokens.space6),
          ],
          // 搜索历史
          if (_searchHistory.isNotEmpty) ...[
            _buildHistorySection(),
           const SizedBox(height: SpacingTokens.space6),
          ],
          // 热门搜索
          _buildHotSearchSection(),
         const SizedBox(height: 100),
        ],
      ),
    );
  }

  Widget _buildSuggestionsSection() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20),
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         const Text(
            '搜索建议',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Color(0xFF1A1E25),
            ),
          ),
         const SizedBox(height: 12),
          ...List.generate(
            math.min(_suggestions.length, 4),
            (index) => InkWell(
              onTap: () {
                HapticFeedback.lightImpact();
                _searchController.text = _suggestions[index];
                _performSearch();
              },
              child: Padding(
                padding: EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Icon(Icons.search, size: 18, color: Colors.grey[400]),
                   const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        _suggestions[index],
                        style: TextStyle(
                          fontSize: 15,
                          color: Colors.grey[700],
                        ),
                      ),
                    ),
                    Icon(Icons.north_west, size: 16, color: Colors.grey[400]),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistorySection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
             const Text(
                '搜索历史',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1A1E25),
                ),
              ),
              TextButton(
                onPressed: () {
                  HapticFeedback.lightImpact();
                  _showClearHistoryDialog();
                },
                child: const Text(
                  '清空',
                  style: TextStyle(color: Color(0xFF4E8BF7)),
                ),
              ),
            ],
          ),
         const SizedBox(height: 12),
          Wrap(
            spacing: 10,
            runSpacing: 10,
            children: _searchHistory.map((keyword) {
              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _searchController.text = keyword;
                  _performSearch();
                },
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.grey[300]!,
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.history, size: 16, color: Colors.grey[600]),
                     const SizedBox(width: 6),
                      Text(
                        keyword,
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildHotSearchSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: const [
             const Icon(
                Icons.local_fire_department,
                color: Color(0xFFFF6B6B),
                size: 24,
              ),
             const SizedBox(width: 8),
             const Text(
                '热门搜索',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1A1E25),
                ),
              ),
            ],
          ),
         const SizedBox(height: SpacingTokens.space4),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 4,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: _hotSearches.length,
            itemBuilder: (context, index) {
              final isTop3 = index < 3;
              return GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _searchController.text = _hotSearches[index];
                  _performSearch();
                },
                child: Container(
                  decoration: BoxDecoration(
                    gradient: isTop3
                        ? LinearGradient(
                            colors: [
                              Color(0xFFFF6B6B).withValues(alpha: 0.1),
                              Color(0xFFFF8E53).withValues(alpha: 0.1),
                            ],
                          )
                        : null,
                    color: isTop3 ? null : Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isTop3
                          ? Color(0xFFFF6B6B).withValues(alpha: 0.3)
                          : Colors.grey[300]!,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      if (isTop3)
                        Text(
                          '${index + 1}',
                          style: const TextStyle(
                            color: Color(0xFFFF6B6B),
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                     const SizedBox(width: 8),
                      Flexible(
                        child: Text(
                          _hotSearches[index],
                          overflow: TextOverflow.ellipsis,
                          style: TextStyle(
                            color: isTop3
                                ? Color(0xFFFF6B6B)
                                : Colors.grey[700],
                            fontSize: 14,
                            fontWeight:
                                isTop3 ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResults() {
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.only(bottom: 80),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        return _buildSearchResultItem(_searchResults[index], index);
      },
    );
  }

  Widget _buildSearchResultItem(SearchResult result, int index) {
    final controller = _createItemController(index);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (controller.value * 0.2),
          child: Opacity(
            opacity: controller.value,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.white, Color(0xFFFBFCFE)],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 15,
                    offset: Offset(0, 5),
                  ),
                ],
              ),
              child: InkWell(
                onTap: () => _navigateToDetail(result),
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: SpacingTokens.paddingMd,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 类型标签和标题
                      Row(
                        children: [
                          _buildTypeLabel(result.type),
                         const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              result.title,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: Color(0xFF1A1E25),
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                     const SizedBox(height: SpacingTokens.space2),
                      // 副标题
                      Text(
                        result.subtitle,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      if (result.author.isNotEmpty) ...[
                       const SizedBox(height: SpacingTokens.space2),
                        Row(
                          children: [
                            CircleAvatar(
                              radius: 12,
                              backgroundColor: Colors.grey[300],
                              child: Icon(
                                Icons.person,
                                size: 16,
                                color: Colors.grey[600],
                              ),
                            ),
                           const SizedBox(width: 8),
                            Text(
                              result.author,
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.grey[600],
                              ),
                            ),
                           const Spacer(),
                            Text(
                              result.time,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[500],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildTypeLabel(String type) {
    IconData icon;
    Color color;
    String label;

    switch (type) {
      case 'moment':
        icon = Icons.article;
        color = Color(0xFF4E8BF7);
        label = '动态';
        break;
      case 'user':
        icon = Icons.person;
        color = Color(0xFF667EEA);
        label = '用户';
        break;
      case 'spot':
        icon = Icons.location_on;
        color = Color(0xFF22C55E);
        label = '钓点';
        break;
      default:
        icon = Icons.info;
        color = Colors.grey;
        label = '其他';
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14, color: color),
         const SizedBox(width: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 加载动画
          Stack(
            alignment: Alignment.center,
            children: [
              AnimatedBuilder(
                animation: _rippleController,
                builder: (context, child) {
                  return Container(
                    width: 100 + (_rippleAnimation.value * 50),
                    height: 100 + (_rippleAnimation.value * 50),
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: Color(0xFF4E8BF7)
                            .withValues(alpha: 1 - _rippleAnimation.value),
                        width: 2,
                      ),
                      shape: BoxShape.circle,
                    ),
                  );
                },
              ),
              Container(
                width: 80,
                height: 80,
                padding: SpacingTokens.paddingLg,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: const [
                      Color(0xFF4E8BF7),
                      Color(0xFF667EEA),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: Color(0xFF4E8BF7).withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: Offset(0, 10),
                    ),
                  ],
                ),
                child: const CircularProgressIndicator(
                  color: Colors.white,
                  strokeWidth: 3,
                ),
              ),
            ],
          ),
         const SizedBox(height: SpacingTokens.space6),
          Shimmer.fromColors(
            baseColor: Colors.grey[300]!,
            highlightColor: Colors.grey[100]!,
            child: const Text(
              '搜索中...',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyResultState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF4E8BF7).withValues(alpha: 0.1),
                    Color(0xFF667EEA).withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.search_off,
                size: 60,
                color: Color(0xFF4E8BF7),
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          Text(
            '未找到"$_searchQuery"相关内容',
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1A1E25),
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            '换个关键词试试吧',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _searchController.clear();
              setState(() {
                _hasSearched = false;
                _searchResults = [];
              });
              _searchFocusNode.requestFocus();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF4E8BF7),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              padding: EdgeInsets.symmetric(horizontal: 30, vertical: 12),
            ),
            child: const Text(
              '重新搜索',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearHistoryDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '清空搜索历史',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: const Text(
          '确定要清空所有搜索历史吗？',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: Text(
              '取消',
              style: TextStyle(color: Colors.grey[600]),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _searchHistory.clear();
              });
              Navigator.pop(context);
              HapticFeedback.lightImpact();
            },
            child: const Text(
              '确定',
              style: TextStyle(color: Color(0xFF4E8BF7)),
            ),
          ),
        ],
      ),
    );
  }
}

// 搜索结果数据模型
class SearchResult {
  final String id;
  final String type; // moment, user, spot
  final String title;
  final String subtitle;
  final String author;
  final String time;
  final String imageUrl;

  SearchResult({
    String? id,
    required this.type,
    required this.title,
    required this.subtitle,
    required this.author,
    required this.time,
    required this.imageUrl,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString();
}
