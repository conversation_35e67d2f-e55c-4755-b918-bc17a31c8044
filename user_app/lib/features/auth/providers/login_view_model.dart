import 'package:flutter/material.dart';
import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/features/auth/services/session_service.dart';

class LoginViewModel extends BaseViewModel {
  final BaseAuthViewModel baseAuthViewModel;
  final SessionService sessionService;

  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController passwordController = TextEditingController();

  LoginViewModel({
    required this.baseAuthViewModel,
    required this.sessionService,
  });

  Future<bool> login(BuildContext context) async {
    setBusy(true);
    final phoneNumber = phoneNumberController.text;
    final password = passwordController.text;

    // 本地验证
    if (!_validateInputs(context, phoneNumber, password)) {
      setBusy(false);
      return false;
    }

    try {
      // 调用登录API
      final loginData = await sessionService.login(phoneNumber, password);
      
      // 存储用户信息和token
      await baseAuthViewModel.storeUserAndToken(loginData.user, loginData.token);
      
      setBusy(false);
      return true;
    } catch (e) {
      setBusy(false);
      
      // 处理错误
      if (e is ApiError) {
        // 显示后端返回的具体错误信息
        _showError(context, e.message);
      } else {
        // 其他未知错误
        _showError(context, '登录失败，请稍后重试');
        debugPrint('Login error: $e');
      }
      
      return false;
    }
  }

  bool _validateInputs(
      BuildContext context, String phoneNumber, String password) {
    if (phoneNumber.isEmpty) {
      _showError(context, '请输入手机号');
      return false;
    }

    if (!RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumber)) {
      _showError(context, '请输入正确的手机号');
      return false;
    }

    if (password.isEmpty) {
      _showError(context, '请输入密码');
      return false;
    }

    return true;
  }

  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message),
      backgroundColor: ColorTokens.error,
    ));
  }

  @override
  void dispose() {
    phoneNumberController.dispose();
    passwordController.dispose();
    super.dispose();
  }
}