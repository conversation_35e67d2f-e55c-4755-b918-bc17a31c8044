import 'package:flutter/material.dart';
import 'package:user_app/core/view_models/base_view_model.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/auth/providers/base_auth_view_model.dart';
import 'package:user_app/features/auth/services/verification_code_service.dart';
import 'package:user_app/models/session/login_data.dart';
import 'package:user_app/core/network/api_error.dart';
import 'package:user_app/features/auth/services/session_service.dart';

class ResetPasswordViewModel extends BaseViewModel {
  final BaseAuthViewModel baseAuthViewModel;
  final SessionService sessionService;
  final VerificationCodeService verificationCodeService;

  final TextEditingController phoneNumberController = TextEditingController();
  final TextEditingController verificationCodeController =
      TextEditingController();
  final TextEditingController passwordController = TextEditingController();
  final TextEditingController confirmPasswordController =
      TextEditingController();

  final PageController pageController = PageController();

  ResetPasswordViewModel({
    required this.baseAuthViewModel,
    required this.sessionService,
    required this.verificationCodeService,
  }) {
    verificationCodeService.addListener(_forwardVerificationCodeChanges);
  }

  void _forwardVerificationCodeChanges() {
    notifyListeners();
  }

  int get countdown => verificationCodeService.countdown;

  Future<void> sendVerificationCode(BuildContext context) async {
    await verificationCodeService.sendVerificationCode(
        context, phoneNumberController.text);
    pageController.nextPage(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeIn,
    );
  }

  Future<bool> resetPassword(BuildContext context) async {
    final phoneNumber = phoneNumberController.text;
    final verificationCode = verificationCodeController.text;
    final password = passwordController.text;
    final confirmPassword = confirmPasswordController.text;

    if (!_validateInputs(
        context, phoneNumber, verificationCode, password, confirmPassword)) {
      return false;
    }

    try {
      setBusy(true);
      LoginData loginData = await sessionService.resetPassword(
        phoneNumber,
        verificationCode,
        password,
        confirmPassword,
      );

      await baseAuthViewModel.storeUserAndToken(
          loginData.user, loginData.token);

      setBusy(false);
      return true;
    } on ApiError catch (error) {
      _showError(context, error.message);
      setBusy(false);
      return false;
    } catch (error) {
      _showError(context, '重置密码失败，请稍后重试');
      debugPrint('重置密码异常: $error');
      setBusy(false);
      return false;
    }
  }

  bool _validateInputs(BuildContext context, String phoneNumber,
      String verificationCode, String password, String confirmPassword) {
    // Validate phone number
    if (phoneNumber.isEmpty ||
        !RegExp(r'^1[3456789]\d{9}$').hasMatch(phoneNumber)) {
      _showError(context, '请输入正确的手机号');
      return false;
    }

    // Validate verification code
    if (verificationCode.isEmpty) {
      _showError(context, '请输入验证码');
      return false;
    }

    // Validate password
    if (password.isEmpty) {
      _showError(context, '请输入密码');
      return false;
    }

    // Validate confirm password
    if (confirmPassword.isEmpty) {
      _showError(context, '请输入确认密码');
      return false;
    }

    // Validate passwords match
    if (password != confirmPassword) {
      _showError(context, '两次输入的密码不一致');
      return false;
    }

    return true;
  }

  String? validateVerificationCode(BuildContext context) {
    final result =
        verificationCodeService.validateCode(verificationCodeController.text);

    if (result == null) {
      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Text('验证码验证成功'),
          behavior: SnackBarBehavior.floating,
          duration: Duration(seconds: 1),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Text('验证码验证失败'),
          behavior: SnackBarBehavior.floating,
        ),
      );
    }

    return result;
  }


  void _showError(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message),
      backgroundColor: ColorTokens.error,
    ));
  }

  @override
  void dispose() {
    verificationCodeService.removeListener(_forwardVerificationCodeChanges);
    phoneNumberController.dispose();
    verificationCodeController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    pageController.dispose();
    super.dispose();
  }
}
