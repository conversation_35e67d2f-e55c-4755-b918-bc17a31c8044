import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/widgets/shared/auth_input_field.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class FlatRegisterPhoneNumber extends StatefulWidget {
 const FlatRegisterPhoneNumber({super.key});

  @override
  State<FlatRegisterPhoneNumber> createState() =>
      _FlatRegisterPhoneNumberState();
}

class _FlatRegisterPhoneNumberState extends State<FlatRegisterPhoneNumber> {
  FocusNode phoneNumberFocusNode = FocusNode();

  @override
  void dispose() {
    phoneNumberFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final registerViewModel = context.watch<RegisterViewModel>();
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () {
        phoneNumberFocusNode.unfocus();
      },
      child: Padding(
        padding: SpacingTokens.paddingLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '手机号码',
              style: TypographyTokens.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
            SpacingTokens.verticalSpaceSm,
            Text(
              '请输入您的手机号，我们将发送验证码',
              style: TypographyTokens.bodyMedium.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
            SpacingTokens.verticalSpaceLg,
            AuthInputField(
              controller: registerViewModel.phoneNumberController,
              labelText: '手机号',
              hintText: '请输入11位手机号',
              keyboardType: TextInputType.phone,
              focusNode: phoneNumberFocusNode,
              prefixIcon: Icon(Icons.phone_android, color: colorScheme.primary),
              suffixIcon:
                  registerViewModel.phoneNumberController.text.isNotEmpty
                      ? IconButton(
                          icon: const Icon(Icons.clear, color: ColorTokens.outline),
                          onPressed: () {
                            registerViewModel.phoneNumberController.clear();
                            setState(() {});
                          },
                        )
                      : null,
            ),
           const Spacer(),
            SizedBox(
              height: 56,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: registerViewModel.busy ||
                        registerViewModel.countdown > 0
                    ? null
                    : () async {
                        await registerViewModel.sendVerificationCode(context);
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: ColorTokens.onPrimary,
                  disabledBackgroundColor: ColorTokens.disabled,
                  elevation: 0,
                  shape: const RoundedRectangleBorder(
                    borderRadius: ShapeTokens.borderRadiusLg,
                  ),
                ),
                child: Text(
                  registerViewModel.countdown > 0
                      ? '${registerViewModel.countdown}秒后重新发送'
                      : '获取验证码',
                  style: TypographyTokens.labelLarge.copyWith(
                    fontWeight: FontWeight.bold,
                    color: ColorTokens.onPrimary,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
