import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/auth/providers/reset_password_view_model.dart';
import 'package:user_app/features/auth/widgets/shared/auth_input_field.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class ResetPasswordSetPassword extends StatefulWidget {
 const ResetPasswordSetPassword({super.key});

  @override
  State<ResetPasswordSetPassword> createState() =>
      _ResetPasswordSetPasswordState();
}

class _ResetPasswordSetPasswordState extends State<ResetPasswordSetPassword> {
  FocusNode setPasswordFocusNode = FocusNode();
  FocusNode confirmPasswordFocusNode = FocusNode();
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;

  @override
  void dispose() {
    setPasswordFocusNode.dispose();
    confirmPasswordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final resetPasswordViewModel = context.watch<ResetPasswordViewModel>();
    final colorScheme = Theme.of(context).colorScheme;

    return GestureDetector(
      onTap: () {
        setPasswordFocusNode.unfocus();
        confirmPasswordFocusNode.unfocus();
      },
      child: Padding(
        padding: SpacingTokens.paddingLg,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '设置密码',
              style: TypographyTokens.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
            SpacingTokens.verticalSpaceSm,
            Text(
              '创建一个安全的密码，长度为6-16位',
              style: TypographyTokens.bodyMedium.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
            SpacingTokens.verticalSpaceLg,

            // Password field using AuthInputField
            AuthInputField(
              controller: resetPasswordViewModel.passwordController,
              labelText: '密码',
              hintText: '请输入6-16位密码',
              obscureText: !_isPasswordVisible,
              focusNode: setPasswordFocusNode,
              prefixIcon:
                 const Icon(Icons.lock_outline, color: ColorTokens.primary),
              suffixIcon: IconButton(
                icon: Icon(
                  _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                  color: ColorTokens.outline,
                  size: 20,
                ),
                onPressed: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入密码';
                }
                if (value.length < 6 || value.length > 16) {
                  return '密码长度应为6-16位';
                }
                return null;
              },
            ),

           const SizedBox(height: SpacingTokens.space5),

            // Confirm password field using AuthInputField
            AuthInputField(
              controller: resetPasswordViewModel.confirmPasswordController,
              labelText: '确认密码',
              hintText: '请再次输入密码',
              obscureText: !_isConfirmPasswordVisible,
              focusNode: confirmPasswordFocusNode,
              prefixIcon:
                 const Icon(Icons.lock_outline, color: ColorTokens.primary),
              suffixIcon: IconButton(
                icon: Icon(
                  _isConfirmPasswordVisible
                      ? Icons.visibility_off
                      : Icons.visibility,
                  color: ColorTokens.outline,
                  size: 20,
                ),
                onPressed: () {
                  setState(() {
                    _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                  });
                },
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请再次输入密码';
                }
                if (value != resetPasswordViewModel.passwordController.text) {
                  return '两次输入的密码不一致';
                }
                return null;
              },
            ),

            SpacingTokens.verticalSpaceLg,

            SizedBox(
              height: 56,
              width: double.infinity,
              child: ElevatedButton(
                onPressed: resetPasswordViewModel.busy
                    ? null
                    : () async {
                        final success =
                            await resetPasswordViewModel.resetPassword(context);
                        if (success && context.mounted) {
                          context.goUntilRoute(AppRoutes.fishingSpots);
                        }
                      },
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: ColorTokens.onPrimary,
                  disabledBackgroundColor: ColorTokens.disabled,
                  elevation: 0,
                  shape: const RoundedRectangleBorder(
                    borderRadius: ShapeTokens.borderRadiusLg,
                  ),
                ),
                child: resetPasswordViewModel.busy
                    ? const SizedBox(
                        width: SpacingTokens.space6,
                        height: SpacingTokens.space6,
                        child: CircularProgressIndicator(
                          color: ColorTokens.onPrimary,
                          strokeWidth: 2,
                        ),
                      )
                    : Text(
                        '完成',
                        style: TypographyTokens.labelLarge.copyWith(
                          fontWeight: FontWeight.bold,
                          color: ColorTokens.onPrimary,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
