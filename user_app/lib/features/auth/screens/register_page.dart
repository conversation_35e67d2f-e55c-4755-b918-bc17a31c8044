import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:provider/provider.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:user_app/features/auth/providers/register_view_model.dart';
import 'package:user_app/features/auth/widgets/register/flat_register_phone_number.dart';
import 'package:user_app/features/auth/widgets/register/flat_register_set_password.dart';
import 'package:user_app/features/auth/widgets/register/flat_register_verification_code.dart';
import 'package:user_app/features/auth/widgets/shared/auth_animated_container.dart';
import 'package:user_app/features/auth/widgets/shared/auth_card.dart';
import 'package:user_app/features/auth/widgets/shared/auth_footer.dart';
import 'package:user_app/features/auth/widgets/shared/auth_header.dart';
import 'package:user_app/features/auth/widgets/shared/auth_scaffold.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class RegisterPage extends StatefulWidget {
  const RegisterPage({super.key});

  @override
  State<RegisterPage> createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: MotionTokens.pageTransitionDuration,
    );

    _fadeInAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 1.0, curve: MotionTokens.pageTransitionCurve),
    );

    _slideAnimation = Tween<double>(begin: 30, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.8, curve: MotionTokens.emphasized),
      ),
    );

    _animationController.forward();

    // Listen for page changes to provide haptic feedback
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<RegisterViewModel>().addPageChangeListener(_onPageChanged);
    });
  }

  void _onPageChanged() {
    // Provide haptic feedback when changing pages
    HapticFeedback.mediumImpact();
  }

  @override
  void dispose() {
    context.read<RegisterViewModel>().removePageChangeListener(_onPageChanged);
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final RegisterViewModel registerViewModel = context.read<RegisterViewModel>();
    final ColorScheme colorScheme = Theme.of(context).colorScheme;

    return PopScope(
      canPop: registerViewModel.currentPage == 0,
      onPopInvokedWithResult: (bool didPop, Object? result) {
        // Handle back button press
        if (!didPop && registerViewModel.currentPage > 0) {
          registerViewModel.goToPreviousPage();
        }
      },
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: AuthScaffold(
          useDefaultBackButton: false,
          backButton: registerViewModel.currentPage > 0
              ? IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: ColorTokens.onPrimary,
                    size: 22,
                  ),
                  onPressed: () => registerViewModel.goToPreviousPage(),
                )
              : IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios,
                    color: ColorTokens.onPrimary,
                    size: 22,
                  ),
                  onPressed: () => context.pop(),
                ),
          header: AuthAnimatedContainer(
            fadeAnimation: _fadeInAnimation,
            slideAnimation: _slideAnimation,
            padding: const EdgeInsets.only(top: SpacingTokens.space10, bottom: SpacingTokens.space4),
            child: const AuthHeader(
              title: '创建账号',
              subtitle: '注册一个新账号，开启您的钓鱼之旅',
            ),
          ),
          body: Column(
            children: [
              // Card container with registration steps
              AuthAnimatedContainer(
                fadeAnimation: _fadeInAnimation,
                slideAnimation: _slideAnimation,
                child: AuthCard(
                  height: 390, // Fixed height to reduce layout calculations
                  child: PageView(
                    controller: registerViewModel.pageController,
                    physics: const NeverScrollableScrollPhysics(),
                    onPageChanged: (index) {
                      registerViewModel.setCurrentPage(index);
                    },
                    children: const [
                      // Using RepaintBoundary for performance optimization
                      RepaintBoundary(child: FlatRegisterPhoneNumber()),
                      RepaintBoundary(child: FlatRegisterVerificationCode()),
                      RepaintBoundary(child: FlatRegisterSetPassword()),
                    ],
                  ),
                ),
              ),

              // Page indicator
              Padding(
                padding: const EdgeInsets.only(bottom: SpacingTokens.space5, top: SpacingTokens.space5),
                child: SmoothPageIndicator(
                  controller: registerViewModel.pageController,
                  count: 3,
                  effect: ExpandingDotsEffect(
                    dotWidth: SpacingTokens.space2,
                    dotHeight: SpacingTokens.space2,
                    activeDotColor: colorScheme.primary,
                    dotColor: ColorTokens.outline,
                    spacing: SpacingTokens.space2,
                    expansionFactor:
                        2.5, // A bit more expansion for better visibility
                  ),
                ),
              ),
            ],
          ),
          footer: AuthFooter(
            message: '已有账号？',
            actionText: '立即登录',
            onAction: () => context.pop(),
          ),
        ),
      ),
    );
  }
}
