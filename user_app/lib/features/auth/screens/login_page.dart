import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/auth/providers/login_view_model.dart';
import 'package:user_app/features/auth/widgets/shared/auth_animated_container.dart';
import 'package:user_app/features/auth/widgets/shared/auth_card.dart';
import 'package:user_app/features/auth/widgets/shared/auth_footer.dart';
import 'package:user_app/features/auth/widgets/shared/auth_header.dart';
import 'package:user_app/features/auth/widgets/shared/auth_input_field.dart';
import 'package:user_app/features/auth/widgets/shared/auth_scaffold.dart';

import 'package:user_app/core/design_tokens/design_tokens.dart';

class LoginPage extends StatefulWidget {
  final String? fromPath;

  const LoginPage({super.key, this.fromPath});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  late AnimationController _animationController;
  late Animation<double> _fadeInAnimation;
  late Animation<double> _slideAnimation;
  final _usernameFocusNode = FocusNode();
  final _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: MotionTokens.pageTransitionDuration,
    );

    _fadeInAnimation = CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.1, 1.0, curve: MotionTokens.pageTransitionCurve),
    );

    _slideAnimation = Tween<double>(begin: 50, end: 0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 0.7, curve: MotionTokens.emphasized),
      ),
    );

    _animationController.forward();
  }

  @override
  void dispose() {
    _usernameFocusNode.dispose();
    _passwordFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final ColorScheme colorScheme = Theme.of(context).colorScheme;
    final LoginViewModel loginViewModel = context.watch<LoginViewModel>();

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Stack(
        children: [
          AuthScaffold(
            onBackPressed: () => context.goUntilRoute(AppRoutes.fishingSpots),
            header: AuthAnimatedContainer(
              fadeAnimation: _fadeInAnimation,
              slideAnimation: _slideAnimation,
              padding: const EdgeInsets.only(
                  top: SpacingTokens.space20, bottom: SpacingTokens.space6),
              child: const AuthHeader(
                title: '欢迎回来',
                subtitle: '登录您的账号以继续您的钓鱼之旅',
              ),
            ),
            body: AuthAnimatedContainer(
              fadeAnimation: _fadeInAnimation,
              slideAnimation: _slideAnimation,
              child: AuthCard(
                child: Form(
                  key: _formKey,
                  child: Padding(
                    padding: SpacingTokens.paddingXl,
                    child:
                        _buildLoginForm(context, loginViewModel, colorScheme),
                  ),
                ),
              ),
            ),
            footer: AuthFooter(
              message: '没有账号？',
              actionText: '立即注册',
              onAction: () => context.navigateTo(AppRoutes.register),
            ),
          ),

          // 加载遮罩
          if (_isLoading)
            Container(
              color: colorScheme.scrim.withValues(alpha: 0.4),
              child: Center(
                child: Container(
                  padding: SpacingTokens.paddingXl,
                  decoration: ShapeTokens.cardDecoration(
                    color: colorScheme.surface,
                    boxShadow: ElevationTokens.shadow5(context),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: SpacingTokens.space12,
                        height: SpacingTokens.space12,
                        child: CircularProgressIndicator(
                          color: colorScheme.primary,
                          strokeWidth: 3,
                        ),
                      ),
                      SpacingTokens.verticalSpaceMd,
                      Text(
                        '登录中...',
                        style: TypographyTokens.bodyLarge.copyWith(
                          fontWeight: FontWeight.w600,
                          color: colorScheme.onSurface,
                        ),
                      )
                    ],
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildLoginForm(
    BuildContext context,
    LoginViewModel loginViewModel,
    ColorScheme colorScheme,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Text(
          '登录账号',
          style: TypographyTokens.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
            letterSpacing: 0.5,
          ),
        ),
        SpacingTokens.verticalSpaceSm,
        Text(
          '请输入您的账号信息',
          style: TypographyTokens.bodyMedium.copyWith(
            color: colorScheme.onSurfaceVariant,
          ),
        ),
        SpacingTokens.verticalSpaceLg,
        AuthInputField(
          controller: loginViewModel.phoneNumberController,
          labelText: '手机号',
          hintText: '请输入11位手机号',
          prefixIcon:
              Icon(Icons.phone_android_rounded, color: colorScheme.primary),
          keyboardType: TextInputType.phone,
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入手机号';
            }
            if (!RegExp(r'^1[3-9]\d{9}$').hasMatch(value)) {
              return '请输入有效的手机号';
            }
            return null;
          },
          focusNode: _usernameFocusNode,
          onEditingComplete: () {
            _usernameFocusNode.unfocus();
          },
        ),
        SpacingTokens.verticalSpaceLg,
        AuthInputField(
          controller: loginViewModel.passwordController,
          labelText: '密码',
          hintText: '请输入登录密码',
          prefixIcon:
              Icon(Icons.lock_outline_rounded, color: colorScheme.primary),
          obscureText: !_isPasswordVisible,
          // Replace current IconButton with this
          suffixIcon: Padding(
            padding: const EdgeInsets.only(right: SpacingTokens.space1),
            child: Material(
              color: ColorTokens.transparent,
              borderRadius: BorderRadius.circular(ShapeTokens.radiusLg + 4),
              child: InkWell(
                borderRadius: BorderRadius.circular(ShapeTokens.radiusLg + 4),
                onTap: () {
                  setState(() {
                    _isPasswordVisible = !_isPasswordVisible;
                  });
                },
                child: Padding(
                  padding: SpacingTokens.paddingSm,
                  child: Icon(
                    _isPasswordVisible
                        ? Icons.visibility_off_rounded
                        : Icons.visibility_rounded,
                    color: ColorTokens.outline,
                    size: 22,
                  ),
                ),
              ),
            ),
          ),
          focusNode: _passwordFocusNode,
          onEditingComplete: () {
            _passwordFocusNode.unfocus();
            if (_formKey.currentState?.validate() ?? false) {
              _handleLogin(context);
            }
          },
          validator: (value) {
            if (value == null || value.isEmpty) {
              return '请输入密码';
            }
            return null;
          },
        ),
        Align(
          alignment: Alignment.centerRight,
          child: TextButton(
            onPressed: () {
              context.navigateTo(AppRoutes.resetPassword);
            },
            style: TextButton.styleFrom(
              foregroundColor: colorScheme.primary,
              padding: const EdgeInsets.symmetric(
                horizontal: SpacingTokens.space2,
                vertical: SpacingTokens.space1,
              ),
            ),
            child: Text(
              '忘记密码？',
              style: TypographyTokens.labelMedium.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ),
        SpacingTokens.verticalSpace2xl,
        SizedBox(
          height: 58,
          child: ElevatedButton(
            onPressed: () => _handleLogin(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.surface,
              // Match content background
              foregroundColor: colorScheme.primary,
              // Primary color for text
              elevation: 0,
              shape: RoundedRectangleBorder(
                borderRadius: ShapeTokens.borderRadiusLg,
                side: BorderSide(
                    color: colorScheme.primary,
                    width: 1.5), // Add border for definition
              ),
              padding: SpacingTokens.buttonPaddingMedium,
            ),
            child: Text(
              '登录',
              style: TypographyTokens.labelLarge.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
                letterSpacing: 1,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleLogin(BuildContext context) async {
    FocusScope.of(context).unfocus();

    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      // Get context-dependent values before async operations
      final String? queryFromPath = GoRouterState.of(context).uri.queryParameters['from'];
      final String? targetPath = widget.fromPath ?? queryFromPath;
      
      final LoginViewModel loginViewModel = context.read<LoginViewModel>();
      final bool success = await loginViewModel.login(context);

      if (success && mounted) {
        context.goUntilRoute(targetPath ?? AppRoutes.fishingSpots);
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
