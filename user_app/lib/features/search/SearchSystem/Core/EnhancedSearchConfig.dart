import 'package:flutter/material.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/config/app_routes.dart';
import '../SearchSystem.dart';
import 'package:user_app/features/search/screens/universal_search_page.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

/// 增强的搜索配置系统
/// 
/// 基于原有 SearchPageConfig 扩展，支持更多搜索场景
class EnhancedSearchConfig extends SearchPageConfig {
  /// 搜索模式
  final SearchMode mode;
  
  /// UI配置
  final SearchUIConfig? uiConfig;
  
  /// 快捷操作
  final List<SearchQuickAction>? quickActions;
  
  /// 预设搜索关键词
  final List<String>? presetKeywords;
  
  /// 搜索范围配置
  final SearchScope? scope;
  
  /// 搜索栏后缀图标
  final Widget? suffix;
  
  /// 是否显示筛选按钮
  final bool showFilterButton;

 const EnhancedSearchConfig({
    required super.title,
    required super.algoliaIndexName,
    super.searchType = 'all',
    super.placeholder = '搜索...',
    super.showFilterTags = true,
    super.customFilterTags,
    super.showSearchHistory = true,
    super.showHotSearch = true,
    super.showSuggestions = true,
    super.hitsPerPage = 20,
    super.onResultTap,
    super.resultItemBuilder,
    super.primaryColor,
    super.searchableAttributes,
    super.enableGeoSearch = false,
    super.searchRadius,
    this.mode = SearchMode.fullPage,
    this.uiConfig,
    this.quickActions,
    this.presetKeywords,
    this.scope,
    this.suffix,
    this.showFilterButton = false,
  });
}

/// 搜索模式枚举
enum SearchMode {
  fullPage,    // 全屏搜索页
  dialog,      // 弹窗搜索
  bottomSheet, // 底部弹窗
  inline,      // 内嵌搜索
}

/// UI配置
class SearchUIConfig {
  final bool showAvatar;        // 是否显示头像
  final bool showImage;         // 是否显示图片
  final bool showStats;         // 是否显示统计信息
  final bool showDescription;   // 是否显示描述
  final SearchResultLayout layout; // 布局样式

 const SearchUIConfig({
    this.showAvatar = true,
    this.showImage = true,
    this.showStats = true,
    this.showDescription = true,
    this.layout = SearchResultLayout.list,
  });
}

/// 搜索结果布局样式
enum SearchResultLayout {
  list,   // 列表样式
  grid,   // 网格样式
  card,   // 卡片样式
}

/// 搜索快捷操作
class SearchQuickAction {
  final String id;
  final String label;
  final IconData icon;
  final Color? color;
  final VoidCallback onTap;

 const SearchQuickAction({
    required this.id,
    required this.label,
    required this.icon,
    required this.onTap,
    this.color,
  });
}

/// 搜索范围配置
class SearchScope {
  final List<String> indices;    // 搜索的索引范围
  final Map<String, dynamic>? filters; // 预设筛选条件
  final bool enableGeoFilter;    // 是否启用地理筛选
  final double? defaultRadius;   // 默认搜索半径

 const SearchScope({
    required this.indices,
    this.filters,
    this.enableGeoFilter = false,
    this.defaultRadius,
  });
}

/// 统一搜索服务
/// 
/// 管理所有搜索配置和提供统一的搜索入口
class UnifiedSearchService {
  static final UnifiedSearchService _instance = UnifiedSearchService._internal();
  factory UnifiedSearchService() => _instance;
  UnifiedSearchService._internal();

  // 预定义的搜索配置键
  static const String SPOT_SEARCH = 'spot_search';
  static const String USER_SEARCH = 'user_search';
  static const String MOMENT_SEARCH = 'moment_search';
  static const String PLAN_SEARCH = 'plan_search';
  static const String UNIVERSAL_SEARCH = 'universal_search';

  // 缓存搜索配置
  final Map<String, EnhancedSearchConfig> _configCache = {};

  /// 注册搜索配置
  void registerConfig(String key, EnhancedSearchConfig config) {
    _configCache[key] = config;
  }

  /// 获取搜索配置
  EnhancedSearchConfig? getConfig(String key) {
    return _configCache[key];
  }

  /// 快速搜索方法
  Future<void> quickSearch(
    BuildContext context,
    String configKey, {
    String? initialKeyword,
    SearchMode? mode,
    Map<String, dynamic>? extraParams,
  }) async {
    final config = getConfig(configKey);
    if (config == null) {
      debugPrint('搜索配置未找到: $configKey');
      return;
    }

    final searchMode = mode ?? config.mode;

    switch (searchMode) {
      case SearchMode.fullPage:
        await context.navigateToWithResult(
          AppRoutes.universalSearch,
          extra: {'config': config},
        );
        break;
      case SearchMode.dialog:
        await SearchDialog.show(
          context,
          title: config.title,
          hintText: config.placeholder,
          filters: config.filters?.map((f) => SearchFilterItem(
            id: f.key,
            label: f.label,
            icon: f.icon,
          )).toList(),
          searchHistory: [], // TODO: 从服务获取
          hotSearches: config.presetKeywords ?? [],
          onSearch: (keyword, selectedFilters) {
            // TODO: 执行搜索逻辑
            Navigator.of(context).pop();
          },
        );
        break;
      case SearchMode.bottomSheet:
        await SearchBottomSheet.show(
          context,
          title: config.title,
          hintText: config.placeholder,
          filters: config.filters?.map((f) => SearchFilterItem(
            id: f.key,
            label: f.label,
            icon: f.icon,
          )).toList(),
          searchHistory: [], // TODO: 从服务获取
          hotSearches: config.presetKeywords ?? [],
          onSearch: (keyword, selectedFilters) {
            // TODO: 执行搜索逻辑
          },
        );
        break;
      case SearchMode.inline:
        // TODO: 实现内嵌搜索逻辑
        debugPrint('内嵌搜索模式暂未实现');
        break;
    }
  }

  /// 批量注册默认配置
  void registerDefaultConfigs() {
    // 钓点搜索配置
    registerConfig(SPOT_SEARCH, EnhancedSearchConfig(
      title: '搜索钓点',
      placeholder: '搜索钓点、位置',
      algoliaIndexName: 'fishing_spots',
      searchType: 'spot',
      enableGeoSearch: true,
      customFilterTags: [
        SearchFilterTag(key: 'all', label: '全部', icon: Icons.apps),
        SearchFilterTag(key: 'free', label: '免费', icon: Icons.money_off),
        SearchFilterTag(key: 'paid', label: '付费', icon: Icons.payment),
        SearchFilterTag(key: 'verified', label: '认证', icon: Icons.verified),
      ],
      uiConfig: const SearchUIConfig(
        showImage: true,
        showStats: true,
        layout: SearchResultLayout.card,
      ),
      presetKeywords: ['鲫鱼塘', '黑坑', '野钓', '免费钓场'],
      showFilterButton: true,
    ));

    // 用户搜索配置
    registerConfig(USER_SEARCH, EnhancedSearchConfig(
      title: '搜索钓友',
      placeholder: '搜索用户、昵称',
      algoliaIndexName: 'users',
      searchType: 'user',
      customFilterTags: [
        SearchFilterTag(key: 'all', label: '全部', icon: Icons.people),
        SearchFilterTag(key: 'expert', label: '钓鱼达人', icon: Icons.star),
        SearchFilterTag(key: 'nearby', label: '附近', icon: Icons.location_on),
      ],
      uiConfig: const SearchUIConfig(
        showAvatar: true,
        showStats: false,
        layout: SearchResultLayout.list,
      ),
      presetKeywords: ['钓鱼达人', '新手', '本地钓友'],
    ));

    // 动态搜索配置
    registerConfig(MOMENT_SEARCH, EnhancedSearchConfig(
      title: '搜索动态',
      placeholder: '搜索动态、话题',
      algoliaIndexName: 'moments',
      searchType: 'moment',
      customFilterTags: [
        SearchFilterTag(key: 'all', label: '全部', icon: Icons.article),
        SearchFilterTag(key: 'catch', label: '渔获', icon: Icons.phishing),
        SearchFilterTag(key: 'skill', label: '技巧', icon: Icons.school),
        SearchFilterTag(key: 'gear', label: '装备', icon: Icons.build),
      ],
      uiConfig: const SearchUIConfig(
        showImage: true,
        showStats: true,
        layout: SearchResultLayout.card,
      ),
      presetKeywords: ['渔获分享', '钓鱼技巧', '装备测评', '钓场攻略'],
      showFilterButton: true,
    ));

    // 计划搜索配置
    registerConfig(PLAN_SEARCH, EnhancedSearchConfig(
      title: '搜索计划',
      placeholder: '搜索钓鱼计划',
      algoliaIndexName: 'plans',
      searchType: 'plan',
      customFilterTags: [
        SearchFilterTag(key: 'all', label: '全部', icon: Icons.event),
        SearchFilterTag(key: 'upcoming', label: '即将开始', icon: Icons.schedule),
        SearchFilterTag(key: 'joinable', label: '可参与', icon: Icons.group_add),
      ],
      uiConfig: const SearchUIConfig(
        showStats: true,
        layout: SearchResultLayout.list,
      ),
      presetKeywords: ['周末钓鱼', '夜钓', '路亚', '海钓'],
    ));

    // 综合搜索配置
    registerConfig(UNIVERSAL_SEARCH, EnhancedSearchConfig(
      title: '综合搜索',
      placeholder: '搜索钓点、动态、用户',
      algoliaIndexName: 'universal',
      searchType: 'all',
      enableGeoSearch: true,
      customFilterTags: [
        SearchFilterTag(key: 'all', label: '全部', icon: Icons.apps),
        SearchFilterTag(key: 'spot', label: '钓点', icon: Icons.location_on),
        SearchFilterTag(key: 'moment', label: '动态', icon: Icons.article),
        SearchFilterTag(key: 'user', label: '用户', icon: Icons.person),
        SearchFilterTag(key: 'plan', label: '计划', icon: Icons.event),
      ],
      uiConfig: const SearchUIConfig(
        showImage: true,
        showAvatar: true,
        showStats: true,
        layout: SearchResultLayout.list,
      ),
      presetKeywords: ['鲫鱼', '黑坑', '野钓', '路亚', '海钓'],
      showFilterButton: true,
    ));
  }

  /// 获取所有注册的配置键
  List<String> getAllConfigKeys() {
    return _configCache.keys.toList();
  }

  /// 清除所有配置
  void clearAllConfigs() {
    _configCache.clear();
  }
}