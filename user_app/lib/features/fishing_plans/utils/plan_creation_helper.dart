import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_plans/screens/plan_creation_entry_page.dart';
import 'package:user_app/features/fishing_plans/screens/create_fishing_plan_page.dart';
import 'package:user_app/features/fishing_plans/screens/standard_create_plan_page.dart';
import 'package:user_app/features/fishing_plans/screens/copy_history_plan_page.dart';

/// 钓鱼计划创建助手
/// 提供各种创建入口的导航方法
class PlanCreationHelper {
  
  /// 显示新的创建中心页面
  static Future<bool?> showCreationOptions(BuildContext context) {
    context.navigateTo('/create-center');
    return Future.value(true);
  }
  
  /// 直接跳转到快速创建页面（新模板系统）
  static Future<bool?> showQuickCreate(BuildContext context, {
    Map<String, dynamic>? arguments,
  }) {
    context.navigateTo('/fishing-plans/quick-create');
    return Future.value(true);
  }
  
  /// 直接跳转到标准创建页面
  static Future<bool?> showStandardCreate(BuildContext context) {
    return context.navigateToWithResult<bool>(AppRoutes.standardCreatePlan);
  }
  
  /// 显示复制历史计划页面
  static Future<bool?> showCopyHistory(BuildContext context) {
    return context.navigateToWithResult<bool>(AppRoutes.copyHistoryPlan);
  }
  
  /// 从钓点详情页创建计划（预填充钓点信息）
  static Future<bool?> createFromSpot(BuildContext context, {
    required String spotName,
    required String spotLocation,
    double? latitude,
    double? longitude,
    String? spotType,
    Map<String, dynamic>? spotData,
  }) {
    return Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => CreateFishingPlanPage(
          // TODO: 创建一个从钓点数据预填充的构造方法
        ),
      ),
    );
  }
  
  /// 从日历页面创建计划（预设定日期）
  static Future<bool?> createFromCalendar(BuildContext context, {
    required DateTime selectedDate,
    TimeOfDay? selectedTime,
  }) {
    return Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => CreateFishingPlanPage(
          // TODO: 创建一个从日期预填充的构造方法
        ),
      ),
    );
  }
  
  /// 获取创建入口的配置
  static List<PlanCreationEntry> getCreationEntries() {
    return [
      PlanCreationEntry(
        id: 'quick',
        title: '快速创建',
        subtitle: '简单快捷，一页搞定所有信息',
        icon: Icons.flash_on,
        color: ColorTokens.primary,
        route: '/fishing-plans/create-quick',
      ),
      PlanCreationEntry(
        id: 'standard',
        title: '标准创建',
        subtitle: '分步向导，详细设置每个环节',
        icon: Icons.format_list_numbered,
        color: ColorTokens.success,
        route: '/fishing-plans/create-standard',
      ),
      PlanCreationEntry(
        id: 'copy',
        title: '复制历史',
        subtitle: '基于历史计划快速创建新计划',
        icon: Icons.copy,
        color: ColorTokens.info,
        route: '/fishing-plans/copy-history',
      ),
    ];
  }
  
  /// 根据用户使用情况推荐创建方式
  static String getRecommendedCreationMethod(
    int userCreatedPlanCount, 
    int userSkillLevel,
  ) {
    if (userCreatedPlanCount == 0) {
      return 'quick'; // 首次使用推荐快速创建
    } else if (userCreatedPlanCount < 5) {
      return 'standard'; // 新用户推荐标准创建学习完整流程
    } else if (userCreatedPlanCount >= 10) {
      return 'copy'; // 老用户推荐复制历史提升效率
    } else {
      return 'quick'; // 默认快速创建
    }
  }
  
  /// 获取创建提示
  static String getCreationTip(String method) {
    switch (method) {
      case 'quick':
        return '💡 首次使用建议选择快速创建，简单易用。';
      case 'standard':
        return '✨ 标准创建提供更详细的选项，适合有经验的用户。';
      case 'copy':
        return '📋 复制历史计划可以快速创建相似的活动，节省时间。';
      default:
        return '🎣 选择最适合你的创建方式，开始你的钓鱼之旅。';
    }
  }
  
  /// 验证创建权限
  static bool canCreatePlan(BuildContext context) {
    // TODO: 实现权限检查逻辑
    // - 检查用户是否已登录
    // - 检查用户是否有创建权限
    // - 检查用户是否达到创建数量限制
    return true;
  }
  
  /// 显示创建权限不足的提示
  static void showPermissionDeniedDialog(
    BuildContext context, 
    String reason,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('无法创建计划'),
        content: Text(reason),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }
}

/// 创建入口配置类
class PlanCreationEntry {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;
  final String route;
  
 const PlanCreationEntry({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
    required this.route,
  });
}