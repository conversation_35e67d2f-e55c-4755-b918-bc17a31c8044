import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/features/fishing_plans/utils/plan_creation_helper.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 快速创建悬浮按钮
/// 可以在首页、钓点列表等页面使用，提供快捷的计划创建入口
class QuickCreateFAB extends StatefulWidget {
  final bool showLabels; // 是否显示标签
  final bool expanded; // 是否展开显示所有选项
  
 const QuickCreateFAB({
    super.key,
    this.showLabels = true,
    this.expanded = false,
  });

  @override
  State<QuickCreateFAB> createState() => _QuickCreateFABState();
}

class _QuickCreateFABState extends State<QuickCreateFAB>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _rotateController;
  late Animation<double> _animation;
  late Animation<double> _rotateAnimation;
  
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: MotionTokens.durationMedium,
      vsync: this,
    );
    
    _rotateController = AnimationController(
      duration: MotionTokens.durationShort,
      vsync: this,
    );
    
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
    
    _rotateAnimation = Tween(begin: 0.0, end: 0.125).animate(
      CurvedAnimation(parent: _rotateController, curve: Curves.easeInOut),
    );
    
    _isExpanded = widget.expanded;
    if (_isExpanded) {
      _animationController.value = 1.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    HapticFeedback.lightImpact();
    setState(() {
      _isExpanded = !_isExpanded;
    });
    
    if (_isExpanded) {
      _animationController.forward();
      _rotateController.forward();
    } else {
      _animationController.reverse();
      _rotateController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authViewModel, child) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            // 展开的选项
            AnimatedBuilder(
              animation: _animation,
              builder: (context, child) {
                return Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (_animation.value > 0) ...[
                      Transform.scale(
                        scale: _animation.value,
                        child: Opacity(
                          opacity: _animation.value,
                          child: _buildOptionFAB(
                            onPressed: () => _handleQuickCreate(authViewModel),
                            icon: Icons.flash_on,
                            label: '快速创建',
                            heroTag: 'quick_create',
                            color: ColorTokens.primary,
                          ),
                        ),
                      ),
                      SizedBox(height: SpacingTokens.space4 * _animation.value),
                      
                      Transform.scale(
                        scale: _animation.value,
                        child: Opacity(
                          opacity: _animation.value,
                          child: _buildOptionFAB(
                            onPressed: () => _handleStandardCreate(authViewModel),
                            icon: Icons.format_list_numbered,
                            label: '标准创建',
                            heroTag: 'standard_create',
                            color: ColorTokens.success,
                          ),
                        ),
                      ),
                      SizedBox(height: SpacingTokens.space4 * _animation.value),
                      
                      Transform.scale(
                        scale: _animation.value,
                        child: Opacity(
                          opacity: _animation.value,
                          child: _buildOptionFAB(
                            onPressed: () => _handleCopyHistory(authViewModel),
                            icon: Icons.copy,
                            label: '复制历史',
                            heroTag: 'copy_history',
                            color: ColorTokens.info,
                          ),
                        ),
                      ),
                      SizedBox(height: SpacingTokens.space4 * _animation.value),
                    ],
                  ],
                );
              },
            ),
            
            // 主按钮
            AnimatedBuilder(
              animation: _rotateAnimation,
              builder: (context, child) {
                return Transform.rotate(
                  angle: _rotateAnimation.value * 2 * 3.14159,
                  child: FloatingActionButton(
                    onPressed: _isExpanded ? _toggleExpanded : () => _handleMainAction(authViewModel),
                    heroTag: 'main_create_fab',
                    backgroundColor: ColorTokens.primary,
                    child: AnimatedSwitcher(
                      duration: MotionTokens.durationShort,
                      child: _isExpanded
                          ? const Icon(Icons.close, key: Key('close'), color: ColorTokens.onPrimary)
                          : const Icon(Icons.add, key: Key('add'), color: ColorTokens.onPrimary),
                    ),
                  ),
                );
              },
            ),
            
            // 长按提示（仅当未展开时显示）
            if (!_isExpanded && widget.showLabels)
             const Padding(
                padding: EdgeInsets.only(top: SpacingTokens.space2),
                child: Text(
                  '长按显示更多选项',
                  style: const TextStyle(
                    fontSize: 12,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildOptionFAB({
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    required String heroTag,
    required Color color,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.showLabels) ...[
          Container(
            padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space3, vertical: SpacingTokens.space2),
            decoration: ShapeTokens.containerDecoration(
              color: ColorTokens.onSurface.withValues(alpha: 0.87),
              borderRadius: ShapeTokens.borderRadiusFull,
            ),
            child: Text(
              label,
              style: const TextStyle(
                color: ColorTokens.surface,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SpacingTokens.horizontalSpaceMd,
        ],
        FloatingActionButton(
          onPressed: onPressed,
          heroTag: heroTag,
          backgroundColor: color,
          mini: true,
          child: Icon(icon, color: ColorTokens.onPrimary, size: 20),
        ),
      ],
    );
  }

  void _handleMainAction(AuthViewModel authViewModel) {
    if (!authViewModel.isUserLoggedIn()) {
      _showLoginPrompt();
      return;
    }
    
    // 如果不展开，直接跳转到创建选择页面
    PlanCreationHelper.showCreationOptions(context);
  }

  void _handleQuickCreate(AuthViewModel authViewModel) {
    if (!authViewModel.isUserLoggedIn()) {
      _showLoginPrompt();
      return;
    }
    
    _toggleExpanded();
    PlanCreationHelper.showQuickCreate(context);
  }

  void _handleStandardCreate(AuthViewModel authViewModel) {
    if (!authViewModel.isUserLoggedIn()) {
      _showLoginPrompt();
      return;
    }
    
    _toggleExpanded();
    PlanCreationHelper.showStandardCreate(context);
  }

  void _handleCopyHistory(AuthViewModel authViewModel) {
    if (!authViewModel.isUserLoggedIn()) {
      _showLoginPrompt();
      return;
    }
    
    _toggleExpanded();
    PlanCreationHelper.showCopyHistory(context);
  }

  void _showLoginPrompt() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要登录'),
        content: const Text('请先登录后再创建钓鱼计划'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.navigateTo('/auth/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              foregroundColor: ColorTokens.onPrimary,
            ),
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }
}

/// 简化版快速创建按钮
/// 用于空间有限的场所，只显示主按钮
class CompactCreateFAB extends StatelessWidget {
 const CompactCreateFAB({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authViewModel, child) {
        return FloatingActionButton(
          onPressed: () {
            HapticFeedback.lightImpact();
            if (!authViewModel.isUserLoggedIn()) {
              _showLoginPrompt(context);
              return;
            }
            PlanCreationHelper.showCreationOptions(context);
          },
          backgroundColor: ColorTokens.primary,
          child: const Icon(Icons.add, color: ColorTokens.onPrimary),
        );
      },
    );
  }

  void _showLoginPrompt(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要登录'),
        content: const Text('请先登录后再创建钓鱼计划'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.navigateTo('/auth/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              foregroundColor: ColorTokens.onPrimary,
            ),
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }
}