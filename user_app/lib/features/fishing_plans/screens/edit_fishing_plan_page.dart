import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';

class EditFishingPlanPage extends StatefulWidget {
  final int planId;

  const EditFishingPlanPage({
    super.key,
    required this.planId,
  });

  @override
  State<EditFishingPlanPage> createState() => _EditFishingPlanPageState();
}

class _EditFishingPlanPageState extends State<EditFishingPlanPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();

  // Form controllers
  final _titleController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _maxParticipantsController = TextEditingController();
  final _weatherController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _contactInfoController = TextEditingController();
  final _timeRangeController = TextEditingController();

  // Form state
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isPublic = true;
  bool _isSubmitting = false;
  bool _isLoading = true;
  String? _error;
  FishingPlan? _originalPlan;

  // Animation
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    _animationController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadPlanData();
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _maxParticipantsController.dispose();
    _weatherController.dispose();
    _temperatureController.dispose();
    _contactInfoController.dispose();
    _timeRangeController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadPlanData() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final viewModel = context.read<FishingPlanViewModel>();
      await viewModel.loadPlanDetail(widget.planId);

      final plan = viewModel.currentPlan;
      if (plan == null) {
        throw Exception('计划不存在');
      }

      _originalPlan = plan;

      // 填充表单数据
      _titleController.text = plan.title;
      _locationController.text = plan.location;
      _descriptionController.text = plan.description;
      _maxParticipantsController.text = plan.maxParticipants.toString();
      _weatherController.text = plan.weather ?? '';
      _temperatureController.text = plan.temperature ?? '';
      _contactInfoController.text = plan.contactInfo ?? '';
      _timeRangeController.text = plan.timeRange ?? '';

      _selectedDate = plan.planDate;
      _selectedTime = TimeOfDay.fromDateTime(plan.planDate);
      _isPublic = plan.isPublic;

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });
    }
  }

  Future<void> _selectDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: ColorTokens.onSurface,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null) {
      setState(() {
        _selectedDate = pickedDate;
      });
    }
  }

  Future<void> _selectTime() async {
    final pickedTime = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Colors.white,
              surface: Colors.white,
              onSurface: ColorTokens.onSurface,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedTime != null) {
      setState(() {
        _selectedTime = pickedTime;
      });
    }
  }

  DateTime get _combinedDateTime {
    return DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );
  }

  bool get _hasChanges {
    if (_originalPlan == null) return false;

    return _titleController.text != _originalPlan!.title ||
        _locationController.text != _originalPlan!.location ||
        _descriptionController.text != _originalPlan!.description ||
        _maxParticipantsController.text !=
            _originalPlan!.maxParticipants.toString() ||
        _weatherController.text != (_originalPlan!.weather ?? '') ||
        _temperatureController.text != (_originalPlan!.temperature ?? '') ||
        _contactInfoController.text != (_originalPlan!.contactInfo ?? '') ||
        _timeRangeController.text != (_originalPlan!.timeRange ?? '') ||
        _combinedDateTime != _originalPlan!.planDate ||
        _isPublic != _originalPlan!.isPublic;
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (!_hasChanges) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('没有修改内容')),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final viewModel = context.read<FishingPlanViewModel>();

      final dto = FishingPlanUpdateDTO(
        title: _titleController.text.trim(),
        location: _locationController.text.trim(),
        planDate: _combinedDateTime,
        timeRange: _timeRangeController.text.trim().isEmpty
            ? null
            : _timeRangeController.text.trim(),
        maxParticipants: int.parse(_maxParticipantsController.text),
        weather: _weatherController.text.trim().isEmpty
            ? null
            : _weatherController.text.trim(),
        temperature: _temperatureController.text.trim().isEmpty
            ? null
            : _temperatureController.text.trim(),
        description: _descriptionController.text.trim().isEmpty
            ? '暂无描述'
            : _descriptionController.text.trim(),
        contactInfo: _contactInfoController.text.trim().isEmpty
            ? null
            : _contactInfoController.text.trim(),
        isPublic: _isPublic,
      );

      await viewModel.updatePlan(widget.planId, dto);

      if (mounted) {
        HapticFeedback.mediumImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('计划更新成功'),
            backgroundColor: Colors.green,
          ),
        );
        context.navigateBack(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('更新失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const UnifiedPageScaffold(
        title: '编辑计划',
        body: UnifiedLoadingState(message: '加载中...'),
      );
    }

    if (_error != null) {
      return UnifiedPageScaffold(
        title: '编辑计划',
        body: UnifiedEmptyState(
          icon: Icons.error_outline,
          title: '加载失败',
          subtitle: _error!,
          action: ElevatedButton(
            onPressed: _loadPlanData,
            child: const Text('重试'),
          ),
        ),
      );
    }

    return UnifiedPageScaffold(
      title: '编辑计划',
      actions: [
        if (_hasChanges)
          TextButton(
            onPressed: _isSubmitting ? null : _submitForm,
            child: Text(
              '保存',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
      ],
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Form(
          key: _formKey,
          child: ListView(
            padding: SpacingTokens.paddingMd,
            children: [
              // 提示信息
              if (_originalPlan != null &&
                  _originalPlan!.currentParticipants > 1)
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border:
                        Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.info_outline,
                          color: Colors.orange, size: 20),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '已有${_originalPlan!.currentParticipants}人参与此计划，修改请谨慎',
                          style: const TextStyle(
                            color: Colors.orange,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

              // 基本信息卡片
              _buildSectionCard(
                title: '基本信息',
                icon: Icons.info,
                children: [
                  _buildTextField(
                    controller: _titleController,
                    label: '计划标题',
                    hint: '例如：周末野钓活动',
                    icon: Icons.title,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '请输入计划标题';
                      }
                      if (value.trim().length < 2) {
                        return '标题至少需要2个字符';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(
                    controller: _locationController,
                    label: '钓点位置',
                    hint: '例如：东湖公园南岸',
                    icon: Icons.location_on,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '请输入钓点位置';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(
                    controller: _descriptionController,
                    label: '计划描述',
                    hint: '描述一下这次钓鱼计划的详情...',
                    icon: Icons.description,
                    maxLines: 4,
                  ),
                ],
              ),
              const SizedBox(height: SpacingTokens.space4),

              // 时间设置卡片
              _buildSectionCard(
                title: '时间设置',
                icon: Icons.schedule,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDateTimeButton(
                          label: '日期',
                          value:
                              DateFormat('yyyy年MM月dd日').format(_selectedDate),
                          icon: Icons.calendar_today,
                          onTap: _selectDate,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildDateTimeButton(
                          label: '时间',
                          value: _selectedTime.format(context),
                          icon: Icons.access_time,
                          onTap: _selectTime,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(
                    controller: _timeRangeController,
                    label: '时间范围',
                    hint: '例如：早上6点-下午3点',
                    icon: Icons.timelapse,
                  ),
                ],
              ),
              const SizedBox(height: SpacingTokens.space4),

              // 参与设置卡片
              _buildSectionCard(
                title: '参与设置',
                icon: Icons.group,
                children: [
                  _buildTextField(
                    controller: _maxParticipantsController,
                    label: '最大参与人数',
                    hint: '包含自己',
                    icon: Icons.people,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入最大参与人数';
                      }
                      final num = int.tryParse(value);
                      if (num == null || num < 1) {
                        return '参与人数至少为1人';
                      }
                      if (num > 100) {
                        return '参与人数不能超过100人';
                      }
                      if (_originalPlan != null &&
                          num < _originalPlan!.currentParticipants) {
                        return '不能小于当前参与人数(${_originalPlan!.currentParticipants}人)';
                      }
                      return null;
                    },
                  ),
                  const SizedBox(height: SpacingTokens.space4),
                  _buildSwitchTile(
                    title: '公开计划',
                    subtitle: '其他用户可以看到并加入此计划',
                    value: _isPublic,
                    onChanged: (value) {
                      setState(() {
                        _isPublic = value;
                      });
                    },
                  ),
                ],
              ),
              const SizedBox(height: SpacingTokens.space4),

              // 其他信息卡片
              _buildSectionCard(
                title: '其他信息',
                icon: Icons.more_horiz,
                children: [
                  _buildTextField(
                    controller: _weatherController,
                    label: '天气预报',
                    hint: '例如：晴天',
                    icon: Icons.wb_sunny,
                  ),
                  const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(
                    controller: _temperatureController,
                    label: '温度',
                    hint: '例如：20-25°C',
                    icon: Icons.thermostat,
                  ),
                  const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(
                    controller: _contactInfoController,
                    label: '联系方式',
                    hint: '例如：微信号、手机号',
                    icon: Icons.phone,
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // 提交按钮
              Container(
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      const Color(0xFFFF6B6B)
                    ],
                  ),
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context)
                          .colorScheme
                          .primary
                          .withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: MaterialButton(
                  onPressed: _isSubmitting || !_hasChanges ? null : _submitForm,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          _hasChanges ? '保存修改' : '没有修改',
                          style: TextStyle(
                            color: Colors.white
                                .withValues(alpha: _hasChanges ? 1.0 : 0.7),
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
              const SizedBox(height: 32),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.primary,
                      const Color(0xFFFF6B6B)
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 20, color: Colors.white),
              ),
              const SizedBox(width: 12),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space5),
          ...children,
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    final int maxLines = 1,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Theme.of(context).colorScheme.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: ColorTokens.divider),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: ColorTokens.divider),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(
              color: Theme.of(context).colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Colors.red),
        ),
        filled: true,
        fillColor: ColorTokens.surface,
      ),
    );
  }

  Widget _buildDateTimeButton({
    required String label,
    required String value,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: SpacingTokens.paddingMd,
        decoration: BoxDecoration(
          border: Border.all(color: ColorTokens.divider),
          borderRadius: BorderRadius.circular(16),
          color: ColorTokens.surface,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 16, color: ColorTokens.onSurfaceVariant),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: const TextStyle(
                    fontSize: 12,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        border: Border.all(color: ColorTokens.divider),
        borderRadius: BorderRadius.circular(16),
        color: ColorTokens.surface,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }
}
