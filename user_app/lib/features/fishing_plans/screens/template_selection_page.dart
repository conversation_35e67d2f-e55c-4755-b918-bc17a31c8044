import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/models/fishing_plan/plan_template.dart';
import 'package:user_app/services/plan_template_service.dart';

import '../widgets/template_category_card_widget.dart';

/// 模板选择页面
class TemplateSelectionPage extends StatefulWidget {
  const TemplateSelectionPage({super.key});

  @override
  State<TemplateSelectionPage> createState() => _TemplateSelectionPageState();
}

class _TemplateSelectionPageState extends State<TemplateSelectionPage> {
  late final PlanTemplateService _templateService;

  List<PlanTemplateCategory> _categories = [];

  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _templateService = getIt<PlanTemplateService>();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final categories = await _templateService.getTemplateCategories();

      setState(() {
        _categories =
            categories.isNotEmpty ? categories : _getFallbackCategories();
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('获取分类数据失败，使用默认数据: $e');
      setState(() {
        _categories = _getFallbackCategories();
        _isLoading = false;
      });
    }
  }

  /// 获取默认分类数据（API失败时使用）
  List<PlanTemplateCategory> _getFallbackCategories() {
    return [
      const PlanTemplateCategory(
        id: 1,
        code: 'newbie',
        name: '新手入门',
        description: '适合钓鱼新手的简单活动模版',
        icon: '⭐',
        color: '#4CAF50',
        sortOrder: 1,
        isActive: true,
        templateCount: 1,
      ),
      const PlanTemplateCategory(
        id: 2,
        code: 'leisure',
        name: '休闲垂钓',
        description: '轻松惬意的垂钓活动模版',
        icon: '🌱',
        color: '#00BCD4',
        sortOrder: 2,
        isActive: true,
        templateCount: 1,
      ),
      const PlanTemplateCategory(
        id: 3,
        code: 'challenge',
        name: '挑战自我',
        description: '适合有经验的钓友的挑战性活动',
        icon: '⚡',
        color: '#F44336',
        sortOrder: 3,
        isActive: true,
        templateCount: 1,
      ),
      const PlanTemplateCategory(
        id: 4,
        code: 'specialty',
        name: '特色钓法',
        description: '独特的钓鱼体验活动',
        icon: '🎯',
        color: '#9C27B0',
        sortOrder: 4,
        isActive: true,
        templateCount: 1,
      ),
      const PlanTemplateCategory(
        id: 5,
        code: 'sea_fishing',
        name: '海钓专区',
        description: '专业海钓活动',
        icon: '🌊',
        color: '#4CAF50',
        sortOrder: 5,
        isActive: true,
        templateCount: 1,
      ),
      const PlanTemplateCategory(
        id: 6,
        code: 'skill_improve',
        name: '技巧提升',
        description: '钓鱼技巧培训和提升',
        icon: '🎓',
        color: '#FF9800',
        sortOrder: 6,
        isActive: true,
        templateCount: 1,
      ),
      const PlanTemplateCategory(
        id: 7,
        code: 'social',
        name: '社交聚会',
        description: '钓友交流和聚会活动',
        icon: '👥',
        color: '#607D8B',
        sortOrder: 7,
        isActive: true,
        templateCount: 1,
      ),
      const PlanTemplateCategory(
        id: 8,
        code: 'family',
        name: '亲子垂钓',
        description: '适合家庭参与的亲子钓鱼',
        icon: '👨‍👩‍👧‍👦',
        color: '#E91E63',
        sortOrder: 8,
        isActive: true,
        templateCount: 1,
      ),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('选择模板'),
        backgroundColor: ColorTokens.primary,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          if (_categories.isNotEmpty)
            IconButton(
              onPressed: () {
                _showCategoryFilterDialog();
              },
              icon: const Icon(Icons.filter_list),
            ),
        ],
        // 移除 TabBar，改为显示分类网格
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorState()
              : _buildCategoryGrid(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: SpacingTokens.paddingXl,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.grey,
            ),
            const SizedBox(height: SpacingTokens.space4),
            const Text(
              '加载模板失败',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: SpacingTokens.space2),
            Text(
              _error!,
              textAlign: TextAlign.center,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: SpacingTokens.space6),
            ElevatedButton.icon(
              onPressed: _loadData,
              icon: const Icon(Icons.refresh),
              label: const Text('重试'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorTokens.primary,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryGrid() {
    if (_categories.isEmpty) {
      return const Center(
        child: Text(
          '暂无分类数据',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    return SingleChildScrollView(
      padding: SpacingTokens.paddingMd,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 标题
          const Text(
            '选择计划类型',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Color(0xFF333333),
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          const Text(
            '根据你的钓鱼偏好选择合适的模版分类',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: SpacingTokens.space5),
          // 分类网格
          LayoutBuilder(
            builder: (context, constraints) {
              // 根据屏幕宽度决定列数和高度比例
              int crossAxisCount = 2;
              double childAspectRatio = 0.85; // 调整为更高的卡片

              if (constraints.maxWidth > 600) {
                crossAxisCount = 3;
                childAspectRatio = 0.9;
              } else if (constraints.maxWidth < 350) {
                crossAxisCount = 1;
                childAspectRatio = 1.8;
              }

              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  childAspectRatio: childAspectRatio,
                  crossAxisSpacing: 12,
                  mainAxisSpacing: 12,
                ),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  return TemplateCategoryCardWidget(
                    category: category,
                    onTap: () => _navigateToCategoryTemplates(category),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Future<void> _navigateToCategoryTemplates(
      PlanTemplateCategory category) async {
    debugPrint('点击分类: ${category.displayName} (${category.code})');

    try {
      // 获取该分类下的模版
      final templates =
          await _templateService.getTemplatesByCategory(category.code);

      // 导航到模版列表页面
      context.navigateTo(AppRoutes.categoryTemplates, extra: CategoryTemplatesRouteData(
        category: category,
        templates: templates,
      ));
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('获取模版失败: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showCategoryFilterDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: SpacingTokens.paddingLg,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.filter_list, color: ColorTokens.primary),
                  const SizedBox(width: 8),
                  const Text(
                    '分类筛选',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              const SizedBox(height: SpacingTokens.space4),
              const Text(
                '按模版数量排序：',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
              const SizedBox(height: SpacingTokens.space2),
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _sortCategoriesByTemplateCount(ascending: false);
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.arrow_downward, size: 16),
                      label: const Text('从多到少'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorTokens.primary,
                        foregroundColor: Colors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        _sortCategoriesByTemplateCount(ascending: true);
                        Navigator.pop(context);
                      },
                      icon: const Icon(Icons.arrow_upward, size: 16),
                      label: const Text('从少到多'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.grey.shade300,
                        foregroundColor: Colors.black87,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              TextButton(
                onPressed: () {
                  _resetCategoryOrder();
                  Navigator.pop(context);
                },
                child: const Text('恢复默认排序'),
              ),
            ],
          ),
        );
      },
    );
  }

  void _sortCategoriesByTemplateCount({required bool ascending}) {
    setState(() {
      _categories.sort((a, b) {
        return ascending
            ? a.templateCount.compareTo(b.templateCount)
            : b.templateCount.compareTo(a.templateCount);
      });
    });
  }

  void _resetCategoryOrder() {
    setState(() {
      _categories.sort((a, b) => a.sortOrder.compareTo(b.sortOrder));
    });
  }
}
