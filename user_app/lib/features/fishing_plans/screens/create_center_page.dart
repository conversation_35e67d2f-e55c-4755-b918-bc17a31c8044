import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';

import 'template_selection_page.dart';

/// 创建中心页面 - 作为所有创建流程的唯一入口
/// 提供三个路径：个人计划、好友约钓、公开活动
class CreateCenterPage extends StatefulWidget {
 const CreateCenterPage({super.key});

  @override
  State<CreateCenterPage> createState() => _CreateCenterPageState();
}

class _CreateCenterPageState extends State<CreateCenterPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late List<Animation<Offset>> _slideAnimations;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: MotionTokens.durationMedium,
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    // 为三个选项创建不同延迟的滑动动画
    _slideAnimations = List.generate(3, (index) {
      return Tween<Offset>(
        begin: Offset(0, 0.5),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Interval(
          index * 0.2,
          0.6 + index * 0.2,
          curve: Curves.easeOutCubic,
        ),
      ));
    });

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authViewModel, child) {
        // 检查用户是否已登录
        if (!authViewModel.isUserLoggedIn()) {
          return _buildLoginRequired();
        }

        return UnifiedPageScaffold(
          title: '创建',
          useSliver: false,
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(SpacingTokens.md),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 标题区域
                  SizedBox(height: SpacingTokens.md),
                  Text(
                    '你想要创建什么？',
                    style: TextStyle(
                      fontSize: TypographyTokens.headlineLarge.fontSize,
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: SpacingTokens.xs),
                  Text(
                    '选择最适合你需求的创建方式',
                    style: TextStyle(
                      fontSize: TypographyTokens.bodyLarge.fontSize,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: SpacingTokens.space10),

                  // 三个创建选项
                  SlideTransition(
                    position: _slideAnimations[0],
                    child: _buildCreationCard(
                      index: 0,
                      title: '创建个人计划',
                      subtitle: '为自己快速制定一份计划，或记录灵感',
                      icon: Icons.person_outline,
                      iconBackgroundGradient: const LinearGradient(
                        colors: [ColorTokens.secondary, ColorTokens.tertiary],
                      ),
                      features: const [
                        '快速记录钓鱼计划',
                        '使用智能模板',
                        '个人私密或公开分享',
                      ],
                      onTap: () => _navigateToQuickCreate(),
                    ),
                  ),
                 const SizedBox(height: SpacingTokens.md),

                  SlideTransition(
                    position: _slideAnimations[1],
                    child: _buildCreationCard(
                      index: 1,
                      title: '发起好友约钓',
                      subtitle: '邀请朋友，一起享受钓鱼的乐趣',
                      icon: Icons.group_outlined,
                      iconBackgroundGradient: const LinearGradient(
                        colors: [ColorTokens.error, ColorTokens.primary],
                      ),
                      features: const [
                        '邀请好友参与',
                        '费用AA管理',
                        '专属讨论群组',
                      ],
                      onTap: () => _navigateToFriendOuting(),
                    ),
                  ),
                 const SizedBox(height: SpacingTokens.md),

                  SlideTransition(
                    position: _slideAnimations[2],
                    child: _buildCreationCard(
                      index: 2,
                      title: '组织公开活动',
                      subtitle: '为您的鱼塘、俱乐部或社区组织一场活动',
                      icon: Icons.campaign_outlined,
                      iconBackgroundGradient: const LinearGradient(
                        colors: [ColorTokens.success, ColorTokens.success],
                      ),
                      features: const [
                        '发布公开活动',
                        '报名与票务管理',
                        '活动推广展示',
                      ],
                      onTap: () => _navigateToPublishEvent(),
                      requiresOrganizer: true,
                    ),
                  ),

                 const SizedBox(height: SpacingTokens.xxl),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoginRequired() {
    return UnifiedPageScaffold(
      title: '创建',
      useSliver: false,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: const BoxDecoration(
                color: ColorTokens.primaryContainer,
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.lock_outline,
                size: 50,
                color: ColorTokens.primary,
              ),
            ),
            SizedBox(height: SpacingTokens.lg),
            Text(
              '请先登录',
              style: TextStyle(
                fontSize: TypographyTokens.headlineMedium.fontSize,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: SpacingTokens.xs),
            Text(
              '登录后即可创建钓鱼计划',
              style: TypographyTokens.bodyLarge.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: SpacingTokens.xl),
            ElevatedButton(
              onPressed: () => context.navigateTo('/auth/login'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorTokens.primary,
                foregroundColor: ColorTokens.onPrimary,
                padding: EdgeInsets.symmetric(
                    horizontal: SpacingTokens.space20,
                    vertical: SpacingTokens.md),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(ShapeTokens.radiusXLarge),
                ),
                elevation: 0,
              ),
              child: Text(
                '立即登录',
                style: TypographyTokens.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreationCard({
    required int index,
    required String title,
    required String subtitle,
    required IconData icon,
    required Gradient iconBackgroundGradient,
    required List<String> features,
    required VoidCallback onTap,
    final bool requiresOrganizer = false,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();

        // 如果需要组织者权限，先检查
        if (requiresOrganizer) {
          // TODO: 检查用户是否有组织者权限
          _showOrganizerRequiredDialog();
          return;
        }

        onTap();
      },
      child: Container(
        padding: EdgeInsets.all(SpacingTokens.md),
        decoration: BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: BorderRadius.circular(ShapeTokens.radiusLg),
          boxShadow: [
            BoxShadow(
              color: ColorTokens.shadow.withValues(alpha: 0.04),
              blurRadius: 6.0,
              offset: Offset(0, 2),
            ),
            BoxShadow(
              color: ColorTokens.shadow.withValues(alpha: 0.02),
              blurRadius: 2.0,
              offset: Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // 图标容器
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    gradient: iconBackgroundGradient,
                    borderRadius:
                        BorderRadius.circular(ShapeTokens.radiusLarge),
                    boxShadow: [
                      BoxShadow(
                        color: iconBackgroundGradient.colors.first
                            .withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: ColorTokens.onPrimary,
                    size: 28,
                  ),
                ),
                SizedBox(width: SpacingTokens.md),
                // 标题和副标题
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            title,
                            style: TypographyTokens.headlineSmall.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                          ),
                          if (requiresOrganizer) ...[
                            SizedBox(width: SpacingTokens.xs),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: SpacingTokens.xs,
                                vertical: SpacingTokens.space1,
                              ),
                              decoration: BoxDecoration(
                                color: ColorTokens.primaryContainer,
                                borderRadius: BorderRadius.circular(
                                    ShapeTokens.radiusSmall),
                              ),
                              child: const Text(
                                '组织者',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: ColorTokens.primary,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      SizedBox(height: SpacingTokens.xs),
                      Text(
                        subtitle,
                        style: TextStyle(
                          fontSize: TypographyTokens.bodyMedium.fontSize,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          height: 1.3,
                        ),
                      ),
                    ],
                  ),
                ),
                // 箭头
               const Icon(
                  Icons.arrow_forward_ios,
                  color: ColorTokens.outlineVariant,
                  size: 16,
                ),
              ],
            ),
            SizedBox(height: SpacingTokens.md),
            // 功能点
            Wrap(
              spacing: SpacingTokens.sm,
              runSpacing: SpacingTokens.xs,
              children: features.map((feature) {
                return Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.check_circle,
                      size: 16,
                      color: iconBackgroundGradient.colors.first,
                    ),
                    SizedBox(width: SpacingTokens.xs),
                    Text(
                      feature,
                      style: TextStyle(
                        fontSize: TypographyTokens.bodySmall.fontSize,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                );
              }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToQuickCreate() {
    // 跳转到模版选择页面（个人计划）
    context.navigateTo(AppRoutes.templateSelection);
  }

  void _navigateToFriendOuting() {
    // 跳转到好友组局页面
    context.navigateTo('/fishing-plans/friend-outing');
  }

  void _navigateToPublishEvent() {
    // 跳转到发布活动页面
    context.navigateTo('/fishing-plans/publish-event');
  }

  void _showOrganizerRequiredDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ShapeTokens.radiusLarge),
        ),
        title: const Text('需要组织者权限'),
        content: const Text(
          '组织公开活动需要组织者权限。\n请联系客服申请成为组织者。',
          style: TextStyle(height: 1.5),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('我知道了'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: 跳转到客服页面
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              foregroundColor: ColorTokens.surface,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(ShapeTokens.radiusMedium),
              ),
            ),
            child: const Text('联系客服'),
          ),
        ],
      ),
    );
  }
}
