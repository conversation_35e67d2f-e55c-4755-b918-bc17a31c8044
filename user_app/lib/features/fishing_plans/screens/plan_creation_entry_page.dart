import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';

/// 钓鱼计划创建入口页面
/// 提供多种创建方式：快速创建、标准创建、复制历史
class PlanCreationEntryPage extends StatefulWidget {
  const PlanCreationEntryPage({super.key});

  @override
  State<PlanCreationEntryPage> createState() => _PlanCreationEntryPageState();
}

class _PlanCreationEntryPageState extends State<PlanCreationEntryPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authViewModel, child) {
        // 检查用户是否已登录
        if (!authViewModel.isUserLoggedIn()) {
          return _buildLoginRequired();
        }

        return UnifiedPageScaffold(
          title: '创建钓鱼计划',
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: Padding(
                padding: SpacingTokens.paddingLg,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 欢迎文本
                    const SizedBox(height: SpacingTokens.space5),
                    const Text(
                      '选择创建方式',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: ColorTokens.onSurface,
                      ),
                    ),
                    const SizedBox(height: SpacingTokens.space2),
                    const Text(
                      '根据你的需求选择最适合的创建方式',
                      style: TextStyle(
                        fontSize: 16,
                        color: ColorTokens.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(height: 40),

                    // 创建选项
                    Expanded(
                      child: Column(
                        children: [
                          _buildCreationOption(
                            title: '快速创建',
                            subtitle: '简单快捷，一页搞定所有信息',
                            icon: Icons.flash_on,
                            color: ColorTokens.primary,
                            delay: 0,
                            onTap: () => _navigateToQuickCreate(),
                          ),
                          const SizedBox(height: SpacingTokens.space5),

                          _buildCreationOption(
                            title: '标准创建',
                            subtitle: '分步向导，详细设置每个环节',
                            icon: Icons.format_list_numbered,
                            color: ColorTokens.success,
                            delay: 200,
                            onTap: () => _navigateToStandardCreate(),
                          ),
                          const SizedBox(height: SpacingTokens.space5),

                          _buildCreationOption(
                            title: '复制历史',
                            subtitle: '基于历史计划快速创建新计划',
                            icon: Icons.copy,
                            color: ColorTokens.secondary,
                            delay: 400,
                            onTap: () => _navigateToCopyHistory(),
                          ),

                          const Spacer(),

                          // 底部提示
                          Container(
                            padding: SpacingTokens.paddingMd,
                            decoration: BoxDecoration(
                              color: ColorTokens.primary.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color:
                                    ColorTokens.primary.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                const Icon(
                                  Icons.lightbulb_outline,
                                  color: ColorTokens.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    '首次使用建议选择"快速创建"，熟悉后可尝试"标准创建"获得更丰富的功能。',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[700],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: SpacingTokens.space5),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildLoginRequired() {
    return UnifiedPageScaffold(
      title: '创建钓鱼计划',
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.login,
              size: 80,
              color: Colors.grey[400],
            ),
            const SizedBox(height: SpacingTokens.space5),
            Text(
              '请先登录后创建钓鱼计划',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: 30),
            ElevatedButton(
              onPressed: () => context.navigateTo('/auth/login'),
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorTokens.primary,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(25),
                ),
              ),
              child: const Text('去登录'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreationOption({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required int delay,
    required VoidCallback onTap,
  }) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 600 + delay),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutBack,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              onTap();
            },
            child: Container(
              height: 100,
              padding: SpacingTokens.paddingLg,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: color.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [color, color.withValues(alpha: 0.8)],
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Icon(
                      icon,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: ColorTokens.onSurface,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey[400],
                    size: 16,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _navigateToQuickCreate() {
    // 跳转到快速创建页面（现有的创建页面）
    Navigator.pushNamed(context, '/fishing-plans/create-quick');
  }

  void _navigateToStandardCreate() {
    // 跳转到标准创建页面（分步向导）
    Navigator.pushNamed(context, '/fishing-plans/create-standard');
  }

  void _navigateToCopyHistory() {
    // 跳转到复制历史页面
    Navigator.pushNamed(context, '/fishing-plans/copy-history');
  }
}
