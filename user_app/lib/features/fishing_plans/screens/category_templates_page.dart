import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/models/fishing_plan/plan_template.dart';

import '../widgets/template_card_widget.dart';

/// 分类模版列表页面
class CategoryTemplatesPage extends StatefulWidget {
  final PlanTemplateCategory category;
  final List<PlanTemplate> templates;

  const CategoryTemplatesPage({
    super.key,
    required this.category,
    required this.templates,
  });

  @override
  State<CategoryTemplatesPage> createState() => _CategoryTemplatesPageState();
}

class _CategoryTemplatesPageState extends State<CategoryTemplatesPage> {
  late List<PlanTemplate> _displayTemplates;
  String _sortBy = 'name';

  @override
  void initState() {
    super.initState();
    _displayTemplates = List.from(widget.templates);
    _sortTemplates();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.category.displayName),
        backgroundColor: _getCategoryColor(),
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _showSortDialog,
            icon: const Icon(Icons.sort),
          ),
        ],
      ),
      body: Column(
        children: [
          // 分类信息卡片
          _buildCategoryHeader(),
          // 模版列表
          Expanded(child: _buildTemplatesList()),
        ],
      ),
    );
  }

  Widget _buildCategoryHeader() {
    return Container(
      width: double.infinity,
      margin: SpacingTokens.paddingMd,
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _getCategoryColor(),
            _getCategoryColor().withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: Icon(
                  _getIconData(widget.category.categoryIcon),
                  color: Colors.white,
                  size: 28,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.category.displayName,
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${widget.category.templateCount}个模版',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (widget.category.description != null &&
              widget.category.description!.isNotEmpty) ...[
            const SizedBox(height: 12),
            Text(
              widget.category.description!,
              style: TextStyle(
                fontSize: 14,
                color: Colors.white.withValues(alpha: 0.9),
                height: 1.4,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTemplatesList() {
    if (_displayTemplates.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: SpacingTokens.space4),
            Text(
              '该分类下暂无模版',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: SpacingTokens.space2),
            Text(
              '请选择其他分类或稍后再试',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _displayTemplates.length,
      itemBuilder: (context, index) {
        final template = _displayTemplates[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: TemplateCardWidget(
            template: template,
            showUsageCount: true,
            onTap: () => _navigateToCreatePlan(context, template),
            onFavoriteToggle: () => _recordTemplateUsage(template),
          ),
        );
      },
    );
  }

  void _navigateToCreatePlan(BuildContext context, PlanTemplate template) {
    context.navigateTo(AppRoutes.createPlanFromTemplate, extra: CreateFromTemplateRouteData(
      template: template,
    ));
  }

  void _recordTemplateUsage(PlanTemplate template) {
    // TODO: 记录模版使用
    debugPrint('记录模版使用: ${template.name}');
  }

  void _showSortDialog() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: SpacingTokens.paddingLg,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '排序方式',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: SpacingTokens.space4),
              ListTile(
                leading: const Icon(Icons.sort_by_alpha),
                title: const Text('按名称排序'),
                trailing: _sortBy == 'name'
                    ? const Icon(Icons.check, color: ColorTokens.primary)
                    : null,
                onTap: () {
                  setState(() {
                    _sortBy = 'name';
                    _sortTemplates();
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.trending_up),
                title: const Text('按使用次数排序'),
                trailing: _sortBy == 'usage_count'
                    ? const Icon(Icons.check, color: ColorTokens.primary)
                    : null,
                onTap: () {
                  setState(() {
                    _sortBy = 'usage_count';
                    _sortTemplates();
                  });
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.access_time),
                title: const Text('按创建时间排序'),
                trailing: _sortBy == 'created_at'
                    ? const Icon(Icons.check, color: ColorTokens.primary)
                    : null,
                onTap: () {
                  setState(() {
                    _sortBy = 'created_at';
                    _sortTemplates();
                  });
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _sortTemplates() {
    switch (_sortBy) {
      case 'name':
        _displayTemplates.sort((a, b) => a.name.compareTo(b.name));
        break;
      case 'usage_count':
        _displayTemplates
            .sort((a, b) => (b.usageCount ?? 0).compareTo(a.usageCount ?? 0));
        break;
      case 'created_at':
        _displayTemplates.sort((a, b) {
          final aDate = a.createdAt != null
              ? DateTime.tryParse(a.createdAt!) ?? DateTime.now()
              : DateTime.now();
          final bDate = b.createdAt != null
              ? DateTime.tryParse(b.createdAt!) ?? DateTime.now()
              : DateTime.now();
          return bDate.compareTo(aDate);
        });
        break;
    }
  }

  Color _getCategoryColor() {
    final colorStr = widget.category.categoryColor.replaceAll('#', '');
    try {
      // 检查颜色字符串是否有效
      if (colorStr.length == 6 &&
          RegExp(r'^[0-9A-Fa-f]{6}$').hasMatch(colorStr)) {
        return Color(int.parse('FF$colorStr', radix: 16));
      }
    } catch (e) {
      debugPrint('Error parsing category color: $e');
    }

    // 根据分类代码返回默认颜色
    switch (widget.category.code) {
      case 'leisure':
        return ColorTokens.primary;
      case 'seasonal':
        return Color(0xFF3F51B5);
      case 'challenge':
        return Color(0xFFF44336);
      case 'night':
        return Color(0xFF9C27B0);
      case 'lure':
        return Color(0xFF2196F3);
      case 'competition':
        return Color(0xFFFF9800);
      case 'newbie':
        return Color(0xFF4CAF50);
      case 'specialty':
        return Color(0xFFE91E63);
      case 'sea_fishing':
        return Color(0xFF00BCD4);
      case 'social':
        return Color(0xFF607D8B);

      case 'family':
        return Color(0xFFFFC107);
      default:
        return Color(0xFF9E9E9E);
    }
  }

  /// 根据图标名称获取对应的IconData
  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'school':
        return Icons.school;
      case 'deck':
        return Icons.deck;
      case 'trending_up':
        return Icons.trending_up;
      case 'star':
        return Icons.star;
      case 'sailing':
        return Icons.sailing;
      case 'psychology':
        return Icons.psychology;
      case 'groups':
        return Icons.groups;
      case 'family_restroom':
        return Icons.family_restroom;
      case 'ac_unit':
        return Icons.ac_unit;
      case 'emoji_events':
        return Icons.emoji_events;
      default:
        return Icons.category; // 默认图标
    }
  }
}
