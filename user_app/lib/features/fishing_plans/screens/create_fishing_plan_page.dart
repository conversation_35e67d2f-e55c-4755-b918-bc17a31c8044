import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/modern_map_modal.dart';
import 'package:user_app/features/fishing_spots/screens/map_location_picker_page.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/models/fishing_plan/activity_type.dart';
import 'package:user_app/features/fishing_plans/widgets/activity_type_selector_widget.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class CreateFishingPlanPage extends StatefulWidget {
  final FishingPlan? copyFromPlan; // 用于复制历史计划时预填充数据
  
 const CreateFishingPlanPage({
    super.key,
    this.copyFromPlan,
  });

  @override
  State<CreateFishingPlanPage> createState() => _CreateFishingPlanPageState();
}

class _CreateFishingPlanPageState extends State<CreateFishingPlanPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  
  // Form controllers
  final _titleController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _maxParticipantsController = TextEditingController(text: '10');
  final _weatherController = TextEditingController();
  final _temperatureController = TextEditingController();
  final _contactInfoController = TextEditingController();
  final _timeRangeController = TextEditingController();
  
  // Form state
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _selectedTime = TimeOfDay.now();
  bool _isPublic = true;
  bool _isSubmitting = false;
  
  // Activity type state
  ActivityTypeInfo? _selectedActivityType;
  
  // Location data
  double? _selectedLatitude;
  double? _selectedLongitude;
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedCounty = '';
  
  // Animation
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
    
    _animationController.forward();
    
    // 如果是复制历史计划，预填充数据
    if (widget.copyFromPlan != null) {
      _prefillFromHistoryPlan(widget.copyFromPlan!);
    }
  }
  
  @override
  void dispose() {
    _titleController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _maxParticipantsController.dispose();
    _weatherController.dispose();
    _temperatureController.dispose();
    _contactInfoController.dispose();
    _timeRangeController.dispose();
    _animationController.dispose();
    super.dispose();
  }
  
  Future<void> _selectDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              surface: Theme.of(context).colorScheme.surface,
              onSurface: ColorTokens.onSurface,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (pickedDate != null) {
      setState(() {
        _selectedDate = pickedDate;
      });
    }
  }
  
  Future<void> _selectTime() async {
    final pickedTime = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: Theme.of(context).colorScheme.primary,
              onPrimary: Theme.of(context).colorScheme.onPrimary,
              surface: Theme.of(context).colorScheme.surface,
              onSurface: ColorTokens.onSurface,
            ),
          ),
          child: child!,
        );
      },
    );
    
    if (pickedTime != null) {
      setState(() {
        _selectedTime = pickedTime;
      });
    }
  }

  /// 打开地图选择器
  Future<void> _openLocationPicker() async {
    try {
      final result = await showModalBottomSheet<LocationResult>(
        context: context,
        isScrollControlled: true,
        backgroundColor: ColorTokens.transparent,
        builder: (context) => ModernMapModal(
          initialLatitude: _selectedLatitude ?? 0,
          initialLongitude: _selectedLongitude ?? 0,
          initialFormattedAddress: _locationController.text,
        ),
      );

      if (result != null) {
        setState(() {
          _locationController.text = result.formattedAddress;
          _selectedLatitude = result.latitude;
          _selectedLongitude = result.longitude;
          _selectedProvince = result.province;
          _selectedCity = result.city;
          _selectedCounty = result.county;
        });

        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已选择位置：${result.formattedAddress}'),
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (e) {
      debugPrint('地图选择错误: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Text('地图选择失败，请重试'),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }
  
  DateTime get _combinedDateTime {
    return DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );
  }
  
  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    
    setState(() {
      _isSubmitting = true;
    });
    
    try {
      final viewModel = context.read<FishingPlanViewModel>();
      
      final dto = FishingPlanCreateDTO(
        title: _titleController.text.trim(),
        location: _locationController.text.trim(),
        planDate: _combinedDateTime,
        timeRange: _timeRangeController.text.trim().isEmpty ? null : _timeRangeController.text.trim(),
        maxParticipants: int.parse(_maxParticipantsController.text),
        weather: _weatherController.text.trim().isEmpty ? null : _weatherController.text.trim(),
        temperature: _temperatureController.text.trim().isEmpty ? null : _temperatureController.text.trim(),
        description: _descriptionController.text.trim().isEmpty ? '暂无描述' : _descriptionController.text.trim(),
        contactInfo: _contactInfoController.text.trim().isEmpty ? null : _contactInfoController.text.trim(),
        isPublic: _isPublic,
        planType: _selectedActivityType?.orgType.name ?? 'personal',
        activityType: _selectedActivityType?.fishingType.name,
        latitude: _selectedLatitude,
        longitude: _selectedLongitude,
        province: _selectedProvince.isEmpty ? null : _selectedProvince,
        city: _selectedCity.isEmpty ? null : _selectedCity,
        county: _selectedCounty.isEmpty ? null : _selectedCounty,
      );
      
      await viewModel.createPlan(dto);
      
      if (mounted) {
        HapticFeedback.mediumImpact();
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Text('计划创建成功'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('创建失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
  
  @override
  Widget build(BuildContext context) {
    return Consumer<AuthViewModel>(
      builder: (context, authViewModel, child) {
        // 检查用户是否已登录
        if (!authViewModel.isUserLoggedIn()) {
          return UnifiedPageScaffold(
            title: '创建钓鱼计划',
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.login,
                    size: 80,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                     const SizedBox(height: SpacingTokens.space5),
                  Text(
                    '请先登录后创建钓鱼计划',
                    style: TextStyle(
                      fontSize: 18,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                     const SizedBox(height: 30),
                  ElevatedButton(
                    onPressed: () {
                      context.navigateTo('/auth/login');
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: const Text('去登录'),
                  ),
                ],
              ),
            ),
          );
        }

        // 用户已登录，显示正常的创建界面
        return UnifiedPageScaffold(
          title: '创建钓鱼计划',
          body: FadeTransition(
            opacity: _fadeAnimation,
            child: Form(
              key: _formKey,
              child: ListView(
                padding: SpacingTokens.paddingMd,
                children: [
              // 模板选择卡片
              _buildSectionCard(context,
                title: '快速创建',
                icon: Icons.dashboard,
                children: [
                  _buildTemplateSelector(),
                ],
              ),
               const SizedBox(height: SpacingTokens.space4),
              
              // 基本信息卡片
              _buildSectionCard(context,
                title: '基本信息',
                icon: Icons.info,
                children: [
                  _buildTextField(context,
                    controller: _titleController,
                    label: '计划标题',
                    hint: '例如：周末野钓活动',
                    icon: Icons.title,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return '请输入计划标题';
                      }
                      if (value.trim().length < 2) {
                        return '标题至少需要2个字符';
                      }
                      return null;
                    },
                  ),
                    const SizedBox(height: SpacingTokens.space4),
                  _buildLocationSelector(context),
                    const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(context,
                    controller: _descriptionController,
                    label: '计划描述',
                    hint: '描述一下这次钓鱼计划的详情...',
                    icon: Icons.description,
                    maxLines: 4,
                  ),
                ],
              ),
               const SizedBox(height: SpacingTokens.space4),
              
              // 活动类型选择卡片
              _buildActivityTypeCard(),
               const SizedBox(height: SpacingTokens.space4),
              
              // 时间设置卡片
              _buildSectionCard(context,
                title: '时间设置',
                icon: Icons.schedule,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _buildDateTimeButton(
                          label: '日期',
                          value: DateFormat('yyyy年MM月dd日').format(_selectedDate),
                          icon: Icons.calendar_today,
                          onTap: _selectDate,
                        ),
                      ),
                        const SizedBox(width: 12),
                      Expanded(
                        child: _buildDateTimeButton(
                          label: '时间',
                          value: _selectedTime.format(context),
                          icon: Icons.access_time,
                          onTap: _selectTime,
                        ),
                      ),
                    ],
                  ),
                    const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(context,
                    controller: _timeRangeController,
                    label: '时间范围',
                    hint: '例如：早上6点-下午3点',
                    icon: Icons.timelapse,
                  ),
                ],
              ),
               const SizedBox(height: SpacingTokens.space4),
              
              // 参与设置卡片
              _buildSectionCard(context,
                title: '参与设置',
                icon: Icons.group,
                children: [
                  _buildTextField(context,
                    controller: _maxParticipantsController,
                    label: '最大参与人数',
                    hint: '包含自己',
                    icon: Icons.people,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                    ],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入最大参与人数';
                      }
                      final num = int.tryParse(value);
                      if (num == null || num < 1) {
                        return '参与人数至少为1人';
                      }
                      if (num > 100) {
                        return '参与人数不能超过100人';
                      }
                      return null;
                    },
                  ),
                    const SizedBox(height: SpacingTokens.space4),
                  _buildSwitchTile(
                    title: '公开计划',
                    subtitle: '其他用户可以看到并加入此计划',
                    value: _isPublic,
                    onChanged: (value) {
                      setState(() {
                        _isPublic = value;
                      });
                    },
                  ),
                ],
              ),
               const SizedBox(height: SpacingTokens.space4),
              
              // 其他信息卡片
              _buildSectionCard(context,
                title: '其他信息',
                icon: Icons.more_horiz,
                children: [
                  _buildTextField(context,
                    controller: _weatherController,
                    label: '天气预报',
                    hint: '例如：晴天',
                    icon: Icons.wb_sunny,
                  ),
                    const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(context,
                    controller: _temperatureController,
                    label: '温度',
                    hint: '例如：20-25°C',
                    icon: Icons.thermostat,
                  ),
                    const SizedBox(height: SpacingTokens.space4),
                  _buildTextField(context,
                    controller: _contactInfoController,
                    label: '联系方式',
                    hint: '例如：微信号、手机号',
                    icon: Icons.phone,
                  ),
                ],
              ),
               const SizedBox(height: 32),
              
              // 提交按钮
              Container(
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.error],
                  ),
                  borderRadius: BorderRadius.circular(28),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: MaterialButton(
                  onPressed: _isSubmitting ? null : _submitForm,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(28),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text(
                          '创建计划',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                ),
              ),
               const SizedBox(height: 32),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildSectionCard(BuildContext context, {
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.error],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, size: 20, color: Colors.white),
              ),
               const SizedBox(width: 12),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space5),
          ...children,
        ],
      ),
    );
  }
  
  Widget _buildTextField(BuildContext context, {
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    final int maxLines = 1,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: Icon(icon, color: Theme.of(context).colorScheme.primary),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: ColorTokens.divider),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: ColorTokens.divider),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(16),
          borderSide: const BorderSide(color: Colors.red),
        ),
        filled: true,
        fillColor: ColorTokens.surface,
      ),
    );
  }
  
  Widget _buildDateTimeButton({
    required String label,
    required String value,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: SpacingTokens.paddingMd,
        decoration: BoxDecoration(
          border: Border.all(color: ColorTokens.divider),
          borderRadius: BorderRadius.circular(16),
          color: ColorTokens.surface,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, size: 16, color: ColorTokens.onSurfaceVariant),
                 const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
             const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建位置选择器（文本输入 + 地图按钮）
  Widget _buildLocationSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _locationController,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入或选择钓点位置';
                  }
                  return null;
                },
                decoration: InputDecoration(
                  labelText: '钓点位置',
                  hintText: '输入位置或点击地图选择',
                  prefixIcon: Icon(Icons.location_on, color: Theme.of(context).colorScheme.primary),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: ColorTokens.divider),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: ColorTokens.divider),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: BorderSide(color: Theme.of(context).colorScheme.primary, width: 2),
                  ),
                ),
              ),
            ),
             const SizedBox(width: 12),
            // 地图选择按钮
            GestureDetector(
              onTap: _openLocationPicker,
              child: Container(
                height: 56,
                width: 56,
                decoration: BoxDecoration(
                  color: ColorTokens.primary,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.map_rounded,
                  color: Colors.white,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
        // 显示选择的位置详情
        if (_selectedLatitude != null && _selectedLongitude != null)
          Padding(
            padding: const EdgeInsets.only(top: 8),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.location_on,
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                    const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '已选择精确位置',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.primary,
                          ),
                        ),
                        if (_selectedProvince.isNotEmpty || _selectedCity.isNotEmpty)
                          Text(
                            '$_selectedProvince $_selectedCity $_selectedCounty',
                            style: TextStyle(
                              fontSize: 11,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Text(
                    '${_selectedLatitude!.toStringAsFixed(4)}, ${_selectedLongitude!.toStringAsFixed(4)}',
                    style: TextStyle(
                      fontSize: 10,
                      fontFamily: 'monospace',
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }
  
  Widget _buildActivityTypeCard() {
    return _buildSectionCard(context,
      title: '活动类型',
      icon: Icons.category,
      children: [
        GestureDetector(
          onTap: () {
            ActivityTypeBottomSheet.show(
              context,
              selectedType: _selectedActivityType,
              onTypeSelected: (type) {
                setState(() {
                  _selectedActivityType = type;
                  // 根据活动类型自动调整参与人数
                  _maxParticipantsController.text = 
                      type.orgType.suggestedMaxParticipants.toString();
                  // 根据组织类型设置公开性
                  _isPublic = type.orgType == ActivityOrgType.public;
                });
              },
            );
          },
          child: Container(
            padding: SpacingTokens.paddingMd,
            decoration: BoxDecoration(
              color: _selectedActivityType != null
                  ? _selectedActivityType!.primaryColor.withValues(alpha: 0.05)
                  : Colors.grey[50],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _selectedActivityType != null
                    ? _selectedActivityType!.primaryColor.withValues(alpha: 0.3)
                    : Colors.grey[300]!,
              ),
            ),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: _selectedActivityType != null
                        ? _selectedActivityType!.primaryColor.withValues(alpha: 0.1)
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    _selectedActivityType?.fishingType.icon ?? Icons.help_outline,
                    color: _selectedActivityType?.primaryColor ?? Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ),
                  const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedActivityType?.combinedDisplayName ?? '选择活动类型',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: _selectedActivityType != null
                              ? Colors.grey.shade800
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      if (_selectedActivityType != null) ...[
                          const SizedBox(height: 4),
                        Text(
                          _selectedActivityType!.fishingType.description,
                          style: TextStyle(
                            fontSize: 12,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ] else ...[
                          const SizedBox(height: 4),
                        Text(
                          '选择适合的活动组织方式和钓鱼类型',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey.shade500,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Icon(
                  Icons.keyboard_arrow_right,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        border: Border.all(color: ColorTokens.divider),
        borderRadius: BorderRadius.circular(16),
        color: ColorTokens.surface,
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                  const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 14,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }
  
  /// 构建模板选择器
  Widget _buildTemplateSelector() {
    return Container(
      padding: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.error.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                '使用模板快速创建',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
           const SizedBox(height: 12),
          Text(
            '选择预设模板，快速填充计划信息',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: SpacingTokens.space4),
          Row(
            children: [
              Expanded(
                child: _buildTemplateOption(context,
                  '周末野钓',
                  Icons.landscape,
                  () => _applyTemplate('weekend'),
                ),
              ),
               const SizedBox(width: 12),
              Expanded(
                child: _buildTemplateOption(context,
                  '黑坑台钓',
                  Icons.pool,
                  () => _applyTemplate('blackpit'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: _buildTemplateOption(context,
                  '路亚专场',
                  Icons.waves,
                  () => _applyTemplate('lure'),
                ),
              ),
               const SizedBox(width: 12),
              Expanded(
                child: _buildTemplateOption(context,
                  '夜钓专场',
                  Icons.nightlight_round,
                  () => _applyTemplate('night'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
  
  /// 构建模板选项
  Widget _buildTemplateOption(BuildContext context, String title, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey.shade300),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 18,
              color: Theme.of(context).colorScheme.primary,
            ),
             const SizedBox(width: 6),
            Text(
              title,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  /// 应用模板
  void _applyTemplate(String templateType) {
    HapticFeedback.lightImpact();
    
    // 根据模板类型设置默认值
    switch (templateType) {
      case 'weekend':
        _titleController.text = '${_formatDate(_selectedDate)} 周末野钓活动';
        _descriptionController.text = '周末放松钓鱼，享受自然风光，适合钓友交流。';
        _maxParticipantsController.text = '8';
        _timeRangeController.text = '早上6:00-下午16:00';
        _contactInfoController.text = '微信群联系';
        break;
      case 'blackpit':
        _titleController.text = '${_formatDate(_selectedDate)} 黑坑台钓';
        _descriptionController.text = '黑坑台钓，鱼情稳定，适合新手练手。鱼塘定期放鱼，鱼种丰富。';
        _maxParticipantsController.text = '12';
        _timeRangeController.text = '早上5:00-下午17:00';
        _contactInfoController.text = '提前预约';
        break;
      case 'lure':
        _titleController.text = '${_formatDate(_selectedDate)} 路亚钓鱼专场';
        _descriptionController.text = '路亚专场，主打掠食性鱼类，体验刺激的搏鱼快感。';
        _maxParticipantsController.text = '6';
        _timeRangeController.text = '早上7:00-下午15:00';
        _contactInfoController.text = '路亚交流群';
        break;
      case 'night':
        _titleController.text = '${_formatDate(_selectedDate)} 夜钓专场';
        _descriptionController.text = '夜钓专场，享受夜晚钓鱼的宁静与刺激，鱼口更好。';
        _maxParticipantsController.text = '10';
        _timeRangeController.text = '晚上18:00-次日6:00';
        _contactInfoController.text = '夜钓安全第一';
        break;
    }
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已应用${_getTemplateTitle(templateType)}模板'),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  String _getTemplateTitle(String templateType) {
    switch (templateType) {
      case 'weekend': return '周末野钓';
      case 'blackpit': return '黑坑台钓';
      case 'lure': return '路亚专场';
      case 'night': return '夜钓专场';
      default: return '通用';
    }
  }
  
  String _formatDate(DateTime date) {
    return '${date.month}月${date.day}日';
  }
  
  /// 从历史计划预填充数据
  void _prefillFromHistoryPlan(FishingPlan plan) {
    setState(() {
      // 复制标题，但添加"复制"标识
      _titleController.text = '${plan.title} (复制)';
      _locationController.text = plan.location;
      _descriptionController.text = plan.description != '暂无描述' ? plan.description : '';
      _maxParticipantsController.text = plan.maxParticipants.toString();
      
      // 设置时间为明天（不复制原时间）
      _selectedDate = DateTime.now().add(const Duration(days: 1));
      
      // 如果有其他字段，也可以在这里预填充
      if (plan.weather != null && plan.weather!.isNotEmpty) {
        _weatherController.text = plan.weather!;
      }
      if (plan.temperature != null && plan.temperature!.isNotEmpty) {
        _temperatureController.text = plan.temperature!;
      }
      if (plan.contactInfo != null && plan.contactInfo!.isNotEmpty) {
        _contactInfoController.text = plan.contactInfo!;
      }
      if (plan.timeRange != null && plan.timeRange!.isNotEmpty) {
        _timeRangeController.text = plan.timeRange!;
      }
      
      // 复制地理位置信息（如果有）
      // TODO: 这些字段需要在FishingPlan模型中添加
      // _selectedLatitude = plan.latitude;
      // _selectedLongitude = plan.longitude;
      // _selectedProvince = plan.province ?? '';
      // _selectedCity = plan.city ?? '';
      // _selectedCounty = plan.district ?? '';
      
      _isPublic = plan.isPublic;
    });
    
    // 显示复制成功提示
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已从历史计划"${plan.title}"复制信息'),
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }
}