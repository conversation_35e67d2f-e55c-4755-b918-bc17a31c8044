import 'dart:math' show sin, pi;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/features/fishing_plans/widgets/quick_create_fab.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/utils/share_util.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class FishingPlansPage extends StatefulWidget {
 const FishingPlansPage({super.key});

  @override
  State<FishingPlansPage> createState() => _FishingPlansPageState();
}

class _FishingPlansPageState extends State<FishingPlansPage>
    with TickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _rotateController;
  late AnimationController _waveController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _rotateAnimation;
  late Animation<double> _waveAnimation;

  // 列表项动画
  final List<AnimationController> _itemControllers = [];

  // 搜索和筛选
  final TextEditingController _searchController = TextEditingController();
  final bool _isSearching = false;
  String _selectedFilter = 'all'; // all, upcoming, completed, cancelled
  
  // 区域选择
  String? _selectedProvince;
  String? _selectedCity;

  // ViewModel
  late FishingPlanViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    // 根据登录状态决定标签数量
    final authViewModel = context.read<AuthViewModel>();
    final tabCount = authViewModel.isUserLoggedIn() ? 3 : 1;
    _tabController = TabController(length: tabCount, vsync: this);

    // 初始化动画
    _fadeController = AnimationController(
      duration: MotionTokens.pageTransitionDuration,
      vsync: this,
    );
    _slideController = AnimationController(
      duration: MotionTokens.cardEnterDuration,
      vsync: this,
    );
    _rotateController = AnimationController(
      duration: MotionTokens.loadingDuration,
      vsync: this,
    );
    _waveController = AnimationController(
      duration: Duration(milliseconds: MotionTokens.loadingDuration.inMilliseconds * 2),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: MotionTokens.pageTransitionCurve,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: MotionTokens.cardEnterCurve,
    ));
    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 0.05,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: MotionTokens.loadingCurve,
    ));
    _waveAnimation = CurvedAnimation(
      parent: _waveController,
      curve: MotionTokens.loadingCurve,
    );

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _rotateController.forward();

    // 获取ViewModel
    _viewModel = context.read<FishingPlanViewModel>();
    
    // 加载数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAllPlans();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _rotateController.dispose();
    _waveController.dispose();
    _searchController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadAllPlans() {
    // 使用区域参数加载计划
    _viewModel.loadUpcomingPlansWithLocation(
      province: _selectedProvince,
      city: _selectedCity,
      refresh: true
    );
    final authViewModel = context.read<AuthViewModel>();
    if (authViewModel.isUserLoggedIn()) {
      _viewModel.loadMyPlans(refresh: true);
      _viewModel.loadCompletedPlans(refresh: true);
    }
  }

  List<Tab> _buildTabs() {
    final authViewModel = context.read<AuthViewModel>();
    if (authViewModel.isUserLoggedIn()) {
      return const [
        Tab(text: '即将开始'),
        Tab(text: '我的计划'),
        Tab(text: '已完成'),
      ];
    } else {
      return const [
        Tab(text: '发现计划'),
      ];
    }
  }

  List<Widget> _buildTabViews() {
    final authViewModel = context.read<AuthViewModel>();
    if (authViewModel.isUserLoggedIn()) {
      return [
        _buildPlansList('upcoming'),
        _buildPlansList('my'),
        _buildPlansList('completed'),
      ];
    } else {
      return [
        _buildPlansList('upcoming'),
      ];
    }
  }

  AnimationController _createItemController(int index) {
    // 确保列表有足够的控制器
    while (index >= _itemControllers.length) {
      final controllerIndex = _itemControllers.length;
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (controllerIndex * 100)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  Future<void> _onRefresh() async {
    HapticFeedback.mediumImpact();
    _loadAllPlans();
    // Wait for all loads to complete
    await Future.wait([
      _viewModel.loadUpcomingPlans(refresh: true),
      _viewModel.loadMyPlans(refresh: true),
      _viewModel.loadCompletedPlans(refresh: true),
    ]);
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '钓鱼计划',
      hasTabBar: true,
      tabController: _tabController,
      tabs: _buildTabs(),
      onRefresh: _onRefresh,
      enableSearch: true,
      onSearchChanged: (value) {
        _viewModel.setSearchQuery(value);
      },
      showAnimations: true,
      gradientColors: [
        Theme.of(context).colorScheme.primary,
        Theme.of(context).colorScheme.secondary,
      ],
      backgroundDecoration: _buildBackgroundDecoration(),
      expandedHeight: 200,
      actions: [
        IconButton(
          icon: Container(
            padding: EdgeInsets.all(SpacingTokens.space2),
            decoration: ShapeTokens.buttonDecoration(
              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
              borderRadius: ShapeTokens.borderRadiusMd,
            ),
            child: Icon(Icons.location_on, color: Theme.of(context).colorScheme.onPrimary, size: 20),
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            _showLocationOptions();
          },
        ),
        IconButton(
          icon: Container(
            padding: EdgeInsets.all(SpacingTokens.space2),
            decoration: ShapeTokens.buttonDecoration(
              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
              borderRadius: ShapeTokens.borderRadiusMd,
            ),
            child: Icon(Icons.filter_list, color: Theme.of(context).colorScheme.onPrimary, size: 20),
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            _showFilterOptions();
          },
        ),
      ],
      body: TabBarView(
        controller: _tabController,
        children: _buildTabViews(),
      ),
      floatingActionButton: Consumer<AuthViewModel>(
        builder: (context, authViewModel, child) {
          return authViewModel.isUserLoggedIn()
              ? const QuickCreateFAB(
                  showLabels: true,
                  expanded: false,
                )
              : FloatingActionButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    _showLoginPrompt();
                  },
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  child: Icon(Icons.login, color: Theme.of(context).colorScheme.onPrimary),
                );
        },
      ),
    );
  }

  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 波浪装饰
        Positioned(
          bottom: -50,
          left: 0,
          right: 0,
          child: AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return CustomPaint(
                size: Size(MediaQuery.of(context).size.width, 100),
                painter: WavePainter(_waveAnimation.value),
              );
            },
          ),
        ),
        // 日历图标装饰
        Positioned(
          top: 100,
          right: 30,
          child: AnimatedBuilder(
            animation: _rotateAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotateAnimation.value * 0.5,
                child: Icon(
                  Icons.calendar_today,
                  size: 40,
                  color: ColorTokens.onPrimary.withValues(alpha: 0.15),
                ),
              );
            },
          ),
        ),
        // 脉冲圆圈
        Positioned(
          top: 50,
          left: -30,
          child: AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: 0.8 + (sin(_waveAnimation.value * 2 * pi) * 0.2),
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        ColorTokens.onPrimary.withValues(alpha: 0.1),
                        ColorTokens.onPrimary.withValues(alpha: 0.05),
                        ColorTokens.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPlansList(String type) {
    return Consumer<FishingPlanViewModel>(
      builder: (context, viewModel, child) {
        // 根据类型获取不同的数据和状态
        bool isLoading;
        String? error;
        List<FishingPlan> plans;
        
        switch (type) {
          case 'upcoming':
            isLoading = viewModel.isLoadingUpcoming;
            error = viewModel.errorUpcoming;
            plans = viewModel.upcomingPlans;
            break;
          case 'my':
            isLoading = viewModel.isLoadingMy;
            error = viewModel.errorMy;
            plans = viewModel.myPlans;
            break;
          case 'completed':
            isLoading = viewModel.isLoadingCompleted;
            error = viewModel.errorCompleted;
            plans = viewModel.completedPlans;
            break;
          default:
            isLoading = false;
            error = null;
            plans = [];
        }
        
        if (isLoading) {
          return const UnifiedLoadingState(message: '加载中...');
        }
        
        if (error != null) {
          return UnifiedEmptyState(
            icon: Icons.error_outline,
            title: '加载失败',
            subtitle: error,
            action: ElevatedButton(
              onPressed: () => _loadAllPlans(),
              child: const Text('重试'),
            ),
          );
        }
        
        // 调试信息
        debugPrint('🔍 Tab type: $type, Plans count: ${plans.length}, Selected filter: $_selectedFilter');
        for (int i = 0; i < plans.length && i < 3; i++) {
          final plan = plans[i];
          debugPrint('🔍 Plan $i: status=${plan.status}, isUpcoming=${plan.isUpcoming}, title=${plan.title}');
        }
        
        // 根据筛选条件过滤
        final filteredPlans = _filterPlans(plans);
        
        debugPrint('🔍 After filtering: ${filteredPlans.length} plans');
        
        if (filteredPlans.isEmpty) {
          return _buildEmptyState(type);
        }

        return ListView.builder(
          padding: EdgeInsets.only(bottom: SpacingTokens.xxl * 2),
          itemCount: filteredPlans.length,
          itemBuilder: (context, index) {
            return _buildPlanCard(filteredPlans[index], index);
          },
        );
      },
    );
  }
  
  List<FishingPlan> _filterPlans(List<FishingPlan> plans) {
    return plans.where((plan) {
      // 应用筛选器
      if (_selectedFilter != 'all') {
        if (_selectedFilter == 'upcoming' && !plan.isUpcoming) return false;
        if (_selectedFilter == 'completed' && !plan.isCompleted) return false;
        if (_selectedFilter == 'cancelled' && !plan.isCancelled) return false;
      }
      
      return true;
    }).toList();
  }

  Widget _buildPlanCard(FishingPlan plan, int index) {
    final controller = _createItemController(index);
    final isCompleted = plan.isCompleted;
    final isCancelled = plan.isCancelled;
    final isOwner = plan.isOwner;
    final colorScheme = Theme.of(context).colorScheme;

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (controller.value * 0.2),
          child: Opacity(
            opacity: controller.value,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: SpacingTokens.space6, vertical: SpacingTokens.space3),
              decoration: ShapeTokens.cardDecoration(
                color: isCompleted
                    ? colorScheme.surfaceVariant
                    : isCancelled
                        ? colorScheme.errorContainer
                        : colorScheme.surface,
                boxShadow: ElevationTokens.cardShadow(context),
              ),
              child: Material(
                color: ColorTokens.transparent,
                borderRadius: ShapeTokens.borderRadiusXl,
                child: InkWell(
                  onTap: () => _viewPlanDetail(plan),
                  borderRadius: ShapeTokens.borderRadiusXl,
                  child: Padding(
                    padding: EdgeInsets.all(SpacingTokens.space6),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 头部信息
                        Row(
                          children: [
                            // 日期标签
                            Container(
                              padding: EdgeInsets.all(SpacingTokens.space4),
                              decoration: ShapeTokens.buttonDecoration(
                                gradient: LinearGradient(
                                  colors: isCompleted
                                      ? [colorScheme.onSurfaceVariant, colorScheme.onSurfaceVariant]
                                      : [colorScheme.primary, colorScheme.secondary],
                                ),
                                borderRadius: ShapeTokens.borderRadiusMd,
                                boxShadow: ElevationTokens.shadow2(context),
                              ),
                              child: Column(
                                children: [
                                  Text(
                                    plan.planDate.day.toString(),
                                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                      color: colorScheme.onPrimary,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  Text(
                                    _getMonthName(plan.planDate.month),
                                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                      color: colorScheme.onPrimary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(width: SpacingTokens.space4),
                            // 标题和创建者
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    plan.title,
                                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: isCompleted
                                          ? colorScheme.onSurfaceVariant
                                          : colorScheme.onSurface,
                                      decoration: isCancelled
                                          ? TextDecoration.lineThrough
                                          : null,
                                    ),
                                  ),
                                  SizedBox(height: SpacingTokens.space1),
                                  Row(
                                    children: [
                                      Icon(Icons.person,
                                          size: 14,
                                          color: colorScheme.onSurfaceVariant),
                                      SizedBox(width: SpacingTokens.space1),
                                      Text(
                                        plan.ownerName,
                                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                          color: colorScheme.onSurfaceVariant,
                                        ),
                                      ),
                                      if (isOwner) ...[
                                        SizedBox(width: SpacingTokens.space2),
                                        Container(
                                          padding: SpacingTokens.paddingXs,
                                          decoration: ShapeTokens.containerDecoration(
                                            color: colorScheme.primaryContainer,
                                            borderRadius: ShapeTokens.borderRadiusSm,
                                          ),
                                          child: Text(
                                            '创建者',
                                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                              color: colorScheme.onPrimaryContainer,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            // 状态标签
                            if (plan.status != 'upcoming')
                              Container(
                                padding: EdgeInsets.all(SpacingTokens.space4),
                                decoration: BoxDecoration(
                                  color: _getStatusColor(plan.status)
                                      .withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
                                ),
                                child: Text(
                                  _getStatusText(plan.status),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: _getStatusColor(plan.status),
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                          ],
                        ),
                       const SizedBox(height: SpacingTokens.space4),
                        // 详细信息
                        _buildInfoRow(Icons.location_on, plan.location),
                       const SizedBox(height: SpacingTokens.space2),
                        if (plan.timeRange != null) ...[
                          _buildInfoRow(Icons.access_time, plan.timeRange!),
                         const SizedBox(height: SpacingTokens.space2),
                        ],
                        if (plan.weather != null && plan.temperature != null) ...[
                          _buildInfoRow(Icons.wb_sunny,
                              '${plan.weather} ${plan.temperature}'),
                         const SizedBox(height: SpacingTokens.space2),
                        ],
                       const SizedBox(height: 12),
                        // 描述
                        Text(
                          plan.description,
                          style: const TextStyle(
                            fontSize: 14,
                            color: ColorTokens.onSurfaceVariant,
                            height: 1.4,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                       const SizedBox(height: SpacingTokens.space4),
                        // 底部操作栏
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // 参与人数和分享按钮
                            Expanded(
                              child: Row(
                                children: [
                                 const Icon(Icons.group,
                                      size: 20, color: ColorTokens.onSurfaceVariant),
                                 const SizedBox(width: 6),
                                  Text(
                                    '${plan.currentParticipants}/${plan.maxParticipants}人',
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: ColorTokens.onSurface,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                 const SizedBox(width: 12),
                                  // 分享按钮
                                  IconButton(
                                    icon: const Icon(
                                      Icons.share,
                                      size: 20,
                                      color: ColorTokens.onSurfaceVariant,
                                    ),
                                    padding: EdgeInsets.zero,
                                   constraints: const BoxConstraints(),
                                    onPressed: () => ShareUtil.showShareOptions(context, plan),
                                  ),
                                 const SizedBox(width: 8),
                                  // 参与者头像
                                ...List.generate(
                                  plan.currentParticipants > 3
                                      ? 3
                                      : plan.currentParticipants,
                                  (index) => Container(
                                    margin: EdgeInsets.only(left: 4),
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      gradient: const LinearGradient(
                                        colors: [
                                          ColorTokens.primary,
                                          ColorTokens.secondary
                                        ],
                                      ),
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: ColorTokens.surface,
                                        width: 2,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: ColorTokens.primary
                                              .withValues(alpha: 0.3),
                                          blurRadius: 4,
                                          offset: Offset(0, 2),
                                        ),
                                      ],
                                    ),
                                    child: Center(
                                      child: Text(
                                        'U',
                                        style: const TextStyle(
                                          color: ColorTokens.surface,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                                if (plan.currentParticipants > 3)
                                  Container(
                                    margin: EdgeInsets.only(left: 4),
                                    width: 24,
                                    height: 24,
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.surfaceContainerHigh,
                                      shape: BoxShape.circle,
                                      border: Border.all(
                                        color: ColorTokens.surface,
                                        width: 2,
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        '+${plan.currentParticipants - 3}',
                                        style: const TextStyle(
                                          color: ColorTokens.onSurface,
                                          fontSize: 10,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            ),
                            // 操作按钮
                            (!isCompleted && !isCancelled)
                                ? _buildActionButton(plan, isOwner)
                                : const SizedBox.shrink(),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInfoRow(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, size: 16, color: ColorTokens.onSurfaceVariant),
       const SizedBox(width: 8),
        Expanded(
          child: Text(
            text,
            style: const TextStyle(
              fontSize: 14,
              color: ColorTokens.onSurface,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(FishingPlan plan, bool isOwner) {
    if (isOwner) {
      return PopupMenuButton<String>(
        onSelected: (value) => _handleOwnerAction(value, plan),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [ColorTokens.primary, ColorTokens.secondary],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '管理',
                style: TextStyle(
                  color: ColorTokens.onPrimary,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
             const SizedBox(width: 4),
              Icon(Icons.arrow_drop_down, color: ColorTokens.onPrimary, size: 20),
            ],
          ),
        ),
        itemBuilder: (context) => [
         const PopupMenuItem(
            value: 'edit',
            child: Row(
              children: [
                Icon(Icons.edit, size: 20),
               const SizedBox(width: 8),
                Text('编辑'),
              ],
            ),
          ),
         const PopupMenuItem(
            value: 'share',
            child: Row(
              children: [
                Icon(Icons.share, size: 20, color: ColorTokens.info),
               const SizedBox(width: 8),
                Text('分享'),
              ],
            ),
          ),
         const PopupMenuItem(
            value: 'cancel',
            child: Row(
              children: [
                Icon(Icons.cancel, size: 20, color: ColorTokens.error),
               const SizedBox(width: 8),
                Text('取消计划', style: TextStyle(color: ColorTokens.error)),
              ],
            ),
          ),
        ],
      );
    } else if (plan.hasJoined) {
      return GestureDetector(
        onTap: () => _leavePlan(plan),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).colorScheme.outline),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Text(
            '退出',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    } else if (plan.currentParticipants < plan.maxParticipants) {
      return GestureDetector(
        onTap: () => _joinPlan(plan),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [ColorTokens.primary, ColorTokens.secondary],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: const Text(
            '加入',
            style: TextStyle(
              color: ColorTokens.onPrimary,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      );
    } else {
      return Container(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surfaceContainerHigh,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          '已满',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
      );
    }
  }

  Widget _buildEmptyState(String type) {
    String message;
    String subtitle;
    IconData icon;

    switch (type) {
      case 'upcoming':
        message = '暂无即将开始的计划';
        subtitle = '创建或加入钓鱼计划';
        icon = Icons.event_available;
        break;
      case 'my':
        message = '您还没有参与任何计划';
        subtitle = '快去探索精彩的钓鱼计划吧';
        icon = Icons.event_note;
        break;
      case 'completed':
        message = '暂无已完成的计划';
        subtitle = '期待您的下一次钓鱼之旅';
        icon = Icons.event_available;
        break;
      default:
        message = '暂无计划';
        subtitle = '创建或加入钓鱼计划';
        icon = Icons.event;
    }

    return UnifiedEmptyState(
      icon: icon,
      title: message,
      subtitle: subtitle,
    );
  }

  void _createNewPlan() {
    final authViewModel = context.read<AuthViewModel>();
    
    // 检查用户是否已登录
    if (!authViewModel.isUserLoggedIn()) {
      // 显示登录提示
      _showLoginPrompt();
      return;
    }
    
    // 用户已登录，跳转到创建入口页面
    Navigator.pushNamed(context, '/fishing-plans/creation-entry');
  }
  
  void _showLoginPrompt() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('需要登录'),
        content: const Text('请先登录后再创建钓鱼计划'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.navigateTo('/auth/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              foregroundColor: ColorTokens.onPrimary,
            ),
            child: const Text('去登录'),
          ),
        ],
      ),
    );
  }

  void _showLocationOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorTokens.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: DraggableScrollableSheet(
          initialChildSize: 0.6,
          maxChildSize: 0.8,
          minChildSize: 0.4,
          builder: (context, scrollController) => Column(
            children: [
             const SizedBox(height: 12),
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
             const SizedBox(height: SpacingTokens.space5),
             const Text(
                '选择地区',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
             const SizedBox(height: SpacingTokens.space5),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildLocationOption('全国', null, null),
                    _buildLocationOption('北京', '北京', '北京'),
                    _buildLocationOption('上海', '上海', '上海'),
                    _buildLocationOption('广东·广州', '广东', '广州'),
                    _buildLocationOption('广东·深圳', '广东', '深圳'),
                    _buildLocationOption('江苏·南京', '江苏', '南京'),
                    _buildLocationOption('浙江·杭州', '浙江', '杭州'),
                    _buildLocationOption('湖北·武汉', '湖北', '武汉'),
                    _buildLocationOption('四川·成都', '四川', '成都'),
                    _buildLocationOption('云南·昆明', '云南', '昆明'),
                    _buildLocationOption('海南·三亚', '海南', '三亚'),
                    _buildLocationOption('山东·青岛', '山东', '青岛'),
                    _buildLocationOption('辽宁·大连', '辽宁', '大连'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLocationOption(String label, String? province, String? city) {
    final isSelected = (_selectedProvince == province && _selectedCity == city);
    
    return ListTile(
      title: Text(label),
      trailing: isSelected ? const Icon(Icons.check, color: ColorTokens.primary) : null,
      onTap: () {
        setState(() {
          _selectedProvince = province;
          _selectedCity = city;
        });
        Navigator.of(context).pop();
        _loadAllPlans(); // 重新加载数据
      },
    );
  }

  void _showFilterOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: BorderRadius.vertical(top: Radius.circular(25)),
        ),
        padding: SpacingTokens.paddingXl,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                margin: EdgeInsets.only(bottom: 20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHigh,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
           const Text(
              '筛选计划',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
           const SizedBox(height: SpacingTokens.space5),
            _buildFilterOption('全部', 'all'),
            _buildFilterOption('即将开始', 'upcoming'),
            _buildFilterOption('已完成', 'completed'),
            _buildFilterOption('已取消', 'cancelled'),
           const SizedBox(height: SpacingTokens.space5),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(String label, String value) {
    final isSelected = _selectedFilter == value;

    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedFilter = value;
        });
        Navigator.of(context).pop();
      },
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 12),
        child: Row(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                  color:
                      isSelected ? ColorTokens.primary : Theme.of(context).colorScheme.outline,
                  width: 2,
                ),
              ),
              child: isSelected
                  ? Center(
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: const BoxDecoration(
                          shape: BoxShape.circle,
                          color: ColorTokens.primary,
                        ),
                      ),
                    )
                  : null,
            ),
           const SizedBox(width: 12),
            Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: isSelected ? ColorTokens.primary : Theme.of(context).colorScheme.onSurface,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getMonthName(int month) {
   const months = [
      '',
      '1月',
      '2月',
      '3月',
      '4月',
      '5月',
      '6月',
      '7月',
      '8月',
      '9月',
      '10月',
      '11月',
      '12月'
    ];
    return months[month];
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case 'completed':
        return ColorTokens.success;
      case 'cancelled':
        return ColorTokens.error;
      default:
        return ColorTokens.primary;
    }
  }

  String _getStatusText(String status) {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'cancelled':
        return '已取消';
      default:
        return '即将开始';
    }
  }

  void _viewPlanDetail(FishingPlan plan) {
    _viewModel.setCurrentPlan(plan);
    Navigator.pushNamed(
      context, 
      AppRoutes.fishingPlanDetail,
      arguments: plan.id,
    );
  }

  Future<void> _handleOwnerAction(String action, dynamic plan) async {
    switch (action) {
      case 'edit':
        _viewModel.setCurrentPlan(plan);
        Navigator.pushNamed(
          context,
          AppRoutes.editFishingPlan,
          arguments: plan.id,
        );
        break;
      case 'share':
        ShareUtil.showShareOptions(context, plan);
        break;
      case 'cancel':
        final confirm = await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('确认取消'),
            content: Text('确定要取消"${plan.title}"吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: TextButton.styleFrom(
                  foregroundColor: ColorTokens.error,
                ),
                child: const Text('确定'),
              ),
            ],
          ),
        );
        
        if (confirm == true) {
          try {
            await _viewModel.cancelPlan(plan.id);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('已取消 ${plan.title}')),
            );
          } catch (e) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('取消失败: $e'),
                backgroundColor: ColorTokens.error,
              ),
            );
          }
        }
        break;
    }
  }

  Future<void> _joinPlan(dynamic plan) async {
    try {
      await _viewModel.joinPlan(plan.id);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('已加入 ${plan.title}')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('加入失败: $e'),
          backgroundColor: ColorTokens.error,
        ),
      );
    }
  }

  Future<void> _leavePlan(dynamic plan) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认退出'),
        content: Text('确定要退出"${plan.title}"吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: ColorTokens.error,
            ),
            child: const Text('退出'),
          ),
        ],
      ),
    );
    
    if (confirm == true) {
      try {
        await _viewModel.leavePlan(plan.id);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('已退出 ${plan.title}')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('退出失败: $e'),
            backgroundColor: ColorTokens.error,
          ),
        );
      }
    }
  }
}

// 波浪画笔
class WavePainter extends CustomPainter {
  final double animationValue;

  WavePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = ColorTokens.surface.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = 20.0;
    final waveLength = size.width / 3;

    path.moveTo(0, size.height);

    for (double x = 0; x <= size.width; x++) {
      final y = waveHeight *
              sin((x / waveLength * 2 * pi) + (animationValue * 2 * pi)) +
          size.height / 2;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
