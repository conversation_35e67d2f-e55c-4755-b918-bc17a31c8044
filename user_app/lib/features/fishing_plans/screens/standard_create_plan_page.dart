import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_plans/widgets/special_spot_info_widget.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/modern_map_modal.dart';
import 'package:user_app/features/fishing_spots/screens/map_location_picker_page.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

/// 标准创建钓鱼计划页面 - 分步向导
class StandardCreatePlanPage extends StatefulWidget {
 const StandardCreatePlanPage({super.key});

  @override
  State<StandardCreatePlanPage> createState() => _StandardCreatePlanPageState();
}

class _StandardCreatePlanPageState extends State<StandardCreatePlanPage>
    with TickerProviderStateMixin {
  final PageController _pageController = PageController();
  final _formKey = GlobalKey<FormState>();
  
  // 当前步骤
  int _currentStep = 0;
  final int _totalSteps = 4;
  
  // 动画控制器
  late AnimationController _stepAnimationController;
  late AnimationController _fadeAnimationController;
  late Animation<double> _fadeAnimation;
  
  // 表单控制器
  final _titleController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _maxParticipantsController = TextEditingController(text: '10');
  final _feeAmountController = TextEditingController();
  final _contactInfoController = TextEditingController();
  final _equipmentController = TextEditingController();
  
  // 表单状态
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _selectedTime = const TimeOfDay(hour: 6, minute: 0);
  String _selectedTimeRange = 'full_day'; // early, full_day, night, multi_day
  bool _unlimitedParticipants = false;
  
  // 地点相关
  double? _selectedLatitude;
  double? _selectedLongitude;
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedCounty = '';
  
  // 钓法和鱼种
  final List<int> _selectedFishingMethods = [];
  final List<int> _selectedFishTypes = [];
  
  // 费用类型
  String _feeType = 'free'; // free, aa, self, sponsor
  
  // 选中的钓场类型和相关数据
  String _selectedSpotType = '';
  Map<String, dynamic>? _spotData;
  
  bool _isSubmitting = false;

  @override
  void initState() {
    super.initState();
    
    _stepAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _fadeAnimationController,
      curve: Curves.easeInOut,
    );
    
    _fadeAnimationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _stepAnimationController.dispose();
    _fadeAnimationController.dispose();
    _titleController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _maxParticipantsController.dispose();
    _feeAmountController.dispose();
    _contactInfoController.dispose();
    _equipmentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '标准创建',
      body: Column(
        children: [
          // 步骤指示器
          _buildStepIndicator(),
          
          // 内容区域
          Expanded(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: PageView(
                controller: _pageController,
                onPageChanged: (index) {
                  setState(() {
                    _currentStep = index;
                  });
                  _stepAnimationController.forward();
                },
                children: [
                  _buildStep1BasicInfo(),
                  _buildStep2LocationSelect(),
                  _buildStep3MethodsAndTarget(),
                  _buildStep4FeeAndDetails(),
                ],
              ),
            ),
          ),
          
          // 底部按钮
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                '${_currentStep + 1}/$_totalSteps',
                style: TypographyTokens.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
             const Spacer(),
              Text(
                _getStepTitle(),
                style: TypographyTokens.bodyLarge.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),
         const SizedBox(height: 12),
          Row(
            children: List.generate(_totalSteps, (index) {
              return Expanded(
                child: Container(
                  height: 4,
                  margin: EdgeInsets.only(right: index < _totalSteps - 1 ? SpacingTokens.space2 : 0),
                  decoration: BoxDecoration(
                    color: index <= _currentStep 
                        ? ColorTokens.primary
                        : ColorTokens.outline,
                    borderRadius: ShapeTokens.borderRadiusXs,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  String _getStepTitle() {
    switch (_currentStep) {
      case 0: return '基础信息';
      case 1: return '选择地点';
      case 2: return '钓法与目标';
      case 3: return '费用与详情';
      default: return '';
    }
  }

  Widget _buildStep1BasicInfo() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         const Text(
            '基础信息',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 计划标题
          const Text(
            '计划标题 *',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          TextFormField(
            controller: _titleController,
            decoration: InputDecoration(
              hintText: '请输入计划标题...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入计划标题';
              }
              return null;
            },
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            '推荐：周末垂钓、东江钓鲫鱼',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 钓鱼时间
          const Text(
            '钓鱼时间 *',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          
          // 日期选择
          GestureDetector(
            onTap: _selectDate,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(SpacingTokens.space4),
              decoration: BoxDecoration(
                border: Border.all(color: ColorTokens.outline),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                 const Icon(Icons.calendar_today, color: ColorTokens.primary),
                 const SizedBox(width: 12),
                  Text(
                    DateFormat('yyyy年MM月dd日').format(_selectedDate),
                    style: TypographyTokens.bodyLarge,
                  ),
                ],
              ),
            ),
          ),
         const SizedBox(height: 12),
          
          // 时间选择
          GestureDetector(
            onTap: _selectTime,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.all(SpacingTokens.space4),
              decoration: BoxDecoration(
                border: Border.all(color: ColorTokens.outline),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                 const Icon(Icons.access_time, color: ColorTokens.primary),
                 const SizedBox(width: 12),
                  Text(
                    _selectedTime.format(context),
                    style: TypographyTokens.bodyLarge,
                  ),
                ],
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 时间段选择
          const Text(
            '时间段',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          Column(
            children: [
              _buildTimeRangeOption('early', '早钓 (4:00-10:00)'),
              _buildTimeRangeOption('full_day', '全天 (4:00-18:00)'),
              _buildTimeRangeOption('night', '夜钓 (17:00-次日7:00)'),
              _buildTimeRangeOption('multi_day', '多日游'),
            ],
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 参与人数
          const Text(
            '参与人数',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          if (!_unlimitedParticipants) ...[
            Slider(
              value: double.parse(_maxParticipantsController.text),
              min: 1,
              max: 50,
              divisions: 49,
              activeColor: ColorTokens.primary,
              label: '${_maxParticipantsController.text}人',
              onChanged: (value) {
                setState(() {
                  _maxParticipantsController.text = value.round().toString();
                });
              },
            ),
            Text(
              '${_maxParticipantsController.text}人',
              textAlign: TextAlign.center,
              style: TypographyTokens.titleLarge.copyWith(
                fontWeight: FontWeight.bold,
                color: ColorTokens.primary,
              ),
            ),
            const SizedBox(height: SpacingTokens.space3),
          ],
          CheckboxListTile(
            title: const Text('不限制人数'),
            value: _unlimitedParticipants,
            activeColor: ColorTokens.primary,
            onChanged: (value) {
              setState(() {
                _unlimitedParticipants = value ?? false;
                if (_unlimitedParticipants) {
                  _maxParticipantsController.text = '99';
                } else {
                  _maxParticipantsController.text = '10';
                }
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRangeOption(String value, String label) {
    return RadioListTile<String>(
      title: Text(label),
      value: value,
      groupValue: _selectedTimeRange,
      activeColor: ColorTokens.primary,
      onChanged: (value) {
        setState(() {
          _selectedTimeRange = value!;
        });
      },
    );
  }

  Widget _buildStep2LocationSelect() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         const Text(
            '选择地点',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 标签栏
          Row(
            children: [
              _buildLocationTab('最近', true),
             const SizedBox(width: 12),
              _buildLocationTab('收藏', false),
             const SizedBox(width: 12),
              _buildLocationTab('附近', false),
             const SizedBox(width: 12),
              _buildLocationTab('搜索', false),
            ],
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 搜索框
          TextFormField(
            decoration: InputDecoration(
              hintText: '输入钓点名称...',
              prefixIcon: const Icon(Icons.search, color: ColorTokens.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 最近去过
          const Text(
            '📍 最近去过',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          _buildLocationCard(
            name: '东江钓场',
            type: '黑坑',
            distance: '5km',
            rating: 4.5,
            extra: '放鱼日周三',
            lastVisit: '上次：3月9日 钓获8斤',
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 附近推荐
          const Text(
            '📍 附近推荐',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          _buildLocationCard(
            name: '白云湖',
            type: '野钓',
            distance: '12km',
            rating: 4.3,
            extra: '免费',
            lastVisit: '评分4.3 签到256次',
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 底部按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _openLocationPicker,
                  icon: const Icon(Icons.map_rounded),
                  label: const Text('地图选点'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: ColorTokens.primary,
                    side: const BorderSide(color: ColorTokens.primary),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
             const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _manualInputLocation,
                  icon: const Icon(Icons.edit),
                  label: const Text('手动输入'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: ColorTokens.primary,
                    side: const BorderSide(color: ColorTokens.primary),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          // 显示已选择的位置
          if (_locationController.text.isNotEmpty) ...[
            const SizedBox(height: SpacingTokens.space5),
            Container(
              padding: EdgeInsets.all(SpacingTokens.space4),
              decoration: BoxDecoration(
                color: ColorTokens.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ColorTokens.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                 const Icon(
                    Icons.location_on,
                    color: ColorTokens.primary,
                  ),
                 const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                       const Text(
                          '已选择位置',
                          style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: ColorTokens.primary,
                          ),
                        ),
                        Text(_locationController.text),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            
            // 显示特殊钓场信息
            if (_selectedSpotType.isNotEmpty && _selectedSpotType != 'general')
              SpecialSpotInfoWidget(
                spotType: _selectedSpotType,
                spotData: _spotData,
              ),
          ],
        ],
      ),
    );
  }

  Widget _buildLocationTab(String title, bool isSelected) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: isSelected ? ColorTokens.primary : ColorTokens.surfaceContainerLow,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Text(
        title,
        style: TextStyle(
          color: isSelected ? ColorTokens.onPrimary : ColorTokens.onSurfaceVariant,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildLocationCard({
    required String name,
    required String type,
    required String distance,
    required double rating,
    required String extra,
    required String lastVisit,
  }) {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TypographyTokens.bodyLarge.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: SpacingTokens.space1),
                Text(
                  '$type · $distance · ⭐$rating',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
                Text(
                  lastVisit,
                  style: TextStyle(
                    fontSize: 12,
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: () {
              setState(() {
                _locationController.text = name;
                // 根据选择的钓点设置钓场类型
                if (name.contains('黑坑') || type == '黑坑') {
                  _selectedSpotType = 'blackpit';
                  _spotData = _getBlackPitData(name);
                } else if (name.contains('海') || type == '海钓') {
                  _selectedSpotType = 'sea';
                  _spotData = _getSeaFishingData(name);
                } else if (name.contains('水库') || type == '水库') {
                  _selectedSpotType = 'reservoir';
                  _spotData = _getReservoirData(name);
                } else if (name.contains('河') || name.contains('江') || type == '江河') {
                  _selectedSpotType = 'river';
                  _spotData = _getRiverData(name);
                } else {
                  _selectedSpotType = 'general';
                  _spotData = null;
                }
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: ColorTokens.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('选择此处'),
          ),
        ],
      ),
    );
  }

  Widget _buildStep3MethodsAndTarget() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         const Text(
            '钓法与目标',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 选择钓法
          const Text(
            '🎣 选择钓法 * (多选)',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          Container(
            padding: EdgeInsets.all(SpacingTokens.space4),
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(child: _buildMethodCheckbox(1, '台钓')),
                    Expanded(child: _buildMethodCheckbox(4, '传统钓')),
                  ],
                ),
                Row(
                  children: [
                    Expanded(child: _buildMethodCheckbox(2, '路亚')),
                    Expanded(child: _buildMethodCheckbox(3, '筏钓')),
                  ],
                ),
                Row(
                  children: [
                    Expanded(child: _buildMethodCheckbox(5, '海钓')),
                    Expanded(child: _buildMethodCheckbox(6, '矶钓')),
                  ],
                ),
              ],
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 目标鱼种
          const Text(
            '🐟 目标鱼种',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            '根据钓点推荐（东江钓场）',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
         const SizedBox(height: 12),
          Wrap(
            spacing: 12,
            runSpacing: 8,
            children: [
              _buildFishTypeChip(1, '鲫鱼'),
              _buildFishTypeChip(2, '鲤鱼'),
              _buildFishTypeChip(3, '草鱼'),
              _buildFishTypeChip(4, '青鱼'),
            ],
          ),
          const SizedBox(height: SpacingTokens.space4),
          
          // 搜索添加
          TextFormField(
            decoration: InputDecoration(
              hintText: '搜索鱼种...',
              prefixIcon: const Icon(Icons.search, color: ColorTokens.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
          ),
          const SizedBox(height: SpacingTokens.space4),
          
          if (_selectedFishTypes.isNotEmpty) ...[
            Text(
              '已选择：${_getSelectedFishNames()}',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
            const SizedBox(height: SpacingTokens.space5),
          ],
          
          // 装备要求
          const Text(
            '🎒 装备要求',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          DropdownButtonFormField<String>(
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
            hint: const Text('选择模板'),
            items: const [
              DropdownMenuItem(value: 'basic', child: Text('基础装备')),
              DropdownMenuItem(value: 'advanced', child: Text('进阶装备')),
              DropdownMenuItem(value: 'professional', child: Text('专业装备')),
              DropdownMenuItem(value: 'custom', child: Text('自定义')),
            ],
            onChanged: (value) {
              if (value == 'basic') {
                _equipmentController.text = '鱼竿：4.5-5.4米\n线组：主1.5 子0.8\n饵料：自备';
              } else if (value == 'advanced') {
                _equipmentController.text = '鱼竿：4.5-5.4米台钓竿\n线组：主线1.5号，子线0.8号\n浮漂：根据水深选择\n抄网、鱼护';
              } else if (value == 'professional') {
                _equipmentController.text = '鱼竿：4.5-5.4米台钓竿\n支架：炮台或支架\n线组：主线1.5号，子线0.8号\n浮漂：根据水深选择\n抄网、鱼护、遮阳伞\n饵料：商品饵+添加剂';
              }
            },
          ),
         const SizedBox(height: 12),
          TextFormField(
            controller: _equipmentController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: '详细装备要求...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMethodCheckbox(int id, String name) {
    return CheckboxListTile(
      title: Text(name),
      value: _selectedFishingMethods.contains(id),
      activeColor: ColorTokens.primary,
      onChanged: (value) {
        setState(() {
          if (value == true) {
            _selectedFishingMethods.add(id);
          } else {
            _selectedFishingMethods.remove(id);
          }
        });
      },
    );
  }

  Widget _buildFishTypeChip(int id, String name) {
    final isSelected = _selectedFishTypes.contains(id);
    return FilterChip(
      label: Text(name),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          if (selected) {
            _selectedFishTypes.add(id);
          } else {
            _selectedFishTypes.remove(id);
          }
        });
      },
      selectedColor: ColorTokens.primary.withValues(alpha: 0.2),
      checkmarkColor: ColorTokens.primary,
    );
  }

  String _getSelectedFishNames() {
    final names = <String>[];
    for (int id in _selectedFishTypes) {
      switch (id) {
        case 1: names.add('鲫鱼'); break;
        case 2: names.add('鲤鱼'); break;
        case 3: names.add('草鱼'); break;
        case 4: names.add('青鱼'); break;
      }
    }
    return names.join('、');
  }

  Widget _buildStep4FeeAndDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
         const Text(
            '费用与详情',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 费用类型
          const Text(
            '💰 费用类型',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: 12),
          Column(
            children: [
              _buildFeeTypeOption('free', '免费'),
              _buildFeeTypeOption('aa', 'AA制'),
              _buildFeeTypeOption('self', '自费'),
              _buildFeeTypeOption('sponsor', '赞助'),
            ],
          ),
         const SizedBox(height: SpacingTokens.space5),
          
          // 费用说明
          const Text(
            '费用说明',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          TextFormField(
            controller: _feeAmountController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: _getFeeHint(),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 补充说明
          const Text(
            '📝 补充说明',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          TextFormField(
            controller: _descriptionController,
            maxLines: 4,
            decoration: InputDecoration(
              hintText: '例如：\n1. 禁止使用红虫、虾粉\n2. 钓位抽签决定\n3. 请自备遮阳伞',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 联系方式
          const Text(
            '📞 联系方式',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          TextFormField(
            controller: _contactInfoController,
            decoration: InputDecoration(
              hintText: '138****8888（已自动填充）',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          
          // 隐私设置
          const Text(
            '🔒 隐私设置',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: ColorTokens.onSurface,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          CheckboxListTile(
            title: const Text('公开（其他钓友可申请加入）'),
            value: true,
            activeColor: ColorTokens.primary,
            onChanged: (value) {},
          ),
        ],
      ),
    );
  }

  Widget _buildFeeTypeOption(String value, String label) {
    return RadioListTile<String>(
      title: Text(label),
      value: value,
      groupValue: _feeType,
      activeColor: ColorTokens.primary,
      onChanged: (value) {
        setState(() {
          _feeType = value!;
        });
      },
    );
  }

  String _getFeeHint() {
    switch (_feeType) {
      case 'free': return '免费垂钓';
      case 'aa': return '塘费：100元/人\n饵料：自备或现场购买\n午餐：AA制约30元\n预计总费用：130-150元/人';
      case 'self': return '费用自理，参考金额...';
      case 'sponsor': return '本次活动由XXX赞助';
      default: return '';
    }
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, -5),
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentStep > 0) ...[
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  foregroundColor: ColorTokens.primary,
                  side: const BorderSide(color: ColorTokens.primary),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('上一步'),
              ),
            ),
           const SizedBox(width: 12),
          ] else ...[
            Expanded(
              child: OutlinedButton(
                onPressed: () => Navigator.pop(context),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Colors.grey[600],
                  side: BorderSide(color: Colors.grey[300]!),
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('取消'),
              ),
            ),
           const SizedBox(width: 12),
          ],
          Expanded(
            child: ElevatedButton(
              onPressed: _isSubmitting ? null : _nextStepOrSubmit,
              style: ElevatedButton.styleFrom(
                backgroundColor: ColorTokens.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 14),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isSubmitting
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(_currentStep < _totalSteps - 1 ? '下一步' : '立即发布'),
            ),
          ),
          if (_currentStep == _totalSteps - 1) ...[
           const SizedBox(width: 12),
            OutlinedButton(
              onPressed: _saveDraft,
              style: OutlinedButton.styleFrom(
                foregroundColor: ColorTokens.primary,
                side: const BorderSide(color: ColorTokens.primary),
                padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text('保存草稿'),
            ),
          ],
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (pickedDate != null) {
      setState(() {
        _selectedDate = pickedDate;
      });
    }
  }

  Future<void> _selectTime() async {
    final pickedTime = await showTimePicker(
      context: context,
      initialTime: _selectedTime,
    );
    
    if (pickedTime != null) {
      setState(() {
        _selectedTime = pickedTime;
      });
    }
  }

  Future<void> _openLocationPicker() async {
    try {
      final result = await showModalBottomSheet<LocationResult>(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => ModernMapModal(
          initialLatitude: _selectedLatitude ?? 0,
          initialLongitude: _selectedLongitude ?? 0,
          initialFormattedAddress: _locationController.text,
        ),
      );

      if (result != null) {
        setState(() {
          _locationController.text = result.formattedAddress;
          _selectedLatitude = result.latitude;
          _selectedLongitude = result.longitude;
          _selectedProvince = result.province;
          _selectedCity = result.city;
          _selectedCounty = result.county;
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Text('地图选择失败，请重试'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _manualInputLocation() {
    showDialog(
      context: context,
      builder: (context) {
        final controller = TextEditingController();
        return AlertDialog(
          title: const Text('手动输入位置'),
          content: TextFormField(
            controller: controller,
            decoration: const InputDecoration(
              hintText: '请输入钓点位置',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('取消'),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.text.trim().isNotEmpty) {
                  setState(() {
                    _locationController.text = controller.text.trim();
                  });
                  Navigator.pop(context);
                }
              },
              child: const Text('确定'),
            ),
          ],
        );
      },
    );
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _nextStepOrSubmit() {
    if (_currentStep < _totalSteps - 1) {
      // 验证当前步骤
      if (_validateCurrentStep()) {
        _pageController.nextPage(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
        );
      }
    } else {
      // 提交表单
      _submitPlan();
    }
  }

  bool _validateCurrentStep() {
    switch (_currentStep) {
      case 0: // 基础信息
        if (_titleController.text.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
           const SnackBar(content: Text('请输入计划标题')),
          );
          return false;
        }
        return true;
      case 1: // 地点选择
        if (_locationController.text.trim().isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
           const SnackBar(content: Text('请选择或输入钓点位置')),
          );
          return false;
        }
        return true;
      case 2: // 钓法与目标
        if (_selectedFishingMethods.isEmpty) {
          ScaffoldMessenger.of(context).showSnackBar(
           const SnackBar(content: Text('请至少选择一种钓法')),
          );
          return false;
        }
        return true;
      case 3: // 费用与详情
        return true;
      default:
        return true;
    }
  }

  Future<void> _submitPlan() async {
    if (!_validateCurrentStep()) return;
    
    setState(() {
      _isSubmitting = true;
    });
    
    try {
      final viewModel = context.read<FishingPlanViewModel>();
      
      final dto = FishingPlanCreateDTO(
        title: _titleController.text.trim(),
        location: _locationController.text.trim(),
        planDate: DateTime(
          _selectedDate.year,
          _selectedDate.month,
          _selectedDate.day,
          _selectedTime.hour,
          _selectedTime.minute,
        ),
        timeRange: _getTimeRangeDescription(),
        maxParticipants: int.parse(_maxParticipantsController.text),
        description: _descriptionController.text.trim().isEmpty 
            ? '暂无描述' 
            : _descriptionController.text.trim(),
        contactInfo: _contactInfoController.text.trim().isEmpty 
            ? null 
            : _contactInfoController.text.trim(),
        isPublic: true,
        // TODO: 添加其他字段的映射
      );
      
      await viewModel.createPlan(dto);
      
      if (mounted) {
        HapticFeedback.mediumImpact();
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Text('计划创建成功'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('创建失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  String _getTimeRangeDescription() {
    switch (_selectedTimeRange) {
      case 'early': return '早钓 (4:00-10:00)';
      case 'full_day': return '全天 (4:00-18:00)';
      case 'night': return '夜钓 (17:00-次日7:00)';
      case 'multi_day': return '多日游';
      default: return '全天';
    }
  }

  void _saveDraft() {
    // TODO: 实现保存草稿功能
    ScaffoldMessenger.of(context).showSnackBar(
     const SnackBar(content: Text('草稿已保存')),
    );
  }
  
  /// 获取黑坑特殊数据
  Map<String, dynamic> _getBlackPitData(String spotName) {
    // 这里应该从API获取真实数据，现在返回模拟数据
    return {
      'lastFishRelease': '3月13日 1000斤',
      'nextFishRelease': '3月16日（计划当天）',
      'fishTypes': '鲫鱼、鲤鱼、草鱼',
      'rodLimit': '限竿4.5米',
      'baitLimit': '禁用红虫、虾粉',
      'feeInfo': '正钓200元，偷驴100元',
      'spotAssignment': '抽签决定钓位',
      'specialReminder': '• 建议提前1天预约钓位\n• 开钓前30分钟到场抽签\n• 严格遵守钓场规定\n• 自备钓椅和抄网',
    };
  }
  
  /// 获取海钓特殊数据
  Map<String, dynamic> _getSeaFishingData(String spotName) {
    return {
      'highTide': '06:23  18:45',
      'lowTide': '00:12  12:34',
      'bestTime': '涨潮前后2小时',
      'tideType': '中潮',
      'windInfo': '东南风3-4级',
      'waveHeight': '0.5-1.0米',
      'visibility': '良好(>10km)',
      'waterTemp': '22-24℃',
      'safetyReminder': '• 必须穿救生衣\n• 注意防晒防风\n• 关注天气变化\n• 结伴而行，保持联系\n• 遵守海域管制规定\n• 携带应急通讯设备',
    };
  }
  
  /// 获取水库特殊数据
  Map<String, dynamic> _getReservoirData(String spotName) {
    return {
      'waterLevel': '正常水位',
      'waterQuality': '良好',
      'mainFish': '鲢鳙、草鱼、鲤鱼',
      'openStatus': '正常开放',
    };
  }
  
  /// 获取江河特殊数据
  Map<String, dynamic> _getRiverData(String spotName) {
    return {
      'flowSpeed': '缓流',
      'depthRange': '2-5米',
      'bottomType': '泥沙底',
      'commonFish': '鲫鱼、鲤鱼、黄颡鱼',
    };
  }
}