import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_spots/screens/map_location_picker_page.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

/// 好友约钓页面 - 私人好友约钓活动
/// 无需许可，仅限好友参与
class FriendOutingPage extends StatefulWidget {
 const FriendOutingPage({super.key});

  @override
  State<FriendOutingPage> createState() => _FriendOutingPageState();
}

class _FriendOutingPageState extends State<FriendOutingPage>
    with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  // 表单控制器
  final _titleController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _maxParticipantsController = TextEditingController(text: '6');
  
  // 表单状态
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 1));
  TimeOfDay _selectedStartTime = const TimeOfDay(hour: 6, minute: 0);
  TimeOfDay _selectedEndTime = const TimeOfDay(hour: 14, minute: 0);
  bool _isSubmitting = false;
  
  // 位置数据
  double? _selectedLatitude;
  double? _selectedLongitude;
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedCounty = '';

  // 好友邀请数据
  final List<FriendInfo> _selectedFriends = [];
  final List<FriendInfo> _availableFriends = [
    const FriendInfo(id: '1', name: '张三', avatar: '🎣', isOnline: true),
    const FriendInfo(id: '2', name: '李四', avatar: '🐟', isOnline: false),
    const FriendInfo(id: '3', name: '王五', avatar: '🎯', isOnline: true),
    const FriendInfo(id: '4', name: '赵六', avatar: '⭐', isOnline: true),
    const FriendInfo(id: '5', name: '钱七', avatar: '🌊', isOnline: false),
  ];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
    
    // 设置默认值
    _titleController.text = '周末约钓';
    _descriptionController.text = '一起去钓鱼，享受愉快的垂钓时光！分享彼此的钓鱼技巧，留下美好回忆~';
  }

  @override
  void dispose() {
    _animationController.dispose();
    _titleController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _maxParticipantsController.dispose();
    super.dispose();
  }

  DateTime get _startDateTime {
    return DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedStartTime.hour,
      _selectedStartTime.minute,
    );
  }


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: _buildCustomAppBar(),
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.fromLTRB(20, 120, 20, 20), // 增加顶部间距避免与AppBar重叠
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 美化的页面头部
                  _buildModernHeader(),
                 const SizedBox(height: 32),
                  
                  // 基本信息卡片
                  _buildModernCard(
                    title: '基本信息',
                    icon: Icons.edit_note_rounded,
                    iconColor: Theme.of(context).colorScheme.primary,
                    children: [
                      _buildModernTextField(
                        controller: _titleController,
                        label: '活动标题',
                        hint: '给你的约钓活动起个响亮的名字',
                        icon: Icons.title_rounded,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入活动标题';
                          }
                          return null;
                        },
                      ),
                     const SizedBox(height: SpacingTokens.space5),
                      _buildModernTextField(
                        controller: _descriptionController,
                        label: '活动描述',
                        hint: '描述一下这次约钓的计划和期待',
                        icon: Icons.description_rounded,
                        maxLines: 3,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入活动描述';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                 const SizedBox(height: SpacingTokens.space6),
                  
                  // 时间地点卡片
                  _buildModernCard(
                    title: '时间地点',
                    icon: Icons.schedule_rounded,
                    iconColor: Theme.of(context).colorScheme.secondary,
                    children: [
                      _buildDateTimeSelectors(),
                     const SizedBox(height: SpacingTokens.space5),
                      _buildLocationSelector(),
                    ],
                  ),
                 const SizedBox(height: SpacingTokens.space6),
                  
                  // 邀请好友卡片
                  _buildModernCard(
                    title: '邀请好友',
                    icon: Icons.people_rounded,
                    iconColor: Theme.of(context).colorScheme.error,
                    children: [
                      _buildFriendInviteSection(),
                    ],
                  ),
                 const SizedBox(height: SpacingTokens.space6),
                  
                  // 参与设置卡片
                  _buildModernCard(
                    title: '参与设置',
                    icon: Icons.settings_rounded,
                    iconColor: Theme.of(context).colorScheme.tertiary,
                    children: [
                      _buildParticipantSettings(),
                    ],
                  ),
                 const SizedBox(height: 40),
                  
                  // 创建按钮
                  _buildModernCreateButton(),
                 const SizedBox(height: SpacingTokens.space5),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  PreferredSizeWidget _buildCustomAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(100),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              ColorTokens.primary,
              ColorTokens.secondary,
              ColorTokens.tertiary,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                // 返回按钮
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.15),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Material(
                    color: ColorTokens.transparent,
                    borderRadius: BorderRadius.circular(12),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(12),
                      onTap: () {
                        HapticFeedback.lightImpact();
                        Navigator.of(context).pop();
                      },
                      child: const Icon(
                        Icons.arrow_back_ios_rounded,
                        color: Theme.of(context).colorScheme.surface,
                        size: 20,
                      ),
                    ),
                  ),
                ),
               const SizedBox(width: 16),
                
                // 标题和副标题
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                     const Text(
                        '发起好友约钓',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.surface,
                          // 使用 Theme.of(context).textTheme
                          fontWeight: FontWeight.bold,
                          letterSpacing: 0.5,
                        ),
                      ),
                     const SizedBox(height: 2),
                      Row(
                        children: [
                          Container(
                            width: 6,
                            height: 6,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                         const SizedBox(width: 6),
                          Text(
                            '创建私人钓鱼活动',
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.85),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                
                // 右侧装饰
                Container(
                  width: 44,
                  height: 44,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.groups_2_rounded,
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                    size: 24,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(28),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [
            Color(0xFF6366F1),
            Color(0xFF8B5CF6),
            Color(0xFFA855F7),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF6366F1).withValues(alpha: 0.3),
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.groups_2_rounded,
                  color: Theme.of(context).colorScheme.surface,
                  size: 32,
                ),
              ),
             const SizedBox(width: 20),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                   const Text(
                      '好友约钓',
                      style: TextStyle(
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.surface,
                      ),
                    ),
                   const SizedBox(height: 6),
                    Text(
                      '邀请好友一起钓鱼，分享美好时光',
                      style: TextStyle(
                        fontSize: 16,
                        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
         const SizedBox(height: SpacingTokens.space5),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 10),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
               const Icon(
                  Icons.info_outline_rounded,
                  color: Theme.of(context).colorScheme.surface,
                  size: 18,
                ),
               const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '私人约钓，无需审批，只有受邀好友可参与',
                    style: TextStyle(
                      fontSize: 14,
                      color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernCard({
    required String title,
    required IconData icon,
    required Color iconColor,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      padding: SpacingTokens.paddingXl,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: iconColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: iconColor,
                  size: 24,
                ),
              ),
             const SizedBox(width: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF1F2937),
                ),
              ),
            ],
          ),
         const SizedBox(height: SpacingTokens.space6),
          ...children,
        ],
      ),
    );
  }


  Widget _buildDateTimeSelectors() {
    return Column(
      children: [
        // 日期选择
        InkWell(
          onTap: () => _selectDate(context),
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: SpacingTokens.paddingLg,
            decoration: BoxDecoration(
              color: const Color(0xFFF0FDF4),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFBBF7D0)),
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: const Color(0xFF10B981),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.calendar_today_rounded,
                    color: Theme.of(context).colorScheme.surface,
                    size: 24,
                  ),
                ),
               const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                     const Text(
                        '约钓日期',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF059669),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                     const SizedBox(height: 4),
                      Text(
                        DateFormat('yyyy年MM月dd日 EEEE', 'zh_CN').format(_selectedDate),
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF065F46),
                        ),
                      ),
                    ],
                  ),
                ),
               const Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Color(0xFF059669),
                  size: 18,
                ),
              ],
            ),
          ),
        ),
       const SizedBox(height: SpacingTokens.space4),
        
        // 时间选择
        Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: () => _selectTime(context, true),
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: SpacingTokens.paddingMd,
                  decoration: BoxDecoration(
                    color: const Color(0xFFFEF3C7),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: const Color(0xFFFDE68A)),
                  ),
                  child: Column(
                    children: [
                     const Icon(
                        Icons.wb_sunny_rounded,
                        color: Color(0xFFD97706),
                        size: 24,
                      ),
                     const SizedBox(height: SpacingTokens.space2),
                     const Text(
                        '开始时间',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF92400E),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                     const SizedBox(height: 4),
                      Text(
                        _selectedStartTime.format(context),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF78350F),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
           const SizedBox(width: 16),
            Expanded(
              child: InkWell(
                onTap: () => _selectTime(context, false),
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: SpacingTokens.paddingMd,
                  decoration: BoxDecoration(
                    color: const Color(0xFFDDD6FE),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: const Color(0xFFC4B5FD)),
                  ),
                  child: Column(
                    children: [
                     const Icon(
                        Icons.wb_twilight_rounded,
                        color: Color(0xFF7C3AED),
                        size: 24,
                      ),
                     const SizedBox(height: SpacingTokens.space2),
                     const Text(
                        '结束时间',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF5B21B6),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                     const SizedBox(height: 4),
                      Text(
                        _selectedEndTime.format(context),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF4C1D95),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildLocationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.location_on_rounded, size: 18, color: Color(0xFF6B7280)),
            SizedBox(width: 8),
            Text(
              '钓点位置',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF374151),
              ),
            ),
          ],
        ),
       const SizedBox(height: 12),
        InkWell(
          onTap: _selectLocation,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: SpacingTokens.paddingLg,
            decoration: BoxDecoration(
              color: const Color(0xFFF0F9FF),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: const Color(0xFFBAE6FD)),
            ),
            child: Row(
              children: [
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: const Color(0xFF0EA5E9),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.map_rounded,
                    color: Theme.of(context).colorScheme.surface,
                    size: 24,
                  ),
                ),
               const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                     const Text(
                        '选择钓点',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFF0284C7),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                     const SizedBox(height: 4),
                      Text(
                        _locationController.text.isEmpty 
                          ? '点击选择钓点位置' 
                          : _locationController.text,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: _locationController.text.isEmpty 
                            ? Color(0xFF64748B) 
                            : Color(0xFF0C4A6E),
                        ),
                      ),
                    ],
                  ),
                ),
               const Icon(
                  Icons.arrow_forward_ios_rounded,
                  color: Color(0xFF0284C7),
                  size: 18,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildFriendInviteSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.person_add_rounded, size: 18, color: Color(0xFF6B7280)),
            SizedBox(width: 8),
            Text(
              '选择要邀请的好友',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF374151),
              ),
            ),
          ],
        ),
       const SizedBox(height: SpacingTokens.space4),
        
        // 已选择的好友
        if (_selectedFriends.isNotEmpty) ...[
          Container(
            padding: SpacingTokens.paddingMd,
            decoration: BoxDecoration(
              color: const Color(0xFFFEF2F2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: const Color(0xFFFECACA)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
               const Text(
                  '已邀请好友',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFFDC2626),
                  ),
                ),
               const SizedBox(height: 12),
                Wrap(
                  spacing: 8,
                  runSpacing: 8,
                  children: _selectedFriends.map((friend) => 
                    _buildSelectedFriendChip(friend)
                  ).toList(),
                ),
              ],
            ),
          ),
         const SizedBox(height: SpacingTokens.space4),
        ],
        
        // 好友列表
        Container(
          decoration: BoxDecoration(
            color: const Color(0xFFF9FAFB),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: const Color(0xFFE5E7EB)),
          ),
          child: Column(
            children: [
              Padding(
                padding: SpacingTokens.paddingMd,
                child: Row(
                  children: [
                   const Icon(Icons.contacts_rounded, size: 20, color: Color(0xFF6B7280)),
                   const SizedBox(width: 8),
                   const Text(
                      '我的好友',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF374151),
                      ),
                    ),
                   const Spacer(),
                    Text(
                      '${_availableFriends.length}位好友',
                      style: const TextStyle(
                        fontSize: 12,
                        color: Color(0xFF6B7280),
                      ),
                    ),
                  ],
                ),
              ),
             const Divider(height: 1, color: const Color(0xFFE5E7EB)),
              ..._availableFriends.map((friend) => _buildFriendItem(friend)),
            ],
          ),
        ),
       const SizedBox(height: SpacingTokens.space4),
        
        // 提示信息
        Container(
          padding: SpacingTokens.paddingMd,
          decoration: BoxDecoration(
            color: const Color(0xFFF0F9FF),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: const Color(0xFFBAE6FD)),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline_rounded,
                color: Color(0xFF0284C7),
                size: 20,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  '邀请发出后，好友会收到通知。只有接受邀请的好友才能参与活动。',
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF0284C7),
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedFriendChip(FriendInfo friend) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: const Color(0xFFE11D48)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            friend.avatar,
            style: const TextStyle(fontSize: 16),
          ),
         const SizedBox(width: 6),
          Text(
            friend.name,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: const Color(0xFFE11D48),
            ),
          ),
         const SizedBox(width: 4),
          GestureDetector(
            onTap: () {
              setState(() {
                _selectedFriends.removeWhere((f) => f.id == friend.id);
              });
            },
            child: const Icon(
              Icons.close_rounded,
              size: 16,
              color: const Color(0xFFE11D48),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFriendItem(FriendInfo friend) {
    final isSelected = _selectedFriends.any((f) => f.id == friend.id);
    
    return InkWell(
      onTap: () {
        setState(() {
          if (isSelected) {
            _selectedFriends.removeWhere((f) => f.id == friend.id);
          } else {
            _selectedFriends.add(friend);
          }
        });
      },
      child: Container(
        padding: SpacingTokens.paddingMd,
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFFFEF2F2) : Theme.of(context).colorScheme.surface,
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: friend.isOnline ? const Color(0xFF10B981) : Color(0xFF6B7280),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Center(
                child: Text(
                  friend.avatar,
                  style: const TextStyle(fontSize: 18),
                ),
              ),
            ),
           const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        friend.name,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF1F2937),
                        ),
                      ),
                     const SizedBox(width: 8),
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: friend.isOnline ? const Color(0xFF10B981) : Color(0xFF6B7280),
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                    ],
                  ),
                 const SizedBox(height: 2),
                  Text(
                    friend.isOnline ? '在线' : '离线',
                    style: TextStyle(
                      fontSize: 12,
                      color: friend.isOnline ? const Color(0xFF10B981) : Color(0xFF6B7280),
                    ),
                  ),
                ],
              ),
            ),
            AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isSelected ? const Color(0xFFE11D48) : ColorTokens.transparent,
                border: isSelected ? null : Border.all(color: Color(0xFFD1D5DB), width: 2),
                borderRadius: BorderRadius.circular(6),
              ),
              child: isSelected
                ? const Icon(
                    Icons.check_rounded,
                    color: Theme.of(context).colorScheme.surface,
                    size: 16,
                  )
                : null,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildParticipantSettings() {
    return Column(
      children: [
        Row(
          children: [
            Icon(Icons.group_rounded, size: 18, color: Color(0xFF6B7280)),
            SizedBox(width: 8),
            Text(
              '参与人数限制',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF374151),
              ),
            ),
          ],
        ),
       const SizedBox(height: SpacingTokens.space4),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: _maxParticipantsController,
                keyboardType: TextInputType.number,
                decoration: InputDecoration(
                  labelText: '最大参与人数',
                  hintText: '包括你在内的总人数',
                  filled: true,
                  fillColor: const Color(0xFFF9FAFB),
                  prefixIcon: const Icon(Icons.people_outline_rounded, color: Color(0xFF6B7280)),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: const Color(0xFFE5E7EB)),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: const Color(0xFFE5E7EB)),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(16),
                    borderSide: const BorderSide(color: Color(0xFFF59E0B), width: 2),
                  ),
                  contentPadding: SpacingTokens.paddingMd,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入最大参与人数';
                  }
                  final number = int.tryParse(value);
                  if (number == null || number < 2 || number > 20) {
                    return '参与人数应在2-20人之间';
                  }
                  return null;
                },
              ),
            ),
           const SizedBox(width: 16),
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: BoxDecoration(
                color: const Color(0xFFFEF3C7),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: const Color(0xFFFDE68A)),
              ),
              child: Column(
                children: [
                 const Text(
                    '已邀请',
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF92400E),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                 const SizedBox(height: 4),
                  Text(
                    '${_selectedFriends.length}位好友',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF78350F),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    String? Function(String?)? validator,
    final int maxLines = 1,
    TextInputType? keyboardType,
  }) {
    return TextFormField(
      controller: controller,
      validator: validator,
      maxLines: maxLines,
      keyboardType: keyboardType,
      style: TypographyTokens.bodyLarge,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        filled: true,
        fillColor: ColorTokens.surface,
        prefixIcon: Icon(icon, color: ColorTokens.primary),
        border: OutlineInputBorder(
          borderRadius: ShapeTokens.borderRadiusLg,
          borderSide: BorderSide(color: ColorTokens.outline),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: ShapeTokens.borderRadiusLg,
          borderSide: BorderSide(color: ColorTokens.outline),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: ShapeTokens.borderRadiusLg,
          borderSide: BorderSide(color: ColorTokens.primary, width: 2),
        ),
        contentPadding: SpacingTokens.inputPadding,
      ),
    );
  }

  Widget _buildModernCreateButton() {
    return DSButton.primary(
      text: '发起约钓邀请',
      onPressed: _isSubmitting ? null : _createFriendOuting,
      icon: const Icon(Icons.send_rounded),
      isLoading: _isSubmitting,
      isFullWidth: true,
      size: DSButtonSize.large,
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('zh', 'CN'),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF6366F1),
              onPrimary: Theme.of(context).colorScheme.surface,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectTime(BuildContext context, bool isStartTime) async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: isStartTime ? _selectedStartTime : _selectedEndTime,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF6366F1),
              onPrimary: Theme.of(context).colorScheme.surface,
            ),
          ),
          child: child!,
        );
      },
    );
    if (picked != null) {
      setState(() {
        if (isStartTime) {
          _selectedStartTime = picked;
        } else {
          _selectedEndTime = picked;
        }
      });
    }
  }

  Future<void> _selectLocation() async {
    HapticFeedback.lightImpact();
    
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MapLocationPickerPage(
          initialLatitude: _selectedLatitude ?? 39.9042,
          initialLongitude: _selectedLongitude ?? 116.4074,
        ),
      ),
    );

    if (result != null) {
      setState(() {
        _selectedLatitude = result.latitude;
        _selectedLongitude = result.longitude;
        _locationController.text = result.formattedAddress.isNotEmpty ? result.formattedAddress : '已选择位置';
        _selectedProvince = result.province;
        _selectedCity = result.city;
        _selectedCounty = result.county;
      });
    }
  }

  Future<void> _createFriendOuting() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_locationController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
          content: Text('请选择钓点位置'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final viewModel = Provider.of<FishingPlanViewModel>(context, listen: false);

      final plan = FishingPlanCreateDTO(
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim().isEmpty ? '暂无描述' : _descriptionController.text.trim(),
        location: _locationController.text.trim(),
        latitude: _selectedLatitude,
        longitude: _selectedLongitude,
        province: _selectedProvince.isEmpty ? null : _selectedProvince,
        city: _selectedCity.isEmpty ? null : _selectedCity,
        county: _selectedCounty.isEmpty ? null : _selectedCounty,
        planDate: _startDateTime,
        timeRange: '${_selectedStartTime.format(context)} - ${_selectedEndTime.format(context)}',
        maxParticipants: int.parse(_maxParticipantsController.text),
        isPublic: false, // 好友约钓为私人活动
        planType: 'friend',
        activityType: 'leisure',
      );

      await viewModel.createPlan(plan);

      if (mounted) {
        HapticFeedback.mediumImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
               Icon(Icons.check_circle_rounded, color: Theme.of(context).colorScheme.surface),
               const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                     const Text('约钓邀请发送成功！'),
                      if (_selectedFriends.isNotEmpty)
                        Text(
                          '已邀请 ${_selectedFriends.map((f) => f.name).join('、')} 等${_selectedFriends.length}位好友',
                          style: const TextStyle(fontSize: 12),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            backgroundColor: const Color(0xFF10B981),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('创建失败: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

/// 好友信息模型
class FriendInfo {
  final String id;
  final String name;
  final String avatar;
  final bool isOnline;

 const FriendInfo({
    required this.id,
    required this.name,
    required this.avatar,
    required this.isOnline,
  });
}