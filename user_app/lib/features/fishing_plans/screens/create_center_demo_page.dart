import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';

/// 创建中心演示页面
/// 展示新的钓鱼计划创建功能
class CreateCenterDemoPage extends StatefulWidget {
  const CreateCenterDemoPage({super.key});

  @override
  State<CreateCenterDemoPage> createState() => _CreateCenterDemoPageState();
}

class _CreateCenterDemoPageState extends State<CreateCenterDemoPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<DemoFeature> _features = [
    DemoFeature(
      title: '创建中心',
      subtitle: '全新的三位一体创建入口',
      description: '提供个人计划、好友约钓、公开活动三种创建路径，满足不同用户需求',
      icon: Icons.dashboard_customize,
      color: ColorTokens.primary,
      route: '/create-center',
    ),
    DemoFeature(
      title: '快速创建（个人计划）',
      subtitle: '基于模板的智能创建',
      description: '10个精心设计的分类，每个分类都有专业的模板内容和装备建议',
      icon: Icons.flash_on,
      color: ColorTokens.success,
      route: '/fishing-plans/quick-create',
    ),
    DemoFeature(
      title: '好友组局',
      subtitle: '社交化钓鱼体验',
      description: '邀请好友、费用管理、分步创建，让约钓变得简单有趣',
      icon: Icons.group,
      color: ColorTokens.primary,
      route: '/fishing-plans/friend-outing',
    ),
    DemoFeature(
      title: '模板系统',
      subtitle: '专业的钓鱼模板库',
      description: '涵盖休闲垂钓、夜钓、路亚、黑坑等多个专业分类，提供完整的装备和注意事项',
      icon: Icons.auto_awesome,
      color: ColorTokens.secondary,
      route: '/fishing-plans/plan-confirmation/leisure',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '新功能演示',
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: ListView(
          padding: SpacingTokens.paddingLg,
          children: [
            // 顶部介绍
            Container(
              padding: SpacingTokens.paddingXl,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    ColorTokens.primary.withValues(alpha: 0.1),
                    ColorTokens.primaryContainer.withValues(alpha: 0.3),
                  ],
                ),
                borderRadius: ShapeTokens.borderRadiusXl,
                border: Border.all(
                  color: ColorTokens.primary.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: SpacingTokens.paddingMd,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [ColorTokens.primary, ColorTokens.primaryContainer],
                          ),
                          borderRadius: ShapeTokens.borderRadiusLg,
                        ),
                        child: const Icon(
                          Icons.celebration,
                          color: ColorTokens.onPrimary,
                          size: 24,
                        ),
                      ),
                      SizedBox(width: SpacingTokens.space4),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '钓鱼计划创建模块',
                              style: TypographyTokens.titleLarge.copyWith(
                                color: ColorTokens.primary,
                              ),
                            ),
                            SizedBox(height: SpacingTokens.space1),
                            Text(
                              '全新升级 v2.0',
                              style: TypographyTokens.bodyMedium.copyWith(
                                color: ColorTokens.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: SpacingTokens.space4),
                  Text(
                    '基于产品设计文档，全新的三位一体创建中心已经完成！支持个人计划、好友组局、公开活动三种创建路径，让不同类型的用户都能找到最适合的创建方式。',
                    style: TypographyTokens.bodyMedium.copyWith(
                      height: 1.5,
                      color: ColorTokens.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: SpacingTokens.space6),

            // 功能列表
            ...List.generate(_features.length, (index) {
              return TweenAnimationBuilder<double>(
                duration: Duration(milliseconds: 400 + index * 100),
                tween: Tween(begin: 0.0, end: 1.0),
                curve: Curves.easeOutBack,
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: Container(
                      margin: EdgeInsets.only(bottom: SpacingTokens.space4),
                      child: _buildFeatureCard(_features[index]),
                    ),
                  );
                },
              );
            }),

            SizedBox(height: SpacingTokens.space6),

            // 技术特色
            _buildTechHighlights(),
            SizedBox(height: SpacingTokens.space6),

            // 底部说明
            Container(
              padding: SpacingTokens.paddingLg,
              decoration: BoxDecoration(
                color: ColorTokens.surfaceContainer,
                borderRadius: ShapeTokens.borderRadiusLg,
                border: Border.all(color: ColorTokens.divider),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: ColorTokens.onSurfaceVariant),
                      SizedBox(width: SpacingTokens.space2),
                      Text(
                        '实现说明',
                        style: TypographyTokens.titleMedium.copyWith(
                          fontWeight: FontWeight.w600,
                          color: ColorTokens.onSurface,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: SpacingTokens.space3),
                  _buildInfoItem('✅', '严格遵循 CLAUDE.md 开发规范'),
                  _buildInfoItem('✅', '使用显式数据结构和 Lambda 表达式'),
                  _buildInfoItem('✅', '完整的模板系统和服务层'),
                  _buildInfoItem('✅', '响应式UI设计和动画效果'),
                  _buildInfoItem('✅', '支持状态管理和错误处理'),
                  _buildInfoItem('🔄', 'API集成已完成，可直接对接后端'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureCard(DemoFeature feature) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        try {
          context.navigateTo(feature.route);
        } catch (e) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('页面跳转: ${feature.route}'),
              backgroundColor: feature.color,
            ),
          );
        }
      },
      child: Container(
        padding: SpacingTokens.paddingLg,
        decoration: BoxDecoration(
          color: ColorTokens.surface,
          borderRadius: ShapeTokens.borderRadiusXl,
          boxShadow: ElevationTokens.elevation2,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 56,
                  height: 56,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        feature.color,
                        feature.color.withValues(alpha: 0.8)
                      ],
                    ),
                    borderRadius: ShapeTokens.borderRadiusLg,
                    boxShadow: ElevationTokens.elevation2,
                  ),
                  child: Icon(
                    feature.icon,
                    color: ColorTokens.onPrimary,
                    size: 28,
                  ),
                ),
                SizedBox(width: SpacingTokens.space4),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        feature.title,
                        style: TypographyTokens.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: ColorTokens.onSurface,
                        ),
                      ),
                      SizedBox(height: SpacingTokens.space1),
                      Text(
                        feature.subtitle,
                        style: TypographyTokens.bodyMedium.copyWith(
                          color: feature.color,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: ColorTokens.onSurfaceVariant,
                  size: 16,
                ),
              ],
            ),
            SizedBox(height: SpacingTokens.space4),
            Text(
              feature.description,
              style: TypographyTokens.bodyMedium.copyWith(
                color: ColorTokens.onSurfaceVariant,
                height: 1.4,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTechHighlights() {
    return Container(
      padding: SpacingTokens.paddingLg,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ColorTokens.primaryContainer.withValues(alpha: 0.3),
            ColorTokens.secondaryContainer.withValues(alpha: 0.3),
          ],
        ),
        borderRadius: ShapeTokens.borderRadiusXl,
        border: Border.all(color: ColorTokens.primary.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [ColorTokens.primary, ColorTokens.primary.withValues(alpha: 0.8)],
                  ),
                  borderRadius: ShapeTokens.borderRadiusMd,
                ),
                child: const Icon(
                  Icons.code,
                  color: ColorTokens.onPrimary,
                  size: 20,
                ),
              ),
              SizedBox(width: SpacingTokens.space3),
              Text(
                '技术亮点',
                style: TypographyTokens.titleMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: ColorTokens.primary,
                ),
              ),
            ],
          ),
          SizedBox(height: SpacingTokens.space4),
          _buildTechItem('模板驱动架构', '基于模板的智能创建流程，支持10个专业分类'),
          _buildTechItem('用户分层设计', '三种角色对应三种创建路径，精准满足不同需求'),
          _buildTechItem('渐进增强体验', '从简单到复杂，逐步引导用户使用高级功能'),
          _buildTechItem('状态管理优化', '使用Provider模式，支持加载状态和错误处理'),
          _buildTechItem('响应式UI设计', '精美的动画效果和交互体验'),
        ],
      ),
    );
  }

  Widget _buildTechItem(String title, String description) {
    return Padding(
      padding: EdgeInsets.only(bottom: SpacingTokens.space3),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: EdgeInsets.only(top: SpacingTokens.space2),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [ColorTokens.primary, ColorTokens.primary.withValues(alpha: 0.8)],
              ),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TypographyTokens.labelLarge.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ColorTokens.primary,
                  ),
                ),
                SizedBox(height: SpacingTokens.space1),
                Text(
                  description,
                  style: TypographyTokens.labelMedium.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoItem(String prefix, String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: SpacingTokens.space2),
      child: Row(
        children: [
          Text(
            prefix,
            style: TypographyTokens.bodyMedium.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(width: SpacingTokens.space2),
          Expanded(
            child: Text(
              text,
              style: TypographyTokens.bodyMedium.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class DemoFeature {
  final String title;
  final String subtitle;
  final String description;
  final IconData icon;
  final Color color;
  final String route;

  const DemoFeature({
    required this.title,
    required this.subtitle,
    required this.description,
    required this.icon,
    required this.color,
    required this.route,
  });
}
