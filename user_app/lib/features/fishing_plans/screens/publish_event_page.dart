import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/fishing_spots/screens/map_location_picker_page.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/modern_map_modal.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';

/// 发布公开活动页面 - 路径C：公开活动
/// 商业/社群组织者专用
class PublishEventPage extends StatefulWidget {
  const PublishEventPage({super.key});

  @override
  State<PublishEventPage> createState() => _PublishEventPageState();
}

class _PublishEventPageState extends State<PublishEventPage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TabController _tabController;

  // 表单控制器
  final _titleController = TextEditingController();
  final _locationController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _organizerController = TextEditingController();
  final _contactController = TextEditingController();
  final _costController = TextEditingController();
  final _minParticipantsController = TextEditingController(text: '5');
  final _maxParticipantsController = TextEditingController(text: '30');

  // 表单状态
  DateTime _selectedDate = DateTime.now().add(const Duration(days: 7));
  TimeOfDay _selectedStartTime = const TimeOfDay(hour: 6, minute: 0);
  TimeOfDay _selectedEndTime = const TimeOfDay(hour: 18, minute: 0);
  DateTime? _registrationDeadline;
  bool _isSubmitting = false;
  bool _requiresApproval = false;
  bool _isFreeEvent = true;

  // 位置数据
  double? _selectedLatitude;
  double? _selectedLongitude;
  String _selectedProvince = '';
  String _selectedCity = '';
  String _selectedCounty = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // 设置默认报名截止时间为活动前一天
    _registrationDeadline = _selectedDate.subtract(const Duration(days: 1));

    // 设置默认组织者信息
    _organizerController.text = '渔乐会钓鱼俱乐部';
    _contactController.text = '微信群: fishing2025';
  }

  @override
  void dispose() {
    _tabController.dispose();
    _titleController.dispose();
    _locationController.dispose();
    _descriptionController.dispose();
    _organizerController.dispose();
    _contactController.dispose();
    _costController.dispose();
    _minParticipantsController.dispose();
    _maxParticipantsController.dispose();
    super.dispose();
  }

  DateTime get _startDateTime {
    return DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedStartTime.hour,
      _selectedStartTime.minute,
    );
  }

  Future<void> _submitForm() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      final viewModel = context.read<FishingPlanViewModel>();

      // 构建活动描述
      final description = _buildEventDescription();

      final dto = FishingPlanCreateDTO(
        title: _titleController.text.trim(),
        location: _locationController.text.trim(),
        planDate: _startDateTime,
        timeRange:
            '${_selectedStartTime.format(context)} - ${_selectedEndTime.format(context)}',
        maxParticipants: int.parse(_maxParticipantsController.text),
        description: description,
        contactInfo: _contactController.text.trim(),
        isPublic: true,
        // 公开活动
        planType: 'public',
        latitude: _selectedLatitude,
        longitude: _selectedLongitude,
        province: _selectedProvince,
        city: _selectedCity,
        county: _selectedCounty,
        // TODO: 添加活动特有字段
        // registrationDeadline: _registrationDeadline,
        // minParticipants: int.tryParse(_minParticipantsController.text),
        // costPerPerson: _isFreeEvent ? 0 : double.tryParse(_costController.text),
        // activityImages: _activityImages,
      );

      await viewModel.createPlan(dto);

      if (mounted) {
        HapticFeedback.mediumImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('活动发布成功，等待审核'),
            backgroundColor: ColorTokens.success,
          ),
        );
        context.goUntilRoute('/fishing-plans');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('发布失败: $e'),
            backgroundColor: ColorTokens.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  String _buildEventDescription() {
    final buffer = StringBuffer()
      ..writeln('【活动类型】公开钓鱼活动')
      ..writeln('【主办方】${_organizerController.text}')
      ..writeln(
          '【活动时间】${DateFormat('yyyy年MM月dd日').format(_selectedDate)} ${_selectedStartTime.format(context)} - ${_selectedEndTime.format(context)}');
    if (_registrationDeadline != null) {
      buffer.writeln(
          '【报名截止】${DateFormat('yyyy年MM月dd日').format(_registrationDeadline!)}');
    }
    buffer
      ..writeln(
          '【参与人数】${_minParticipantsController.text}-${_maxParticipantsController.text}人')
      ..writeln(!_isFreeEvent && _costController.text.isNotEmpty
          ? '【活动费用】￥${_costController.text}/人'
          : '【活动费用】免费')
      ..writeln('')
      ..writeln(_descriptionController.text);
    return buffer.toString();
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '发布公开活动',
      useSliver: false,
      body: Column(
        children: [
          // Tab栏
          Container(
            margin: const EdgeInsets.all(SpacingTokens.space4),
            decoration: const BoxDecoration(
              color: ColorTokens.surfaceContainer,
              borderRadius: ShapeTokens.borderRadiusMd,
            ),
            child: TabBar(
              controller: _tabController,
              indicator: const BoxDecoration(
                color: ColorTokens.primary,
                borderRadius: ShapeTokens.borderRadiusLg,
              ),
              labelColor: ColorTokens.onPrimary,
              unselectedLabelColor: ColorTokens.onSurfaceVariant,
              indicatorSize: TabBarIndicatorSize.tab,
              dividerColor: Colors.transparent,
              tabs: const [
                Tab(text: '基本信息'),
                Tab(text: '报名设置'),
                Tab(text: '发布设置'),
              ],
            ),
          ),

          // Tab内容
          Expanded(
            child: Form(
              key: _formKey,
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildBasicInfoTab(),
                  _buildRegistrationTab(),
                  _buildPublishSettingsTab(),
                ],
              ),
            ),
          ),

          // 底部按钮
          _buildBottomButtons(),
        ],
      ),
    );
  }

  Widget _buildBasicInfoTab() {
    return ListView(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      children: [
        // 活动标题
        _buildTextField(
          controller: _titleController,
          label: '活动标题',
          hint: '给你的活动起个吸引人的名字',
          icon: Icons.title,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入活动标题';
            }
            if (value.trim().length < 4) {
              return '标题至少需要4个字符';
            }
            return null;
          },
        ),
        const SizedBox(height: SpacingTokens.space4),

        // 主办方信息
        _buildTextField(
          controller: _organizerController,
          label: '主办方',
          hint: '输入主办方名称',
          icon: Icons.business,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入主办方信息';
            }
            return null;
          },
        ),
        const SizedBox(height: SpacingTokens.space4),

        // 活动描述
        _buildTextField(
          controller: _descriptionController,
          label: '活动详情',
          hint: '详细描述活动内容、规则、注意事项等...',
          icon: Icons.description,
          maxLines: 5,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入活动详情';
            }
            if (value.trim().length < 20) {
              return '活动详情至少需要20个字符';
            }
            return null;
          },
        ),
        const SizedBox(height: SpacingTokens.space5),

        // 时间设置
        _buildTimeSettings(),
        const SizedBox(height: SpacingTokens.space4),

        // 地点设置
        _buildLocationSelector(),
        const SizedBox(height: SpacingTokens.space4),

        // 联系方式
        _buildTextField(
          controller: _contactController,
          label: '联系方式',
          hint: '微信群、QQ群、电话等',
          icon: Icons.contact_phone,
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return '请输入联系方式';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildRegistrationTab() {
    return ListView(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      children: [
        // 参与人数设置
        Row(
          children: [
            Expanded(
              child: _buildTextField(
                controller: _minParticipantsController,
                label: '最少人数',
                hint: '活动成行的最少人数',
                icon: Icons.people_outline,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入最少人数';
                  }
                  final num = int.tryParse(value);
                  if (num == null || num < 1) {
                    return '最少人数至少为1';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: SpacingTokens.space3),
            Expanded(
              child: _buildTextField(
                controller: _maxParticipantsController,
                label: '最多人数',
                hint: '活动容纳的最多人数',
                icon: Icons.people,
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return '请输入最多人数';
                  }
                  final num = int.tryParse(value);
                  if (num == null || num < 1) {
                    return '最多人数至少为1';
                  }
                  final minNum =
                      int.tryParse(_minParticipantsController.text) ?? 0;
                  if (num <= minNum) {
                    return '最多人数必须大于最少人数';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
        const SizedBox(height: SpacingTokens.space4),

        // 报名截止时间
        _buildRegistrationDeadline(),
        const SizedBox(height: SpacingTokens.space5),

        // 费用设置
        _buildFeeSettings(),
        const SizedBox(height: SpacingTokens.space5),

        // 报名审核
        _buildApprovalSettings(),
      ],
    );
  }

  Widget _buildPublishSettingsTab() {
    return ListView(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      children: [
        // 活动图片上传
        _buildImageUpload(),
        const SizedBox(height: SpacingTokens.space5),

        // 活动标签
        _buildEventTags(),
        const SizedBox(height: SpacingTokens.space5),

        // 发布提醒
        _buildPublishReminder(),
      ],
    );
  }

  Widget _buildTimeSettings() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusMd,
        border: Border.all(color: ColorTokens.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.schedule, color: ColorTokens.primary),
              SizedBox(width: SpacingTokens.space2),
              Text(
                '活动时间',
                style: TypographyTokens.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space4),

          // 日期选择
          InkWell(
            onTap: () async {
              final date = await showDatePicker(
                context: context,
                initialDate: _selectedDate,
                firstDate: DateTime.now().add(const Duration(days: 1)),
                lastDate: DateTime.now().add(const Duration(days: 365)),
              );
              if (date != null) {
                setState(() {
                  _selectedDate = date;
                  // 自动更新报名截止时间
                  _registrationDeadline =
                      date.subtract(const Duration(days: 1));
                });
              }
            },
            borderRadius: ShapeTokens.borderRadiusMd,
            child: Container(
              padding: const EdgeInsets.all(SpacingTokens.space3),
              decoration: BoxDecoration(
                color: ColorTokens.surfaceContainer,
                borderRadius: ShapeTokens.borderRadiusMd,
                border: Border.all(color: ColorTokens.outlineVariant),
              ),
              child: Row(
                children: [
                  const Icon(Icons.calendar_today,
                      size: 20, color: ColorTokens.onSurfaceVariant),
                  const SizedBox(width: SpacingTokens.space2),
                  Text(
                    DateFormat('yyyy年MM月dd日 EEEE').format(_selectedDate),
                    style: TypographyTokens.titleMedium,
                  ),
                  const Spacer(),
                  const Icon(Icons.edit,
                      size: 16, color: ColorTokens.onSurfaceVariant),
                ],
              ),
            ),
          ),
          const SizedBox(height: 12),

          // 开始和结束时间
          Row(
            children: [
              Expanded(
                child: _buildTimeButton(
                  '开始时间',
                  _selectedStartTime.format(context),
                  () async {
                    final time = await showTimePicker(
                      context: context,
                      initialTime: _selectedStartTime,
                    );
                    if (time != null) {
                      setState(() => _selectedStartTime = time);
                    }
                  },
                ),
              ),
              const SizedBox(width: SpacingTokens.space3),
              Expanded(
                child: _buildTimeButton(
                  '结束时间',
                  _selectedEndTime.format(context),
                  () async {
                    final time = await showTimePicker(
                      context: context,
                      initialTime: _selectedEndTime,
                    );
                    if (time != null) {
                      setState(() => _selectedEndTime = time);
                    }
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTimeButton(String label, String time, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: ShapeTokens.borderRadiusSm,
      child: Container(
        padding: const EdgeInsets.all(SpacingTokens.space3),
        decoration: BoxDecoration(
          color: ColorTokens.surfaceContainer,
          borderRadius: ShapeTokens.borderRadiusSm,
          border: Border.all(color: ColorTokens.outline),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TypographyTokens.bodySmall.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: SpacingTokens.space1),
            Text(
              time,
              style: TypographyTokens.titleMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegistrationDeadline() {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: _registrationDeadline ??
              _selectedDate.subtract(const Duration(days: 1)),
          firstDate: DateTime.now(),
          lastDate: _selectedDate.subtract(const Duration(hours: 1)),
        );
        if (date != null) {
          setState(() => _registrationDeadline = date);
        }
      },
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(SpacingTokens.space4),
        decoration: BoxDecoration(
          border: Border.all(color: ColorTokens.outline),
          borderRadius: BorderRadius.circular(16),
          color: ColorTokens.surface,
        ),
        child: Row(
          children: [
            const Icon(Icons.event_busy, color: Color(0xFF4CAF50)),
            const SizedBox(width: SpacingTokens.space3),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '报名截止时间',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  Text(
                    _registrationDeadline != null
                        ? DateFormat('yyyy年MM月dd日')
                            .format(_registrationDeadline!)
                        : '点击设置截止时间',
                    style: const TextStyle(
                      fontSize: 14,
                      color: ColorTokens.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.edit, size: 16, color: Colors.grey[500]),
          ],
        ),
      ),
    );
  }

  Widget _buildFeeSettings() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusMd,
        border: Border.all(color: ColorTokens.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.monetization_on, color: Color(0xFF4CAF50)),
              SizedBox(width: SpacingTokens.space2),
              Text(
                '费用设置',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space4),

          // 免费/收费切换
          Row(
            children: [
              Expanded(
                child: RadioListTile<bool>(
                  title: const Text('免费活动', style: TextStyle(fontSize: 14)),
                  value: true,
                  groupValue: _isFreeEvent,
                  onChanged: (value) => setState(() => _isFreeEvent = value!),
                  activeColor: const Color(0xFF4CAF50),
                ),
              ),
              Expanded(
                child: RadioListTile<bool>(
                  title: const Text('收费活动', style: TextStyle(fontSize: 14)),
                  value: false,
                  groupValue: _isFreeEvent,
                  onChanged: (value) => setState(() => _isFreeEvent = value!),
                  activeColor: const Color(0xFF4CAF50),
                ),
              ),
            ],
          ),

          if (!_isFreeEvent) ...[
            const SizedBox(height: 12),
            _buildTextField(
              controller: _costController,
              label: '人均费用',
              hint: '输入每人需要支付的费用',
              icon: Icons.attach_money,
              keyboardType: TextInputType.number,
              suffixText: '元',
              validator: (value) {
                if (!_isFreeEvent && (value == null || value.isEmpty)) {
                  return '请输入活动费用';
                }
                return null;
              },
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildApprovalSettings() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        border: Border.all(color: ColorTokens.outline),
        borderRadius: ShapeTokens.borderRadiusLg,
        color: ColorTokens.surface,
      ),
      child: Row(
        children: [
          const Icon(Icons.verified_user, color: ColorTokens.primary),
          const SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '报名需要审核',
                  style: TypographyTokens.titleMedium,
                ),
                Text(
                  '开启后需要手动审核每个报名申请',
                  style: TypographyTokens.bodyMedium
                      .copyWith(color: ColorTokens.onSurfaceVariant),
                ),
              ],
            ),
          ),
          Switch(
            value: _requiresApproval,
            onChanged: (value) => setState(() => _requiresApproval = value),
            activeColor: ColorTokens.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildImageUpload() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusMd,
        border: Border.all(color: ColorTokens.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.photo_library, color: ColorTokens.primary),
              SizedBox(width: SpacingTokens.space2),
              Text(
                '活动图片',
                style: TypographyTokens.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space4),

          // 图片上传区域
          GestureDetector(
            onTap: () {
              // TODO: 实现图片上传功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('图片上传功能开发中...')),
              );
            },
            child: Container(
              height: 120,
              decoration: BoxDecoration(
                color: ColorTokens.surfaceContainer,
                borderRadius: ShapeTokens.borderRadiusMd,
                border: Border.all(
                  color: ColorTokens.outline,
                  style: BorderStyle.solid,
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.add_photo_alternate,
                      size: 40,
                      color: ColorTokens.onSurfaceVariant,
                    ),
                    const SizedBox(height: SpacingTokens.space2),
                    Text(
                      '点击上传活动封面图片',
                      style: TypographyTokens.bodyMedium.copyWith(
                        color: ColorTokens.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      '建议尺寸 16:9，最多3张',
                      style: TypographyTokens.bodySmall.copyWith(
                        color: ColorTokens.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEventTags() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.surface,
        borderRadius: ShapeTokens.borderRadiusMd,
        border: Border.all(color: ColorTokens.outline),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.local_offer, color: ColorTokens.primary),
              SizedBox(width: SpacingTokens.space2),
              Text(
                '活动标签',
                style: TypographyTokens.titleMedium,
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space4),
          Wrap(
            spacing: SpacingTokens.space2,
            runSpacing: SpacingTokens.space2,
            children: [
              '黑坑',
              '野钓',
              '路亚',
              '夜钓',
              '海钓',
              '竞技',
              '亲子',
              '团建',
              '教学',
              '比赛'
            ].map((tag) {
              return Container(
                padding: const EdgeInsets.symmetric(
                    horizontal: SpacingTokens.space3,
                    vertical: SpacingTokens.space2),
                decoration: BoxDecoration(
                  color: ColorTokens.surfaceContainer,
                  borderRadius: ShapeTokens.borderRadiusLg,
                  border: Border.all(color: ColorTokens.outline),
                ),
                child: Text(
                  tag,
                  style: TypographyTokens.bodySmall.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildPublishReminder() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: ColorTokens.warningContainer,
        borderRadius: ShapeTokens.borderRadiusMd,
        border: Border.all(color: ColorTokens.warning),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.info_outline, color: ColorTokens.warning),
              const SizedBox(width: 8),
              Text(
                '发布说明',
                style: TypographyTokens.titleMedium.copyWith(
                  color: ColorTokens.onWarningContainer,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            '• 公开活动需要管理员审核后才能显示\n• 活动发布后将在首页和活动列表中展示\n• 请确保活动信息真实有效\n• 违规活动将被删除并可能被限制发布权限',
            style: TypographyTokens.bodyMedium.copyWith(
              color: ColorTokens.onWarningContainer,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomButtons() {
    return Container(
      padding: const EdgeInsets.all(SpacingTokens.space5),
      child: Row(
        children: [
          if (_tabController.index > 0)
            Expanded(
              flex: 1,
              child: OutlinedButton(
                onPressed: () {
                  _tabController.animateTo(_tabController.index - 1);
                },
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                      vertical: SpacingTokens.space4),
                  side: const BorderSide(color: ColorTokens.primary),
                ),
                child: Text(
                  '上一步',
                  style: TypographyTokens.labelLarge.copyWith(
                    color: ColorTokens.primary,
                  ),
                ),
              ),
            ),
          if (_tabController.index > 0) const SizedBox(width: 12),
          Expanded(
            flex: 2,
            child: Container(
              height: 56,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [ColorTokens.primary, ColorTokens.primaryContainer],
                ),
                borderRadius: ShapeTokens.borderRadiusFull,
                boxShadow: [
                  BoxShadow(
                    color: ColorTokens.primary.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: MaterialButton(
                onPressed: _isSubmitting
                    ? null
                    : _tabController.index < 2
                        ? () {
                            if (_tabController.index == 0) {
                              // 验证基本信息
                              if (_formKey.currentState!.validate()) {
                                _tabController.animateTo(1);
                              }
                            } else {
                              _tabController.animateTo(2);
                            }
                          }
                        : _submitForm,
                shape: const RoundedRectangleBorder(
                  borderRadius: ShapeTokens.borderRadiusFull,
                ),
                child: _isSubmitting
                    ? const SizedBox(
                        width: 24,
                        height: 24,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                              ColorTokens.onPrimary),
                        ),
                      )
                    : Text(
                        _tabController.index < 2 ? '下一步' : '发布活动',
                        style: TypographyTokens.labelLarge.copyWith(
                          color: ColorTokens.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    final int maxLines = 1,
    TextInputType? keyboardType,
    String? suffixText,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      maxLines: maxLines,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        suffixText: suffixText,
        prefixIcon: Icon(icon, color: ColorTokens.primary),
        border: const OutlineInputBorder(
          borderRadius: ShapeTokens.borderRadiusLg,
          borderSide: BorderSide(color: ColorTokens.outline),
        ),
        enabledBorder: const OutlineInputBorder(
          borderRadius: ShapeTokens.borderRadiusLg,
          borderSide: BorderSide(color: ColorTokens.outline),
        ),
        focusedBorder: const OutlineInputBorder(
          borderRadius: ShapeTokens.borderRadiusLg,
          borderSide: BorderSide(color: ColorTokens.primary, width: 2),
        ),
        filled: true,
        fillColor: ColorTokens.surface,
      ),
    );
  }

  Widget _buildLocationSelector() {
    return Row(
      children: [
        Expanded(
          child: TextFormField(
            controller: _locationController,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入或选择活动地点';
              }
              return null;
            },
            decoration: const InputDecoration(
              labelText: '活动地点',
              hintText: '输入详细地址或选择位置',
              prefixIcon: Icon(Icons.location_on, color: ColorTokens.primary),
              border: OutlineInputBorder(
                borderRadius: ShapeTokens.borderRadiusLg,
                borderSide: BorderSide(color: ColorTokens.outline),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: ShapeTokens.borderRadiusLg,
                borderSide: BorderSide(color: ColorTokens.outline),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: ShapeTokens.borderRadiusLg,
                borderSide: BorderSide(color: ColorTokens.primary, width: 2),
              ),
            ),
          ),
        ),
        const SizedBox(width: SpacingTokens.space3),
        GestureDetector(
          onTap: () async {
            try {
              final result = await showModalBottomSheet<LocationResult>(
                context: context,
                isScrollControlled: true,
                backgroundColor: Colors.transparent,
                builder: (context) => ModernMapModal(
                  initialLatitude: _selectedLatitude ?? 0,
                  initialLongitude: _selectedLongitude ?? 0,
                  initialFormattedAddress: _locationController.text,
                ),
              );

              if (result != null) {
                setState(() {
                  _locationController.text = result.formattedAddress;
                  _selectedLatitude = result.latitude;
                  _selectedLongitude = result.longitude;
                  _selectedProvince = result.province;
                  _selectedCity = result.city;
                  _selectedCounty = result.county;
                });
              }
            } catch (e) {
              debugPrint('地图选择错误: $e');
            }
          },
          child: Container(
            height: 56,
            width: 56,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [ColorTokens.primary, ColorTokens.primaryContainer],
              ),
              borderRadius: ShapeTokens.borderRadiusLg,
            ),
            child: const Icon(Icons.map_rounded,
                color: ColorTokens.onPrimary, size: 24),
          ),
        ),
      ],
    );
  }
}
