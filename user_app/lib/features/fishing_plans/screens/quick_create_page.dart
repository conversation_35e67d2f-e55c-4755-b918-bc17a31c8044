import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/features/fishing_plans/view_models/fishing_plan_view_model.dart';
import 'package:user_app/features/fishing_plans/models/fishing_plan.dart';
import 'package:user_app/features/fishing_spots/widgets/create_spot/modern_map_modal.dart';
import 'package:user_app/features/fishing_spots/screens/map_location_picker_page.dart';
import 'package:intl/intl.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

/// 快速创建页面 - 路径A：个人计划
/// 第一步：意图选择页面
class QuickCreatePage extends StatefulWidget {
 const QuickCreatePage({super.key});

  @override
  State<QuickCreatePage> createState() => _QuickCreatePageState();
}

class _QuickCreatePageState extends State<QuickCreatePage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  
  // 10个意图分类
  final List<IntentCategory> _categories = [
    IntentCategory(
      id: 'leisure',
      name: '休闲垂钓',
      icon: Icons.weekend,
      tagline: '放下烦恼，享受属于自己的宁静时光',
      keywords: ['放松', '解压', '野钓'],
      color: ColorTokens.success,
      templateCount: 3,
    ),
    IntentCategory(
      id: 'seasonal',
      name: '季节特色',
      icon: Icons.eco,
      tagline: '顺应季节，捕获时令鱼种',
      keywords: ['时令', '季节性', '应季'],
      color: ColorTokens.warning,
      templateCount: 4,
    ),
    IntentCategory(
      id: 'challenge',
      name: '挑战自我',
      icon: Icons.emoji_events,
      tagline: '突破极限，征服大物',
      keywords: ['挑战', '大物', '竞技'],
      color: ColorTokens.error,
      templateCount: 2,
    ),
    IntentCategory(
      id: 'night',
      name: '夜钓专场',
      icon: Icons.nightlight_round,
      tagline: '夜幕下的垂钓，别有一番滋味',
      keywords: ['夜钓', '夜晚', '安静'],
      color: ColorTokens.primary,
      templateCount: 2,
    ),
    IntentCategory(
      id: 'lure',
      name: '路亚专项',
      icon: Icons.waves,
      tagline: '动感路亚，体验不一样的钓鱼乐趣',
      keywords: ['路亚', '拟饵', '运动'],
      color: ColorTokens.secondary,
      templateCount: 3,
    ),
    IntentCategory(
      id: 'blackpit',
      name: '黑坑竞技',
      icon: Icons.pool,
      tagline: '技术与策略的较量',
      keywords: ['黑坑', '竞技', '台钓'],
      color: ColorTokens.tertiary,
      templateCount: 2,
    ),
    IntentCategory(
      id: 'sea',
      name: '海钓远征',
      icon: Icons.sailing,
      tagline: '征服大海，收获惊喜',
      keywords: ['海钓', '船钓', '海洋'],
      color: ColorTokens.primary,
      templateCount: 3,
    ),
    IntentCategory(
      id: 'ice',
      name: '冰钓探险',
      icon: Icons.ac_unit,
      tagline: '冰天雪地，独特体验',
      keywords: ['冰钓', '冬季', '探险'],
      color: ColorTokens.secondary,
      templateCount: 1,
    ),
    IntentCategory(
      id: 'fly',
      name: '飞蝇钓法',
      icon: Icons.flight,
      tagline: '优雅的钓鱼艺术',
      keywords: ['飞蝇', '技术', '艺术'],
      color: ColorTokens.tertiary,
      templateCount: 2,
    ),
    IntentCategory(
      id: 'custom',
      name: '自定义',
      icon: Icons.edit,
      tagline: '创建属于你的独特计划',
      keywords: ['自定义', '个性化', '创意'],
      color: ColorTokens.primary,
      templateCount: 0,
    ),
  ];

  @override
  void initState() {
    super.initState();
    
    _animationController = AnimationController(
      duration: MotionTokens.durationSlow,
      vsync: this,
    );
    
    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '选择计划类型',
      useSliver: false,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // 顶部说明
            Container(
              padding: SpacingTokens.paddingLg,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '你想要什么样的钓鱼体验？',
                    style: TypographyTokens.headlineSmall.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                 const SizedBox(height: SpacingTokens.space2),
                  Text(
                    '选择一个分类，我们将为你推荐合适的模板',
                    style: TypographyTokens.bodyMedium.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            
            // 分类网格
            Expanded(
              child: GridView.builder(
                padding: EdgeInsets.fromLTRB(20, 0, 20, 20),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  childAspectRatio: 0.85,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: _categories.length,
                itemBuilder: (context, index) {
                  final category = _categories[index];
                  return _buildCategoryCard(category, index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(IntentCategory category, int index) {
    // 创建交错动画效果
    final delay = index * 50.0;
    
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 400 + delay.toInt()),
      tween: Tween(begin: 0.0, end: 1.0),
      curve: Curves.easeOutBack,
      builder: (context, value, child) {
        return Transform.scale(
          scale: value,
          child: GestureDetector(
            onTap: () {
              HapticFeedback.lightImpact();
              _navigateToPlanConfirmation(category);
            },
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: category.color.withValues(alpha: 0.1),
                    blurRadius: 20,
                    offset: Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // 主要内容
                  Padding(
                    padding: SpacingTokens.paddingMd,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 图标
                        Container(
                          width: 48,
                          height: 48,
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                category.color,
                                category.color.withValues(alpha: 0.8),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(14),
                          ),
                          child: Icon(
                            category.icon,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                       const SizedBox(height: 12),
                        
                        // 分类名称
                        Text(
                          category.name,
                          style: TypographyTokens.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                       const SizedBox(height: 4),
                        
                        // 标语
                        Expanded(
                          child: Text(
                            category.tagline,
                            style: TypographyTokens.bodySmall.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                              height: 1.3,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        
                        // 关键词标签
                        Wrap(
                          spacing: 4,
                          runSpacing: 4,
                          children: category.keywords.map((keyword) {
                            return Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: category.color.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                '#$keyword',
                                style: TextStyle(
                                  fontSize: 11,
                                  color: category.color,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                  
                  // 模板数量提示（如果有多个模板）
                  if (category.templateCount > 1)
                    Positioned(
                      right: 12,
                      bottom: 12,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.black87,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              '${category.templateCount}个模板',
                              style: const TextStyle(
                                fontSize: 11,
                                color: Colors.white,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                           const SizedBox(width: 2),
                           const Icon(
                              Icons.arrow_forward_ios,
                              size: 10,
                              color: Colors.white,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  void _navigateToPlanConfirmation(IntentCategory category) {
    // 如果是自定义类型，直接跳转到空白创建页面
    if (category.id == 'custom') {
      context.navigateTo('/fishing-plans/create-custom');
      return;
    }
    
    // 如果有多个模板，先跳转到模板选择页面
    if (category.templateCount > 1) {
      context.navigateTo('/fishing-plans/template-selection/${category.id}');
      return;
    }
    
    // 否则直接跳转到计划确认页面
    context.navigateTo('/fishing-plans/plan-confirmation/${category.id}');
  }
}

/// 意图分类模型
class IntentCategory {
  final String id;
  final String name;
  final IconData icon;
  final String tagline;
  final List<String> keywords;
  final Color color;
  final int templateCount;

 const IntentCategory({
    required this.id,
    required this.name,
    required this.icon,
    required this.tagline,
    required this.keywords,
    required this.color,
    required this.templateCount,
  });
}