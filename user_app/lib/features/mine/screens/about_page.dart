import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class AboutPage extends StatefulWidget {
  const AboutPage({super.key});

  @override
  State<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> with TickerProviderStateMixin {
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _floatingController;
  late AnimationController _pulseController;
  late AnimationController _rotateController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _floatingAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotateAnimation;

  final List<AnimationController> _itemAnimationControllers = [];

  final String _currentVersion = '1.2.0';
  final String _buildNumber = '20240115';
  bool _isCheckingUpdate = false;

  // 增强的状态管理
  String? _latestVersion;
  String? _updateDescription;

  // 功能特性列表
  final List<FeatureItem> _features = [
    const FeatureItem(
      icon: Icons.location_on,
      title: '钓点推荐',
      description: '发现周边优质钓点，查看详细信息',
      color: ColorTokens.success,
    ),
    const FeatureItem(
      icon: Icons.photo_camera,
      title: '动态分享',
      description: '记录精彩钓鱼时光，分享渔获喜悦',
      color: ColorTokens.warning,
    ),
    const FeatureItem(
      icon: Icons.people,
      title: '社交互动',
      description: '结识志同道合的钓友，交流经验',
      color: ColorTokens.primary,
    ),
    const FeatureItem(
      icon: Icons.book,
      title: '技巧学习',
      description: '掌握专业钓鱼知识，提升技术',
      color: ColorTokens.secondary,
    ),
  ];

  // 团队成员
  final List<TeamMember> _teamMembers = [
    const TeamMember(
      name: '产品设计',
      role: 'UI/UX Designer',
      avatar: Icons.design_services,
      color: ColorTokens.primary,
    ),
    const TeamMember(
      name: '开发团队',
      role: 'Development Team',
      avatar: Icons.code,
      color: ColorTokens.secondary,
    ),
    const TeamMember(
      name: '运营团队',
      role: 'Operations Team',
      avatar: Icons.support_agent,
      color: ColorTokens.warning,
    ),
  ];

  @override
  void initState() {
    super.initState();
    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    _rotateController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _floatingAnimation = Tween<double>(
      begin: -15,
      end: 15,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 2 * 3.14159,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.linear,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _floatingController.dispose();
    _pulseController.dispose();
    _rotateController.dispose();
    for (final controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.surfaceVariant,
      body: CustomScrollView(
        slivers: [
          // 现代化的 AppBar
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                children: [
                  // 渐变背景
                  Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          ColorTokens.primary,
                          ColorTokens.secondary,
                        ],
                      ),
                    ),
                  ),
                  // 装饰元素
                  Positioned(
                    top: -100,
                    right: -100,
                    child: AnimatedBuilder(
                      animation: _rotateAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _rotateAnimation.value,
                          child: Container(
                            width: 300,
                            height: 300,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: ColorTokens.onSurface
                                    .withValues(alpha: 0.1),
                                width: 2,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    bottom: -50,
                    left: -50,
                    child: AnimatedBuilder(
                      animation: _floatingAnimation,
                      builder: (context, child) {
                        return Transform.translate(
                          offset: Offset(0, _floatingAnimation.value),
                          child: Container(
                            width: 200,
                            height: 200,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color:
                                  ColorTokens.onSurface.withValues(alpha: 0.05),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  // Logo和标题
                  SafeArea(
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SlideTransition(
                            position: _slideAnimation,
                            child: AnimatedBuilder(
                              animation: _pulseAnimation,
                              builder: (context, child) {
                                return Transform.scale(
                                  scale: _pulseAnimation.value,
                                  child: Container(
                                    width: 100,
                                    height: 100,
                                    decoration: const BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: Colors.white,
                                      boxShadow: [
                                        BoxShadow(
                                          color: ColorTokens.shadow,
                                          blurRadius: 20,
                                          offset: Offset(0, 10),
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.phishing,
                                      size: 60,
                                      color: ColorTokens.primary,
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          const SizedBox(height: SpacingTokens.space5),
                          FadeTransition(
                            opacity: _fadeAnimation,
                            child: const Text(
                              '钓鱼之旅',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: TypographyTokens.sizeDisplaySmall,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 2,
                              ),
                            ),
                          ),
                          const SizedBox(height: SpacingTokens.space2),
                          FadeTransition(
                            opacity: _fadeAnimation,
                            child: Container(
                              padding: SpacingTokens.paddingMd,
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: ShapeTokens.borderRadiusXl,
                                border: Border.all(
                                  color: Colors.white.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Text(
                                'v$_currentVersion',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: TypographyTokens.sizeBodyLarge,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            leading: IconButton(
              icon: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: ShapeTokens.borderRadiusMd,
                ),
                child: const Icon(Icons.arrow_back_ios, color: Colors.white),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                context.pop();
              },
            ),
            actions: [
              IconButton(
                icon: Container(
                  padding: SpacingTokens.paddingSm,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: ShapeTokens.borderRadiusMd,
                  ),
                  child: const Icon(Icons.share, color: Colors.white),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  _shareApp();
                },
              ),
            ],
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(30),
              child: Container(
                height: 30,
                decoration: const BoxDecoration(
                  color: ColorTokens.surfaceVariant,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(SpacingTokens.space6),
                    topRight: Radius.circular(SpacingTokens.space6),
                  ),
                ),
              ),
            ),
          ),

          // 内容区域
          SliverPadding(
            padding: SpacingTokens.paddingLg,
            sliver: SliverList(
              delegate: SliverChildListDelegate([
                // 版本信息卡片
                _buildModernVersionCard(),
                const SizedBox(height: SpacingTokens.space5),

                // 功能特性
                _buildModernFeatures(),
                const SizedBox(height: SpacingTokens.space5),

                // 团队信息
                _buildModernTeam(),
                const SizedBox(height: SpacingTokens.space5),

                // 联系方式
                _buildModernContact(),
                const SizedBox(height: SpacingTokens.space5),

                // 底部信息
                _buildModernFooter(),
                const SizedBox(height: 40),
              ]),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernVersionCard() {
    return ScaleTransition(
      scale: _scaleAnimation,
      child: Container(
        padding: SpacingTokens.paddingXl,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              ColorTokens.surface,
            ],
          ),
          borderRadius: ShapeTokens.borderRadiusXl,
          boxShadow: ElevationTokens.shadow3(context),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: SpacingTokens.paddingMd,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [ColorTokens.primary, ColorTokens.secondary],
                    ),
                    borderRadius: ShapeTokens.borderRadiusLg,
                    boxShadow: [
                      BoxShadow(
                        color: ColorTokens.primary.withValues(alpha: 0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.info_outline,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '当前版本',
                        style: TextStyle(
                          fontSize: TypographyTokens.sizeLabelLarge,
                          color: Colors.grey,
                        ),
                      ),
                      Text(
                        'v$_currentVersion (Build $_buildNumber)',
                        style: const TextStyle(
                          fontSize: TypographyTokens.sizeTitleMedium,
                          fontWeight: FontWeight.bold,
                          color: ColorTokens.onSurface,
                        ),
                      ),
                    ],
                  ),
                ),
                GestureDetector(
                  onTap: _checkForUpdate,
                  child: Container(
                    padding: SpacingTokens.paddingMd,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [ColorTokens.primary, ColorTokens.secondary],
                      ),
                      borderRadius: ShapeTokens.borderRadiusXl,
                      boxShadow: [
                        BoxShadow(
                          color: ColorTokens.primary.withValues(alpha: 0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        if (_isCheckingUpdate)
                          const SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        else
                          const Icon(
                            Icons.refresh,
                            color: Colors.white,
                            size: 16,
                          ),
                        const SizedBox(width: 6),
                        Text(
                          _isCheckingUpdate ? '检查中...' : '检查更新',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: TypographyTokens.sizeLabelLarge,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: SpacingTokens.space5),
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: BoxDecoration(
                color: ColorTokens.primary.withValues(alpha: 0.05),
                borderRadius: ShapeTokens.borderRadiusLg,
                border: Border.all(
                  color: ColorTokens.primary.withValues(alpha: 0.1),
                ),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.new_releases,
                    color: ColorTokens.primary,
                    size: 20,
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '最新功能：优化了钓点推荐算法，提升用户体验',
                      style: TextStyle(
                        fontSize: TypographyTokens.sizeLabelMedium,
                        color: ColorTokens.onSurface,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernFeatures() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '核心功能',
          style: TextStyle(
            fontSize: TypographyTokens.sizeTitleLarge,
            fontWeight: FontWeight.bold,
            color: ColorTokens.onSurface,
          ),
        ),
        const SizedBox(height: SpacingTokens.space4),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.2,
          ),
          itemCount: _features.length,
          itemBuilder: (context, index) {
            // 创建动画控制器
            if (_itemAnimationControllers.length <= index) {
              final controller = AnimationController(
                duration: Duration(milliseconds: 600 + (index * 100)),
                vsync: this,
              );
              _itemAnimationControllers.add(controller);
              controller.forward();
            }

            final feature = _features[index];

            return AnimatedBuilder(
              animation: _itemAnimationControllers[index],
              builder: (context, child) {
                final scaleAnimation = Tween<double>(
                  begin: 0.8,
                  end: 1.0,
                ).animate(CurvedAnimation(
                  parent: _itemAnimationControllers[index],
                  curve: Curves.elasticOut,
                ));

                return ScaleTransition(
                  scale: scaleAnimation,
                  child: child,
                );
              },
              child: Container(
                padding: SpacingTokens.paddingLg,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      feature.color.withValues(alpha: 0.1),
                      feature.color.withValues(alpha: 0.05),
                    ],
                  ),
                  borderRadius: ShapeTokens.borderRadiusXl,
                  border: Border.all(
                    color: feature.color.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: SpacingTokens.paddingMd,
                      decoration: BoxDecoration(
                        color: feature.color.withValues(alpha: 0.1),
                        borderRadius: ShapeTokens.borderRadiusMd,
                      ),
                      child: Icon(
                        feature.icon,
                        color: feature.color,
                        size: 28,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Text(
                      feature.title,
                      style: const TextStyle(
                        fontSize: TypographyTokens.sizeBodyLarge,
                        fontWeight: FontWeight.bold,
                        color: ColorTokens.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      feature.description,
                      style: const TextStyle(
                        fontSize: TypographyTokens.sizeLabelMedium,
                        color: ColorTokens.onSurfaceVariant,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildModernTeam() {
    return Container(
      padding: SpacingTokens.paddingXl,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            ColorTokens.surface,
          ],
        ),
        borderRadius: ShapeTokens.borderRadiusXl,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [ColorTokens.secondary, ColorTokens.primary],
                  ),
                  borderRadius: ShapeTokens.borderRadiusMd,
                ),
                child: const Icon(
                  Icons.groups,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '开发团队',
                style: TextStyle(
                  fontSize: TypographyTokens.sizeTitleMedium,
                  fontWeight: FontWeight.bold,
                  color: ColorTokens.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space5),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: _teamMembers.map((member) {
              return Column(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          member.color.withValues(alpha: 0.8),
                          member.color,
                        ],
                      ),
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: member.color.withValues(alpha: 0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      member.avatar,
                      color: Colors.white,
                      size: 28,
                    ),
                  ),
                  const SizedBox(height: SpacingTokens.space2),
                  Text(
                    member.name,
                    style: const TextStyle(
                      fontSize: TypographyTokens.sizeLabelLarge,
                      fontWeight: FontWeight.w600,
                      color: ColorTokens.onSurface,
                    ),
                  ),
                  Text(
                    member.role,
                    style: const TextStyle(
                      fontSize: TypographyTokens.sizeLabelSmall,
                      color: ColorTokens.onSurfaceVariant,
                    ),
                  ),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildModernContact() {
    return Container(
      padding: SpacingTokens.paddingXl,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            ColorTokens.surface,
          ],
        ),
        borderRadius: ShapeTokens.borderRadiusXl,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [ColorTokens.success, Color(0xFF8BC34A)],
                  ),
                  borderRadius: ShapeTokens.borderRadiusMd,
                ),
                child: const Icon(
                  Icons.contact_support,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                '联系我们',
                style: TextStyle(
                  fontSize: TypographyTokens.sizeTitleMedium,
                  fontWeight: FontWeight.bold,
                  color: ColorTokens.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: SpacingTokens.space5),
          _buildContactItem(
            icon: Icons.email,
            title: '邮箱',
            value: '<EMAIL>',
            onTap: () => _launchEmail(),
          ),
          const SizedBox(height: 12),
          _buildContactItem(
            icon: Icons.public,
            title: '官网',
            value: 'www.fishingtrip.com',
            onTap: () => _launchWebsite(),
          ),
          const SizedBox(height: 12),
          _buildContactItem(
            icon: Icons.phone,
            title: '客服热线',
            value: '************',
            onTap: () => _makePhoneCall(),
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem({
    required IconData icon,
    required String title,
    required String value,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        padding: SpacingTokens.paddingMd,
        decoration: BoxDecoration(
          color: ColorTokens.surfaceVariant,
          borderRadius: ShapeTokens.borderRadiusLg,
          border: Border.all(
            color: ColorTokens.outline,
          ),
        ),
        child: Row(
          children: [
            Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: ColorTokens.primary.withValues(alpha: 0.1),
                borderRadius: ShapeTokens.borderRadiusSm,
              ),
              child: Icon(
                icon,
                color: ColorTokens.primary,
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: TypographyTokens.sizeLabelMedium,
                      color: ColorTokens.onSurfaceVariant,
                    ),
                  ),
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: TypographyTokens.sizeLabelLarge,
                      fontWeight: FontWeight.w600,
                      color: ColorTokens.onSurface,
                    ),
                  ),
                ],
              ),
            ),
            const Icon(
              Icons.arrow_forward_ios,
              size: 16,
              color: ColorTokens.onSurfaceVariant,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModernFooter() {
    return Container(
      padding: SpacingTokens.paddingXl,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            ColorTokens.primary.withValues(alpha: 0.05),
            ColorTokens.secondary.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: ShapeTokens.borderRadiusXl,
        border: Border.all(
          color: ColorTokens.primary.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          const Text(
            '让钓鱼成为一种生活方式',
            style: TextStyle(
              fontSize: TypographyTokens.sizeTitleMedium,
              fontWeight: FontWeight.bold,
              color: ColorTokens.onSurface,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          const Text(
            '© 2024 钓鱼之旅. All rights reserved.',
            style: TextStyle(
              fontSize: TypographyTokens.sizeLabelMedium,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: SpacingTokens.space4),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildSocialButton(Icons.language, () => _launchWebsite()),
              const SizedBox(width: 16),
              _buildSocialButton(Icons.email, () => _launchEmail()),
              const SizedBox(width: 16),
              _buildSocialButton(Icons.share, () => _shareApp()),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSocialButton(IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      child: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [ColorTokens.primary, ColorTokens.secondary],
          ),
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: ColorTokens.primary.withValues(alpha: 0.3),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
      ),
    );
  }

  Future<void> _checkForUpdate() async {
    setState(() {
      _isCheckingUpdate = true;
    });

    try {
      HapticFeedback.mediumImpact();

      // TODO: 调用API检查版本更新
      debugPrint('检查更新: 当前版本 $_currentVersion');

      // 模拟网络请求检查更新
      await Future.delayed(const Duration(seconds: 2));

      // 模拟随机有更新（20%概率）
      final hasUpdate = DateTime.now().millisecond % 5 == 0;

      if (hasUpdate) {
        setState(() {
          _latestVersion = '1.3.0';
          _updateDescription = '优化了钓点推荐算法，修复了已知问题，提升了应用性能。';
        });
      }

      if (mounted) {
        if (hasUpdate) {
          _showUpdateDialog();
        } else {
          HapticFeedback.lightImpact();
          _showSnackBar('已是最新版本');
        }
      }
    } catch (e) {
      debugPrint('检查更新失败: $e');
      if (mounted) {
        // Network connection issue handled
        HapticFeedback.heavyImpact();
        _showSnackBar('检查更新失败，请检查网络连接');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCheckingUpdate = false;
        });
      }
    }
  }

  Future<void> _launchEmail() async {
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: '<EMAIL>',
      queryParameters: {
        'subject': '钓鱼之旅 - 用户反馈',
      },
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      _showSnackBar('无法打开邮箱应用');
    }
  }

  Future<void> _launchWebsite() async {
    final Uri url = Uri.parse('https://www.fishingtrip.com');
    if (await canLaunchUrl(url)) {
      await launchUrl(url, mode: LaunchMode.externalApplication);
    } else {
      _showSnackBar('无法打开网页');
    }
  }

  Future<void> _makePhoneCall() async {
    final Uri phoneUri = Uri(scheme: 'tel', path: '************');
    if (await canLaunchUrl(phoneUri)) {
      await launchUrl(phoneUri);
    } else {
      _showSnackBar('无法拨打电话');
    }
  }

  void _shareApp() {
    HapticFeedback.lightImpact();
    // TODO: 实现分享功能，调用API获取分享链接
    try {
      // 模拟分享数据
      final shareData = {
        'appName': '钓鱼之旅',
        'description': '发现优质钓点，分享钓鱼乐趣',
        'downloadUrl': 'https://www.fishingtrip.com/download',
        'version': _currentVersion,
      };

      debugPrint('分享应用数据: $shareData');
      _showSnackBar('分享链接已复制到剪贴板');
    } catch (e) {
      debugPrint('分享失败: $e');
      _showSnackBar('分享功能暂时不可用');
    }
  }

  void _showUpdateDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: const RoundedRectangleBorder(
          borderRadius: ShapeTokens.borderRadiusXl,
        ),
        child: Container(
          padding: SpacingTokens.paddingXl,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                ColorTokens.surfaceVariant,
              ],
            ),
            borderRadius: ShapeTokens.borderRadiusXl,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [ColorTokens.primary, ColorTokens.secondary],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: ColorTokens.primary.withValues(alpha: 0.3),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.system_update,
                  color: Colors.white,
                  size: 40,
                ),
              ),
              const SizedBox(height: SpacingTokens.space6),
              const Text(
                '发现新版本',
                style: TextStyle(
                  fontSize: TypographyTokens.sizeTitleLarge,
                  fontWeight: FontWeight.bold,
                  color: ColorTokens.onSurface,
                ),
              ),
              const SizedBox(height: SpacingTokens.space2),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: ColorTokens.primary.withValues(alpha: 0.1),
                  borderRadius: ShapeTokens.borderRadiusLg,
                ),
                child: Text(
                  'v$_latestVersion',
                  style: const TextStyle(
                    fontSize: TypographyTokens.sizeBodyLarge,
                    fontWeight: FontWeight.bold,
                    color: ColorTokens.primary,
                  ),
                ),
              ),
              const SizedBox(height: SpacingTokens.space4),
              Text(
                _updateDescription ?? '新版本包含功能优化和问题修复',
                style: const TextStyle(
                  fontSize: TypographyTokens.sizeLabelLarge,
                  color: ColorTokens.onSurfaceVariant,
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: SpacingTokens.space6),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: const RoundedRectangleBorder(
                          borderRadius: ShapeTokens.borderRadiusXl,
                          side: BorderSide(color: ColorTokens.outline),
                        ),
                      ),
                      child: const Text(
                        '稍后更新',
                        style: TextStyle(
                          fontSize: TypographyTokens.sizeLabelLarge,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _downloadUpdate();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: ColorTokens.primary,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: const RoundedRectangleBorder(
                          borderRadius: ShapeTokens.borderRadiusXl,
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        '立即更新',
                        style: TextStyle(
                          fontSize: TypographyTokens.sizeLabelLarge,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _downloadUpdate() {
    HapticFeedback.lightImpact();
    // TODO: 实现应用更新下载
    _showSnackBar('正在跳转到应用商店...');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: ColorTokens.primary,
        behavior: SnackBarBehavior.floating,
        shape: const RoundedRectangleBorder(
          borderRadius: ShapeTokens.borderRadiusSm,
        ),
      ),
    );
  }
}

// 功能特性数据模型
class FeatureItem {
  final IconData icon;
  final String title;
  final String description;
  final Color color;

  const FeatureItem({
    required this.icon,
    required this.title,
    required this.description,
    required this.color,
  });
}

// 团队成员数据模型
class TeamMember {
  final String name;
  final String role;
  final IconData avatar;
  final Color color;

  const TeamMember({
    required this.name,
    required this.role,
    required this.avatar,
    required this.color,
  });
}
