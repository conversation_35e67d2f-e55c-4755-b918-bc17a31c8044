import 'dart:math' show sin, cos, pi;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/config/route_data.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';

class MyAlbumPage extends StatefulWidget {
 const MyAlbumPage({super.key});

  @override
  State<MyAlbumPage> createState() => _MyAlbumPageState();
}

class _MyAlbumPageState extends State<MyAlbumPage>
    with TickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;
  
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late AnimationController _floatController;
  
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _floatAnimation;
  
  // 列表项动画
  final List<AnimationController> _itemControllers = [];
  
  // 视图模式
  bool _isGridView = true;
  int _gridCrossAxisCount = 3;

  // 选择模式
  bool _isSelectionMode = false;
  final Set<int> _selectedPhotos = {};

  // 数据
  List<AlbumPhoto> _photos = [];
  bool _isLoading = true;

  // 筛选和搜索
  String _filterType = 'all'; // all, fish, spot, moment
  String _searchQuery = '';
  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    
    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    _floatController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat();
    
    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.9,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _floatAnimation = CurvedAnimation(
      parent: _floatController,
      curve: Curves.easeInOut,
    );
    
    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
    
    _loadPhotos();
  }
  
  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    _floatController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _loadPhotos() {
    // 模拟加载数据
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _photos = List.generate(50, (index) {
          final types = ['fish', 'spot', 'moment'];
          return AlbumPhoto(
            id: index + 1,
            url: 'https://picsum.photos/400/600?random=$index',
            thumbnailUrl: 'https://picsum.photos/200/300?random=$index',
            type: types[index % 3],
            title: '渔获照片 ${index + 1}',
            description: '今天钓到了一条大鱼！',
            location: '北京市朝阳区某某湖',
            createdAt: DateTime.now().subtract(Duration(days: index)),
            fileSize: (1.5 + index * 0.1) * 1024 * 1024,
            // MB to bytes
            width: 1920,
            height: 1080,
            tags: ['鲤鱼', '野钓', '路亚'][index % 3],
          );
        });
        _isLoading = false;
      });
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }
  
  AnimationController _createItemController(int index) {
    // 确保列表有足够的控制器
    while (index >= _itemControllers.length) {
      final controllerIndex = _itemControllers.length;
      final controller = AnimationController(
        duration: Duration(milliseconds: 300 + (controllerIndex * 30)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: _isSelectionMode ? '已选择 ${_selectedPhotos.length} 张' : '我的相册',
      hasTabBar: true,
      tabController: _tabController,
      tabs: const [
        Tab(text: '渔获'),
        Tab(text: '钓点'),
        Tab(text: '动态'),
      ],
      onRefresh: () async {
        HapticFeedback.lightImpact();
        _loadPhotos();
      },
      enableSearch: true,
      onSearchChanged: _onSearchChanged,
      showAnimations: true,
      gradientColors: [
        ColorTokens.primary,
        ColorTokens.secondary,
      ],
      backgroundDecoration: _buildBackgroundDecoration(),
      expandedHeight: 200,
      actions: [
        if (!_isSelectionMode) ...[
          IconButton(
            icon: Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                _isGridView ? Icons.view_list : Icons.grid_view,
                color: Theme.of(context).colorScheme.surface,
                size: 20,
              ),
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          PopupMenuButton<String>(
            icon: Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.more_vert,
                color: Theme.of(context).colorScheme.surface,
                size: 20,
              ),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
             const PopupMenuItem(
                value: 'select',
                child: Row(
                  children: [
                    Icon(Icons.check_circle_outline, size: 20),
                   const SizedBox(width: 8),
                    Text('选择照片'),
                  ],
                ),
              ),
             const PopupMenuItem(
                value: 'upload',
                child: Row(
                  children: [
                    Icon(Icons.cloud_upload_outlined, size: 20),
                   const SizedBox(width: 8),
                    Text('上传照片'),
                  ],
                ),
              ),
             const PopupMenuItem(
                value: 'storage',
                child: Row(
                  children: [
                    Icon(Icons.storage, size: 20),
                   const SizedBox(width: 8),
                    Text('存储空间'),
                  ],
                ),
              ),
            ],
          ),
        ] else ...[
          if (_selectedPhotos.isNotEmpty) ...[
            IconButton(
              icon: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.share,
                  color: Theme.of(context).colorScheme.surface,
                  size: 20,
                ),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                _shareSelectedPhotos();
              },
            ),
            IconButton(
              icon: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(
                  Icons.delete,
                  color: Theme.of(context).colorScheme.surface,
                  size: 20,
                ),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                _deleteSelectedPhotos();
              },
            ),
          ],
          IconButton(
            icon: Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                _selectedPhotos.length == _photos.length
                    ? Icons.deselect
                    : Icons.select_all,
                color: Theme.of(context).colorScheme.surface,
                size: 20,
              ),
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              _selectAll();
            },
          ),
        ],
      ],
      body: Column(
        children: [
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildPhotoTab('fish'),
                _buildPhotoTab('spot'),
                _buildPhotoTab('moment'),
              ],
            ),
          ),
          if (_isSelectionMode) _buildSelectionBottomBar(),
        ],
      ),
      floatingActionButton: ScaleTransition(
        scale: _scaleAnimation,
        child: FloatingActionButton.extended(
          onPressed: () {
            HapticFeedback.mediumImpact();
            _uploadPhotos();
          },
          backgroundColor: Theme.of(context).colorScheme.primary,
          icon: Icon(Icons.add_a_photo, color: Theme.of(context).colorScheme.surface),
          label: const Text(
            '上传照片',
            style: TextStyle(
              color: Theme.of(context).colorScheme.surface,
              fontWeight: FontWeight.bold,
            ),
          ),
          elevation: 8,
        ),
      ),
    );
  }
  
  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 浮动相片装饰
        Positioned(
          top: 100,
          right: -30,
          child: AnimatedBuilder(
            animation: _floatAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: sin(_floatAnimation.value * 2 * pi) * 0.1,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.photo,
                    size: 50,
                    color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                  ),
                ),
              );
            },
          ),
        ),
        // 脉冲圆圈
        Positioned(
          bottom: 80,
          left: -50,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 150,
                  height: 150,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.surface.withValues(alpha: 0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  Widget _buildPhotoTab(String type) {
    final photos = _getFilteredPhotos(type);
    
    if (_isLoading) {
      return const UnifiedLoadingState(message: '加载中...');
    }
    
    if (photos.isEmpty) {
      return _buildEmptyState();
    }
    
    return Column(
      children: [
        // 统计信息栏
        _buildModernStatisticsBar(photos),
        // 照片内容
        Expanded(
          child: _isGridView ? _buildModernGridView(photos) : _buildTimelineView(photos),
        ),
      ],
    );
  }

  Widget _buildModernStatisticsBar(List<AlbumPhoto> photos) {
    final totalSize = photos.fold<double>(
      0,
      (sum, photo) => sum + photo.fileSize,
    );

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      padding: SpacingTokens.paddingMd,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.surface,
            Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF845EC2).withValues(alpha: 0.08),
            blurRadius: 12,
            offset: Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatItem(
            Icons.photo_library,
            '${photos.length}',
            '张照片',
            Color(0xFF845EC2),
          ),
          Container(
            height: 40,
            width: 1,
            color: Colors.grey[200],
          ),
          _buildStatItem(
            Icons.storage,
            _formatFileSize(totalSize),
            '占用空间',
            Colors.blue,
          ),
          if (_gridCrossAxisCount > 0) ...[
            Container(
              height: 40,
              width: 1,
              color: Colors.grey[200],
            ),
            GestureDetector(
              onTap: () {
                HapticFeedback.lightImpact();
                setState(() {
                  _gridCrossAxisCount = _gridCrossAxisCount == 5 ? 3 : _gridCrossAxisCount + 1;
                });
              },
              child: _buildStatItem(
                Icons.grid_view,
                '$_gridCrossAxisCount',
                '每行',
                Colors.orange,
              ),
            ),
          ],
        ],
      ),
    );
  }
  
  Widget _buildStatItem(IconData icon, String value, String label, Color color) {
    return Column(
      children: [
        Container(
          padding: SpacingTokens.paddingSm,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 20,
            color: color,
          ),
        ),
       const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: ColorTokens.onSurface,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: ColorTokens.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Color(0xFF845EC2).withValues(alpha: 0.1),
                    Color(0xFF9B89B3).withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.photo_library_outlined,
                size: 60,
                color: Color(0xFF845EC2),
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          FadeTransition(
            opacity: _fadeAnimation,
            child: const Text(
              '相册还是空的',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            '拍照记录你的钓鱼时光',
            style: const TextStyle(
              fontSize: 16,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernGridView(List<AlbumPhoto> photos) {
    return GridView.builder(
      padding: SpacingTokens.paddingSm,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: _gridCrossAxisCount,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: photos.length,
      itemBuilder: (context, index) {
        final photo = photos[index];
        return _buildAnimatedPhotoItem(photo, index);
      },
    );
  }

  Widget _buildTimelineView(List<AlbumPhoto> photos) {
    final groupedPhotos = _groupPhotosByDate(photos);

    return ListView.builder(
      padding: SpacingTokens.paddingMd,
      itemCount: groupedPhotos.length,
      itemBuilder: (context, index) {
        final date = groupedPhotos.keys.elementAt(index);
        final photos = groupedPhotos[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 日期标题
            Padding(
              padding: EdgeInsets.symmetric(vertical: 12),
              child: Text(
                _formatDate(date),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // 照片列表
            ...photos.map((photo) => _buildTimelinePhotoItem(photo)).toList(),

           const SizedBox(height: SpacingTokens.space4),
          ],
        );
      },
    );
  }

  Widget _buildAnimatedPhotoItem(AlbumPhoto photo, int index) {
    final controller = _createItemController(index);
    final isSelected = _selectedPhotos.contains(photo.id);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.scale(
          scale: 0.8 + (controller.value * 0.2),
          child: Opacity(
            opacity: controller.value,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          if (_isSelectionMode) {
            _togglePhotoSelection(photo.id);
          } else {
            _viewPhoto(photo);
          }
        },
        onLongPress: () {
          HapticFeedback.mediumImpact();
          if (!_isSelectionMode) {
            _enterSelectionMode(photo.id);
          }
        },
        child: Stack(
          fit: StackFit.expand,
          children: [
            // 照片容器
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Color(0xFF845EC2).withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Hero(
                  tag: 'photo_${photo.id}',
                  child: CachedNetworkImage(
                    imageUrl: photo.thumbnailUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            Color(0xFF845EC2).withValues(alpha: 0.1),
                            Color(0xFF9B89B3).withValues(alpha: 0.1),
                          ],
                        ),
                      ),
                      child: const Center(
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF845EC2)),
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Color(0xFF845EC2).withValues(alpha: 0.1),
                      child: Icon(
                        Icons.broken_image,
                        color: Color(0xFF845EC2).withValues(alpha: 0.5),
                      ),
                    ),
                  ),
                ),
              ),
            ),

            // 渐变遮罩
            if (!_isSelectionMode)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withValues(alpha: 0.3),
                      ],
                      stops: const [0.6, 1.0],
                    ),
                  ),
                ),
              ),
            
            // 标签信息
            if (!_isSelectionMode)
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: _getTypeGradient(photo.type),
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getPhotoTypeIcon(photo.type),
                            size: 12,
                            color: Theme.of(context).colorScheme.surface,
                          ),
                         const SizedBox(width: 4),
                          Text(
                            _getPhotoTypeLabel(photo.type),
                            style: const TextStyle(
                              color: Theme.of(context).colorScheme.surface,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (photo.tags != null)
                      Container(
                        padding: EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.local_offer,
                          size: 12,
                          color: Theme.of(context).colorScheme.surface,
                        ),
                      ),
                  ],
                ),
              ),

            // 选中状态
            if (_isSelectionMode)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: isSelected
                        ? Color(0xFF845EC2).withValues(alpha: 0.3)
                        : Colors.transparent,
                  ),
                  child: Align(
                    alignment: Alignment.topRight,
                    child: Container(
                      margin: SpacingTokens.paddingSm,
                      width: 28,
                      height: 28,
                      decoration: BoxDecoration(
                        gradient: isSelected
                            ? const LinearGradient(
                                colors: [Color(0xFF845EC2), Color(0xFF9B89B3)],
                              )
                            : null,
                        color: isSelected ? null : Theme.of(context).colorScheme.surface,
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: isSelected ? Colors.transparent : Colors.grey[400]!,
                          width: 2,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.1),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: isSelected
                          ? const Icon(
                              Icons.check,
                              size: 18,
                              color: Theme.of(context).colorScheme.surface,
                            )
                          : null,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelinePhotoItem(AlbumPhoto photo) {
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _viewPhoto(photo),
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 照片
            ClipRRect(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: AspectRatio(
                aspectRatio: 16 / 9,
                child: CachedNetworkImage(
                  imageUrl: photo.url,
                  fit: BoxFit.cover,
                ),
              ),
            ),

            // 信息
            Padding(
              padding: EdgeInsets.all(12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        _getPhotoTypeIcon(photo.type),
                        size: 16,
                        color: Colors.blue,
                      ),
                     const SizedBox(width: 4),
                      Text(
                        photo.title ?? '无标题',
                        style: const TextStyle(
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                  if (photo.description != null) ...[
                   const SizedBox(height: 4),
                    Text(
                      photo.description!,
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.grey[600],
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                 const SizedBox(height: SpacingTokens.space2),
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 14,
                        color: Colors.grey[500],
                      ),
                     const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          photo.location ?? '未知位置',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[500],
                          ),
                        ),
                      ),
                      Text(
                        _formatTime(photo.createdAt),
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectionBottomBar() {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildBottomAction(
            icon: Icons.share,
            label: '分享',
            onTap: _shareSelectedPhotos,
            enabled: _selectedPhotos.isNotEmpty,
          ),
          _buildBottomAction(
            icon: Icons.download,
            label: '下载',
            onTap: _downloadSelectedPhotos,
            enabled: _selectedPhotos.isNotEmpty,
          ),
          _buildBottomAction(
            icon: Icons.delete,
            label: '删除',
            onTap: _deleteSelectedPhotos,
            enabled: _selectedPhotos.isNotEmpty,
            color: Theme.of(context).colorScheme.error,
          ),
        ],
      ),
    );
  }

  Widget _buildBottomAction({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    final bool enabled = true,
    Color? color,
  }) {
    final effectiveColor =
        enabled ? (color ?? Theme.of(context).primaryColor) : Colors.grey[400]!;

    return InkWell(
      onTap: enabled ? onTap : null,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 24, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: effectiveColor),
           const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: effectiveColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<AlbumPhoto> _getFilteredPhotos(String type) {
    var photos = _photos.where((photo) => photo.type == type).toList();
    
    if (_searchQuery.isNotEmpty) {
      photos = photos.where((photo) {
        final title = (photo.title ?? '').toLowerCase();
        final description = (photo.description ?? '').toLowerCase();
        final location = (photo.location ?? '').toLowerCase();
        final tags = (photo.tags ?? '').toLowerCase();
        
        return title.contains(_searchQuery) ||
               description.contains(_searchQuery) ||
               location.contains(_searchQuery) ||
               tags.contains(_searchQuery);
      }).toList();
    }
    
    return photos;
  }
  
  List<Color> _getTypeGradient(String type) {
    switch (type) {
      case 'fish':
        return [Colors.blue, Colors.cyan];
      case 'spot':
        return [Colors.green, Colors.teal];
      case 'moment':
        return [Colors.orange, Colors.deepOrange];
      default:
        return [Colors.grey, Colors.blueGrey];
    }
  }

  Map<DateTime, List<AlbumPhoto>> _groupPhotosByDate(List<AlbumPhoto> photos) {
    final grouped = <DateTime, List<AlbumPhoto>>{};

    for (final photo in photos) {
      final date = DateTime(
        photo.createdAt.year,
        photo.createdAt.month,
        photo.createdAt.day,
      );

      if (grouped.containsKey(date)) {
        grouped[date]!.add(photo);
      } else {
        grouped[date] = [photo];
      }
    }

    return Map.fromEntries(
      grouped.entries.toList()..sort((a, b) => b.key.compareTo(a.key)),
    );
  }

  String _getFilterLabel() {
    switch (_filterType) {
      case 'fish':
        return '渔获';
      case 'spot':
        return '钓点';
      case 'moment':
        return '动态';
      default:
        return '全部';
    }
  }

  IconData _getPhotoTypeIcon(String type) {
    switch (type) {
      case 'fish':
        return Icons.phishing;
      case 'spot':
        return Icons.location_on;
      case 'moment':
        return Icons.dynamic_feed;
      default:
        return Icons.photo;
    }
  }

  String _getPhotoTypeLabel(String type) {
    switch (type) {
      case 'fish':
        return '渔获';
      case 'spot':
        return '钓点';
      case 'moment':
        return '动态';
      default:
        return '照片';
    }
  }

  String _formatFileSize(double bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    if (date.isAfter(today)) {
      return '今天';
    } else if (date.isAfter(yesterday)) {
      return '昨天';
    } else if (date.year == now.year) {
      return '${date.month}月${date.day}日';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'select':
        _enterSelectionMode(null);
        break;
      case 'upload':
        _uploadPhotos();
        break;
      case 'storage':
        _showStorageInfo();
        break;
    }
  }

  void _enterSelectionMode(int? photoId) {
    setState(() {
      _isSelectionMode = true;
      if (photoId != null) {
        _selectedPhotos.add(photoId);
      }
    });
  }

  void _exitSelectionMode() {
    setState(() {
      _isSelectionMode = false;
      _selectedPhotos.clear();
    });
  }

  void _togglePhotoSelection(int photoId) {
    setState(() {
      if (_selectedPhotos.contains(photoId)) {
        _selectedPhotos.remove(photoId);
      } else {
        _selectedPhotos.add(photoId);
      }
    });
  }

  void _selectAll() {
    setState(() {
      final allPhotoIds = _photos.map((p) => p.id).toSet();
      if (_selectedPhotos.length == allPhotoIds.length) {
        _selectedPhotos.clear();
      } else {
        _selectedPhotos.addAll(allPhotoIds);
      }
    });
  }

  void _viewPhoto(AlbumPhoto photo) {
    final photos = _photos;
    final index = photos.indexOf(photo);

    context.navigateTo(AppRoutes.photoViewPage, extra: PhotoViewRouteData(
      images: photos.map((p) => p.url).toList(),
      initialIndex: index,
    ));
  }

  void _uploadPhotos() {
    // TODO: 实现上传照片
    ScaffoldMessenger.of(context).showSnackBar(
     const SnackBar(content: Text('上传功能开发中...')),
    );
  }

  void _shareSelectedPhotos() {
    // TODO: 实现分享
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('分享 ${_selectedPhotos.length} 张照片')),
    );
  }

  void _downloadSelectedPhotos() {
    // TODO: 实现下载
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('下载 ${_selectedPhotos.length} 张照片')),
    );
  }

  void _deleteSelectedPhotos() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('删除照片'),
        content: Text('确定要删除选中的 ${_selectedPhotos.length} 张照片吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _photos.removeWhere((p) => _selectedPhotos.contains(p.id));
                _exitSelectionMode();
              });
              ScaffoldMessenger.of(context).showSnackBar(
               const SnackBar(content: Text('照片已删除')),
              );
            },
            style: TextButton.styleFrom(foregroundColor: Theme.of(context).colorScheme.error),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _showStorageInfo() {
    final totalSize = _photos.fold<double>(
      0,
      (sum, photo) => sum + photo.fileSize,
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('存储空间'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildStorageItem('照片数量', '${_photos.length} 张'),
            _buildStorageItem('占用空间', _formatFileSize(totalSize)),
            _buildStorageItem('剩余空间', '8.5 GB'),
           const SizedBox(height: SpacingTokens.space4),
            LinearProgressIndicator(
              value: totalSize / (10 * 1024 * 1024 * 1024), // 假设总空间10GB
              backgroundColor: Colors.grey[300],
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  Widget _buildStorageItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label, style: TextStyle(color: Colors.grey[600])),
          Text(value, style: const TextStyle(fontWeight: FontWeight.w500)),
        ],
      ),
    );
  }
}

// 照片查看页面
class PhotoViewPage extends StatelessWidget {
  final List<AlbumPhoto> photos;
  final int initialIndex;

 const PhotoViewPage({
    super.key,
    required this.photos,
    required this.initialIndex,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          PhotoViewGallery.builder(
            itemCount: photos.length,
            pageController: PageController(initialPage: initialIndex),
            builder: (context, index) {
              final photo = photos[index];
              return PhotoViewGalleryPageOptions(
                imageProvider: CachedNetworkImageProvider(photo.url),
                heroAttributes:
                    PhotoViewHeroAttributes(tag: 'photo_${photo.id}'),
              );
            },
            scrollPhysics: const BouncingScrollPhysics(),
            backgroundDecoration: const BoxDecoration(color: Colors.black),
          ),
          SafeArea(
            child: IconButton(
              icon: Icon(Icons.close, color: Theme.of(context).colorScheme.surface),
              onPressed: () => Navigator.pop(context),
            ),
          ),
        ],
      ),
    );
  }
}

// 相册照片数据模型
class AlbumPhoto {
  final int id;
  final String url;
  final String thumbnailUrl;
  final String type; // fish, spot, moment
  final String? title;
  final String? description;
  final String? location;
  final DateTime createdAt;
  final double fileSize;
  final int width;
  final int height;
  final String? tags;

  AlbumPhoto({
    required this.id,
    required this.url,
    required this.thumbnailUrl,
    required this.type,
    this.title,
    this.description,
    this.location,
    required this.createdAt,
    required this.fileSize,
    required this.width,
    required this.height,
    this.tags,
  });
}