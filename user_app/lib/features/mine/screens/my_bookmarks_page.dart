import 'dart:math' show sin, pi;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/bookmarks/view_models/bookmark_view_model.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/models/moment/moment_vo.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/widgets/auth_required_widget.dart';

class MyBookmarksPage extends StatefulWidget {
  const MyBookmarksPage({super.key});

  @override
  State<MyBookmarksPage> createState() => _MyBookmarksPageState();
}

class _MyBookmarksPageState extends State<MyBookmarksPage>
    with TickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;
  late BookmarkViewModel _viewModel;

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late AnimationController _floatController;

  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _floatAnimation;

  // 列表项动画
  final List<AnimationController> _itemControllers = [];

  // 搜索功能
  String _searchQuery = '';

  // 滚动控制器
  final ScrollController _momentsScrollController = ScrollController();
  final ScrollController _spotsScrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _viewModel = context.read<BookmarkViewModel>();

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    _floatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _floatAnimation = CurvedAnimation(
      parent: _floatController,
      curve: Curves.easeInOut,
    );

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();

    _loadBookmarks();
    _momentsScrollController.addListener(() => _onScroll('moment'));
    _spotsScrollController.addListener(() => _onScroll('spot'));
  }

  @override
  void dispose() {
    _tabController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    _floatController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    _momentsScrollController.dispose();
    _spotsScrollController.dispose();
    super.dispose();
  }

  void _loadBookmarks() {
    _viewModel.loadBookmarkedMoments(refresh: true);
    _viewModel.loadBookmarkedSpots(refresh: true);
  }

  void _onScroll(String type) {
    final controller =
        type == 'moment' ? _momentsScrollController : _spotsScrollController;
    if (controller.position.pixels >=
        controller.position.maxScrollExtent - 200) {
      if (type == 'moment') {
        _viewModel.loadBookmarkedMoments();
      } else {
        _viewModel.loadBookmarkedSpots();
      }
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  AnimationController _createItemController(int index) {
    // 确保列表有足够的控制器
    while (index >= _itemControllers.length) {
      final controllerIndex = _itemControllers.length;
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (controllerIndex * 50)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  @override
  Widget build(BuildContext context) {
    return AuthRequiredWidget(
      title: '我的收藏',
      child: UnifiedPageScaffold(
        title: '我的收藏',
        hasTabBar: true,
        tabController: _tabController,
        tabs: const [
          Tab(text: '动态'),
          Tab(text: '钓点'),
        ],
        onRefresh: () async {
          HapticFeedback.lightImpact();
          await Future.wait([
            _viewModel.loadBookmarkedMoments(refresh: true),
            _viewModel.loadBookmarkedSpots(refresh: true),
          ]);
        },
        enableSearch: true,
        onSearchChanged: _onSearchChanged,
        showAnimations: true,
        gradientColors: [
          ColorTokens.error,
          ColorTokens.primary,
        ],
        backgroundDecoration: _buildBackgroundDecoration(),
        expandedHeight: 200,
        body: TabBarView(
          controller: _tabController,
          children: [
            _buildMomentsList(),
            _buildSpotsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 浮动装饰
        Positioned(
          top: 120,
          right: -50,
          child: AnimatedBuilder(
            animation: _floatAnimation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(0, sin(_floatAnimation.value * 2 * pi) * 20),
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.surface.withValues(alpha: 0.05),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        // 脉冲圆圈
        Positioned(
          bottom: 50,
          left: -30,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                      width: 2,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        // 书签图标装饰
        Positioned(
          top: 80,
          left: 40,
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: Icon(
                  Icons.bookmark,
                  size: 40,
                  color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.1),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMomentsList() {
    return Consumer<BookmarkViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoadingMoments && viewModel.bookmarkedMoments.isEmpty) {
          return const UnifiedLoadingState(message: '加载中...');
        }

        if (viewModel.bookmarkedMoments.isEmpty) {
          return _buildEmptyState(
            icon: Icons.bookmark_border,
            title: '还没有收藏的动态',
            subtitle: '快去发现精彩内容吧',
            actionText: '探索动态',
            onAction: () => context.goUntilRoute('/moments'),
          );
        }

        // 搜索过滤
        final filteredMoments = _searchQuery.isEmpty
            ? viewModel.bookmarkedMoments
            : viewModel.bookmarkedMoments.where((moment) {
                final content = (moment.content ?? '').toLowerCase();
                final userName = (moment.userName ?? '').toLowerCase();
                return content.contains(_searchQuery) ||
                    userName.contains(_searchQuery);
              }).toList();

        if (filteredMoments.isEmpty && _searchQuery.isNotEmpty) {
          return _buildEmptyState(
            icon: Icons.search_off,
            title: '没有找到相关内容',
            subtitle: '换个关键词试试',
          );
        }

        return ListView.builder(
          controller: _momentsScrollController,
          padding: EdgeInsets.only(bottom: 80),
          itemCount:
              filteredMoments.length + (viewModel.isLoadingMoreMoments ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == filteredMoments.length) {
              return const Padding(
                padding: EdgeInsets.all(SpacingTokens.space4),
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(ColorTokens.error),
                  ),
                ),
              );
            }

            final moment = filteredMoments[index];
            return _buildAnimatedMomentItem(moment, index);
          },
        );
      },
    );
  }

  Widget _buildAnimatedMomentItem(MomentVo moment, int index) {
    final controller = _createItemController(index);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - controller.value)),
          child: Opacity(
            opacity: controller.value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: SpacingTokens.space4,
          vertical: SpacingTokens.space3,
        ),
        child: Material(
          color: ColorTokens.transparent,
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              context.navigateTo('/moments/${moment.id}');
            },
            borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.surface,
                    Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
                  ],
                ),
                borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.error.withValues(alpha: 0.08),
                    blurRadius: 20,
                    offset: Offset(0, 10),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(SpacingTokens.space4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 用户信息
                    Row(
                      children: [
                        Hero(
                          tag: 'avatar_${moment.id}',
                          child: Container(
                            width: 48,
                            height: 48,
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  ColorTokens.error,
                                  ColorTokens.primary
                                ],
                              ),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color:
                                      Theme.of(context).colorScheme.error.withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: moment.userAvatar != null
                                ? ClipOval(
                                    child: CachedNetworkImage(
                                      imageUrl: moment.userAvatar!,
                                      fit: BoxFit.cover,
                                    ),
                                  )
                                : Center(
                                    child: Text(
                                      (moment.userName ?? '用户').substring(0, 1),
                                      style: TextStyle(
                                        color: Theme.of(context).colorScheme.surface,
                                        fontSize: 20,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                          ),
                        ),
                        const SizedBox(width: SpacingTokens.space4),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                moment.userName ?? '匿名用户',
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                  color: ColorTokens.onSurface,
                                ),
                              ),
                              const SizedBox(height: 2),
                              Text(
                                _formatTime(moment.createTime),
                                style: const TextStyle(
                                  fontSize: 13,
                                  color: ColorTokens.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: SpacingTokens.space3,
                            vertical: SpacingTokens.space2,
                          ),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [ColorTokens.error, ColorTokens.primary],
                            ),
                            borderRadius:
                                BorderRadius.circular(ShapeTokens.radiusSm),
                          ),
                          child: const Icon(
                            Icons.bookmark,
                            size: 16,
                            color: Theme.of(context).colorScheme.surface,
                          ),
                        ),
                      ],
                    ),

                    // 内容
                    if (moment.content != null) ...[
                      const SizedBox(height: SpacingTokens.space4),
                      Text(
                        moment.content!,
                        style: const TextStyle(
                          fontSize: 15,
                          color: ColorTokens.onSurface,
                          height: 1.5,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    // 图片
                    if (moment.pictures != null &&
                        moment.pictures!.isNotEmpty) ...[
                      const SizedBox(height: SpacingTokens.space4),
                      _buildModernImageGrid(moment.pictures!),
                    ],

                    // 互动栏
                    const SizedBox(height: SpacingTokens.space4),
                    Row(
                      children: [
                        _buildModernActionButton(
                          icon: Icons.favorite_border,
                          activeIcon: Icons.favorite,
                          count: moment.likeCount ?? 0,
                          isActive: false,
                          color: Theme.of(context).colorScheme.error,
                          onTap: () {},
                        ),
                        const SizedBox(width: SpacingTokens.space6),
                        _buildModernActionButton(
                          icon: Icons.chat_bubble_outline,
                          activeIcon: Icons.chat_bubble,
                          count: moment.commentCount ?? 0,
                          isActive: false,
                          color: ColorTokens.info,
                          onTap: () {},
                        ),
                        const Spacer(),
                        IconButton(
                          icon: const Icon(Icons.bookmark_remove),
                          color: ColorTokens.onSurfaceVariant,
                          onPressed: () {
                            HapticFeedback.lightImpact();
                            _viewModel.unbookmarkMoment(moment.id!);
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildModernImageGrid(List<String> images) {
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
        child: AspectRatio(
          aspectRatio: 16 / 9,
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: images[0],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: ColorTokens.primary.withValues(alpha: 0.1),
                  child: const Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor:
                          AlwaysStoppedAnimation<Color>(ColorTokens.error),
                    ),
                  ),
                ),
              ),
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      ColorTokens.transparent,
                      ColorTokens.scrim.withValues(alpha: 0.1),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: SpacingTokens.space3,
        mainAxisSpacing: SpacingTokens.space3,
      ),
      itemCount: images.length > 9 ? 9 : images.length,
      itemBuilder: (context, index) {
        return ClipRRect(
          borderRadius: BorderRadius.circular(ShapeTokens.radiusSm),
          child: Stack(
            fit: StackFit.expand,
            children: [
              CachedNetworkImage(
                imageUrl: images[index],
                fit: BoxFit.cover,
                placeholder: (context, url) => Container(
                  color: ColorTokens.primary.withValues(alpha: 0.1),
                ),
              ),
              if (index == 8 && images.length > 9)
                Container(
                  color: ColorTokens.scrim.withValues(alpha: 0.5),
                  child: Center(
                    child: Text(
                      '+${images.length - 9}',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.surface,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildModernActionButton({
    required IconData icon,
    required IconData activeIcon,
    required int count,
    required bool isActive,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
        onTap();
      },
      borderRadius: BorderRadius.circular(ShapeTokens.radiusSm),
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: SpacingTokens.space3,
          vertical: SpacingTokens.space2,
        ),
        decoration: BoxDecoration(
          color: isActive ? color.withValues(alpha: 0.1) : ColorTokens.transparent,
          borderRadius: BorderRadius.circular(ShapeTokens.radiusSm),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              isActive ? activeIcon : icon,
              size: 22,
              color: isActive ? color : ColorTokens.onSurfaceVariant,
            ),
            if (count > 0) ...[
              const SizedBox(width: SpacingTokens.space2),
              Text(
                count.toString(),
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  color: isActive ? color : ColorTokens.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSpotsList() {
    return Consumer<BookmarkViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoadingSpots && viewModel.bookmarkedSpots.isEmpty) {
          return const UnifiedLoadingState(message: '加载中...');
        }

        if (viewModel.bookmarkedSpots.isEmpty) {
          return _buildEmptyState(
            icon: Icons.place_outlined,
            title: '还没有收藏的钓点',
            subtitle: '收藏你喜欢的钓点，方便下次查看',
            actionText: '探索钓点',
            onAction: () => context.goUntilRoute('/spots'),
          );
        }

        // 搜索过滤
        final filteredSpots = _searchQuery.isEmpty
            ? viewModel.bookmarkedSpots
            : viewModel.bookmarkedSpots.where((spot) {
                final name = spot.name.toLowerCase();
                final address = (spot.address ?? '').toLowerCase();
                return name.contains(_searchQuery) ||
                    address.contains(_searchQuery);
              }).toList();

        if (filteredSpots.isEmpty && _searchQuery.isNotEmpty) {
          return _buildEmptyState(
            icon: Icons.search_off,
            title: '没有找到相关钓点',
            subtitle: '换个关键词试试',
          );
        }

        return ListView.builder(
          controller: _spotsScrollController,
          padding: EdgeInsets.only(bottom: 80),
          itemCount:
              filteredSpots.length + (viewModel.isLoadingMoreSpots ? 1 : 0),
          itemBuilder: (context, index) {
            if (index == filteredSpots.length) {
              return const Padding(
                padding: EdgeInsets.all(SpacingTokens.space4),
                child: Center(
                  child: CircularProgressIndicator(
                    valueColor:
                        AlwaysStoppedAnimation<Color>(ColorTokens.error),
                  ),
                ),
              );
            }

            final spot = filteredSpots[index];
            return _buildAnimatedSpotItem(spot, index);
          },
        );
      },
    );
  }

  Widget _buildAnimatedSpotItem(FishingSpotVo spot, int index) {
    final controller = _createItemController(index);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(30 * (1 - controller.value), 0),
          child: Opacity(
            opacity: controller.value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: SpacingTokens.space4,
          vertical: SpacingTokens.space3,
        ),
        child: Material(
          color: ColorTokens.transparent,
          child: InkWell(
            onTap: () {
              HapticFeedback.lightImpact();
              context.navigateTo('/spots/${spot.id}');
            },
            borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.error.withValues(alpha: 0.08),
                    blurRadius: 20,
                    offset: Offset(0, 10),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // 钓点图片
                  Container(
                    width: 120,
                    height: 120,
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(ShapeTokens.radiusMd),
                        bottomLeft: Radius.circular(ShapeTokens.radiusMd),
                      ),
                      gradient: const LinearGradient(
                        colors: [Theme.of(context).colorScheme.error, ColorTokens.primary],
                      ),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(ShapeTokens.radiusMd),
                        bottomLeft: Radius.circular(ShapeTokens.radiusMd),
                      ),
                      child: spot.mainImage != null
                          ? CachedNetworkImage(
                              imageUrl: spot.mainImage!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).colorScheme.surface),
                                ),
                              ),
                              errorWidget: (context, url, error) => const Icon(
                                Icons.place,
                                size: 40,
                                color: Theme.of(context).colorScheme.surface,
                              ),
                            )
                          : const Icon(
                              Icons.place,
                              size: 40,
                              color: Theme.of(context).colorScheme.surface,
                            ),
                    ),
                  ),
                  // 钓点信息
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.all(SpacingTokens.space4),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  spot.name,
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: ColorTokens.onSurface,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (spot.isOfficial)
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: SpacingTokens.space2,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: const LinearGradient(
                                      colors: [Colors.green, Colors.teal],
                                    ),
                                    borderRadius: BorderRadius.circular(
                                        ShapeTokens.radiusSm),
                                  ),
                                  child: const Text(
                                    '官方',
                                    style: TextStyle(
                                      fontSize: 10,
                                      color: Theme.of(context).colorScheme.surface,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: SpacingTokens.space2),
                          Row(
                            children: [
                              const Icon(
                                Icons.location_on_outlined,
                                size: 14,
                                color: ColorTokens.onSurfaceVariant,
                              ),
                              const SizedBox(width: SpacingTokens.space2 / 2),
                              Expanded(
                                child: Text(
                                  spot.address ?? '暂无地址信息',
                                  style: const TextStyle(
                                    fontSize: 13,
                                    color: ColorTokens.onSurfaceVariant,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: SpacingTokens.space3),
                          Row(
                            children: [
                              _buildMiniStat(
                                Icons.star_rounded,
                                spot.rating?.toStringAsFixed(1) ?? '0.0',
                                Colors.amber,
                              ),
                              const SizedBox(width: SpacingTokens.space4),
                              _buildMiniStat(
                                Icons.people_outline,
                                '${spot.visitorCount ?? 0}',
                                Colors.blue,
                              ),
                              const SizedBox(width: SpacingTokens.space4),
                              _buildMiniStat(
                                Icons.chat_bubble_outline,
                                '${spot.recentMomentsCount ?? 0}',
                                Colors.purple,
                              ),
                              const Spacer(),
                              const Icon(
                                Icons.bookmark,
                                size: 20,
                                color: Theme.of(context).colorScheme.error,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMiniStat(IconData icon, String value, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 16, color: color),
        const SizedBox(width: SpacingTokens.space2 / 2),
        Text(
          value,
          style: const TextStyle(
            fontSize: 13,
            fontWeight: FontWeight.w600,
            color: ColorTokens.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
    String? actionText,
    VoidCallback? onAction,
  }) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(SpacingTokens.space8),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            ScaleTransition(
              scale: _scaleAnimation,
              child: Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                      ColorTokens.primary.withValues(alpha: 0.1),
                    ],
                  ),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 60,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
            ),
            const SizedBox(height: SpacingTokens.space6),
            FadeTransition(
              opacity: _fadeAnimation,
              child: Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: ColorTokens.onSurface,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: SpacingTokens.space3),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 16,
                color: ColorTokens.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            if (actionText != null && onAction != null) ...[
              const SizedBox(height: SpacingTokens.space6),
              ScaleTransition(
                scale: _scaleAnimation,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Theme.of(context).colorScheme.error, ColorTokens.primary],
                    ),
                    borderRadius: BorderRadius.circular(ShapeTokens.radiusMd),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).colorScheme.error.withValues(alpha: 0.3),
                        blurRadius: 12,
                        offset: Offset(0, 6),
                      ),
                    ],
                  ),
                  child: Material(
                    color: ColorTokens.transparent,
                    child: InkWell(
                      onTap: () {
                        HapticFeedback.lightImpact();
                        onAction();
                      },
                      borderRadius:
                          BorderRadius.circular(ShapeTokens.radiusMd),
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: SpacingTokens.space8,
                          vertical: SpacingTokens.space4,
                        ),
                        child: Text(
                          actionText,
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.surface,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _formatTime(DateTime? time) {
    if (time == null) return '';
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return '刚刚';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else {
      return '${time.month}月${time.day}日';
    }
  }
}
