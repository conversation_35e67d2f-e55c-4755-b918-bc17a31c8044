import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class HelpCenterPage extends StatefulWidget {
 const HelpCenterPage({super.key});

  @override
  State<HelpCenterPage> createState() => _HelpCenterPageState();
}

class _HelpCenterPageState extends State<HelpCenterPage>
    with TickerProviderStateMixin {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;
  
  // 增强的状态管理
  bool _isLoading = false;
  String? _errorMessage;
  final Map<String, int> _questionViews = {}; // 问题查看次数
  final Map<String, bool> _questionHelpful = {}; // 问题是否有帮助

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _rotateController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;

  final List<AnimationController> _itemAnimationControllers = [];
  final Map<String, bool> _expandedStates = {};

  final List<HelpCategory> _categories = [
    HelpCategory(
      title: '账号相关',
      icon: Icons.person_outline,
      color: Color(0xFF667EEA),
      description: '注册、登录、密码等',
      questions: [
        HelpQuestion(
          question: '如何注册账号？',
          answer: '您可以通过手机号码注册账号。在登录页面点击"注册"，输入手机号码并获取验证码，设置密码后即可完成注册。',
        ),
        HelpQuestion(
          question: '忘记密码怎么办？',
          answer: '在登录页面点击"忘记密码"，输入您的手机号码，获取验证码后即可重置密码。',
        ),
        HelpQuestion(
          question: '如何修改个人信息？',
          answer: '进入"我的"页面，点击"设置"，然后选择"编辑资料"即可修改您的个人信息。',
        ),
        HelpQuestion(
          question: '如何注销账号？',
          answer: '进入"设置"-"账号与安全"-"注销账号"，按照提示操作即可。注意：注销后所有数据将无法恢复。',
        ),
      ],
    ),
    HelpCategory(
      title: '钓点功能',
      icon: Icons.location_on_outlined,
      color: Color(0xFF4CAF50),
      description: '添加、收藏、分享钓点',
      questions: [
        HelpQuestion(
          question: '如何添加钓点？',
          answer: '在钓点页面点击"+"按钮，选择位置并填写钓点信息，包括名称、描述、鱼种等信息。',
        ),
        HelpQuestion(
          question: '如何收藏钓点？',
          answer: '在钓点详情页面点击收藏按钮（心形图标）即可收藏该钓点。',
        ),
        HelpQuestion(
          question: '钓点信息不准确怎么办？',
          answer: '您可以在钓点详情页面点击"反馈"按钮，告诉我们具体的问题，我们会及时处理。',
        ),
        HelpQuestion(
          question: '如何分享钓点给朋友？',
          answer: '在钓点详情页面点击分享按钮，选择分享方式即可将钓点分享给朋友。',
        ),
      ],
    ),
    HelpCategory(
      title: '社区功能',
      icon: Icons.forum_outlined,
      color: Color(0xFFFF8E53),
      description: '动态、关注、互动',
      questions: [
        HelpQuestion(
          question: '如何发布动态？',
          answer: '在社区页面点击"+"按钮，选择动态类型，添加图片和文字描述，然后发布即可。',
        ),
        HelpQuestion(
          question: '如何关注其他用户？',
          answer: '在用户主页点击"关注"按钮即可关注该用户，关注后可以在动态中看到他们的最新内容。',
        ),
        HelpQuestion(
          question: '如何举报不当内容？',
          answer: '在动态详情页面点击右上角的"..."按钮，选择"举报"，选择举报原因并提交。',
        ),
        HelpQuestion(
          question: '评论被删除了是什么原因？',
          answer: '评论可能因违反社区规范被系统或管理员删除。请确保您的评论内容健康、友善。',
        ),
      ],
    ),
    HelpCategory(
      title: '其他问题',
      icon: Icons.help_outline,
      color: Color(0xFF764BA2),
      description: '常见问题、联系客服',
      questions: [
        HelpQuestion(
          question: '应用闪退怎么办？',
          answer: '请尝试重启应用或重启手机。如果问题持续存在，请通过"反馈与建议"联系我们。',
        ),
        HelpQuestion(
          question: '如何联系客服？',
          answer: '您可以通过"我的"页面中的"反馈与建议"功能联系我们，我们会及时回复。',
        ),
        HelpQuestion(
          question: '应用支持哪些设备？',
          answer: '应用支持iOS 12.0以上和Android 6.0以上的设备。',
        ),
        HelpQuestion(
          question: '如何清理缓存？',
          answer: '进入"设置"-"通用设置"-"清理缓存"，点击确认即可清理应用缓存。',
        ),
      ],
    ),
  ];

  List<HelpCategory> get _filteredCategories {
    if (_searchQuery.isEmpty) {
      return _categories;
    }

    return _categories
        .map((category) {
          final filteredQuestions = category.questions
              .where((question) =>
                  question.question
                      .toLowerCase()
                      .contains(_searchQuery.toLowerCase()) ||
                  question.answer
                      .toLowerCase()
                      .contains(_searchQuery.toLowerCase()))
              .toList();

          return HelpCategory(
            title: category.title,
            icon: category.icon,
            color: category.color,
            description: category.description,
            questions: filteredQuestions,
          );
        })
        .where((category) => category.questions.isNotEmpty)
        .toList();
  }

  @override
  void initState() {
    super.initState();
    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _rotateController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 2 * 3.14159,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.linear,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _rotateController.dispose();
    for (var controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // 现代化的 AppBar
          SliverAppBar(
            expandedHeight: _isSearching ? 200 : 240,
            floating: false,
            pinned: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                children: [
                  // 渐变背景
                  Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          Color(0xFF667EEA),
                          Color(0xFF764BA2),
                        ],
                      ),
                    ),
                  ),
                  // 装饰元素
                  Positioned(
                    top: -100,
                    right: -100,
                    child: AnimatedBuilder(
                      animation: _rotateAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: _rotateAnimation.value,
                          child: Container(
                            width: 300,
                            height: 300,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: Colors.white.withValues(alpha: 0.1),
                                width: 2,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  Positioned(
                    bottom: -50,
                    left: -50,
                    child: AnimatedBuilder(
                      animation: _rotateAnimation,
                      builder: (context, child) {
                        return Transform.rotate(
                          angle: -_rotateAnimation.value,
                          child: Container(
                            width: 200,
                            height: 200,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              color: Colors.white.withValues(alpha: 0.05),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  // 内容
                  SafeArea(
                    child: Padding(
                      padding: SpacingTokens.paddingLg,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (!_isSearching) ...[
                            SlideTransition(
                              position: _slideAnimation,
                              child: ScaleTransition(
                                scale: _scaleAnimation,
                                child: Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Colors.white.withValues(alpha: 0.2),
                                    boxShadow: [
                                      BoxShadow(
                                        color: Colors.white.withValues(alpha: 0.3),
                                        blurRadius: 20,
                                        spreadRadius: 5,
                                      ),
                                    ],
                                  ),
                                  child: const Icon(
                                    Icons.help_center,
                                    size: 40,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                           const SizedBox(height: SpacingTokens.space4),
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: const Text(
                                '帮助中心',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 28,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                           const SizedBox(height: SpacingTokens.space2),
                            FadeTransition(
                              opacity: _fadeAnimation,
                              child: Text(
                                '有什么可以帮助您？',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.8),
                                  fontSize: 16,
                                ),
                              ),
                            ),
                          ],
                         const SizedBox(height: SpacingTokens.space5),
                          // 搜索框
                          _buildSearchBar(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            leading: IconButton(
              icon: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.arrow_back_ios, color: Colors.white),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                context.pop();
              },
            ),
            actions: [
              IconButton(
                icon: Container(
                  padding: SpacingTokens.paddingSm,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(Icons.headset_mic, color: Colors.white),
                ),
                onPressed: () {
                  HapticFeedback.lightImpact();
                  _contactSupport();
                },
              ),
            ],
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(30),
              child: Container(
                height: 30,
                decoration: const BoxDecoration(
                  color: Color(0xFFF8FAFC),
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(30),
                    topRight: Radius.circular(30),
                  ),
                ),
              ),
            ),
          ),

          // 内容区域
          if (_filteredCategories.isEmpty)
            SliverFillRemaining(
              child: _buildEmptyState(),
            )
          else
            SliverPadding(
              padding: SpacingTokens.paddingLg,
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    return _buildModernCategoryCard(
                      _filteredCategories[index],
                      index,
                    );
                  },
                  childCount: _filteredCategories.length,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        onTap: () {
          setState(() {
            _isSearching = true;
          });
        },
        decoration: InputDecoration(
          hintText: '搜索帮助内容...',
          hintStyle: TextStyle(color: Colors.grey[400]),
          prefixIcon: const Icon(
            Icons.search,
            color: Color(0xFF667EEA),
          ),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Colors.grey),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                      _isSearching = false;
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(25),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
          contentPadding:
              EdgeInsets.symmetric(horizontal: 20, vertical: 16),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildModernCategoryCard(HelpCategory category, int index) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 100)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    final isExpanded = _expandedStates[category.title] ?? false;

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: Offset(0.3, 0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOutBack,
        ));

        final scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.elasticOut,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 20),
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.white,
              Color(0xFFFBFCFE),
            ],
          ),
          borderRadius: BorderRadius.circular(25),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 20,
              offset: Offset(0, 8),
            ),
          ],
        ),
        child: Theme(
          data: Theme.of(context).copyWith(
            dividerColor: Colors.transparent,
          ),
          child: ExpansionTile(
            key: PageStorageKey(category.title),
            initiallyExpanded: _expandedStates[category.title] ?? false,
            onExpansionChanged: (expanded) {
              HapticFeedback.lightImpact();
              setState(() {
                _expandedStates[category.title] = expanded;
              });
            },
            leading: Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    category.color.withValues(alpha: 0.8),
                    category.color,
                  ],
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: category.color.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                category.icon,
                color: Colors.white,
                size: 24,
              ),
            ),
            title: Text(
              category.title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF1A1E25),
              ),
            ),
            subtitle: AnimatedCrossFade(
              firstChild: Text(
                category.description,
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
              secondChild: Text(
                '${category.questions.length} 个问题',
                style: TextStyle(
                  fontSize: 13,
                  color: Colors.grey[600],
                ),
              ),
              crossFadeState: isExpanded
                  ? CrossFadeState.showSecond
                  : CrossFadeState.showFirst,
              duration: const Duration(milliseconds: 200),
            ),
            trailing: AnimatedRotation(
              turns: isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: category.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  Icons.expand_more,
                  color: category.color,
                ),
              ),
            ),
            children: category.questions.asMap().entries.map((entry) {
              final questionIndex = entry.key;
              final question = entry.value;
              return _buildModernQuestionTile(
                  question, category.color, questionIndex);
            }).toList(),
          ),
        ),
      ),
    );
  }

  Widget _buildModernQuestionTile(
      HelpQuestion question, Color color, int index) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(15),
        border: Border.all(
          color: Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(
          dividerColor: Colors.transparent,
        ),
        child: ExpansionTile(
          tilePadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          childrenPadding: EdgeInsets.fromLTRB(16, 0, 16, 16),
          leading: Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  color.withValues(alpha: 0.1),
                  color.withValues(alpha: 0.2),
                ],
              ),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                'Q',
                style: TextStyle(
                  color: color,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          title: Text(
            question.question,
            style: const TextStyle(
              fontSize: 15,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1A1E25),
            ),
          ),
          children: [
            Container(
              padding: SpacingTokens.paddingMd,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.03),
                    blurRadius: 10,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: color.withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Center(
                          child: Text(
                            'A',
                            style: TextStyle(
                              color: color,
                              fontWeight: FontWeight.bold,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ),
                     const SizedBox(width: 8),
                      Text(
                        '回答',
                        style: TextStyle(
                          fontSize: 13,
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                 const SizedBox(height: 12),
                  Text(
                    question.answer,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      height: 1.5,
                    ),
                  ),
                 const SizedBox(height: SpacingTokens.space4),
                  Row(
                    children: [
                      _buildActionButton(
                        icon: Icons.thumb_up_outlined,
                        label: '有帮助',
                        onTap: () {
                          HapticFeedback.lightImpact();
                          _markQuestionHelpful(question.question, true);
                        },
                        isSelected: _questionHelpful[question.question] == true,
                      ),
                     const SizedBox(width: 12),
                      _buildActionButton(
                        icon: Icons.thumb_down_outlined,
                        label: '没帮助',
                        onTap: () {
                          HapticFeedback.lightImpact();
                          _markQuestionHelpful(question.question, false);
                        },
                        isSelected: _questionHelpful[question.question] == false,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    final bool isSelected = false,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected 
              ? Color(0xFF667EEA).withValues(alpha: 0.1)
              : Colors.grey[100],
          borderRadius: BorderRadius.circular(15),
          border: isSelected 
              ? Border.all(color: Color(0xFF667EEA), width: 1)
              : null,
        ),
        child: Row(
          children: [
            Icon(
              icon, 
              size: 16, 
              color: isSelected ? Color(0xFF667EEA) : Colors.grey[700],
            ),
           const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: isSelected ? Color(0xFF667EEA) : Colors.grey[700],
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF667EEA).withValues(alpha: 0.1),
                  Color(0xFF764BA2).withValues(alpha: 0.1),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.search_off,
              size: 48,
              color: Color(0xFF667EEA),
            ),
          ),
         const SizedBox(height: SpacingTokens.space4),
         const Text(
            '没有找到相关内容',
            style: TextStyle(
              fontSize: 18,
              color: Color(0xFF1A1E25),
              fontWeight: FontWeight.w600,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            '试试其他关键词',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
         const SizedBox(height: SpacingTokens.space6),
          ElevatedButton.icon(
            onPressed: () {
              HapticFeedback.lightImpact();
              _searchController.clear();
              setState(() {
                _searchQuery = '';
                _isSearching = false;
              });
            },
            icon: const Icon(Icons.refresh),
            label: const Text('清除搜索'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Color(0xFF667EEA),
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _contactSupport() {
    context.navigateTo('/feedback');
  }

  void _showFeedbackSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
           const Icon(Icons.check_circle, color: Colors.white, size: 20),
           const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Color(0xFF667EEA),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  // 标记问题是否有帮助
  void _markQuestionHelpful(String question, bool isHelpful) {
    setState(() {
      _questionHelpful[question] = isHelpful;
    });
    
    if (isHelpful) {
      _showFeedbackSnackBar('感谢您的反馈！');
    } else {
      _showFeedbackDialog();
    }
    
    // TODO: 将反馈数据发送到服务器
    debugPrint('Question: $question, Helpful: $isHelpful');
  }

  void _showFeedbackDialog() {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Padding(
          padding: SpacingTokens.paddingLg,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
             const Text(
                '需要更多帮助？',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
             const SizedBox(height: SpacingTokens.space4),
              Text(
                '如果这个答案没有解决您的问题，您可以联系客服获得更多帮助。',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
             const SizedBox(height: SpacingTokens.space6),
              Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      child: const Text('稍后再说'),
                    ),
                  ),
                 const SizedBox(width: 12),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _contactSupport();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Color(0xFF667EEA),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(25),
                        ),
                      ),
                      child: const Text('联系客服'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// 帮助分类数据模型
class HelpCategory {
  final String title;
  final IconData icon;
  final Color color;
  final String description;
  final List<HelpQuestion> questions;

  HelpCategory({
    required this.title,
    required this.icon,
    required this.color,
    required this.description,
    required this.questions,
  });
}

// 帮助问题数据模型
class HelpQuestion {
  final String question;
  final String answer;

  HelpQuestion({
    required this.question,
    required this.answer,
  });
}
