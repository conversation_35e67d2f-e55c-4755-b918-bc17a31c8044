import 'dart:math' show pi;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'dart:async';
import 'package:dio/dio.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';
import 'package:user_app/shared/data/browsing_history_api.dart';
import 'package:user_app/shared/services/browsing_history_service.dart';
import 'package:user_app/models/browsing_history_models.dart';
import 'package:user_app/core/models/page_result.dart';
import 'package:user_app/core/di/injection.dart';

class BrowsingHistoryPage extends StatefulWidget {
 const BrowsingHistoryPage({super.key});

  @override
  State<BrowsingHistoryPage> createState() => _BrowsingHistoryPageState();
}

class _BrowsingHistoryPageState extends State<BrowsingHistoryPage>
    with TickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;
  
  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _pulseController;
  late AnimationController _rotateController;
  
  late Animation<double> _fadeAnimation;
  // Removed unused _slideAnimation field
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotateAnimation;

  final List<AnimationController> _itemAnimationControllers = [];

  // 历史记录数据
  List<BrowsingHistoryResponse> _allHistory = [];
  final Map<DateTime, List<BrowsingHistoryResponse>> _groupedHistory = {};
  
  // API 服务
  late BrowsingHistoryService _browsingHistoryService;

  // 编辑模式
  bool _isEditMode = false;
  final Set<int> _selectedItems = {};

  // 搜索
  String _searchQuery = '';

  bool _isLoading = true;
  bool _hasError = false;
  String _errorMessage = '';

  // 分页相关
  int _currentPage = 1;
  final int _pageSize = 20;
  bool _hasMoreData = true;
  final ScrollController _scrollController = ScrollController();
  
  // 防抖搜索
  Timer? _searchDebounceTimer;

  @override
  void initState() {
    super.initState();
    
    // 初始化 API 服务
    final dio = getIt<Dio>();
    _browsingHistoryService = BrowsingHistoryService(
      BrowsingHistoryApi(dio),
    );
    
    _tabController = TabController(length: 4, vsync: this);
    
    // 添加滚动监听
    _scrollController.addListener(_onScroll);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),  
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    _rotateController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    // Slide animation removed as unused
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 2 * pi,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.linear,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();

    _loadHistory();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _pulseController.dispose();
    _rotateController.dispose();
    _searchDebounceTimer?.cancel();
    for (final controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadHistory({bool isRefresh = false}) async {
    if (isRefresh) {
      _currentPage = 1;
      _hasMoreData = true;
      _allHistory.clear();
    }

    setState(() {
      _isLoading = _allHistory.isEmpty;
      _hasError = false;
    });

    try {
      // 获取当前选中的类型
      String? browseType;
      if (_tabController.index > 0) {
        final types = ['moment', 'spot', 'user'];
        browseType = types[_tabController.index - 1];
      }

      final request = BrowsingHistoryQueryRequest(
        pageNum: _currentPage,
        pageSize: _pageSize,
        browseType: browseType,
        keyword: _searchQuery.isNotEmpty ? _searchQuery : null,
      );

      final PageResult<BrowsingHistoryResponse> result = 
          await _browsingHistoryService.getBrowsingHistoryList(request);

      setState(() {
        if (isRefresh) {
          _allHistory = result.records;
        } else {
          _allHistory.addAll(result.records);
        }
        
        _hasMoreData = _allHistory.length < result.total;
        _isLoading = false;
        _groupHistory();
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
        _hasError = true;
        _errorMessage = e.toString();
      });
    }
  }

  void _groupHistory() {
    _groupedHistory.clear();
    
    for (final item in _allHistory) {
      final date = DateTime(
        item.visitTime.year,
        item.visitTime.month,
        item.visitTime.day,
      );

      if (_groupedHistory.containsKey(date)) {
        _groupedHistory[date]!.add(item);
      } else {
        _groupedHistory[date] = [item];
      }
    }
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      _loadMoreData();
    }
  }

  Future<void> _loadMoreData() async {
    if (_isLoading || !_hasMoreData) return;

    _currentPage++;
    _loadHistory();
  }

  Future<void> _refreshData() async {
    return _loadHistory(isRefresh: true);
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
    _debounceSearch();
  }

  void _debounceSearch() {
    _searchDebounceTimer?.cancel();
    _searchDebounceTimer = Timer(const Duration(milliseconds: 500), () {
      _refreshData();
    });
  }

  AnimationController _createItemController(int index) {
    // 确保列表有足够的控制器
    while (index >= _itemAnimationControllers.length) {
      final controllerIndex = _itemAnimationControllers.length;
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (controllerIndex * 50)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }
    return _itemAnimationControllers[index];
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: _isEditMode ? '已选择 ${_selectedItems.length} 条' : '浏览历史',
      hasTabBar: true,
      tabController: _tabController,
      tabs: const [
        Tab(text: '全部'),
        Tab(text: '动态'),
        Tab(text: '钓点'),
        Tab(text: '用户'),
      ],
      onRefresh: _refreshData,
      enableSearch: true,
      onSearchChanged: _onSearchChanged,
      showAnimations: true,
      gradientColors: const [
        Color(0xFF4E8BF7),
        Color(0xFF5F95FF),
      ],
      backgroundDecoration: _buildBackgroundDecoration(),
      expandedHeight: 200,
      leading: _isEditMode
          ? IconButton(
              icon: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.close, color: Colors.white),
              ),
              onPressed: () {
                HapticFeedback.lightImpact();
                _exitEditMode();
              },
            )
          : null,
      actions: _buildAppBarActions(),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildHistoryTab(null),
          _buildHistoryTab('moment'),
          _buildHistoryTab('spot'),
          _buildHistoryTab('user'),
        ],
      ),
    );
  }
  
  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 旋转的时钟装饰
        Positioned(
          top: 80,
          right: -40,
          child: AnimatedBuilder(
            animation: _rotateAnimation,
            builder: (context, child) {
              return Transform.rotate(
                angle: _rotateAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.1),
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white.withValues(alpha: 0.2),
                      width: 2,
                    ),
                  ),
                  child: Icon(
                    Icons.access_time,
                    size: 60,
                    color: Colors.white.withValues(alpha: 0.2),
                  ),
                ),
              );
            },
          ),
        ),
        // 脉冲圆圈
        Positioned(
          bottom: 100,
          left: -60,
          child: AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 180,
                  height: 180,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: RadialGradient(
                      colors: [
                        Colors.white.withValues(alpha: 0.1),
                        Colors.white.withValues(alpha: 0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        // 历史图标装饰
        Positioned(
          top: 140,
          left: 30,
          child: AnimatedBuilder(
            animation: _scaleAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value * 0.8,
                child: Icon(
                  Icons.history,
                  size: 40,
                  color: Colors.white.withValues(alpha: 0.15),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
  
  Widget _buildHistoryTab(String? type) {
    if (_hasError) {
      return UnifiedErrorState(
        title: '加载失败',
        subtitle: _errorMessage,
        onRetry: _refreshData,
      );
    }
    
    if (_isLoading && _allHistory.isEmpty) {
      return const UnifiedLoadingState(message: '加载中...');
    }
    
    if (_groupedHistory.isEmpty) {
      return _buildModernEmptyState(type);
    }
    
    return _buildHistoryContent();
  }
  
  List<Widget> _buildAppBarActions() {
    if (_isEditMode) {
      return [
        if (_selectedItems.isNotEmpty)
          IconButton(
            icon: Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.delete, color: Colors.white, size: 20),
            ),
            onPressed: () {
              HapticFeedback.mediumImpact();
              _deleteSelectedItems();
            },
          ),
        IconButton(
          icon: Container(
            padding: SpacingTokens.paddingSm,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              _selectedItems.length == _allHistory.length
                  ? Icons.deselect
                  : Icons.select_all,
              color: Colors.white,
              size: 20,
            ),
          ),
          onPressed: () {
            HapticFeedback.lightImpact();
            _selectAll();
          },
        ),
      ];
    } else {
      return [
        PopupMenuButton<String>(
          icon: Container(
            padding: SpacingTokens.paddingSm,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(Icons.more_vert, color: Colors.white, size: 20),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          onSelected: _handleMenuAction,
          itemBuilder: (context) => [
           const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 20),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
           const PopupMenuItem(
              value: 'clear_all',
              child: Row(
                children: [
                  Icon(Icons.delete_sweep, size: 20, color: Colors.red),
                  SizedBox(width: 8),
                  Text('清空历史', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ];
    }
  }

  Widget _buildHistoryContent() {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(bottom: 80),
      itemCount: _groupedHistory.length + (_hasMoreData ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == _groupedHistory.length) {
          // 加载更多指示器
          return const Padding(
            padding: SpacingTokens.paddingMd,
            child: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF4E8BF7)),
              ),
            ),
          );
        }

        final date = _groupedHistory.keys.elementAt(index);
        final items = _groupedHistory[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildDateHeader(date, items.length),
            ...items.asMap().entries.map((entry) {
              final itemIndex = entry.key;
              final item = entry.value;
              return _buildAnimatedHistoryItem(item, index * 10 + itemIndex);
            }),
          ],
        );
      },
    );
  }

  Widget _buildDateHeader(DateTime date, int count) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF4E8BF7), Color(0xFF5F95FF)],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: const Color(0xFF4E8BF7).withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              _formatDateHeader(date),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
         const SizedBox(width: 12),
          Text(
            '$count 条记录',
            style: const TextStyle(
              fontSize: 12,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedHistoryItem(BrowsingHistoryResponse item, int index) {
    final controller = _createItemController(index);
    final isSelected = _selectedItems.contains(item.id);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(30 * (1 - controller.value), 0),
          child: Opacity(
            opacity: controller.value,
            child: child,
          ),
        );
      },
      child: GestureDetector(
        onTap: () {
          HapticFeedback.lightImpact();
          if (_isEditMode) {
            setState(() {
              if (_selectedItems.contains(item.id)) {
                _selectedItems.remove(item.id);
              } else {
                _selectedItems.add(item.id);
              }
            });
          } else {
            _navigateToItem(item);
          }
        },
        onLongPress: () {
          HapticFeedback.mediumImpact();
          if (!_isEditMode) {
            _enterEditMode(item.id);
          }
        },
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: isSelected
                  ? [
                      const Color(0xFF4E8BF7).withValues(alpha: 0.1),
                      const Color(0xFF5F95FF).withValues(alpha: 0.1),
                    ]
                  : [
                      Colors.white,
                      Colors.white.withValues(alpha: 0.95),
                    ],
            ),
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: isSelected
                    ? const Color(0xFF4E8BF7).withValues(alpha: 0.15)
                    : Colors.black.withValues(alpha: 0.05),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(
              color: isSelected
                  ? const Color(0xFF4E8BF7).withValues(alpha: 0.3)
                  : Colors.transparent,
              width: 2,
            ),
          ),
          child: Padding(
            padding: SpacingTokens.paddingMd,
            child: Row(
              children: [
                // 图标或图片
                Hero(
                  tag: 'history_${item.id}',
                  child: Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: _getTypeGradient(item.browseType),
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: _getTypeColor(item.browseType).withValues(alpha: 0.3),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: item.imageUrl != null
                        ? ClipRRect(
                            borderRadius: BorderRadius.circular(16),
                            child: CachedNetworkImage(
                              imageUrl: item.imageUrl!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              ),
                              errorWidget: (context, url, error) => Icon(
                                _getTypeIcon(item.browseType),
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                          )
                        : Icon(
                            _getTypeIcon(item.browseType),
                            color: Colors.white,
                            size: 28,
                          ),
                  ),
                ),
               const SizedBox(width: 16),

                // 内容
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              item.title ?? '未知标题',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: ColorTokens.onSurface,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          _buildTypeBadge(item.browseType),
                        ],
                      ),
                     const SizedBox(height: 6),
                      if (item.description != null)
                        Text(
                          item.description!,
                          style: const TextStyle(
                            fontSize: 13,
                            color: ColorTokens.onSurfaceVariant,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                     const SizedBox(height: SpacingTokens.space2),
                      Row(
                        children: [
                         const Icon(
                            Icons.access_time,
                            size: 14,
                            color: ColorTokens.onSurfaceVariant,
                          ),
                         const SizedBox(width: 4),
                          Text(
                            _formatViewTime(item.visitTime),
                            style: const TextStyle(
                              fontSize: 12,
                              color: ColorTokens.onSurfaceVariant,
                            ),
                          ),
                          if (item.formattedDuration != null) ...[
                           const SizedBox(width: 16),
                           const Icon(
                              Icons.timer,
                              size: 14,
                              color: ColorTokens.onSurfaceVariant,
                            ),
                           const SizedBox(width: 4),
                            Text(
                              item.formattedDuration!,
                              style: const TextStyle(
                                fontSize: 12,
                                color: ColorTokens.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),
                ),

                // 选择指示器或操作按钮
                if (_isEditMode)
                  Container(
                    width: 28,
                    height: 28,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: isSelected
                          ? const LinearGradient(
                              colors: [Color(0xFF4E8BF7), Color(0xFF5F95FF)],
                            )
                          : null,
                      color: isSelected ? null : Colors.white,
                      border: Border.all(
                        color: isSelected
                            ? Colors.transparent
                            : Colors.grey[400]!,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: isSelected
                        ? const Icon(Icons.check, size: 18, color: Colors.white)
                        : null,
                  )
                else
                  IconButton(
                    icon: const Icon(Icons.more_vert, color: ColorTokens.onSurfaceVariant),
                    onPressed: () => _showItemOptions(item),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTypeBadge(String type) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: _getTypeGradient(type).map((c) => c.withValues(alpha: 0.15)).toList(),
        ),
        borderRadius: BorderRadius.circular(15),
      ),
      child: Text(
        _getTypeLabel(type),
        style: TextStyle(
          fontSize: 11,
          color: _getTypeColor(type),
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
  
  Widget _buildModernEmptyState(String? type) {
    IconData icon;
    String title;
    String subtitle;

    if (_searchQuery.isNotEmpty) {
      icon = Icons.search_off;
      title = '没有找到相关记录';
      subtitle = '试试其他关键词';
    } else if (type != null) {
      final typeLabels = {'moment': '动态', 'spot': '钓点', 'user': '用户'};
      icon = _getTypeIcon(type);
      title = '还没有浏览过${typeLabels[type] ?? '内容'}';
      subtitle = '快去探索吧';
    } else {
      icon = Icons.history;
      title = '暂无浏览记录';
      subtitle = '你浏览过的内容会显示在这里';
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    const Color(0xFF4E8BF7).withValues(alpha: 0.1),
                    const Color(0xFF5F95FF).withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 60,
                color: const Color(0xFF4E8BF7),
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
          FadeTransition(
            opacity: _fadeAnimation,
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: ColorTokens.onSurface,
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 16,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getTypeGradient(String type) {
    switch (type) {
      case 'moment':
        return [const Color(0xFF667EEA), const Color(0xFF764BA2)];
      case 'spot':
        return [const Color(0xFF4CAF50), const Color(0xFF8BC34A)];
      case 'user':
        return [const Color(0xFFFF6B6B), const Color(0xFFFFD93D)];
      case 'topic':
        return [const Color(0xFF00BCD4), const Color(0xFF03A9F4)];
      default:
        return [Colors.grey, Colors.grey[700]!];
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'moment':
        return const Color(0xFF667EEA);
      case 'spot':
        return const Color(0xFF4CAF50);
      case 'user':
        return const Color(0xFFFF6B6B);
      case 'topic':
        return const Color(0xFF00BCD4);
      default:
        return Colors.grey;
    }
  }

  IconData _getTypeIcon(String type) {
    switch (type) {
      case 'moment':
        return Icons.article;
      case 'spot':
        return Icons.location_on;
      case 'user':
        return Icons.person;
      case 'topic':
        return Icons.tag;
      default:
        return Icons.history;
    }
  }

  String _getTypeLabel(String type) {
    switch (type) {
      case 'moment':
        return '动态';
      case 'spot':
        return '钓点';
      case 'user':
        return '用户';
      case 'topic':
        return '话题';
      default:
        return '其他';
    }
  }

  void _enterEditMode(int? initialSelectedId) {
    setState(() {
      _isEditMode = true;
      if (initialSelectedId != null) {
        _selectedItems.add(initialSelectedId);
      }
    });
  }

  void _exitEditMode() {
    setState(() {
      _isEditMode = false;
      _selectedItems.clear();
    });
  }

  void _selectAll() {
    setState(() {
      if (_selectedItems.length == _allHistory.length) {
        _selectedItems.clear();
      } else {
        _selectedItems
          ..clear()
          ..addAll(_allHistory.map((item) => item.id));
      }
    });
  }

  Future<void> _deleteSelectedItems() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text('删除选中记录'),
        content: Text('确定要删除 ${_selectedItems.length} 条浏览记录吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              
              try {
                await _browsingHistoryService.deleteBrowsingRecords(_selectedItems.toList());
                
                setState(() {
                  _allHistory.removeWhere((item) => _selectedItems.contains(item.id));
                  _groupHistory();
                  _exitEditMode();
                });
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('已删除 ${_selectedItems.length} 条记录'),
                      backgroundColor: const Color(0xFF4E8BF7),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('删除失败: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteItem(BrowsingHistoryResponse item) async {
    try {
      await _browsingHistoryService.deleteBrowsingRecord(item.id);
      
      setState(() {
        _allHistory.removeWhere((h) => h.id == item.id);
        _groupHistory();
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('已删除该记录'),
            backgroundColor: const Color(0xFF4E8BF7),
          action: SnackBarAction(
            label: '撤销',
            textColor: Colors.white,
            onPressed: () {
              // 注意：真实的撤销需要重新调用API添加记录
              _refreshData();
            },
          ),
        ),
      );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除失败: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _showClearAllDialog() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: const Text('清空历史记录'),
        content: const Text('确定要清空所有浏览历史吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              
              try {
                await _browsingHistoryService.clearAllBrowsingHistory();
                
                setState(() {
                  _allHistory.clear();
                  _groupedHistory.clear();
                });
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                   const SnackBar(
                      content: Text('已清空所有历史记录'),
                      backgroundColor: Color(0xFF4E8BF7),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('清空失败: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('清空'),
          ),
        ],
      ),
    );
  }

  void _showItemOptions(BrowsingHistoryResponse item) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        padding: const EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Colors.red.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.delete, color: Colors.red),
              ),
              title: const Text(
                '删除记录',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                HapticFeedback.mediumImpact();
                _deleteItem(item);
              },
            ),
            ListTile(
              leading: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: const Color(0xFF4E8BF7).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.share, color: Color(0xFF4E8BF7)),
              ),
              title: const Text(
                '分享',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: 实现分享功能
              },
            ),
          ],
        ),
      ),
    );
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'edit':
        _enterEditMode(null);
        break;
      case 'clear_all':
        _showClearAllDialog();
        break;
    }
  }

  void _navigateToItem(BrowsingHistoryResponse item) {
    switch (item.browseType) {
      case 'moment':
        context.navigateTo('/moment-detail/${item.targetId}');
        break;
      case 'spot':
        context.navigateTo('/spot-detail/${item.targetId}');
        break;
      case 'user':
        context.navigateTo('/profile/${item.targetId}');
        break;
    }
  }

  String _formatDateHeader(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));

    if (date == today) {
      return '今天';
    } else if (date == yesterday) {
      return '昨天';
    } else if (date.year == now.year) {
      return '${date.month}月${date.day}日';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }

  String _formatViewTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}