import 'dart:math' show sin, pi;

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:user_app/features/fishing_spots/data/fishing_spot_api.dart';
import 'package:user_app/features/fishing_spots/models/fishing_spot_vo.dart';
import 'package:user_app/scaffold/unified_page_scaffold.dart';

class MySpotsPage extends StatefulWidget {
 const MySpotsPage({super.key});

  @override
  State<MySpotsPage> createState() => _MySpotsPageState();
}

class _MySpotsPageState extends State<MySpotsPage>
    with TickerProviderStateMixin {
  // Tab控制器
  late TabController _tabController;

  // 动画控制器
  late AnimationController _waveController;
  late AnimationController _itemAnimationController;
  late Animation<double> _waveAnimation;

  // 搜索
  String _searchQuery = '';

  // 滚动控制器
  final ScrollController _createdScrollController = ScrollController();
  final ScrollController _visitedScrollController = ScrollController();

  // 数据
  List<FishingSpotVo> _createdSpots = [];
  List<FishingSpotVo> _visitedSpots = [];
  bool _isLoadingCreated = true;
  bool _isLoadingVisited = true;
  int _createdPage = 0;
  int _visitedPage = 0;
  static const int _pageSize = 20;
  String? _createdError;
  String? _visitedError;
  bool _hasMoreCreated = true;
  bool _hasMoreVisited = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    // 初始化动画控制器
    _waveController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
    _itemAnimationController = AnimationController(
      duration: MotionTokens.durationSlow,
      vsync: this,
    );

    _waveAnimation = CurvedAnimation(
      parent: _waveController,
      curve: Curves.easeInOut,
    );

    // 启动动画
    _itemAnimationController.forward();

    // 加载数据
    _loadSpots();

    // 添加滚动监听
    _createdScrollController.addListener(_onCreatedScroll);
    _visitedScrollController.addListener(_onVisitedScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _waveController.dispose();
    _itemAnimationController.dispose();
    _createdScrollController.dispose();
    _visitedScrollController.dispose();
    super.dispose();
  }

  void _loadSpots() {
    _loadCreatedSpots(refresh: true);
    _loadVisitedSpots(refresh: true);
  }

  Future<void> _loadCreatedSpots({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _createdPage = 0;
        _isLoadingCreated = true;
        _createdError = null;
        _hasMoreCreated = true;
      });
    }

    try {
      final api = getIt<FishingSpotApi>();
      final result = await api.getMyCreatedSpots(
        page: _createdPage,
        pageSize: _pageSize,
      );

      setState(() {
        if (refresh) {
          _createdSpots = result.records;
        } else {
          _createdSpots.addAll(result.records);
        }
        _isLoadingCreated = false;
        _hasMoreCreated = _createdSpots.length < result.total;
      });
    } catch (e) {
      setState(() {
        _isLoadingCreated = false;
        _createdError = '加载失败，请重试';
      });
    }
  }

  Future<void> _loadVisitedSpots({bool refresh = false}) async {
    if (refresh) {
      setState(() {
        _visitedPage = 0;
        _isLoadingVisited = true;
        _visitedError = null;
        _hasMoreVisited = true;
      });
    }

    try {
      final api = getIt<FishingSpotApi>();
      final result = await api.getRecentCheckins(
        page: _visitedPage,
        pageSize: _pageSize,
      );

      setState(() {
        if (refresh) {
          _visitedSpots = result.records;
        } else {
          _visitedSpots.addAll(result.records);
        }
        _isLoadingVisited = false;
        _hasMoreVisited = _visitedSpots.length < result.total;
      });
    } catch (e) {
      setState(() {
        _isLoadingVisited = false;
        _visitedError = '加载失败，请重试';
      });
    }
  }

  void _onCreatedScroll() {
    if (_createdScrollController.position.pixels >=
        _createdScrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingCreated && _hasMoreCreated) {
        _createdPage++;
        _loadCreatedSpots();
      }
    }
  }

  void _onVisitedScroll() {
    if (_visitedScrollController.position.pixels >=
        _visitedScrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingVisited && _hasMoreVisited) {
        _visitedPage++;
        _loadVisitedSpots();
      }
    }
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query.toLowerCase();
    });
  }

  List<FishingSpotVo> _getFilteredSpots(List<FishingSpotVo> spots) {
    if (_searchQuery.isEmpty) return spots;
    return spots
        .where((spot) =>
            spot.name.toLowerCase().contains(_searchQuery) ||
            (spot.address.toLowerCase().contains(_searchQuery)))
        .toList();
  }

  @override
  Widget build(BuildContext context) {
    return UnifiedPageScaffold(
      title: '我的钓点',
      hasTabBar: true,
      tabController: _tabController,
      tabs: const [
        Tab(text: '我创建的'),
        Tab(text: '访问过的'),
      ],
      onRefresh: () async {
        HapticFeedback.lightImpact();
        _loadSpots();
      },
      enableSearch: true,
      onSearchChanged: _onSearchChanged,
      showAnimations: true,
      gradientColors: const [
        ColorTokens.primaryContainer,
        ColorTokens.secondaryContainer,
      ],
      backgroundDecoration: _buildBackgroundDecoration(),
      expandedHeight: 200,
      floatingActionButton: TweenAnimationBuilder<double>(
        tween: Tween(begin: 0, end: 1),
        duration: MotionTokens.durationSlow,
        curve: Curves.elasticOut,
        builder: (context, value, child) {
          return Transform.scale(
            scale: value,
            child: FloatingActionButton.extended(
              onPressed: () {
                HapticFeedback.mediumImpact();
                context.navigateTo('/spots/create');
              },
              backgroundColor: ColorTokens.primary,
              icon: const Icon(Icons.add_location_alt,
                  color: ColorTokens.onPrimary),
              label: Text(
                '创建钓点',
                style: TypographyTokens.labelLarge.copyWith(
                  color: ColorTokens.onPrimary,
                ),
              ),
              elevation: 4,
            ),
          );
        },
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSpotsList(
            spots: _getFilteredSpots(_createdSpots),
            isLoading: _isLoadingCreated,
            error: _createdError,
            scrollController: _createdScrollController,
            emptyIcon: Icons.add_location_alt_rounded,
            emptyTitle: '还没有创建钓点',
            emptySubtitle: '分享你发现的好钓点，让更多钓友受益',
            isCreated: true,
          ),
          _buildSpotsList(
            spots: _getFilteredSpots(_visitedSpots),
            isLoading: _isLoadingVisited,
            error: _visitedError,
            scrollController: _visitedScrollController,
            emptyIcon: Icons.explore_outlined,
            emptyTitle: '还没有访问过钓点',
            emptySubtitle: '去探索更多精彩的钓点吧',
            isCreated: false,
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 波浪装饰
        Positioned(
          bottom: -50,
          left: 0,
          right: 0,
          child: AnimatedBuilder(
            animation: _waveAnimation,
            builder: (context, child) {
              return CustomPaint(
                size: Size(MediaQuery.of(context).size.width, 100),
                painter: WavePainter(_waveAnimation.value),
              );
            },
          ),
        ),
        // 装饰圆圈
        Positioned(
          top: 100,
          left: -50,
          child: Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: ColorTokens.onPrimary.withValues(alpha: 0.1),
                width: 2,
              ),
            ),
          ),
        ),
        Positioned(
          bottom: 100,
          right: -30,
          child: Container(
            width: 100,
            height: 100,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: ColorTokens.onPrimary.withValues(alpha: 0.05),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSpotsList({
    required List<FishingSpotVo> spots,
    required bool isLoading,
    required String? error,
    required ScrollController scrollController,
    required IconData emptyIcon,
    required String emptyTitle,
    required String emptySubtitle,
    required bool isCreated,
  }) {
    if (isLoading && spots.isEmpty) {
      return const UnifiedLoadingState(message: '加载中...');
    }

    if (error != null && spots.isEmpty) {
      return UnifiedErrorState(
        title: '加载失败',
        subtitle: error,
        onRetry: _loadSpots,
      );
    }

    if (spots.isEmpty) {
      return UnifiedEmptyState(
        icon: emptyIcon,
        title: emptyTitle,
        subtitle: emptySubtitle,
      );
    }

    return ListView.builder(
      controller: scrollController,
      padding: EdgeInsets.only(bottom: SpacingTokens.space8 * 2),
      itemCount: spots.length + (isLoading ? 1 : 0),
      itemBuilder: (context, index) {
        if (index == spots.length) {
          return const Padding(
            padding: EdgeInsets.all(SpacingTokens.space4),
            child: Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(ColorTokens.primary),
              ),
            ),
          );
        }

        final spot = spots[index];
        return _buildSpotItem(spot, isCreated, index);
      },
    );
  }

  Widget _buildSpotItem(FishingSpotVo spot, bool isCreated, int index) {
    // 交错动画效果
    final delay = index * 50;

    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: Duration(
          milliseconds: MotionTokens.durationFast.inMilliseconds + delay),
      curve: Curves.easeInOut,
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, SpacingTokens.space6 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: SpacingTokens.space4,
          vertical: SpacingTokens.space2,
        ),
        child: Stack(
          children: [
            // 卡片背景
            UnifiedCard(
              margin: EdgeInsets.zero,
              onTap: () {
                HapticFeedback.lightImpact();
                context.navigateTo('/spots/${spot.id}');
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 钓点图片区域
                  ClipRRect(
                    borderRadius: BorderRadius.vertical(
                      top: Radius.circular(ShapeTokens.radiusMedium),
                    ),
                    child: Container(
                      height: 160,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            ColorTokens.primaryContainer,
                            ColorTokens.secondaryContainer,
                          ],
                        ),
                      ),
                      child: spot.mainImage != null
                          ? CachedNetworkImage(
                              imageUrl: spot.mainImage!,
                              fit: BoxFit.cover,
                              placeholder: (context, url) => const Center(
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                      ColorTokens.onPrimary),
                                ),
                              ),
                              errorWidget: (context, url, error) => const Icon(
                                Icons.place_outlined,
                                size: 60,
                                color: ColorTokens.onPrimary,
                              ),
                            )
                          : const Icon(
                              Icons.place_outlined,
                              size: 60,
                              color: ColorTokens.onPrimary,
                            ),
                    ),
                  ),

                  // 钓点信息区域
                  Padding(
                    padding: EdgeInsets.all(SpacingTokens.space4),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                spot.name,
                                style: TypographyTokens.titleMedium.copyWith(
                                  color: ColorTokens.onSurface,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            if (isCreated) _buildStatusBadge(spot.isOfficial),
                          ],
                        ),

                        SizedBox(height: SpacingTokens.space1),

                        Row(
                          children: [
                           const Icon(
                              Icons.location_on_outlined,
                              size: 16,
                              color: ColorTokens.onSurfaceVariant,
                            ),
                            SizedBox(width: SpacingTokens.space1 / 2),
                            Expanded(
                              child: Text(
                                spot.address ?? '暂无地址信息',
                                style: TypographyTokens.bodyMedium.copyWith(
                                  color: ColorTokens.onSurfaceVariant,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),

                        SizedBox(height: SpacingTokens.space4),

                        // 统计信息
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            _buildStatCard(
                              Icons.star_rounded,
                              spot.rating.toStringAsFixed(1),
                              '评分',
                              ColorTokens.tertiary,
                            ),
                            _buildStatCard(
                              Icons.people_outline_rounded,
                              '${spot.visitorCount}',
                              '访客',
                              ColorTokens.primary,
                            ),
                            _buildStatCard(
                              Icons.check_circle_outline,
                              '${spot.checkinCount}',
                              '签到',
                              ColorTokens.success,
                            ),
                            _buildStatCard(
                              Icons.chat_bubble_outline,
                              '${spot.recentMomentsCount}',
                              '动态',
                              ColorTokens.secondaryContainer,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // 渐变遮罩（仅用于图片区域）
            if (spot.isPaid)
              Positioned(
                top: 12,
                right: 12,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: SpacingTokens.space2,
                    vertical: SpacingTokens.space1 / 2,
                  ),
                  decoration: BoxDecoration(
                    color: ColorTokens.warning,
                    borderRadius:
                        BorderRadius.circular(ShapeTokens.radiusSmall),
                  ),
                  child: Text(
                    '付费',
                    style: TypographyTokens.labelSmall.copyWith(
                      color: ColorTokens.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusBadge(bool isOfficial) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space2,
        vertical: SpacingTokens.space1 / 2,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isOfficial
              ? [ColorTokens.success, ColorTokens.successContainer]
              : [ColorTokens.warning, ColorTokens.warningContainer],
        ),
        borderRadius: BorderRadius.circular(ShapeTokens.radiusSmall),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isOfficial ? Icons.verified : Icons.person,
            size: 12,
            color: ColorTokens.onPrimary,
          ),
          SizedBox(width: SpacingTokens.space1 / 2),
          Text(
            isOfficial ? '官方' : '用户',
            style: TypographyTokens.labelSmall.copyWith(
              fontWeight: FontWeight.bold,
              color: ColorTokens.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
      IconData icon, String value, String label, Color color) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(SpacingTokens.space2),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(ShapeTokens.radiusSmall),
          ),
          child: Icon(
            icon,
            size: 20,
            color: color,
          ),
        ),
        SizedBox(height: SpacingTokens.space1 / 2),
        Text(
          value,
          style: TypographyTokens.labelMedium.copyWith(
            fontWeight: FontWeight.bold,
            color: ColorTokens.onSurface,
          ),
        ),
        Text(
          label,
          style: TypographyTokens.bodySmall.copyWith(
            color: ColorTokens.onSurfaceVariant,
          ),
        ),
      ],
    );
  }
}

// 波浪装饰画笔
class WavePainter extends CustomPainter {
  final double animationValue;

  WavePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = ColorTokens.onPrimary.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = SpacingTokens.space6.toDouble();
    final waveLength = size.width / 2;

    path.moveTo(0, size.height * 0.5);

    for (double x = 0; x <= size.width; x += 1) {
      final y = size.height * 0.5 +
          waveHeight * sin((x / waveLength + animationValue * 2) * 2 * pi);
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.lineTo(0, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(WavePainter oldDelegate) {
    return animationValue != oldDelegate.animationValue;
  }
}
