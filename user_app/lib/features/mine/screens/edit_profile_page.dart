import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/user/data/user_api.dart';
import 'package:user_app/core/di/injection.dart';
import 'package:user_app/features/auth/providers/auth_view_model.dart';
import 'package:user_app/widgets/region_picker_widget.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

class EditProfilePage extends StatefulWidget {
 const EditProfilePage({super.key});

  @override
  State<EditProfilePage> createState() => _EditProfilePageState();
}

class _EditProfilePageState extends State<EditProfilePage>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _titleController;
  late TextEditingController _introduceController;
  late TextEditingController _addressDetailController;

  String? _selectedGender;
  String? _selectedProvince;
  String? _selectedCity;
  String? _selectedCounty;
  String? _userAvatarUrl; // 添加用户头像URL变量
  File? _selectedImage;
  bool _isLoading = false;

  // 增强的状态管理
  bool _hasUnsavedChanges = false;
  bool _isUploading = false;

  // 用户数据加载状态
  bool _isLoadingUserData = true;
  String? _userDataError;

  // 保存原始数据用于比较
  late String _originalName;
  late String _originalTitle;
  late String _originalIntroduce;
  late String _originalAddressDetail;
  late String? _originalProvince;
  late String? _originalCity;
  late String? _originalCounty;
  late String? _originalGender;

  final ImagePicker _picker = ImagePicker();
  late final UserApi _userApi;

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _rotateController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotateAnimation;

  @override
  void initState() {
    super.initState();

    // 初始化 API
    _userApi = UserApi(getIt());

    // 初始化控制器（空值）
    _nameController = TextEditingController();
    _titleController = TextEditingController();
    _introduceController = TextEditingController();
    _addressDetailController = TextEditingController();

    // 从API加载用户数据
    _loadUserData();

    // 监听输入变化
    _nameController.addListener(_checkForChanges);
    _titleController.addListener(_checkForChanges);
    _introduceController.addListener(_checkForChanges);
    _addressDetailController.addListener(_checkForChanges);

    // 初始化动画
    _fadeController = AnimationController(
      duration: MotionTokens.animationDurationSlow,
      vsync: this,
    );
    _slideController = AnimationController(
      duration: MotionTokens.animationDurationMedium,
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: MotionTokens.animationDurationMedium,
      vsync: this,
    );
    _rotateController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _rotateAnimation = Tween<double>(
      begin: 0,
      end: 2 * 3.14159,
    ).animate(CurvedAnimation(
      parent: _rotateController,
      curve: Curves.linear,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();
  }

  Future<void> _loadUserData() async {
    try {
      setState(() {
        _isLoadingUserData = true;
        _userDataError = null;
      });

      // 从API获取当前用户信息
      final user = await _userApi.getCurrentUser();

      // 设置控制器的值
      _nameController.text = user.name;
      _titleController.text = user.title;
      _introduceController.text = user.introduce ?? '';
      _addressDetailController.text = user.addressDetail ?? '';

      // 设置选择的值
      _selectedGender = user.gender == 1
          ? '男'
          : user.gender == 2
              ? '女'
              : null;
      _selectedProvince =
          user.province?.isNotEmpty == true ? user.province : null;
      _selectedCity = user.city?.isNotEmpty == true ? user.city : null;
      _selectedCounty = user.county?.isNotEmpty == true ? user.county : null;
      _userAvatarUrl = user.avatarUrl;

      // 调试地区数据传递
      debugPrint('=== 地区数据传递检查 ===');
      debugPrint('_selectedProvince: $_selectedProvince');
      debugPrint('_selectedCity: $_selectedCity');
      debugPrint('_selectedCounty: $_selectedCounty');

      // 保存原始数据
      _originalName = _nameController.text;
      _originalTitle = _titleController.text;
      _originalIntroduce = _introduceController.text;
      _originalAddressDetail = _addressDetailController.text;
      _originalProvince = _selectedProvince;
      _originalCity = _selectedCity;
      _originalCounty = _selectedCounty;
      _originalGender = _selectedGender;

      setState(() {
        _isLoadingUserData = false;
      });
    } catch (e) {
      debugPrint('加载用户数据失败: $e');
      setState(() {
        _isLoadingUserData = false;
        _userDataError = e.toString();
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
               const Icon(Icons.error_outline, color: ColorTokens.onError, size: 20),
               const SizedBox(width: 8),
               const Expanded(child: Text('加载用户数据失败，请重试')),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _loadUserData(); // 重试
                  },
                  child:
                     const Text('重试', style: TextStyle(color: Theme.of(context).colorScheme.surface)),
                ),
              ],
            ),
            backgroundColor: ColorTokens.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _titleController.dispose();
    _introduceController.dispose();
    _addressDetailController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _rotateController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // 如果正在加载用户数据，显示加载界面
    if (_isLoadingUserData) {
      return Scaffold(
        backgroundColor: ColorTokens.surfaceContainer,
        appBar: AppBar(
          backgroundColor: ColorTokens.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: ColorTokens.onSurfaceVariant),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            '编辑资料',
            style: TypographyTokens.headlineSmall.copyWith(
              color: ColorTokens.onSurface,
            ),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(ColorTokens.primary),
              ),
              SpacingTokens.verticalSpaceMd,
              Text(
                '正在加载用户数据...',
                style: TypographyTokens.bodyLarge.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      );
    }

    // 如果加载出错，显示错误界面
    if (_userDataError != null) {
      return Scaffold(
        backgroundColor: ColorTokens.surfaceContainer,
        appBar: AppBar(
          backgroundColor: ColorTokens.transparent,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: ColorTokens.onSurfaceVariant),
            onPressed: () => Navigator.pop(context),
          ),
          title: Text(
            '编辑资料',
            style: TypographyTokens.headlineSmall.copyWith(
              color: ColorTokens.onSurface,
            ),
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
             const Icon(
                Icons.error_outline,
                size: 64,
                color: ColorTokens.error,
              ),
              SpacingTokens.verticalSpaceMd,
              Text(
                '加载用户数据失败',
                style: TypographyTokens.headlineSmall.copyWith(
                  color: ColorTokens.onSurface,
                ),
              ),
              SpacingTokens.verticalSpaceSm,
              Text(
                '错误详情: $_userDataError',
                style: TypographyTokens.bodyMedium.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              SpacingTokens.verticalSpaceLg,
              DSButton.primary(
                text: '重试',
                onPressed: _loadUserData,
              ),
            ],
          ),
        ),
      );
    }

    return PopScope(
      canPop: !_hasUnsavedChanges,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop && _hasUnsavedChanges) {
          final shouldPop = await _shouldPop();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        backgroundColor: ColorTokens.surfaceContainer,
        body: Stack(
          children: [
            CustomScrollView(
              slivers: [
                // 现代化的 AppBar
                SliverAppBar(
                  expandedHeight: 320,
                  floating: false,
                  pinned: true,
                  backgroundColor: ColorTokens.transparent,
                  elevation: 0,
                  flexibleSpace: FlexibleSpaceBar(
                    background: Stack(
                      children: [
                        // 渐变背景
                        Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                ColorTokens.primaryContainer,
                                ColorTokens.secondaryContainer,
                              ],
                            ),
                          ),
                        ),
                        // 装饰元素
                        Positioned(
                          top: -100,
                          right: -100,
                          child: AnimatedBuilder(
                            animation: _rotateAnimation,
                            builder: (context, child) {
                              return Transform.rotate(
                                angle: _rotateAnimation.value,
                                child: Container(
                                  width: 300,
                                  height: 300,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      color: ColorTokens.onPrimary.withValues(alpha: 0.1),
                                      width: 2,
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                        // 头像编辑区域
                        SafeArea(
                          child: Center(
                            child: Padding(
                              padding: EdgeInsets.only(top: 40),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SlideTransition(
                                    position: _slideAnimation,
                                    child: ScaleTransition(
                                      scale: _scaleAnimation,
                                      child: _buildAvatarEditor(),
                                    ),
                                  ),
                                 const SizedBox(height: SpacingTokens.space4),
                                  FadeTransition(
                                    opacity: _fadeAnimation,
                                    child: Text(
                                      '点击更换头像',
                                      style: TextStyle(
                                        color: ColorTokens.onPrimary.withValues(alpha: 0.8),
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  leading: IconButton(
                    icon: Container(
                      padding: SpacingTokens.paddingSm,
                      decoration: BoxDecoration(
                        color: ColorTokens.onPrimary.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(ShapeTokens.radiusSmall),
                      ),
                      child:
                         const Icon(Icons.arrow_back_ios, color: ColorTokens.onPrimary),
                    ),
                    onPressed: () async {
                      if (_hasUnsavedChanges) {
                        final shouldPop = await _shouldPop();
                        if (shouldPop && context.mounted) {
                          Navigator.pop(context);
                        }
                      } else {
                        HapticFeedback.lightImpact();
                        Navigator.pop(context);
                      }
                    },
                  ),
                  actions: [
                    Padding(
                      padding: EdgeInsets.only(right: 16),
                      child: TextButton(
                        onPressed:
                            _isLoading || _isUploading ? null : _saveProfile,
                        style: TextButton.styleFrom(
                          backgroundColor: ColorTokens.onPrimary.withValues(alpha: 0.2),
                          padding: EdgeInsets.symmetric(
                              horizontal: 20, vertical: 8),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(ShapeTokens.radiusMedium),
                          ),
                        ),
                        child: Text(
                          '保存',
                          style: TextStyle(
                            color: _isLoading || _isUploading
                                ? ColorTokens.onSurfaceVariant
                                : ColorTokens.onPrimary,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                  bottom: PreferredSize(
                    preferredSize: const Size.fromHeight(30),
                    child: Container(
                      height: 30,
                      decoration: const BoxDecoration(
                        color: ColorTokens.surfaceContainer,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(30),
                          topRight: Radius.circular(30),
                        ),
                      ),
                    ),
                  ),
                ),

                // 表单内容
                SliverToBoxAdapter(
                  child: Form(
                    key: _formKey,
                    child: Padding(
                      padding: SpacingTokens.paddingLg,
                      child: Column(
                        children: [
                          // 基本信息卡片
                          _buildModernSection(
                            title: '基本信息',
                            icon: Icons.person_outline,
                            children: [
                              _buildModernTextField(
                                label: '昵称',
                                controller: _nameController,
                                hint: '请输入昵称',
                                maxLength: 20,
                                icon: Icons.badge_outlined,
                                validator: (value) {
                                  if (value == null || value.trim().isEmpty) {
                                    return '昵称不能为空';
                                  }
                                  if (value.trim().length < 2) {
                                    return '昵称至少2个字符';
                                  }
                                  return null;
                                },
                              ),
                             const Divider(height: 1),
                              _buildModernTextField(
                                label: '头衔',
                                controller: _titleController,
                                hint: '设置您的头衔',
                                maxLength: 15,
                                icon: Icons.stars_outlined,
                              ),
                             const Divider(height: 1),
                              _buildGenderSelector(),
                            ],
                          ),

                         const SizedBox(height: SpacingTokens.space5),

                          // 地区选择卡片
                          _buildModernSection(
                            title: '地区信息',
                            icon: Icons.location_on_outlined,
                            children: [
                              RegionPickerWidget(
                                initialProvince: _selectedProvince,
                                initialCity: _selectedCity,
                                initialCounty: _selectedCounty,
                                onRegionChanged: (province, city, county) {
                                  setState(() {
                                    _selectedProvince = province;
                                    _selectedCity = city;
                                    _selectedCounty = county;
                                  });
                                  _checkForChanges();
                                },
                              ),
                             const Divider(height: 1),
                              _buildAddressDetailField(),
                            ],
                          ),

                         const SizedBox(height: SpacingTokens.space5),

                          // 个人简介卡片
                          _buildModernSection(
                            title: '个人简介',
                            icon: Icons.edit_note_outlined,
                            children: [
                              _buildIntroduceField(),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),

            // 加载遮罩
            if (_isLoading || _isUploading)
              Container(
                color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.5),
                child: Center(
                  child: Container(
                    padding: SpacingTokens.paddingLg,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                       const CircularProgressIndicator(
                          valueColor:
                              AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
                        ),
                       const SizedBox(height: SpacingTokens.space4),
                        Text(
                          _isUploading ? '上传中...' : '保存中...',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ), // Stack结束
      ), // Scaffold结束
    ); // PopScope结束
  }

  Widget _buildAvatarEditor() {
    return GestureDetector(
      onTap: _showImagePickerOptions,
      child: Stack(
        children: [
          // 背景光晕
          Container(
            width: 130,
            height: 130,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.3),
                  blurRadius: 30,
                  spreadRadius: 10,
                ),
              ],
            ),
          ),
          // 头像
          Container(
            width: 120,
            height: 120,
            margin: EdgeInsets.all(5),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              border: Border.all(
                color: Theme.of(context).colorScheme.surface,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 20,
                  offset: Offset(0, 10),
                ),
              ],
            ),
            child: ClipOval(
              child: _getAvatarImage(),
            ),
          ),
          // 编辑图标
          Positioned(
            bottom: 5,
            right: 5,
            child: Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                ),
                shape: BoxShape.circle,
                border: Border.all(color: Theme.of(context).colorScheme.surface, width: 3),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: const Icon(
                Icons.camera_alt,
                size: 20,
                color: Theme.of(context).colorScheme.surface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _getAvatarImage() {
    if (_selectedImage != null) {
      return Image.file(
        _selectedImage!,
        fit: BoxFit.cover,
      );
    }

    // 使用从API加载的用户头像URL
    if (_userAvatarUrl != null && _userAvatarUrl!.isNotEmpty) {
      return CachedNetworkImage(
        imageUrl: _userAvatarUrl!,
        fit: BoxFit.cover,
        placeholder: (context, url) => Container(
          color: Theme.of(context).colorScheme.surfaceVariant,
          child: const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary),
              strokeWidth: 2,
            ),
          ),
        ),
        errorWidget: (context, url, error) => _buildDefaultAvatar(),
      );
    }

    return _buildDefaultAvatar();
  }

  Widget _buildDefaultAvatar() {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
        ),
      ),
      child: const Icon(
        Icons.person,
        size: 60,
        color: Theme.of(context).colorScheme.surface,
      ),
    );
  }

  Widget _buildModernSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surface,
            Color(0xFFFBFCFE),
          ],
        ),
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: SpacingTokens.paddingLg,
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: 20, color: Theme.of(context).colorScheme.surface),
                ),
               const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20),
            child: Column(children: children),
          ),
         const SizedBox(height: SpacingTokens.space5),
        ],
      ),
    );
  }

  Widget _buildModernTextField({
    required String label,
    required TextEditingController controller,
    required String hint,
    required IconData icon,
    int? maxLength,
    final bool readOnly = false,
    VoidCallback? onTap,
    String? Function(String?)? validator,
  }) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Container(
            padding: SpacingTokens.paddingSm,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(icon, size: 20, color: Theme.of(context).colorScheme.primary),
          ),
         const SizedBox(width: 16),
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          Expanded(
            child: TextFormField(
              controller: controller,
              readOnly: readOnly,
              onTap: onTap,
              maxLength: maxLength,
              validator: validator,
              decoration: InputDecoration(
                hintText: hint,
                hintStyle: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
                border: InputBorder.none,
                counterText: '',
                suffixIcon: readOnly
                    ? Icon(Icons.arrow_forward_ios,
                        size: 16, color: Theme.of(context).colorScheme.onSurfaceVariant)
                    : null,
              ),
              style: const TextStyle(fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderSelector() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Container(
            padding: SpacingTokens.paddingSm,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: const Icon(Icons.wc, size: 20, color: Theme.of(context).colorScheme.secondary),
          ),
         const SizedBox(width: 16),
         const SizedBox(
            width: 80,
            child: Text(
              '性别',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
          Expanded(
            child: Row(
              children: [
                _buildGenderOption('男', _selectedGender == '男', Icons.male),
               const SizedBox(width: 24),
                _buildGenderOption('女', _selectedGender == '女', Icons.female),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenderOption(String gender, bool isSelected, IconData icon) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        setState(() {
          _selectedGender = gender;
        });
        _checkForChanges();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                )
              : null,
          color: isSelected ? null : Theme.of(context).colorScheme.surfaceVariant,
          borderRadius: BorderRadius.circular(20),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected ? Theme.of(context).colorScheme.surface : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
           const SizedBox(width: 8),
            Text(
              gender,
              style: TextStyle(
                fontSize: 15,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected ? Theme.of(context).colorScheme.surface : Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIntroduceField() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: SpacingTokens.paddingMd,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Theme.of(context).colorScheme.surfaceVariant!,
                width: 1,
              ),
            ),
            child: TextFormField(
              controller: _introduceController,
              maxLines: 4,
              maxLength: 200,
              decoration: InputDecoration(
                hintText: '介绍一下自己，让更多钓友认识你...',
                hintStyle: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
                border: InputBorder.none,
                counterText: '',
              ),
              style: const TextStyle(fontSize: 15, height: 1.5),
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              '${_introduceController.text.length}/200',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showImagePickerOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(25),
            topRight: Radius.circular(25),
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: 20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: EdgeInsets.only(bottom: 20),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
           const Text(
              '选择图片',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
           const SizedBox(height: SpacingTokens.space5),
            ListTile(
              leading: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Icon(Icons.camera_alt, color: Theme.of(context).colorScheme.primary),
              ),
              title: const Text('拍照'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.camera);
              },
            ),
            ListTile(
              leading: Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child:
                   const Icon(Icons.photo_library, color: Theme.of(context).colorScheme.secondary),
              ),
              title: const Text('从相册选择'),
              onTap: () {
                Navigator.pop(context);
                _pickImage(ImageSource.gallery);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCityPicker() {
    // This method is no longer needed as we're using RegionPickerWidget
    // Keeping it for backward compatibility, but it won't be called
  }

  Future<void> _pickImage(ImageSource source) async {
    if (_isUploading) return;

    try {
      setState(() {
        _isUploading = true;
      });

      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 800,
        maxHeight: 800,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _hasUnsavedChanges = true;
        });
      }
    } catch (e) {
      debugPrint('选择图片失败: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: const [
               const Icon(Icons.error_outline, color: ColorTokens.onError, size: 20),
               const SizedBox(width: 8),
               const Expanded(child: Text('选择图片失败，请重试')),
              ],
            ),
            backgroundColor: ColorTokens.error,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  // 检查是否有未保存的更改
  void _checkForChanges() {
    final hasChanges = _nameController.text != _originalName ||
        _titleController.text != _originalTitle ||
        _introduceController.text != _originalIntroduce ||
        _addressDetailController.text != _originalAddressDetail ||
        _selectedProvince != _originalProvince ||
        _selectedCity != _originalCity ||
        _selectedCounty != _originalCounty ||
        _selectedGender != _originalGender ||
        _selectedImage != null;

    if (hasChanges != _hasUnsavedChanges) {
      setState(() {
        _hasUnsavedChanges = hasChanges;
      });
    }
  }

  // 显示离开确认对话框
  Future<bool> _shouldPop() async {
    if (!_hasUnsavedChanges) return true;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: Row(
          children: [
            Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(10),
              ),
              child: const Icon(
                Icons.warning,
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
            ),
           const SizedBox(width: 12),
           const Text(
              '放弃更改？',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        content: const Text('您有未保存的更改，确定要离开吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('继续编辑'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('放弃更改'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  Future<void> _saveProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_isLoading || _isUploading) return;

    setState(() {
      _isLoading = true;
      // _errorMessage = null; // 暂时注释，因为字段被注释了
    });

    try {
      HapticFeedback.mediumImpact();

      // 准备API数据
      final profileData = <String, dynamic>{};

      // 只更新变化的字段
      if (_nameController.text.trim() != _originalName) {
        profileData['name'] = _nameController.text.trim();
      }

      if (_titleController.text.trim() != _originalTitle) {
        profileData['title'] = _titleController.text.trim();
      }

      if (_introduceController.text.trim() != _originalIntroduce) {
        profileData['introduce'] = _introduceController.text.trim();
      }

      if (_addressDetailController.text.trim() != _originalAddressDetail) {
        profileData['address_detail'] = _addressDetailController.text.trim();
      }

      if (_selectedGender != _originalGender) {
        profileData['gender'] =
            _selectedGender == '男' ? 1 : (_selectedGender == '女' ? 2 : 0);
      }

      if (_selectedProvince != _originalProvince) {
        profileData['province'] = _selectedProvince ?? '';
      }

      if (_selectedCity != _originalCity) {
        profileData['city'] = _selectedCity ?? '';
      }

      if (_selectedCounty != _originalCounty) {
        profileData['county'] = _selectedCounty ?? '';
      }

      // 调用API更新用户信息
      if (profileData.isNotEmpty) {
        final updatedUser = await _userApi.updateUserProfile(profileData);

        // 更新AuthViewModel中的用户信息
        if (mounted) {
          final authViewModel = context.read<AuthViewModel>();
          await authViewModel.loadUserProfile(context); // 重新加载用户信息
        }
      }

      // 如果有选择新图片，还需要上传图片
      if (_selectedImage != null) {
        // TODO: 实现图片上传逻辑
        debugPrint('需要上传图片: ${_selectedImage!.path}');
      }

      if (mounted) {
        // 更新原始数据
        _originalName = _nameController.text;
        _originalTitle = _titleController.text;
        _originalIntroduce = _introduceController.text;
        _originalAddressDetail = _addressDetailController.text;
        _originalProvince = _selectedProvince;
        _originalCity = _selectedCity;
        _originalCounty = _selectedCounty;
        _originalGender = _selectedGender;

        setState(() {
          _hasUnsavedChanges = false;
        });

        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
         const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Theme.of(context).colorScheme.surface, size: 20),
               const SizedBox(width: 8),
                Text('资料更新成功'),
              ],
            ),
            backgroundColor: ColorTokens.primary,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
        Navigator.pop(context);
      }
    } catch (e) {
      debugPrint('保存资料失败: $e');
      if (mounted) {
        setState(() {
          // _errorMessage = e.toString(); // 暂时注释，因为字段被注释了
        });

        HapticFeedback.heavyImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
               const Icon(Icons.error_outline, color: ColorTokens.onError, size: 20),
               const SizedBox(width: 8),
               const Expanded(child: Text('保存失败，请检查网络连接')),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _saveProfile(); // 重试
                  },
                  child:
                     const Text('重试', style: TextStyle(color: Theme.of(context).colorScheme.surface)),
                ),
              ],
            ),
            backgroundColor: ColorTokens.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildAddressDetailField() {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: const Icon(Icons.home_outlined,
                    size: 20, color: Theme.of(context).colorScheme.primary),
              ),
             const SizedBox(width: 16),
             const SizedBox(
                width: 80,
                child: Text(
                  '详细地址',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
            ],
          ),
         const SizedBox(height: 12),
          Container(
            padding: SpacingTokens.paddingMd,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainer,
              borderRadius: BorderRadius.circular(15),
              border: Border.all(
                color: Theme.of(context).colorScheme.surfaceVariant!,
                width: 1,
              ),
            ),
            child: TextFormField(
              controller: _addressDetailController,
              maxLines: 3,
              maxLength: 100,
              onChanged: (value) {
                setState(() {}); // 更新字符计数显示
              },
              decoration: InputDecoration(
                hintText: '请输入详细地址，如街道、门牌号等...',
                hintStyle: TextStyle(color: Theme.of(context).colorScheme.onSurfaceVariant),
                border: InputBorder.none,
                counterText: '',
              ),
              style: const TextStyle(fontSize: 15, height: 1.5),
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Align(
            alignment: Alignment.centerRight,
            child: Text(
              '${_addressDetailController.text.length}/100',
              style: TextStyle(
                fontSize: 12,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
