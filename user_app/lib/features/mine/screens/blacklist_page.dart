import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';

class BlacklistPage extends StatefulWidget {
 const BlacklistPage({super.key});

  @override
  State<BlacklistPage> createState() => _BlacklistPageState();
}

class _BlacklistPageState extends State<BlacklistPage> {
  final TextEditingController _searchController = TextEditingController();
  List<BlacklistedUser> _blacklistedUsers = [];
  List<BlacklistedUser> _filteredUsers = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBlacklistedUsers();
    _searchController.addListener(_filterUsers);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadBlacklistedUsers() {
    // 模拟加载数据
    Future.delayed(const Duration(seconds: 1), () {
      setState(() {
        _blacklistedUsers = [
          BlacklistedUser(
            id: 1,
            name: '钓鱼达人',
            avatarUrl: null,
            blockedAt: DateTime.now().subtract(const Duration(days: 30)),
            reason: '骚扰信息',
          ),
          BlacklistedUser(
            id: 2,
            name: '水边垂钓',
            avatarUrl: null,
            blockedAt: DateTime.now().subtract(const Duration(days: 15)),
            reason: '广告推销',
          ),
          BlacklistedUser(
            id: 3,
            name: '鱼乐无穷',
            avatarUrl: null,
            blockedAt: DateTime.now().subtract(const Duration(days: 7)),
            reason: '不当言论',
          ),
        ];
        _filteredUsers = List.from(_blacklistedUsers);
        _isLoading = false;
      });
    });
  }

  void _filterUsers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredUsers = List.from(_blacklistedUsers);
      } else {
        _filteredUsers = _blacklistedUsers
            .where((user) => user.name.toLowerCase().contains(query))
            .toList();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('黑名单管理'),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        actions: [
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: _showBlacklistInfo,
          ),
        ],
      ),
      body: Column(
        children: [
          // 搜索栏
          Container(
            color: Colors.white,
            padding: SpacingTokens.paddingMd,
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索黑名单用户',
                hintStyle: TextStyle(color: Colors.grey[400]),
                prefixIcon: Icon(Icons.search, color: Colors.grey[600]),
                filled: true,
                fillColor: Colors.grey[100],
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30),
                  borderSide: BorderSide.none,
                ),
                contentPadding: EdgeInsets.symmetric(horizontal: 20),
              ),
            ),
          ),

          // 用户列表
          Expanded(
            child: _buildUserList(),
          ),
        ],
      ),
    );
  }

  Widget _buildUserList() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_blacklistedUsers.isEmpty) {
      return _buildEmptyState();
    }

    if (_filteredUsers.isEmpty) {
      return _buildNoResultsState();
    }

    return ListView.separated(
      padding: EdgeInsets.only(top: 8),
      itemCount: _filteredUsers.length,
      separatorBuilder: (context, index) => const Divider(height: 1),
      itemBuilder: (context, index) {
        final user = _filteredUsers[index];
        return _buildUserItem(user);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.block_outlined,
            size: 80,
            color: Colors.grey[300],
          ),
         const SizedBox(height: SpacingTokens.space4),
          Text(
            '暂无黑名单用户',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
         const SizedBox(height: SpacingTokens.space2),
          Text(
            '被拉黑的用户将无法查看你的动态\n也无法给你发送消息',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[400],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNoResultsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 60,
            color: Colors.grey[300],
          ),
         const SizedBox(height: SpacingTokens.space4),
          Text(
            '未找到相关用户',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserItem(BlacklistedUser user) {
    return Container(
      color: Colors.white,
      child: ListTile(
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          radius: 25,
          backgroundImage: user.avatarUrl != null
              ? CachedNetworkImageProvider(user.avatarUrl!)
              : const AssetImage('assets/default_avatar.png') as ImageProvider,
        ),
        title: Text(
          user.name,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
           const SizedBox(height: 4),
            Text(
              '拉黑原因：${user.reason}',
              style: TextStyle(
                fontSize: 13,
                color: Colors.grey[600],
              ),
            ),
           const SizedBox(height: 2),
            Text(
              '拉黑时间：${_formatDate(user.blockedAt)}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[500],
              ),
            ),
          ],
        ),
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          onSelected: (value) {
            if (value == 'remove') {
              _showRemoveDialog(user);
            } else if (value == 'view_profile') {
              _viewProfile(user);
            }
          },
          itemBuilder: (context) => [
           const PopupMenuItem(
              value: 'view_profile',
              child: Row(
                children: [
                  Icon(Icons.person_outline, size: 20),
                 const SizedBox(width: 8),
                  Text('查看主页'),
                ],
              ),
            ),
           const PopupMenuItem(
              value: 'remove',
              child: Row(
                children: [
                  Icon(Icons.remove_circle_outline,
                      size: 20, color: Colors.red),
                 const SizedBox(width: 8),
                  Text('移出黑名单', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showRemoveDialog(BlacklistedUser user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        title: const Text('移出黑名单'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            RichText(
              text: TextSpan(
                style: DefaultTextStyle.of(context).style,
                children: [
                 const TextSpan(text: '确定要将 '),
                  TextSpan(
                    text: user.name,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                 const TextSpan(text: ' 移出黑名单吗？'),
                ],
              ),
            ),
           const SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  Icon(Icons.warning, color: Colors.orange[700], size: 20),
                 const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '移出后，该用户将可以查看你的动态并发送消息',
                      style: TextStyle(
                        fontSize: 13,
                        color: Colors.orange[700],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _removeFromBlacklist(user);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('确定移出'),
          ),
        ],
      ),
    );
  }

  void _removeFromBlacklist(BlacklistedUser user) {
    setState(() {
      _blacklistedUsers.removeWhere((u) => u.id == user.id);
      _filteredUsers.removeWhere((u) => u.id == user.id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已将 ${user.name} 移出黑名单'),
        action: SnackBarAction(
          label: '撤销',
          onPressed: () {
            setState(() {
              _blacklistedUsers.add(user);
              _filterUsers();
            });
          },
        ),
      ),
    );
  }

  void _viewProfile(BlacklistedUser user) {
    context.navigateTo('/profile/${user.id}');
  }

  void _showBlacklistInfo() {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: SpacingTokens.paddingXl,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.info, color: Colors.blue[700], size: 24),
               const SizedBox(width: 8),
               const Text(
                  '关于黑名单',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
           const SizedBox(height: SpacingTokens.space5),
            _buildInfoItem(
              '拉黑后的效果：',
              '• 对方无法查看你的动态和个人主页\n'
                  '• 对方无法给你发送私信\n'
                  '• 对方无法评论你的内容\n'
                  '• 你也无法看到对方的动态',
            ),
           const SizedBox(height: SpacingTokens.space4),
            _buildInfoItem(
              '注意事项：',
              '• 拉黑是双向的，双方都无法互动\n'
                  '• 移出黑名单后，之前的聊天记录会恢复\n'
                  '• 对方不会收到被拉黑的通知',
            ),
           const SizedBox(height: SpacingTokens.space6),
            SizedBox(
              width: double.infinity,
              child: TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('我知道了'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
          ),
        ),
       const SizedBox(height: 4),
        Text(
          content,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
            height: 1.5,
          ),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return '今天';
    } else if (difference.inDays == 1) {
      return '昨天';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      return '${(difference.inDays / 7).floor()}周前';
    } else if (difference.inDays < 365) {
      return '${(difference.inDays / 30).floor()}个月前';
    } else {
      return '${date.year}年${date.month}月${date.day}日';
    }
  }
}

// 黑名单用户数据模型
class BlacklistedUser {
  final int id;
  final String name;
  final String? avatarUrl;
  final DateTime blockedAt;
  final String reason;

  BlacklistedUser({
    required this.id,
    required this.name,
    this.avatarUrl,
    required this.blockedAt,
    required this.reason,
  });
}
