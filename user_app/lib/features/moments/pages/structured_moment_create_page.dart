import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/core.dart';
import 'package:user_app/shared/shared.dart';
import 'package:user_app/models/moment/structured_moment_models.dart';
import 'package:user_app/features/fishing_spots/view_models/structured_publish_moment_view_model.dart';
import 'package:user_app/features/moments/widgets/caught_fish_input_dialog.dart';
import 'package:image_picker/image_picker.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 重新设计的结构化动态创建页面 - 现代化 Material Design 3 界面
class StructuredMomentCreatePage extends StatefulWidget {
  const StructuredMomentCreatePage({
    super.key,
    this.initialSpotId,
    this.initialMomentType = MomentType.fishingCatch,
  });

  final int? initialSpotId;
  final MomentType initialMomentType;

  @override
  State<StructuredMomentCreatePage> createState() => _StructuredMomentCreatePageState();
}

class _StructuredMomentCreatePageState extends State<StructuredMomentCreatePage> {
  final _formKey = GlobalKey<FormState>();
  final _contentController = TextEditingController();

  @override
  void initState() {
    super.initState();
    
    // 初始化数据
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<StructuredPublishMomentViewModel>();
      if (widget.initialSpotId != null) {
        viewModel.updateFishingSpotId(widget.initialSpotId);
      }
      viewModel.updateMomentType(widget.initialMomentType);
    });
  }

  @override
  void dispose() {
    _contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<StructuredPublishMomentViewModel>(
      builder: (context, viewModel, child) {
        return Scaffold(
          backgroundColor: Theme.of(context).colorScheme.surface,
          body: SafeArea(
            child: Column(
              children: [
                // 自定义顶部导航栏
                _buildCustomAppBar(context, viewModel),
                
                // 动态类型切换卡片
                _buildMomentTypeTabBar(viewModel),
                
                // 主要内容区域
                Expanded(
                  child: Form(
                    key: _formKey,
                    child: PageView(
                      controller: PageController(initialPage: viewModel.momentType.index),
                      onPageChanged: (index) {
                        viewModel.updateMomentType(MomentType.values[index]);
                      },
                      children: [
                        _buildFishingCatchPage(viewModel),
                        _buildEquipmentPage(viewModel),
                        _buildTechniquePage(viewModel),
                        _buildQuestionPage(viewModel),
                      ],
                    ),
                  ),
                ),
                
                // 底部发布栏
                _buildBottomPublishBar(context, viewModel),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 自定义顶部导航栏
  Widget _buildCustomAppBar(BuildContext context, StructuredPublishMomentViewModel viewModel) {
    return Container(
      padding: Spacing.md,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => _handleBackPressed(context, viewModel),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '发布动态',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '选择类型并填写相关信息',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          DSButton(
            label: viewModel.isPublishing ? '发布中...' : '发布',
            onPressed: viewModel.canPublish && !viewModel.isPublishing
                ? () => _publishMoment(context, viewModel)
                : null,
            size: DSButtonSize.small,
            type: DSButtonType.filled,
          ),
        ],
      ),
    );
  }

  /// 动态类型标签栏
  Widget _buildMomentTypeTabBar(StructuredPublishMomentViewModel viewModel) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space4,
        vertical: SpacingTokens.space3,
      ),
      child: Row(
        children: MomentType.values.asMap().entries.map((entry) {
          final index = entry.key;
          final type = entry.value;
          final isSelected = viewModel.momentType == type;
          
          return Expanded(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space1),
              child: Material(
                color: ColorTokens.transparent,
                child: InkWell(
                  onTap: () => viewModel.updateMomentType(type),
                  borderRadius: ShapeTokens.cardShape,
                  child: AnimatedContainer(
                    duration: MotionTokens.durationMedium,
                    curve: MotionTokens.curveEmphasized,
                    padding: EdgeInsets.symmetric(
                      vertical: SpacingTokens.space3,
                      horizontal: SpacingTokens.space2,
                    ),
                    decoration: BoxDecoration(
                      color: isSelected 
                          ? Theme.of(context).colorScheme.primaryContainer
                          : Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: ShapeTokens.cardShape,
                      border: Border.all(
                        color: isSelected
                            ? Theme.of(context).colorScheme.primary
                            : ColorTokens.transparent,
                        width: 2,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getTypeIcon(type),
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          size: 20,
                        ),
                        SizedBox(height: SpacingTokens.space1),
                        Text(
                          type.displayName,
                          textAlign: TextAlign.center,
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: isSelected
                                ? Theme.of(context).colorScheme.onPrimaryContainer
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  IconData _getTypeIcon(MomentType type) {
    switch (type) {
      case MomentType.fishingCatch:
        return Icons.phishing;
      case MomentType.equipment:
        return Icons.build_outlined;
      case MomentType.technique:
        return Icons.school_outlined;
      case MomentType.question:
        return Icons.help_outline;
    }
  }

  /// 通用内容输入卡片
  Widget _buildContentCard(StructuredPublishMomentViewModel viewModel, {
    required String title,
    required String subtitle,
    required Widget child,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space4,
        vertical: SpacingTokens.space2,
      ),
      child: Card(
        elevation: 0,
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        shape: RoundedRectangleBorder(
          borderRadius: ShapeTokens.cardShape,
          side: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        child: Padding(
          padding: Spacing.lg,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    _getTypeIcon(viewModel.momentType),
                    color: _getTypeColor(viewModel.momentType),
                    size: 20,
                  ),
                  SizedBox(width: SpacingTokens.space2),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: _getTypeColor(viewModel.momentType),
                          ),
                        ),
                        if (subtitle.isNotEmpty)
                          Text(
                            subtitle,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: SpacingTokens.space4),
              child,
            ],
          ),
        ),
      ),
    );
  }

  Color _getTypeColor(MomentType type) {
    switch (type) {
      case MomentType.fishingCatch:
        return Theme.of(context).colorScheme.primary;
      case MomentType.equipment:
        return Theme.of(context).colorScheme.tertiary;
      case MomentType.technique:
        return ColorTokens.success;
      case MomentType.question:
        return Theme.of(context).colorScheme.secondary;
    }
  }

  /// 可见性选择器
  Widget _buildVisibilitySelector(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '可见性设置',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        SizedBox(height: SpacingTokens.space2),
        Row(
          children: MomentVisibility.values.map((visibility) {
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: SpacingTokens.space2),
                child: DSRadioButton<MomentVisibility>(
                  value: visibility,
                  groupValue: viewModel.visibility,
                  onChanged: (value) => value != null ? viewModel.updateVisibility(value) : null,
                  title: visibility.displayName,
                  subtitle: _getVisibilityDescription(visibility),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  String _getVisibilityDescription(MomentVisibility visibility) {
    switch (visibility) {
      case MomentVisibility.public:
        return '所有人可见';
      case MomentVisibility.followers:
        return '关注者可见';
      case MomentVisibility.private:
        return '仅自己可见';
    }
  }

  /// 图片上传区域
  Widget _buildImageUpload(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '添加图片',
          style: Theme.of(context).textTheme.titleSmall,
        ),
        SizedBox(height: SpacingTokens.space2),
        SizedBox(
          height: 80,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: viewModel.imageUrls.length + 1,
            itemBuilder: (context, index) {
              if (index == viewModel.imageUrls.length) {
                // 添加按钮
                return GestureDetector(
                  onTap: _pickImage,
                  child: Container(
                    width: 80,
                    margin: EdgeInsets.only(right: SpacingTokens.space2),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: ShapeTokens.cardShape,
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline,
                        style: BorderStyle.solid,
                      ),
                    ),
                    child: Icon(
                      Icons.add_photo_alternate_outlined,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                );
              }

              // 图片预览
              return Container(
                width: 80,
                margin: EdgeInsets.only(right: SpacingTokens.space2),
                decoration: BoxDecoration(
                  borderRadius: ShapeTokens.cardShape,
                ),
                child: Stack(
                  children: [
                    ClipRRect(
                      borderRadius: ShapeTokens.cardShape,
                      child: Image.network(
                        viewModel.imageUrls[index],
                        width: 80,
                        height: 80,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Theme.of(context).colorScheme.errorContainer,
                            child: Icon(
                              Icons.broken_image,
                              color: Theme.of(context).colorScheme.onErrorContainer,
                            ),
                          );
                        },
                      ),
                    ),
                    Positioned(
                      top: 4,
                      right: 4,
                      child: GestureDetector(
                        onTap: () => viewModel.removeImageUrl(index),
                        child: Container(
                          width: 20,
                          height: 20,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 14,
                            color: Theme.of(context).colorScheme.surface,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 类型特定的表单
  Widget _buildTypeSpecificForm(StructuredPublishMomentViewModel viewModel) {
    switch (viewModel.momentType) {
      case MomentType.fishingCatch:
        return _buildFishingCatchForm(viewModel);
      case MomentType.equipment:
        return _buildEquipmentForm(viewModel);
      case MomentType.technique:
        return _buildTechniqueForm(viewModel);
      case MomentType.question:
        return _buildQuestionForm(viewModel);
    }
  }

  /// 钓获分享表单
  Widget _buildFishingCatchForm(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '钓获详情',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        SizedBox(height: SpacingTokens.space3),
        
        Row(
          children: [
            Expanded(
              child: DSTextFormField(
                labelText: '总重量 (kg)',
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final weight = double.tryParse(value);
                  viewModel.updateTotalWeight(weight);
                },
              ),
            ),
            SizedBox(width: SpacingTokens.space3),
            Expanded(
              child: DSTextFormField(
                labelText: '钓法',
                onChanged: viewModel.updateFishingMethod,
              ),
            ),
          ],
        ),
        SizedBox(height: SpacingTokens.space3),
        
        DSTextFormField(
          labelText: '天气条件',
          onChanged: viewModel.updateWeatherConditions,
        ),
        SizedBox(height: SpacingTokens.space4),
        
        // 捕获鱼类列表
        _buildCaughtFishList(viewModel),
      ],
    );
  }

  /// 捕获鱼类列表
  Widget _buildCaughtFishList(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '捕获鱼类',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            DSButton(
              label: '添加鱼类',
              size: DSButtonSize.small,
              type: DSButtonType.outlined,
              onPressed: () => _addCaughtFish(viewModel),
            ),
          ],
        ),
        SizedBox(height: SpacingTokens.space2),
        
        if (viewModel.caughtFishes.isEmpty)
          DSCard(
            child: Padding(
              padding: Spacing.card,
              child: Center(
                child: Text(
                  '尚未添加捕获鱼类\n点击"添加鱼类"按钮开始添加',
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),
          )
        else
          ...viewModel.caughtFishes.asMap().entries.map(
            (entry) {
              final index = entry.key;
              final fish = entry.value;
              return Padding(
                padding: EdgeInsets.only(bottom: SpacingTokens.space2),
                child: DSCard(
                  child: ListTile(
                    title: Text(fish.fishTypeName),
                    subtitle: Text(
                      '数量: ${fish.count}${fish.weight != null ? ", 重量: ${fish.weight}kg" : ""}',
                    ),
                    trailing: IconButton(
                      icon: const Icon(Icons.delete_outline),
                      onPressed: () => viewModel.removeCaughtFish(index),
                    ),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  /// 装备展示表单
  Widget _buildEquipmentForm(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '装备信息',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        SizedBox(height: SpacingTokens.space3),
        
        DSTextFormField(
          labelText: '装备名称*',
          validator: (value) => value?.trim().isEmpty ?? true ? '请输入装备名称' : null,
          onChanged: viewModel.updateEquipmentName,
        ),
        SizedBox(height: SpacingTokens.space3),
        
        Row(
          children: [
            Expanded(
              child: DSTextFormField(
                labelText: '品牌',
                onChanged: viewModel.updateEquipmentBrand,
              ),
            ),
            SizedBox(width: SpacingTokens.space3),
            Expanded(
              child: DSTextFormField(
                labelText: '型号',
                onChanged: viewModel.updateEquipmentModel,
              ),
            ),
          ],
        ),
        SizedBox(height: SpacingTokens.space3),
        
        Row(
          children: [
            Expanded(
              child: DSTextFormField(
                labelText: '价格',
                keyboardType: TextInputType.number,
                onChanged: viewModel.updateEquipmentPrice,
              ),
            ),
            SizedBox(width: SpacingTokens.space3),
            Expanded(
              child: _buildRatingSelector(viewModel),
            ),
          ],
        ),
        SizedBox(height: SpacingTokens.space3),
        
        DSTextFormField(
          labelText: '目标鱼种',
          onChanged: viewModel.updateEquipmentTargetFish,
        ),
      ],
    );
  }

  /// 现代化评分选择器
  Widget _buildRatingSelector(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '评分',
          style: Theme.of(context).textTheme.labelLarge,
        ),
        SizedBox(height: SpacingTokens.space2),
        Container(
          padding: EdgeInsets.all(SpacingTokens.space2),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
            borderRadius: ShapeTokens.cardShape,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(5, (index) {
              final rating = index + 1;
              final isSelected = viewModel.equipmentRating == rating;
              return Padding(
                padding: EdgeInsets.only(right: SpacingTokens.space1),
                child: Material(
                  color: ColorTokens.transparent,
                  child: InkWell(
                    onTap: () => viewModel.updateEquipmentRating(rating),
                    borderRadius: BorderRadius.circular(4),
                    child: Padding(
                      padding: EdgeInsets.all(SpacingTokens.space1),
                      child: Icon(
                        isSelected ? Icons.star : Icons.star_outline,
                        color: isSelected ? Theme.of(context).colorScheme.secondary : Theme.of(context).colorScheme.outline,
                        size: 28,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
        if (viewModel.equipmentRating != null)
          Padding(
            padding: EdgeInsets.only(top: SpacingTokens.space1),
            child: Text(
              '${viewModel.equipmentRating}星',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
      ],
    );
  }


  /// 难度选择器
  Widget _buildDifficultySelector(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '难度等级',
          style: Theme.of(context).textTheme.labelLarge,
        ),
        SizedBox(height: SpacingTokens.space2),
        Wrap(
          spacing: SpacingTokens.space2,
          children: TechniqueDifficulty.values.map((difficulty) {
            final isSelected = viewModel.techniqueDifficulty == difficulty;
            return FilterChip(
              selected: isSelected,
              label: Text(
                difficulty.displayName,
                style: TextStyle(
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              selectedColor: _getDifficultyColor(difficulty).withOpacity(0.2),
              onSelected: (selected) {
                if (selected) {
                  viewModel.updateTechniqueDifficulty(difficulty);
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Color _getDifficultyColor(TechniqueDifficulty difficulty) {
    switch (difficulty) {
      case TechniqueDifficulty.beginner:
        return ColorTokens.success;
      case TechniqueDifficulty.intermediate:
        return ColorTokens.warning;
      case TechniqueDifficulty.advanced:
        return ColorTokens.error;
    }
  }


  /// 标签输入区域
  Widget _buildTagsInput(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '相关标签',
          style: Theme.of(context).textTheme.labelLarge,
        ),
        SizedBox(height: SpacingTokens.space2),
        
        // 标签显示区域
        if (viewModel.questionTags.isNotEmpty) ...[
          Container(
            padding: Spacing.sm,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
              borderRadius: ShapeTokens.cardShape,
            ),
            child: Wrap(
              spacing: SpacingTokens.space1,
              runSpacing: SpacingTokens.space1,
              children: viewModel.questionTags.map((tag) {
                return Chip(
                  label: Text(
                    tag,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  deleteIcon: Icon(
                    Icons.close,
                    size: 16,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                  backgroundColor: Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3),
                  onDeleted: () => viewModel.removeQuestionTag(tag),
                );
              }).toList(),
            ),
          ),
          SizedBox(height: SpacingTokens.space2),
        ],
        
        // 添加标签输入框
        DSTextFormField(
          labelText: '添加标签',
          hintText: '输入标签后按回车添加',
          onFieldSubmitted: (value) {
            if (value.trim().isNotEmpty) {
              viewModel.addQuestionTag(value.trim());
            }
          },
          suffixIcon: Icon(
            Icons.add,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ],
    );
  }

  /// 发布动态
  Future<void> _publishMoment(BuildContext context, StructuredPublishMomentViewModel viewModel) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final result = await viewModel.publishMoment();
    if (result && context.mounted) {
      // 发布成功，返回上一页
      Navigator.of(context).pop(true);
      
      // 显示成功提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.surface,
                size: 20,
              ),
              SizedBox(width: SpacingTokens.space2),
              Text('${viewModel.momentType.displayName}发布成功！'),
            ],
          ),
          backgroundColor: ColorTokens.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: ShapeTokens.cardShape,
          ),
        ),
      );
    }
  }

  /// 选择图片
  Future<void> _pickImage() async {
    final ImagePicker picker = ImagePicker();
    
    // 显示选择来源底部弹窗
    final source = await showModalBottomSheet<ImageSource>(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(SpacingTokens.space6),
        ),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.camera_alt),
              title: const Text('拍照'),
              onTap: () => Navigator.of(context).pop(ImageSource.camera),
            ),
            ListTile(
              leading: const Icon(Icons.photo_library),
              title: const Text('从相册选择'),
              onTap: () => Navigator.of(context).pop(ImageSource.gallery),
            ),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('取消'),
              onTap: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
    
    if (source != null) {
      try {
        final XFile? image = await picker.pickImage(
          source: source,
          imageQuality: 85,
          maxWidth: 1920,
          maxHeight: 1920,
        );
        
        if (image != null && context.mounted) {
          // TODO: 上传图片到服务器
          // 这里先使用本地路径作为示例
          final viewModel = context.read<StructuredPublishMomentViewModel>();
          
          // 显示加载提示
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('正在上传图片...'),
              duration: Duration(seconds: 1),
            ),
          );
          
          // 模拟上传延迟
          await Future.delayed(const Duration(seconds: 1));
          
          // TODO: 这里应该是真实的上传后的URL
          viewModel.addImageUrl(image.path);
          
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('图片上传成功'),
                backgroundColor: ColorTokens.success,
              ),
            );
          }
        }
      } catch (e) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('图片选择失败：$e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  /// 添加捕获鱼类
  void _addCaughtFish(StructuredPublishMomentViewModel viewModel) {
    CaughtFishInputDialog.show(
      context,
      onSave: (caughtFish) {
        viewModel.addCaughtFish(caughtFish);
      },
    );
  }

  /// 技巧分享页面
  Widget _buildTechniquePage(StructuredPublishMomentViewModel viewModel) {
    return SingleChildScrollView(
      padding: Spacing.screen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildContentCard(
            viewModel,
            title: '技巧分享',
            subtitle: '分享你的钓鱼技巧和经验',
            child: Column(
              children: [
                // 内容描述
                DSTextFormField(
                  controller: _contentController,
                  labelText: '技巧描述*',
                  hintText: '详细描述你想分享的钓鱼技巧...',
                  maxLines: 4,
                  validator: (value) => value?.trim().isEmpty ?? true ? '请输入技巧描述' : null,
                  onChanged: (value) => viewModel.updateContent(value),
                ),
                SizedBox(height: SpacingTokens.space4),

                // 技巧特定信息
                _buildTechniqueForm(viewModel),
                SizedBox(height: SpacingTokens.space4),

                // 图片上传
                _buildImageUpload(viewModel),
                SizedBox(height: SpacingTokens.space4),

                // 可见性设置
                _buildVisibilitySelector(viewModel),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 技巧分享表单
  Widget _buildTechniqueForm(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '技巧详情',
          style: Theme.of(context).textTheme.titleMedium,
        ),
        SizedBox(height: SpacingTokens.space3),
        
        DSTextFormField(
          labelText: '技巧名称*',
          hintText: '例如：台钓调漂技巧',
          validator: (value) => value?.trim().isEmpty ?? true ? '请输入技巧名称' : null,
          onChanged: viewModel.updateTechniqueName,
        ),
        SizedBox(height: SpacingTokens.space3),
        
        // 难度等级选择
        _buildDifficultySelector(viewModel),
        SizedBox(height: SpacingTokens.space3),
        
        Row(
          children: [
            Expanded(
              child: DSTextFormField(
                labelText: '所需工具',
                hintText: '浮漂、铅皮、剪刀',
                onChanged: viewModel.updateTechniqueTools,
              ),
            ),
            SizedBox(width: SpacingTokens.space3),
            Expanded(
              child: DSTextFormField(
                labelText: '最佳时间',
                hintText: '春秋季节',
                onChanged: viewModel.updateTechniqueBestTime,
              ),
            ),
          ],
        ),
        SizedBox(height: SpacingTokens.space3),
        
        DSTextFormField(
          labelText: '成功率 (%)',
          keyboardType: TextInputType.number,
          hintText: '85',
          onChanged: (value) {
            final rate = int.tryParse(value);
            viewModel.updateTechniqueSuccessRate(rate);
          },
        ),
        SizedBox(height: SpacingTokens.space3),
        
        DSTextFormField(
          labelText: '详细步骤',
          maxLines: 4,
          hintText: '1. 确定水深\n2. 调节浮漂\n3. ...',
          onChanged: viewModel.updateTechniqueSteps,
        ),
        SizedBox(height: SpacingTokens.space3),
        
        DSTextFormField(
          labelText: '常见错误',
          maxLines: 2,
          hintText: '调漂过钝或过灵',
          onChanged: viewModel.updateTechniqueCommonMistakes,
        ),
      ],
    );
  }

  /// 求助问答页面
  Widget _buildQuestionPage(StructuredPublishMomentViewModel viewModel) {
    return SingleChildScrollView(
      padding: Spacing.screen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildContentCard(
            viewModel,
            title: '求助问答',
            subtitle: '提出你的钓鱼问题，获得帮助',
            child: Column(
              children: [
                // 问题标题
                DSTextFormField(
                  labelText: '问题标题*',
                  hintText: '如何在冬天钓鲫鱼？',
                  validator: (value) => value?.trim().isEmpty ?? true ? '请输入问题标题' : null,
                  onChanged: viewModel.updateQuestionTitle,
                ),
                SizedBox(height: SpacingTokens.space4),

                // 问题描述
                DSTextFormField(
                  controller: _contentController,
                  labelText: '问题描述*',
                  hintText: '详细描述你遇到的问题...',
                  maxLines: 4,
                  validator: (value) => value?.trim().isEmpty ?? true ? '请输入问题描述' : null,
                  onChanged: (value) => viewModel.updateContent(value),
                ),
                SizedBox(height: SpacingTokens.space4),

                // 紧急程度选择
                _buildUrgencySelector(viewModel),
                SizedBox(height: SpacingTokens.space4),

                // 标签输入
                _buildTagsInput(viewModel),
                SizedBox(height: SpacingTokens.space4),

                // 图片上传
                _buildImageUpload(viewModel),
                SizedBox(height: SpacingTokens.space4),

                // 可见性设置
                _buildVisibilitySelector(viewModel),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 紧急程度选择器
  Widget _buildUrgencySelector(StructuredPublishMomentViewModel viewModel) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '紧急程度',
          style: Theme.of(context).textTheme.labelLarge,
        ),
        SizedBox(height: SpacingTokens.space2),
        Wrap(
          spacing: SpacingTokens.space2,
          children: QuestionUrgency.values.map((urgency) {
            final isSelected = viewModel.questionUrgency == urgency;
            return FilterChip(
              selected: isSelected,
              label: Text(
                urgency.displayName,
                style: TextStyle(
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimaryContainer
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              selectedColor: _getUrgencyColor(urgency).withOpacity(0.2),
              onSelected: (selected) {
                if (selected) {
                  viewModel.updateQuestionUrgency(urgency);
                }
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Color _getUrgencyColor(QuestionUrgency urgency) {
    switch (urgency) {
      case QuestionUrgency.low:
        return ColorTokens.success;
      case QuestionUrgency.medium:
        return ColorTokens.warning;
      case QuestionUrgency.high:
        return ColorTokens.error;
    }
  }

  /// 处理返回按钮
  Future<void> _handleBackPressed(BuildContext context, StructuredPublishMomentViewModel viewModel) async {
    // 检查是否有未保存的内容
    if (viewModel.hasUnsavedChanges) {
      final shouldPop = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('放弃编辑'),
          content: const Text('您有未保存的内容，确定要放弃吗？'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('取消'),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: const Text('放弃'),
            ),
          ],
        ),
      );
      
      if (shouldPop == true) {
        viewModel.resetForm();
        Navigator.of(context).pop();
      }
    } else {
      Navigator.of(context).pop();
    }
  }
}