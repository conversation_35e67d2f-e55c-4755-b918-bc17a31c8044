import 'package:flutter/material.dart';
import 'package:user_app/core/core.dart';
import 'package:user_app/shared/shared.dart';
import 'package:user_app/models/moment/structured_moment_models.dart';
import 'package:user_app/services/structured_moment_service.dart';
import 'package:user_app/features/moments/widgets/moment_type_specific_preview.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 结构化动态详情页面 - 现代化 Material Design 3 设计
class StructuredMomentDetailPage extends StatefulWidget {
  const StructuredMomentDetailPage({
    super.key,
    required this.momentId,
    this.initialMoment,
  });

  final int momentId;
  final StructuredMomentModel? initialMoment;

  @override
  State<StructuredMomentDetailPage> createState() => _StructuredMomentDetailPageState();
}

class _StructuredMomentDetailPageState extends State<StructuredMomentDetailPage> {
  final StructuredMomentService _momentService = StructuredMomentService(
    // TODO: 注入 ApiClient
    throw UnimplementedError('需要注入 ApiClient'),
  );

  StructuredMomentModel? _moment;
  bool _isLoading = true;
  String? _error;
  bool _isLiked = false;
  int _likeCount = 0;
  final List<String> _comments = []; // TODO: 实际评论模型

  @override
  void initState() {
    super.initState();
    _moment = widget.initialMoment;
    if (_moment == null) {
      _loadMomentDetail();
    } else {
      _isLoading = false;
    }
  }

  Future<void> _loadMomentDetail() async {
    try {
      final response = await _momentService.getStructuredMomentDetail(widget.momentId);
      if (response.isSuccess && response.data != null) {
        setState(() {
          _moment = response.data;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = response.message ?? '加载失败';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = '网络错误: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading
          ? _buildLoadingState()
          : _error != null
              ? _buildErrorState()
              : _moment != null
                  ? _buildDetailContent()
                  : _buildEmptyState(),
      bottomNavigationBar: _moment != null && !_isLoading
          ? _buildBottomActionBar()
          : null,
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          SizedBox(height: SpacingTokens.space4),
          Text(
            '加载失败',
            style: Theme.of(context).textTheme.titleLarge,
          ),
          SizedBox(height: SpacingTokens.space2),
          Text(
            _error!,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: SpacingTokens.space4),
          DSButton(
            label: '重新加载',
            onPressed: () {
              setState(() {
                _isLoading = true;
                _error = null;
              });
              _loadMomentDetail();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return const Center(
      child: Text('动态不存在'),
    );
  }

  Widget _buildDetailContent() {
    return CustomScrollView(
      slivers: [
        // 自定义 AppBar
        _buildSliverAppBar(),
        
        // 内容区域
        SliverToBoxAdapter(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 用户信息卡片
              _buildUserInfoCard(),
              
              // 动态内容
              _buildContentSection(),
              
              // 图片展示
              if (_moment!.images.isNotEmpty)
                _buildImageGallery(),
              
              // 类型特定详情
              _buildTypeSpecificDetails(),
              
              // 互动统计
              _buildInteractionStats(),
              
              // 评论区域
              _buildCommentsSection(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      flexibleSpace: FlexibleSpaceBar(
        background: _moment!.images.isNotEmpty
            ? Image.network(
                _moment!.images.first.imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: _getTypeColor(_moment!.momentType).withOpacity(0.1),
                    child: Center(
                      child: Icon(
                        _getTypeIcon(_moment!.momentType),
                        size: 64,
                        color: _getTypeColor(_moment!.momentType),
                      ),
                    ),
                  );
                },
              )
            : Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _getTypeColor(_moment!.momentType).withOpacity(0.3),
                      _getTypeColor(_moment!.momentType).withOpacity(0.1),
                    ],
                  ),
                ),
                child: Center(
                  child: Icon(
                    _getTypeIcon(_moment!.momentType),
                    size: 64,
                    color: _getTypeColor(_moment!.momentType),
                  ),
                ),
              ),
      ),
      actions: [
        IconButton(
          onPressed: () => _showMoreOptions(),
          icon: const Icon(Icons.more_vert),
        ),
      ],
    );
  }

  Widget _buildUserInfoCard() {
    return Container(
      padding: Spacing.lg,
      child: Row(
        children: [
          // 用户头像
          CircleAvatar(
            radius: 24,
            backgroundColor: Theme.of(context).colorScheme.primaryContainer,
            child: Text(
              'U', // TODO: 实际用户名首字母
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimaryContainer,
              ),
            ),
          ),
          SizedBox(width: SpacingTokens.space3),
          
          // 用户信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '用户名', // TODO: 实际用户名
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      _formatTime(_moment!.createTime),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    if (_moment!.visibility != MomentVisibility.public) ...[
                      SizedBox(width: SpacingTokens.space2),
                      Icon(
                        _getVisibilityIcon(_moment!.visibility),
                        size: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      SizedBox(width: SpacingTokens.space1),
                      Text(
                        _moment!.visibility.displayName,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          
          // 关注按钮
          DSButton(
            label: '关注',
            size: DSButtonSize.small,
            type: DSButtonType.outlined,
            onPressed: () {
              // TODO: 实现关注功能
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContentSection() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      margin: EdgeInsets.only(bottom: SpacingTokens.space4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 类型标签
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: SpacingTokens.space3,
              vertical: SpacingTokens.space1,
            ),
            decoration: BoxDecoration(
              color: _getTypeColor(_moment!.momentType).withOpacity(0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getTypeIcon(_moment!.momentType),
                  size: 16,
                  color: _getTypeColor(_moment!.momentType),
                ),
                SizedBox(width: SpacingTokens.space1),
                Text(
                  _moment!.momentType.displayName,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: _getTypeColor(_moment!.momentType),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: SpacingTokens.space3),
          
          // 正文内容
          Text(
            _moment!.content,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          
          // 钓点信息
          if (_moment!.fishingSpotId != null) ...[
            SizedBox(height: SpacingTokens.space3),
            InkWell(
              onTap: () {
                // TODO: 跳转到钓点详情
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                padding: EdgeInsets.all(SpacingTokens.space2),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.location_on_outlined,
                      size: 16,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    SizedBox(width: SpacingTokens.space1),
                    Text(
                      '钓点名称', // TODO: 实际钓点名称
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImageGallery() {
    return Container(
      height: 250,
      margin: EdgeInsets.only(bottom: SpacingTokens.space4),
      child: PageView.builder(
        itemCount: _moment!.images.length,
        itemBuilder: (context, index) {
          return Container(
            margin: EdgeInsets.symmetric(horizontal: SpacingTokens.space1),
            child: ClipRRect(
              borderRadius: ShapeTokens.cardShape,
              child: Image.network(
                _moment!.images[index].imageUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Theme.of(context).colorScheme.errorContainer.withOpacity(0.1),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.broken_image_outlined,
                          size: 48,
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                        SizedBox(height: SpacingTokens.space2),
                        Text(
                          '图片加载失败',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onErrorContainer,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTypeSpecificDetails() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      margin: EdgeInsets.only(bottom: SpacingTokens.space4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '详细信息',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: SpacingTokens.space3),
          
          // 根据类型显示详细信息
          _buildDetailsByType(),
        ],
      ),
    );
  }

  Widget _buildDetailsByType() {
    switch (_moment!.momentType) {
      case MomentType.fishingCatch:
        return _buildFishingCatchDetails();
      case MomentType.equipment:
        return _buildEquipmentDetails();
      case MomentType.technique:
        return _buildTechniqueDetails();
      case MomentType.question:
        return _buildQuestionDetails();
    }
  }

  Widget _buildFishingCatchDetails() {
    final data = _moment!.fishingCatchData;
    if (data == null) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 钓获统计卡片
        Container(
          padding: Spacing.lg,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
            borderRadius: ShapeTokens.cardShape,
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                icon: Icons.phishing,
                label: '总数量',
                value: '${data.caughtFishes.fold(0, (sum, fish) => sum + fish.count)}条',
                color: Theme.of(context).colorScheme.primary,
              ),
              if (data.totalWeight != null)
                _buildStatItem(
                  icon: Icons.scale,
                  label: '总重量',
                  value: '${data.totalWeight}kg',
                  color: Theme.of(context).colorScheme.primary,
                ),
              if (data.fishingMethod != null)
                _buildStatItem(
                  icon: Icons.water,
                  label: '钓法',
                  value: data.fishingMethod!,
                  color: Theme.of(context).colorScheme.primary,
                ),
            ],
          ),
        ),
        
        SizedBox(height: SpacingTokens.space4),
        
        // 鱼类列表
        Text(
          '捕获鱼类',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: SpacingTokens.space2),
        
        ...data.caughtFishes.map((fish) => _buildFishItem(fish)),
        
        // 钓获图片
        if (data.catchImages.isNotEmpty) ...[
          SizedBox(height: SpacingTokens.space4),
          Text(
            '钓获照片',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: SpacingTokens.space2),
          SizedBox(
            height: 120,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: data.catchImages.length,
              itemBuilder: (context, index) {
                return Container(
                  width: 120,
                  margin: EdgeInsets.only(right: SpacingTokens.space2),
                  child: ClipRRect(
                    borderRadius: ShapeTokens.cardShape,
                    child: Image.network(
                      data.catchImages[index].imageUrl,
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFishItem(CaughtFishModel fish) {
    return Container(
      margin: EdgeInsets.only(bottom: SpacingTokens.space2),
      padding: Spacing.md,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
        borderRadius: ShapeTokens.cardShape,
      ),
      child: Row(
        children: [
          Icon(
            Icons.set_meal,
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          SizedBox(width: SpacingTokens.space2),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  fish.fishTypeName,
                  style: Theme.of(context).textTheme.titleSmall,
                ),
                if (fish.remark != null)
                  Text(
                    fish.remark!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                '${fish.count}条',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              if (fish.weight != null)
                Text(
                  '${fish.weight}kg',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              if (fish.size != null)
                Text(
                  '${fish.size}cm',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEquipmentDetails() {
    final data = _moment!.equipmentData;
    if (data == null) return const SizedBox.shrink();

    return Container(
      padding: Spacing.lg,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3),
        borderRadius: ShapeTokens.cardShape,
        border: Border.all(
          color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 装备名称和评分
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.equipmentName,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (data.category != null)
                      Text(
                        data.category!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                  ],
                ),
              ),
              if (data.rating != null)
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      index < data.rating! ? Icons.star : Icons.star_outline,
                      color: Theme.of(context).colorScheme.secondary,
                      size: 20,
                    );
                  }),
                ),
            ],
          ),
          
          SizedBox(height: SpacingTokens.space3),
          
          // 装备信息网格
          Wrap(
            spacing: SpacingTokens.space4,
            runSpacing: SpacingTokens.space3,
            children: [
              if (data.brand != null)
                _buildInfoChip(Icons.business, '品牌', data.brand!),
              if (data.model != null)
                _buildInfoChip(Icons.category, '型号', data.model!),
              if (data.price != null)
                _buildInfoChip(Icons.attach_money, '价格', data.price!),
              if (data.targetFish != null)
                _buildInfoChip(Icons.phishing, '目标鱼', data.targetFish!),
            ],
          ),
          
          // 适用鱼种
          if (data.targetFishTypes.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space3),
            Text(
              '适用鱼种',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            SizedBox(height: SpacingTokens.space2),
            Wrap(
              spacing: SpacingTokens.space1,
              runSpacing: SpacingTokens.space1,
              children: data.targetFishTypes.map((fish) {
                return Chip(
                  label: Text(
                    fish.fishTypeName,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  backgroundColor: Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTechniqueDetails() {
    final data = _moment!.techniqueData;
    if (data == null) return const SizedBox.shrink();

    return Container(
      padding: Spacing.lg,
      decoration: BoxDecoration(
        color: ColorTokens.successContainer.withValues(alpha: 0.3),
        borderRadius: ShapeTokens.cardShape,
        border: Border.all(
          color: ColorTokens.success.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 技巧名称和难度
          Row(
            children: [
              Expanded(
                child: Text(
                  data.techniqueName,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: SpacingTokens.space3,
                  vertical: SpacingTokens.space1,
                ),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(data.difficulty).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  data.difficulty.displayName,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: _getDifficultyColor(data.difficulty),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          if (data.description != null) ...[
            SizedBox(height: SpacingTokens.space3),
            Text(
              data.description!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
          
          // 适用环境
          if (data.environments.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space3),
            Text(
              '适用环境',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            SizedBox(height: SpacingTokens.space2),
            Wrap(
              spacing: SpacingTokens.space1,
              runSpacing: SpacingTokens.space1,
              children: data.environments.map((env) {
                return Chip(
                  label: Text(
                    env,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  backgroundColor: ColorTokens.successContainer.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
          ],
          
          // 目标鱼种
          if (data.targetFishTypes.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space3),
            Text(
              '目标鱼种',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            SizedBox(height: SpacingTokens.space2),
            Wrap(
              spacing: SpacingTokens.space1,
              runSpacing: SpacingTokens.space1,
              children: data.targetFishTypes.map((fish) {
                return Chip(
                  label: Text(
                    fish.fishTypeName,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  backgroundColor: ColorTokens.successContainer.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuestionDetails() {
    final data = _moment!.questionData;
    if (data == null) return const SizedBox.shrink();

    return Container(
      padding: Spacing.lg,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3),
        borderRadius: ShapeTokens.cardShape,
        border: Border.all(
          color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 问题标题和状态
          Row(
            children: [
              Expanded(
                child: Text(
                  data.questionTitle,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: SpacingTokens.space3,
                  vertical: SpacingTokens.space1,
                ),
                decoration: BoxDecoration(
                  color: data.resolved
                      ? ColorTokens.success.withValues(alpha: 0.2)
                      : ColorTokens.warning.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  data.resolved ? '已解决' : '待解决',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: data.resolved ? ColorTokens.success : ColorTokens.warning,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          
          if (data.detailedProblem != null) ...[
            SizedBox(height: SpacingTokens.space3),
            Text(
              data.detailedProblem!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
          
          // 标签
          if (data.tags.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space3),
            Wrap(
              spacing: SpacingTokens.space1,
              runSpacing: SpacingTokens.space1,
              children: data.tags.map((tag) {
                return Chip(
                  label: Text(
                    tag.tagName ?? '',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  backgroundColor: Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3),
                );
              }).toList(),
            ),
          ],
          
          // 最佳答案
          if (data.resolved && data.bestAnswerId != null) ...[
            SizedBox(height: SpacingTokens.space4),
            Container(
              padding: Spacing.md,
              decoration: BoxDecoration(
                color: ColorTokens.successContainer.withValues(alpha: 0.3),
                borderRadius: ShapeTokens.cardShape,
                border: Border.all(
                  color: ColorTokens.success.withValues(alpha: 0.2),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle,
                        color: ColorTokens.success,
                        size: 16,
                      ),
                      SizedBox(width: SpacingTokens.space1),
                      Text(
                        '最佳答案',
                        style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: ColorTokens.success,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: SpacingTokens.space2),
                  Text(
                    '这里是最佳答案内容...', // TODO: 实际答案内容
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        SizedBox(height: SpacingTokens.space1),
        Text(
          value,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildInfoChip(IconData icon, String label, String value) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space3,
        vertical: SpacingTokens.space2,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 16,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: SpacingTokens.space1),
          Text(
            '$label: ',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInteractionStats() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space4,
        vertical: SpacingTokens.space3,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatButton(
            icon: Icons.thumb_up_outlined,
            count: _likeCount,
            label: '点赞',
            onTap: () {
              setState(() {
                _isLiked = !_isLiked;
                _likeCount += _isLiked ? 1 : -1;
              });
            },
          ),
          _buildStatButton(
            icon: Icons.comment_outlined,
            count: _comments.length,
            label: '评论',
            onTap: () {
              // TODO: 跳转到评论区
            },
          ),
          _buildStatButton(
            icon: Icons.share_outlined,
            count: 0,
            label: '分享',
            onTap: _shareContent,
          ),
          _buildStatButton(
            icon: Icons.bookmark_outline,
            count: 0,
            label: '收藏',
            onTap: () {
              // TODO: 实现收藏功能
            },
          ),
        ],
      ),
    );
  }

  Widget _buildStatButton({
    required IconData icon,
    required int count,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.all(SpacingTokens.space2),
        child: Column(
          children: [
            Icon(
              icon,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: SpacingTokens.space1),
            Text(
              count > 0 ? count.toString() : label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommentsSection() {
    return Container(
      padding: Spacing.lg,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '评论',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: SpacingTokens.space3),
          
          if (_comments.isEmpty)
            Center(
              child: Container(
                padding: Spacing.xl,
                child: Column(
                  children: [
                    Icon(
                      Icons.comment_outlined,
                      size: 48,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(height: SpacingTokens.space3),
                    Text(
                      '暂无评论',
                      style: Theme.of(context).textTheme.titleSmall,
                    ),
                    SizedBox(height: SpacingTokens.space1),
                    Text(
                      '成为第一个评论的人吧！',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            // TODO: 实际评论列表
            const SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget _buildBottomActionBar() {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space3),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            // 点赞按钮
            IconButton(
              onPressed: () {
                setState(() {
                  _isLiked = !_isLiked;
                  _likeCount += _isLiked ? 1 : -1;
                });
              },
              icon: Icon(
                _isLiked ? Icons.favorite : Icons.favorite_outline,
                color: _isLiked ? ColorTokens.error : null,
              ),
            ),
            
            // 评论输入框
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: SpacingTokens.space3,
                  vertical: SpacingTokens.space2,
                ),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.comment_outlined,
                      size: 18,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: SpacingTokens.space2),
                    Text(
                      '写评论...',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(width: SpacingTokens.space2),
            
            // 分享按钮
            IconButton(
              onPressed: _shareContent,
              icon: const Icon(Icons.share_outlined),
            ),
          ],
        ),
      ),
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(SpacingTokens.space6),
        ),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.edit_outlined),
              title: const Text('编辑'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: 跳转到编辑页面
              },
            ),
            ListTile(
              leading: Icon(Icons.delete_outline, color: ColorTokens.error),
              title: Text('删除', style: TextStyle(color: ColorTokens.error)),
              onTap: () {
                Navigator.of(context).pop();
                _confirmDelete();
              },
            ),
            ListTile(
              leading: const Icon(Icons.report_outlined),
              title: const Text('举报'),
              onTap: () {
                Navigator.of(context).pop();
                // TODO: 实现举报功能
              },
            ),
            ListTile(
              leading: const Icon(Icons.cancel),
              title: const Text('取消'),
              onTap: () => Navigator.of(context).pop(),
            ),
          ],
        ),
      ),
    );
  }

  void _confirmDelete() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条动态吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              // TODO: 实现删除功能
              Navigator.of(context).pop(); // 返回列表页
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  void _shareContent() {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('分享功能开发中...'),
      ),
    );
  }

  // 工具方法
  IconData _getTypeIcon(MomentType type) {
    switch (type) {
      case MomentType.fishingCatch:
        return Icons.phishing;
      case MomentType.equipment:
        return Icons.build_outlined;
      case MomentType.technique:
        return Icons.school_outlined;
      case MomentType.question:
        return Icons.help_outline;
    }
  }

  Color _getTypeColor(MomentType type) {
    switch (type) {
      case MomentType.fishingCatch:
        return ColorTokens.primary;
      case MomentType.equipment:
        return ColorTokens.warning;
      case MomentType.technique:
        return ColorTokens.success;
      case MomentType.question:
        return ColorTokens.secondary;
    }
  }

  IconData _getVisibilityIcon(MomentVisibility visibility) {
    switch (visibility) {
      case MomentVisibility.public:
        return Icons.public;
      case MomentVisibility.followers:
        return Icons.people;
      case MomentVisibility.private:
        return Icons.lock;
    }
  }

  Color _getDifficultyColor(TechniqueDifficulty difficulty) {
    switch (difficulty) {
      case TechniqueDifficulty.beginner:
        return ColorTokens.success;
      case TechniqueDifficulty.intermediate:
        return ColorTokens.warning;
      case TechniqueDifficulty.advanced:
        return ColorTokens.error;
    }
  }

  String _formatTime(DateTime? time) {
    if (time == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inSeconds < 30) {
      return '刚刚';
    } else if (difference.inMinutes < 1) {
      return '${difference.inSeconds}秒前';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}周前';
    } else if (difference.inDays < 365) {
      return '${time.month}月${time.day}日';
    } else {
      return '${time.year}年${time.month}月${time.day}日';
    }
  }
}