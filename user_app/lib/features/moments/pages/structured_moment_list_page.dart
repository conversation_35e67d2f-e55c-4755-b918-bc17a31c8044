import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/core.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/shared.dart';
import 'package:user_app/models/moment/structured_moment_models.dart';
import 'package:user_app/services/structured_moment_service.dart';
import 'package:user_app/features/moments/widgets/structured_moment_card.dart';

/// 重新设计的结构化动态列表页面 - 现代化 Material Design 3 界面
class StructuredMomentListPage extends StatefulWidget {
  const StructuredMomentListPage({
    super.key,
    this.momentType,
    this.userId,
    this.fishingSpotId,
  });

  final MomentType? momentType;
  final int? userId;
  final int? fishingSpotId;

  @override
  State<StructuredMomentListPage> createState() => _StructuredMomentListPageState();
}

class _StructuredMomentListPageState extends State<StructuredMomentListPage> {
  final ScrollController _scrollController = ScrollController();
  final StructuredMomentService _momentService = StructuredMomentService(
    // TODO: 注入 ApiClient
    throw UnimplementedError('需要注入 ApiClient'),
  );

  List<StructuredMomentModel> _moments = [];
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentPage = 1;
  String? _error;
  MomentType? _selectedType;

  @override
  void initState() {
    super.initState();
    _selectedType = widget.momentType;
    _scrollController.addListener(_onScroll);
    _loadMoments();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= 
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoading && _hasMore) {
        _loadMoments(loadMore: true);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // 现代化顶部栏
            _buildModernAppBar(context),
            
            // 类型筛选栏
            _buildTypeFilterBar(),
            
            // 主要内容区域
            Expanded(
              child: RefreshIndicator(
                onRefresh: () => _resetAndReload(),
                child: _buildModernBody(),
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: _buildModernFAB(),
    );
  }

  /// 现代化顶部栏
  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space4,
        vertical: SpacingTokens.space3,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          if (Navigator.of(context).canPop())
            IconButton(
              onPressed: () => Navigator.of(context).pop(),
              icon: const Icon(Icons.arrow_back),
              style: IconButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
                foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          if (Navigator.of(context).canPop())
            SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getPageTitle(),
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (_moments.isNotEmpty)
                  Text(
                    '${_moments.length} 条动态',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _showFilterBottomSheet(context),
            icon: const Icon(Icons.tune),
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  String _getPageTitle() {
    if (widget.momentType != null) {
      return widget.momentType!.displayName;
    }
    if (_selectedType != null) {
      return _selectedType!.displayName;
    }
    return '动态广场';
  }

  /// 类型筛选栏
  Widget _buildTypeFilterBar() {
    return Container(
      height: 60,
      padding: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space4,
        vertical: SpacingTokens.space2,
      ),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          _buildFilterChip(null, '全部'),
          ...MomentType.values.map(
            (type) => _buildFilterChip(type, type.displayName),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(MomentType? type, String label) {
    final isSelected = _selectedType == type;
    return Padding(
      padding: EdgeInsets.only(right: SpacingTokens.space2),
      child: FilterChip(
        selected: isSelected,
        label: Text(
          label,
          style: TextStyle(
            color: isSelected
                ? Theme.of(context).colorScheme.onPrimaryContainer
                : Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
        selectedColor: Theme.of(context).colorScheme.primaryContainer,
        onSelected: (selected) {
          if (selected) {
            setState(() {
              _selectedType = type;
              _resetAndReload();
            });
          }
        },
        avatar: type != null
            ? Icon(
                _getTypeIcon(type),
                size: 16,
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
              )
            : null,
      ),
    );
  }

  IconData _getTypeIcon(MomentType type) {
    switch (type) {
      case MomentType.fishingCatch:
        return Icons.phishing;
      case MomentType.equipment:
        return Icons.build_outlined;
      case MomentType.technique:
        return Icons.school_outlined;
      case MomentType.question:
        return Icons.help_outline;
    }
  }

  /// 现代化主体内容
  Widget _buildModernBody() {
    if (_isLoading && _moments.isEmpty) {
      return _buildLoadingState();
    }

    if (_error != null && _moments.isEmpty) {
      return _buildErrorState();
    }

    if (_moments.isEmpty) {
      return _buildEmptyState();
    }

    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        SliverPadding(
          padding: EdgeInsets.all(SpacingTokens.space4),
          sliver: SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                if (index < _moments.length) {
                  return Container(
                    margin: EdgeInsets.only(bottom: SpacingTokens.space4),
                    child: StructuredMomentCard(
                      moment: _moments[index],
                      onTap: () => _navigateToDetail(_moments[index]),
                      onEdit: () => _navigateToEdit(_moments[index]),
                      onDelete: () => _confirmDelete(_moments[index]),
                    ),
                  );
                } else if (_isLoading) {
                  return _buildLoadingItem();
                } else if (!_hasMore) {
                  return _buildEndItem();
                }
                return const SizedBox.shrink();
              },
              childCount: _moments.length + (_hasMore || _isLoading ? 1 : 0),
            ),
          ),
        ),
      ],
    );
  }

  /// 加载状态
  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(height: SpacingTokens.space4),
          Text(
            '正在加载动态...',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// 错误状态
  Widget _buildErrorState() {
    return Center(
      child: Container(
        padding: Spacing.lg,
        margin: Spacing.lg,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.errorContainer.withOpacity(0.1),
          borderRadius: ShapeTokens.cardShape,
          border: Border.all(
            color: Theme.of(context).colorScheme.error.withOpacity(0.2),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.wifi_off,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: SpacingTokens.space4),
            Text(
              '加载失败',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            SizedBox(height: SpacingTokens.space2),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SpacingTokens.space4),
            DSButton(
              label: '重新加载',
              onPressed: _resetAndReload,
              type: DSButtonType.filled,
            ),
          ],
        ),
      ),
    );
  }

  /// 空状态
  Widget _buildEmptyState() {
    return Center(
      child: Container(
        padding: Spacing.xl,
        margin: Spacing.lg,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
                shape: BoxShape.circle,
              ),
              child: Icon(
                _selectedType != null ? _getTypeIcon(_selectedType!) : Icons.post_add,
                size: 64,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(height: SpacingTokens.space6),
            Text(
              _selectedType != null 
                  ? '还没有${_selectedType!.displayName}'
                  : '还没有动态',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            SizedBox(height: SpacingTokens.space3),
            Text(
              _selectedType != null
                  ? '成为第一个分享${_selectedType!.displayName}的人吧！'
                  : '点击下方按钮发布你的第一条动态',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: SpacingTokens.space6),
            DSButton(
              label: '发布${_selectedType?.displayName ?? "动态"}',
              onPressed: _navigateToCreate,
              type: DSButtonType.filled,
              size: DSButtonSize.large,
            ),
          ],
        ),
      ),
    );
  }

  /// 加载更多项
  Widget _buildLoadingItem() {
    return Container(
      padding: Spacing.lg,
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: SpacingTokens.space2),
            Text(
              '加载更多...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 结束项
  Widget _buildEndItem() {
    return Container(
      padding: Spacing.lg,
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.flag,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              size: 20,
            ),
            SizedBox(height: SpacingTokens.space2),
            Text(
              '没有更多动态了',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 现代化浮动按钮
  Widget _buildModernFAB() {
    return Container(
      margin: EdgeInsets.all(SpacingTokens.space4),
      child: FloatingActionButton.extended(
        onPressed: _navigateToCreate,
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        icon: const Icon(Icons.add),
        label: Text(
          '发布${_selectedType?.displayName ?? "动态"}',
          style: TextStyle(
            fontWeight: FontWeight.w600,
          ),
        ),
        elevation: ElevationTokens.level3,
        extendedPadding: EdgeInsets.symmetric(
          horizontal: SpacingTokens.space6,
        ),
      ),
    );
  }

  /// 筛选底部弹窗
  void _showFilterBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(SpacingTokens.space6),
        ),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.symmetric(
          horizontal: SpacingTokens.space4,
          vertical: SpacingTokens.space6,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Center(
              child: Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
            ),
            SizedBox(height: SpacingTokens.space4),
            Text(
              '筛选动态类型',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: SpacingTokens.space4),
            ...[
              _buildFilterOption(null, '全部', Icons.apps),
              ...MomentType.values.map(
                (type) => _buildFilterOption(
                  type,
                  type.displayName,
                  _getTypeIcon(type),
                ),
              ),
            ],
            SizedBox(height: SpacingTokens.space4),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterOption(MomentType? type, String label, IconData icon) {
    final isSelected = _selectedType == type;
    return Container(
      margin: EdgeInsets.only(bottom: SpacingTokens.space2),
      child: Material(
        color: ColorTokens.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              _selectedType = type;
              _resetAndReload();
            });
            Navigator.of(context).pop();
          },
          borderRadius: ShapeTokens.cardShape,
          child: Container(
            padding: Spacing.md,
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer
                  : ColorTokens.transparent,
              borderRadius: ShapeTokens.cardShape,
              border: isSelected
                  ? Border.all(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    )
                  : null,
            ),
            child: Row(
              children: [
                Icon(
                  icon,
                  color: isSelected
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(width: SpacingTokens.space3),
                Expanded(
                  child: Text(
                    label,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: isSelected
                          ? Theme.of(context).colorScheme.onPrimaryContainer
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                    ),
                  ),
                ),
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _loadMoments({bool loadMore = false}) async {
    if (_isLoading) return;

    setState(() {
      _isLoading = true;
      if (!loadMore) {
        _error = null;
      }
    });

    try {
      final response = await _momentService.getStructuredMomentList(
        page: loadMore ? _currentPage + 1 : 1,
        limit: 20,
        momentType: _selectedType,
        userId: widget.userId,
        fishingSpotId: widget.fishingSpotId,
      );

      if (response.isSuccess && response.data != null) {
        final listResponse = response.data!;
        
        setState(() {
          if (loadMore) {
            _moments.addAll(listResponse.items);
            _currentPage++;
          } else {
            _moments = listResponse.items;
            _currentPage = 1;
          }
          
          _hasMore = listResponse.pagination.hasNext;
          _isLoading = false;
          _error = null;
        });
      } else {
        setState(() {
          _error = response.message ?? '加载失败';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = '网络连接失败，请检查网络后重试';
        _isLoading = false;
      });
    }
  }

  Future<void> _resetAndReload() async {
    _currentPage = 1;
    _hasMore = true;
    await _loadMoments();
  }

  void _navigateToCreate() {
    Navigator.of(context).pushNamed(
      '/moments/structured/create',
      arguments: {
        'momentType': _selectedType ?? MomentType.fishingCatch,
        'fishingSpotId': widget.fishingSpotId,
      },
    ).then((result) {
      // 如果创建成功，刷新列表
      if (result != null) {
        _resetAndReload();
      }
    });
  }

  void _navigateToDetail(StructuredMomentModel moment) {
    Navigator.of(context).pushNamed(
      '/moments/structured/${moment.id}',
      arguments: moment,
    );
  }

  void _navigateToEdit(StructuredMomentModel moment) {
    Navigator.of(context).pushNamed(
      '/moments/structured/${moment.id}/edit',
      arguments: moment,
    ).then((result) {
      // 如果编辑成功，刷新列表
      if (result != null) {
        _resetAndReload();
      }
    });
  }

  Future<void> _confirmDelete(StructuredMomentModel moment) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('确认删除'),
        content: const Text('确定要删除这条动态吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );

    if (confirm == true && context.mounted) {
      await _deleteMoment(moment);
    }
  }

  Future<void> _deleteMoment(StructuredMomentModel moment) async {
    if (moment.id == null) return;

    try {
      final response = await _momentService.deleteStructuredMoment(moment.id!);
      
      if (response.isSuccess) {
        setState(() {
          _moments.removeWhere((m) => m.id == moment.id);
        });
        
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('动态已删除'),
            ),
          );
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('删除失败：${response.message}'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('删除失败：$e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}