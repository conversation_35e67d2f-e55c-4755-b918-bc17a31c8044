import 'package:flutter/material.dart';
import 'package:user_app/core/core.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/shared.dart';
import 'package:user_app/models/moment/structured_moment_models.dart';
import 'package:user_app/features/moments/widgets/moment_type_specific_preview.dart';

/// 结构化动态卡片组件
class StructuredMomentCard extends StatelessWidget {
  const StructuredMomentCard({
    super.key,
    required this.moment,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.showActions = true,
  });

  final StructuredMomentModel moment;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool showActions;

  @override
  Widget build(BuildContext context) {
    return DSCard(
      onTap: onTap,
      child: Padding(
        padding: Spacing.card,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 卡片头部：类型和时间
            _buildHeader(context),
            
            // 动态内容
            if (moment.content.trim().isNotEmpty) ...[
              SizedBox(height: SpacingTokens.space2),
              _buildContent(context),
            ],
            
            // 主图片
            if (moment.images.isNotEmpty) ...[
              SizedBox(height: SpacingTokens.space3),
              _buildImages(context),
            ],
            
            // 类型特定预览
            SizedBox(height: SpacingTokens.space3),
            MomentTypeSpecificPreview(moment: moment),
            
            // 底部操作栏
            if (showActions) ...[
              SizedBox(height: SpacingTokens.space3),
              _buildActions(context),
            ],
          ],
        ),
      ),
    );
  }

  /// 卡片头部
  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        // 类型图标
        Container(
          padding: EdgeInsets.all(SpacingTokens.space1),
          decoration: BoxDecoration(
            color: _getTypeColor(context).withOpacity(0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            _getTypeIcon(),
            size: 16,
            color: _getTypeColor(context),
          ),
        ),
        
        SizedBox(width: SpacingTokens.space2),
        
        // 类型名称
        Text(
          moment.momentType.displayName,
          style: Theme.of(context).textTheme.labelMedium?.copyWith(
            color: _getTypeColor(context),
            fontWeight: FontWeight.w600,
          ),
        ),
        
        const Spacer(),
        
        // 时间
        Text(
          _formatTime(moment.createTime),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// 动态内容
  Widget _buildContent(BuildContext context) {
    return Text(
      moment.content,
      style: Theme.of(context).textTheme.bodyMedium,
      maxLines: 3,
      overflow: TextOverflow.ellipsis,
    );
  }

  /// 图片展示
  Widget _buildImages(context) {
    final images = moment.images.take(4).toList();
    
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: images.length,
        itemBuilder: (context, index) {
          final image = images[index];
          final isLast = index == images.length - 1;
          final remainingCount = moment.images.length - 4;
          
          return Container(
            width: 80,
            margin: EdgeInsets.only(
              right: isLast ? 0 : SpacingTokens.space2,
            ),
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: ShapeTokens.cardShape,
                  child: Image.network(
                    image.imageUrl,
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        color: Theme.of(context).colorScheme.errorContainer,
                        child: Icon(
                          Icons.broken_image,
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                      );
                    },
                  ),
                ),
                
                // 更多图片提示
                if (isLast && remainingCount > 0)
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        color: ColorTokens.scrim.withValues(alpha: 0.7),
                        borderRadius: ShapeTokens.cardShape,
                      ),
                      child: Center(
                        child: Text(
                          '+$remainingCount',
                          style: Theme.of(context).textTheme.labelLarge?.copyWith(
                            color: ColorTokens.onScrim,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// 底部操作栏
  Widget _buildActions(BuildContext context) {
    return Row(
      children: [
        // 点赞按钮
        _ActionButton(
          icon: Icons.favorite_outline,
          label: '0', // TODO: 实际点赞数
          onTap: () {
            // TODO: 实现点赞功能
          },
        ),
        
        SizedBox(width: SpacingTokens.space4),
        
        // 评论按钮
        _ActionButton(
          icon: Icons.comment_outlined,
          label: '0', // TODO: 实际评论数
          onTap: () {
            // TODO: 实现评论功能
          },
        ),
        
        const Spacer(),
        
        // 更多操作
        PopupMenuButton<String>(
          icon: Icon(
            Icons.more_horiz,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            size: 20,
          ),
          onSelected: (value) {
            switch (value) {
              case 'edit':
                onEdit?.call();
                break;
              case 'delete':
                onDelete?.call();
                break;
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit_outlined),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete_outline, color: ColorTokens.error),
                  SizedBox(width: 8),
                  Text('删除', style: TextStyle(color: ColorTokens.error)),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 获取类型图标
  IconData _getTypeIcon() {
    switch (moment.momentType) {
      case MomentType.fishingCatch:
        return Icons.phishing;
      case MomentType.equipment:
        return Icons.build_outlined;
      case MomentType.technique:
        return Icons.school_outlined;
      case MomentType.question:
        return Icons.help_outline;
    }
  }

  /// 获取类型颜色
  Color _getTypeColor(BuildContext context) {
    switch (moment.momentType) {
      case MomentType.fishingCatch:
        return Theme.of(context).colorScheme.primary;
      case MomentType.equipment:
        return ColorTokens.warning;
      case MomentType.technique:
        return ColorTokens.success;
      case MomentType.question:
        return Theme.of(context).colorScheme.secondary;
    }
  }

  /// 格式化时间
  String _formatTime(DateTime? time) {
    if (time == null) return '';
    
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inSeconds < 30) {
      return '刚刚';
    } else if (difference.inMinutes < 1) {
      return '${difference.inSeconds}秒前';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}天前';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '${weeks}周前';
    } else if (difference.inDays < 365) {
      return '${time.month}月${time.day}日';
    } else {
      return '${time.year}年${time.month}月${time.day}日';
    }
  }
}

/// 操作按钮
class _ActionButton extends StatelessWidget {
  const _ActionButton({
    required this.icon,
    required this.label,
    required this.onTap,
  });

  final IconData icon;
  final String label;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 18,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: SpacingTokens.space1),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}