import 'package:flutter/material.dart';
import 'package:user_app/core/core.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/shared.dart';
import 'package:user_app/models/moment/structured_moment_models.dart';
import 'package:user_app/features/moments/widgets/fish_type_selection_dialog.dart';

/// 捕获鱼类输入对话框 - 用于添加/编辑单条鱼类记录
class CaughtFishInputDialog extends StatefulWidget {
  const CaughtFishInputDialog({
    super.key,
    this.initialFish,
    required this.onSave,
  });

  final CreateCaughtFishRequest? initialFish;
  final Function(CreateCaughtFishRequest) onSave;

  static Future<void> show(
    BuildContext context, {
    CreateCaughtFishRequest? initialFish,
    required Function(CreateCaughtFishRequest) onSave,
  }) {
    return showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => CaughtFishInputDialog(
        initialFish: initialFish,
        onSave: onSave,
      ),
    );
  }

  @override
  State<CaughtFishInputDialog> createState() => _CaughtFishInputDialogState();
}

class _CaughtFishInputDialogState extends State<CaughtFishInputDialog> {
  final _formKey = GlobalKey<FormState>();
  final _countController = TextEditingController();
  final _weightController = TextEditingController();
  final _sizeController = TextEditingController();
  final _remarkController = TextEditingController();
  
  FishType? _selectedFishType;
  String _sizeUnit = 'cm';

  @override
  void initState() {
    super.initState();
    if (widget.initialFish != null) {
      _countController.text = widget.initialFish!.count.toString();
      _weightController.text = widget.initialFish!.weight?.toString() ?? '';
      _sizeController.text = widget.initialFish!.size?.toString() ?? '';
      _remarkController.text = widget.initialFish!.remark ?? '';
      // Note: 需要从 fishTypeId 恢复 _selectedFishType
    }
  }

  @override
  void dispose() {
    _countController.dispose();
    _weightController.dispose();
    _sizeController.dispose();
    _remarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: ShapeTokens.dialogShape,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.85,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题栏
            _buildHeader(context),
            
            // 表单内容
            Flexible(
              child: SingleChildScrollView(
                padding: Spacing.lg,
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 选择鱼类
                      _buildFishTypeSelector(context),
                      SizedBox(height: SpacingTokens.space4),
                      
                      // 数量和重量
                      _buildQuantityAndWeight(context),
                      SizedBox(height: SpacingTokens.space4),
                      
                      // 尺寸
                      _buildSizeInput(context),
                      SizedBox(height: SpacingTokens.space4),
                      
                      // 备注
                      _buildRemarkInput(context),
                    ],
                  ),
                ),
              ),
            ),
            
            // 底部按钮
            _buildBottomActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: Spacing.lg,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(ShapeTokens.radiusXxl),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.phishing,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Text(
              widget.initialFish == null ? '添加鱼获' : '编辑鱼获',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFishTypeSelector(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '鱼类种类',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: SpacingTokens.space2),
        InkWell(
          onTap: () async {
            await FishTypeSelectionDialog.show(
              context,
              onSelected: (fishTypes) {
                if (fishTypes.isNotEmpty) {
                  setState(() {
                    _selectedFishType = fishTypes.first;
                  });
                }
              },
              multiSelect: false,
            );
          },
          borderRadius: ShapeTokens.cardShape,
          child: Container(
            padding: Spacing.md,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.5),
              borderRadius: ShapeTokens.cardShape,
              border: Border.all(
                color: _selectedFishType == null
                    ? Theme.of(context).colorScheme.error.withOpacity(0.5)
                    : Theme.of(context).colorScheme.primary,
                width: _selectedFishType == null ? 1 : 2,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.set_meal,
                  color: _selectedFishType == null
                      ? Theme.of(context).colorScheme.onSurfaceVariant
                      : Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: SpacingTokens.space3),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _selectedFishType?.name ?? '请选择鱼类',
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                          color: _selectedFishType == null
                              ? Theme.of(context).colorScheme.onSurfaceVariant
                              : Theme.of(context).colorScheme.onSurface,
                          fontWeight: _selectedFishType == null
                              ? FontWeight.w400
                              : FontWeight.w500,
                        ),
                      ),
                      if (_selectedFishType != null && _selectedFishType!.commonName != null)
                        Text(
                          _selectedFishType!.commonName!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ],
            ),
          ),
        ),
        if (_selectedFishType == null)
          Padding(
            padding: EdgeInsets.only(top: SpacingTokens.space1),
            child: Text(
              '请选择鱼类种类',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildQuantityAndWeight(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '数量',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: SpacingTokens.space2),
              DSTextFormField(
                controller: _countController,
                keyboardType: TextInputType.number,
                hintText: '条数',
                validator: (value) {
                  if (value?.trim().isEmpty ?? true) {
                    return '请输入数量';
                  }
                  final count = int.tryParse(value!);
                  if (count == null || count <= 0) {
                    return '请输入有效数量';
                  }
                  return null;
                },
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: SpacingTokens.space3),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        onPressed: () {
                          final current = int.tryParse(_countController.text) ?? 0;
                          if (current > 1) {
                            _countController.text = (current - 1).toString();
                          }
                        },
                        icon: const Icon(Icons.remove),
                        iconSize: 18,
                      ),
                      IconButton(
                        onPressed: () {
                          final current = int.tryParse(_countController.text) ?? 0;
                          _countController.text = (current + 1).toString();
                        },
                        icon: const Icon(Icons.add),
                        iconSize: 18,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
        SizedBox(width: SpacingTokens.space3),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '重量 (可选)',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: SpacingTokens.space2),
              DSTextFormField(
                controller: _weightController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                hintText: 'kg',
                suffixText: 'kg',
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSizeInput(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '尺寸 (可选)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: SpacingTokens.space2),
        Row(
          children: [
            Expanded(
              child: DSTextFormField(
                controller: _sizeController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                hintText: '长度',
              ),
            ),
            SizedBox(width: SpacingTokens.space2),
            Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                borderRadius: ShapeTokens.cardShape,
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.5),
                ),
              ),
              child: Row(
                children: [
                  _buildUnitOption('cm', _sizeUnit == 'cm'),
                  _buildUnitOption('m', _sizeUnit == 'm'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildUnitOption(String unit, bool isSelected) {
    return InkWell(
      onTap: () => setState(() => _sizeUnit = unit),
      borderRadius: ShapeTokens.cardShape,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: SpacingTokens.space3,
          vertical: SpacingTokens.space2,
        ),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary
              : ColorTokens.transparent,
          borderRadius: ShapeTokens.cardShape,
        ),
        child: Text(
          unit,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: isSelected
                ? Theme.of(context).colorScheme.onPrimary
                : Theme.of(context).colorScheme.onSurfaceVariant,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildRemarkInput(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '备注 (可选)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: SpacingTokens.space2),
        DSTextFormField(
          controller: _remarkController,
          maxLines: 2,
          hintText: '记录一些特殊情况，如钓法、饵料等',
        ),
      ],
    );
  }

  Widget _buildBottomActions(BuildContext context) {
    return Container(
      padding: Spacing.lg,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: DSButton(
              label: '取消',
              type: DSButtonType.outlined,
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
          SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: DSButton(
              label: widget.initialFish == null ? '添加' : '保存',
              type: DSButtonType.filled,
              onPressed: _handleSave,
            ),
          ),
        ],
      ),
    );
  }

  void _handleSave() {
    if (_selectedFishType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('请选择鱼类种类'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
      return;
    }

    if (_formKey.currentState!.validate()) {
      final count = int.tryParse(_countController.text) ?? 1;
      final weight = double.tryParse(_weightController.text);
      
      double? size;
      if (_sizeController.text.isNotEmpty) {
        size = double.tryParse(_sizeController.text);
        if (size != null && _sizeUnit == 'm') {
          size = size * 100; // 转换为厘米存储
        }
      }

      final caughtFish = CreateCaughtFishRequest(
        fishTypeId: _selectedFishType!.id,
        fishTypeName: _selectedFishType!.name,
        count: count,
        weight: weight,
        size: size,
        remark: _remarkController.text.trim().isEmpty ? null : _remarkController.text.trim(),
      );

      widget.onSave(caughtFish);
      Navigator.of(context).pop();
    }
  }
}