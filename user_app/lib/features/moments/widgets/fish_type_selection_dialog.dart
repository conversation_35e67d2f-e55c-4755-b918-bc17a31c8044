import 'package:flutter/material.dart';
import 'package:user_app/core/core.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/shared.dart';
import 'package:user_app/models/moment/structured_moment_models.dart';

/// 鱼类选择对话框 - 现代化 Material Design 3 设计
class FishTypeSelectionDialog extends StatefulWidget {
  const FishTypeSelectionDialog({
    super.key,
    required this.onSelected,
    this.selectedFishTypes = const [],
    this.multiSelect = false,
  });

  final Function(List<FishType>) onSelected;
  final List<FishType> selectedFishTypes;
  final bool multiSelect;

  static Future<void> show(
    BuildContext context, {
    required Function(List<FishType>) onSelected,
    List<FishType> selectedFishTypes = const [],
    bool multiSelect = false,
  }) {
    return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      useSafeArea: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(SpacingTokens.space6),
        ),
      ),
      builder: (context) => FishTypeSelectionDialog(
        onSelected: onSelected,
        selectedFishTypes: selectedFishTypes,
        multiSelect: multiSelect,
      ),
    );
  }

  @override
  State<FishTypeSelectionDialog> createState() => _FishTypeSelectionDialogState();
}

class _FishTypeSelectionDialogState extends State<FishTypeSelectionDialog> {
  final TextEditingController _searchController = TextEditingController();
  List<FishType> _filteredFishTypes = [];
  List<FishType> _selectedFishTypes = [];
  bool _isLoading = false;
  String? _selectedCategory;

  // 模拟鱼类数据 - 实际应该从 API 加载
  final List<FishType> _allFishTypes = [
    FishType(id: 1, name: '鲫鱼', category: '淡水鱼', commonName: '土鲫', scientificName: 'Carassius auratus'),
    FishType(id: 2, name: '鲤鱼', category: '淡水鱼', commonName: '红鲤', scientificName: 'Cyprinus carpio'),
    FishType(id: 3, name: '草鱼', category: '淡水鱼', commonName: '草青', scientificName: 'Ctenopharyngodon idella'),
    FishType(id: 4, name: '鲢鱼', category: '淡水鱼', commonName: '白鲢', scientificName: 'Hypophthalmichthys molitrix'),
    FishType(id: 5, name: '黑鱼', category: '淡水鱼', commonName: '乌鱼', scientificName: 'Channa argus'),
    FishType(id: 6, name: '鲈鱼', category: '海水鱼', commonName: '海鲈', scientificName: 'Lateolabrax japonicus'),
    FishType(id: 7, name: '石斑鱼', category: '海水鱼', commonName: '石斑', scientificName: 'Epinephelus'),
    FishType(id: 8, name: '黄花鱼', category: '海水鱼', commonName: '黄鱼', scientificName: 'Larimichthys polyactis'),
    FishType(id: 9, name: '带鱼', category: '海水鱼', commonName: '刀鱼', scientificName: 'Trichiurus lepturus'),
    FishType(id: 10, name: '罗非鱼', category: '淡水鱼', commonName: '非洲鲫', scientificName: 'Oreochromis niloticus'),
  ];

  final List<String> _categories = ['全部', '淡水鱼', '海水鱼', '观赏鱼', '其他'];

  @override
  void initState() {
    super.initState();
    _selectedFishTypes = List.from(widget.selectedFishTypes);
    _filteredFishTypes = _allFishTypes;
    _searchController.addListener(_filterFishTypes);
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _filterFishTypes() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredFishTypes = _allFishTypes.where((fish) {
        final matchesSearch = query.isEmpty ||
            fish.name.toLowerCase().contains(query) ||
            (fish.commonName?.toLowerCase().contains(query) ?? false) ||
            (fish.scientificName?.toLowerCase().contains(query) ?? false);
        
        final matchesCategory = _selectedCategory == null || 
            _selectedCategory == '全部' ||
            fish.category == _selectedCategory;
        
        return matchesSearch && matchesCategory;
      }).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      child: Column(
        children: [
          // 拖动指示器
          Container(
            margin: EdgeInsets.only(top: SpacingTokens.space2),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // 标题栏
          _buildHeader(context),
          
          // 搜索栏
          _buildSearchBar(context),
          
          // 分类筛选
          _buildCategoryFilter(context),
          
          // 鱼类列表
          Expanded(
            child: _buildFishList(context),
          ),
          
          // 底部操作栏
          if (widget.multiSelect)
            _buildBottomBar(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space4),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.multiSelect ? '选择鱼类' : '选择一种鱼类',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (widget.multiSelect)
                  Text(
                    '已选择 ${_selectedFishTypes.length} 种',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close),
            style: IconButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              foregroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      margin: EdgeInsets.only(bottom: SpacingTokens.space3),
      child: DSTextFormField(
        controller: _searchController,
        hintText: '搜索鱼类名称、俗名或学名',
        prefixIcon: const Icon(Icons.search),
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                icon: const Icon(Icons.clear),
                onPressed: () {
                  _searchController.clear();
                  _filterFishTypes();
                },
              )
            : null,
      ),
    );
  }

  Widget _buildCategoryFilter(BuildContext context) {
    return Container(
      height: 40,
      margin: EdgeInsets.only(bottom: SpacingTokens.space3),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final category = _categories[index];
          final isSelected = (_selectedCategory ?? '全部') == category;
          
          return Padding(
            padding: EdgeInsets.only(right: SpacingTokens.space2),
            child: FilterChip(
              selected: isSelected,
              label: Text(category),
              onSelected: (selected) {
                setState(() {
                  _selectedCategory = selected ? category : null;
                  _filterFishTypes();
                });
              },
              backgroundColor: Theme.of(context).colorScheme.surfaceVariant,
              selectedColor: Theme.of(context).colorScheme.primaryContainer,
            ),
          );
        },
      ),
    );
  }

  Widget _buildFishList(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_filteredFishTypes.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: SpacingTokens.space3),
            Text(
              '没有找到相关鱼类',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: SpacingTokens.space2),
            Text(
              '试试其他关键词或分类',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4),
      itemCount: _filteredFishTypes.length,
      itemBuilder: (context, index) {
        final fish = _filteredFishTypes[index];
        final isSelected = _selectedFishTypes.any((f) => f.id == fish.id);
        
        return _FishTypeItem(
          fish: fish,
          isSelected: isSelected,
          onTap: () => _handleFishSelection(fish),
        );
      },
    );
  }

  Widget _buildBottomBar(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(SpacingTokens.space4),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        top: false,
        child: Row(
          children: [
            Expanded(
              child: DSButton(
                label: '取消',
                type: DSButtonType.outlined,
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
            SizedBox(width: SpacingTokens.space3),
            Expanded(
              child: DSButton(
                label: '确定 (${_selectedFishTypes.length})',
                type: DSButtonType.filled,
                onPressed: _selectedFishTypes.isNotEmpty
                    ? () {
                        widget.onSelected(_selectedFishTypes);
                        Navigator.of(context).pop();
                      }
                    : null,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleFishSelection(FishType fish) {
    if (widget.multiSelect) {
      setState(() {
        if (_selectedFishTypes.any((f) => f.id == fish.id)) {
          _selectedFishTypes.removeWhere((f) => f.id == fish.id);
        } else {
          _selectedFishTypes.add(fish);
        }
      });
    } else {
      widget.onSelected([fish]);
      Navigator.of(context).pop();
    }
  }
}

/// 鱼类项目组件
class _FishTypeItem extends StatelessWidget {
  const _FishTypeItem({
    required this.fish,
    required this.isSelected,
    required this.onTap,
  });

  final FishType fish;
  final bool isSelected;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: SpacingTokens.space2),
      child: Material(
        color: ColorTokens.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: ShapeTokens.cardShape,
          child: Container(
            padding: Spacing.md,
            decoration: BoxDecoration(
              color: isSelected
                  ? Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3)
                  : Theme.of(context).colorScheme.surfaceVariant.withOpacity(0.3),
              borderRadius: ShapeTokens.cardShape,
              border: Border.all(
                color: isSelected
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.outline.withOpacity(0.2),
                width: isSelected ? 2 : 1,
              ),
            ),
            child: Row(
              children: [
                // 鱼类图标
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: _getCategoryColor(fish.category).withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.set_meal,
                    color: _getCategoryColor(fish.category),
                    size: 24,
                  ),
                ),
                
                SizedBox(width: SpacingTokens.space3),
                
                // 鱼类信息
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            fish.name,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                            ),
                          ),
                          if (fish.commonName != null) ...[
                            SizedBox(width: SpacingTokens.space2),
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: SpacingTokens.space2,
                                vertical: SpacingTokens.space1,
                              ),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surfaceVariant,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                fish.commonName!,
                                style: Theme.of(context).textTheme.labelSmall,
                              ),
                            ),
                          ],
                        ],
                      ),
                      if (fish.scientificName != null) ...[
                        SizedBox(height: SpacingTokens.space1),
                        Text(
                          fish.scientificName!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                      if (fish.category != null) ...[
                        SizedBox(height: SpacingTokens.space1),
                        Row(
                          children: [
                            Icon(
                              Icons.category_outlined,
                              size: 12,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                            SizedBox(width: SpacingTokens.space1),
                            Text(
                              fish.category!,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                
                // 选中状态
                if (isSelected)
                  Icon(
                    Icons.check_circle,
                    color: Theme.of(context).colorScheme.primary,
                  )
                else
                  Icon(
                    Icons.circle_outlined,
                    color: Theme.of(context).colorScheme.outline,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(String? category) {
    switch (category) {
      case '淡水鱼':
        return ColorTokens.primary;
      case '海水鱼':
        return ColorTokens.info;
      case '观赏鱼':
        return ColorTokens.secondary;
      default:
        return ColorTokens.onSurfaceVariant;
    }
  }
}