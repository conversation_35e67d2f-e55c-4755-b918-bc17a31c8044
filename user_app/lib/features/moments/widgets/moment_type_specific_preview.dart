import 'package:flutter/material.dart';
import 'package:user_app/core/core.dart';
import 'package:user_app/shared/shared.dart';
import 'package:user_app/models/moment/structured_moment_models.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

/// 动态类型特定预览组件
class MomentTypeSpecificPreview extends StatelessWidget {
  const MomentTypeSpecificPreview({
    super.key,
    required this.moment,
  });

  final StructuredMomentModel moment;

  @override
  Widget build(BuildContext context) {
    switch (moment.momentType) {
      case MomentType.fishingCatch:
        return _buildFishingCatchPreview(context);
      case MomentType.equipment:
        return _buildEquipmentPreview(context);
      case MomentType.technique:
        return _buildTechniquePreview(context);
      case MomentType.question:
        return _buildQuestionPreview(context);
    }
  }

  /// 钓获分享预览
  Widget _buildFishingCatchPreview(BuildContext context) {
    final data = moment.fishingCatchData;
    if (data == null) return const SizedBox.shrink();

    return Container(
      padding: Spacing.sm,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primaryContainer.withValues(alpha: 0.3),
        borderRadius: ShapeTokens.cardShape,
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 主要信息行
          Row(
            children: [
              Icon(
                Icons.phishing,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: SpacingTokens.space1),
              Expanded(
                child: Text(
                  '钓获详情',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (data.totalWeight != null)
                Text(
                  '总重 ${data.totalWeight}kg',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
          
          // 鱼类信息
          if (data.caughtFishes.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space2),
            Wrap(
              spacing: SpacingTokens.space2,
              runSpacing: SpacingTokens.space1,
              children: data.caughtFishes.take(3).map((fish) {
                return Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: SpacingTokens.space2,
                    vertical: SpacingTokens.space1,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primaryContainer,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${fish.fishTypeName} × ${fish.count}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                );
              }).toList(),
            ),
            if (data.caughtFishes.length > 3)
              Padding(
                padding: EdgeInsets.only(top: SpacingTokens.space1),
                child: Text(
                  '还有${data.caughtFishes.length - 3}种鱼类...',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
          ],
          
          // 钓法和天气
          if (data.fishingMethod != null || data.weatherConditions != null) ...[
            SizedBox(height: SpacingTokens.space2),
            Row(
              children: [
                if (data.fishingMethod != null) ...[
                  Icon(
                    Icons.location_on_outlined,
                    size: 14,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  SizedBox(width: SpacingTokens.space1),
                  Text(
                    data.fishingMethod!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ],
                if (data.fishingMethod != null && data.weatherConditions != null)
                  Text(
                    ' • ',
                    style: TextStyle(color: Theme.of(context).colorScheme.primary),
                  ),
                if (data.weatherConditions != null)
                  Text(
                    data.weatherConditions!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 装备展示预览
  Widget _buildEquipmentPreview(BuildContext context) {
    final data = moment.equipmentData;
    if (data == null) return const SizedBox.shrink();

    return Container(
      padding: Spacing.sm,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.tertiaryContainer.withValues(alpha: 0.3),
        borderRadius: ShapeTokens.cardShape,
        border: Border.all(
          color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 装备名称和评分
          Row(
            children: [
              Icon(
                Icons.build_outlined,
                size: 16,
                color: Theme.of(context).colorScheme.tertiary,
              ),
              SizedBox(width: SpacingTokens.space1),
              Expanded(
                child: Text(
                  data.equipmentName,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.tertiary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (data.rating != null)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.star,
                      size: 14,
                      color: Theme.of(context).colorScheme.secondary,
                    ),
                    SizedBox(width: SpacingTokens.space1),
                    Text(
                      '${data.rating}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.tertiary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
            ],
          ),
          
          // 品牌和型号
          if (data.brand != null || data.model != null) ...[
            SizedBox(height: SpacingTokens.space1),
            Row(
              children: [
                if (data.brand != null) ...[
                  Text(
                    data.brand!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.tertiary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (data.model != null)
                    Text(
                      ' • ',
                      style: TextStyle(color: Theme.of(context).colorScheme.tertiary),
                    ),
                ],
                if (data.model != null)
                  Text(
                    data.model!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                  ),
              ],
            ),
          ],
          
          // 价格和目标鱼种
          if (data.price != null || data.targetFish != null) ...[
            SizedBox(height: SpacingTokens.space1),
            Row(
              children: [
                if (data.price != null) ...[
                  Icon(
                    Icons.attach_money,
                    size: 14,
                    color: Theme.of(context).colorScheme.tertiary,
                  ),
                  Text(
                    data.price!,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                  ),
                  if (data.targetFish != null)
                    Text(
                      ' • ',
                      style: TextStyle(color: Theme.of(context).colorScheme.tertiary),
                    ),
                ],
                if (data.targetFish != null)
                  Expanded(
                    child: Text(
                      '适用：${data.targetFish}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.tertiary,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 技巧分享预览
  Widget _buildTechniquePreview(BuildContext context) {
    final data = moment.techniqueData;
    if (data == null) return const SizedBox.shrink();

    return Container(
      padding: Spacing.sm,
      decoration: BoxDecoration(
        color: ColorTokens.successContainer.withValues(alpha: 0.3),
        borderRadius: ShapeTokens.cardShape,
        border: Border.all(
          color: ColorTokens.success.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 技巧名称和难度
          Row(
            children: [
              Icon(
                Icons.school_outlined,
                size: 16,
                color: ColorTokens.success,
              ),
              SizedBox(width: SpacingTokens.space1),
              Expanded(
                child: Text(
                  data.techniqueName,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: ColorTokens.success,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: SpacingTokens.space2,
                  vertical: SpacingTokens.space1,
                ),
                decoration: BoxDecoration(
                  color: _getDifficultyColor(data.difficulty).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  data.difficulty.displayName,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _getDifficultyColor(data.difficulty),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          
          // 描述预览
          if (data.description != null && data.description!.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space2),
            Text(
              data.description!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ColorTokens.success,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          
          // 环境和鱼种
          if (data.environments.isNotEmpty || data.targetFishTypes.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space2),
            Wrap(
              spacing: SpacingTokens.space1,
              runSpacing: SpacingTokens.space1,
              children: [
                ...data.environments.take(2).map((env) => _buildTag(context, env, ColorTokens.success)),
                ...data.targetFishTypes.take(2).map((fish) => _buildTag(context, fish.fishTypeName, ColorTokens.success)),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// 问答求助预览
  Widget _buildQuestionPreview(BuildContext context) {
    final data = moment.questionData;
    if (data == null) return const SizedBox.shrink();

    return Container(
      padding: Spacing.sm,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.secondaryContainer.withValues(alpha: 0.3),
        borderRadius: ShapeTokens.cardShape,
        border: Border.all(
          color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 问题标题和状态
          Row(
            children: [
              Icon(
                Icons.help_outline,
                size: 16,
                color: Theme.of(context).colorScheme.secondary,
              ),
              SizedBox(width: SpacingTokens.space1),
              Expanded(
                child: Text(
                  data.questionTitle,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.secondary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: SpacingTokens.space2,
                  vertical: SpacingTokens.space1,
                ),
                decoration: BoxDecoration(
                  color: data.resolved 
                      ? ColorTokens.success.withValues(alpha: 0.2)
                      : Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  data.resolved ? '已解决' : '待解决',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: data.resolved ? ColorTokens.success : Theme.of(context).colorScheme.tertiary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          
          // 问题详情预览
          if (data.detailedProblem != null && data.detailedProblem!.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space2),
            Text(
              data.detailedProblem!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.secondary,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          
          // 标签
          if (data.tags.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space2),
            Wrap(
              spacing: SpacingTokens.space1,
              runSpacing: SpacingTokens.space1,
              children: data.tags.take(3).map((tag) {
                return _buildTag(context, tag.tagName ?? '', Theme.of(context).colorScheme.secondary);
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建标签
  Widget _buildTag(BuildContext context, String text, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: SpacingTokens.space2,
        vertical: SpacingTokens.space1,
      ),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: color.withOpacity(0.8),
        ),
      ),
    );
  }

  /// 获取难度颜色
  Color _getDifficultyColor(TechniqueDifficulty difficulty) {
    switch (difficulty) {
      case TechniqueDifficulty.beginner:
        return ColorTokens.success;
      case TechniqueDifficulty.intermediate:
        return ColorTokens.warning;
      case TechniqueDifficulty.advanced:
        return ColorTokens.error;
    }
  }
}