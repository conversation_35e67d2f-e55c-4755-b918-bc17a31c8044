import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/services/photo/photo_data_service.dart';
import 'package:user_app/models/photo/photo_data_index.dart';
import 'package:user_app/models/fishing_spot/fishing_spot.dart';
import 'package:user_app/models/moment/moment.dart';
import 'package:user_app/models/activity/activity.dart';
import 'package:user_app/models/catch_record/catch_record.dart';
import 'package:user_app/shared/widgets/design_system/ds_app_bar.dart';
import 'package:user_app/shared/widgets/design_system/ds_card.dart';
import 'package:user_app/shared/widgets/design_system/ds_button.dart';
import 'package:user_app/shared/widgets/design_system/ds_chip.dart';
import 'package:user_app/shared/widgets/design_system/ds_text_field.dart';

/// 照片详情页面 - 展示照片的多维数据关联视图
class PhotoDetailPage extends StatefulWidget {
  final String photoId;

  const PhotoDetailPage({
    super.key,
    required this.photoId,
  });

  @override
  State<PhotoDetailPage> createState() => _PhotoDetailPageState();
}

class _PhotoDetailPageState extends State<PhotoDetailPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _tagController = TextEditingController();
  
  PhotoRelatedData? _photoData;
  bool _isLoading = true;
  bool _isAddingTag = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _loadPhotoData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  Future<void> _loadPhotoData() async {
    setState(() => _isLoading = true);
    
    try {
      final photoService = PhotoDataService();
      final data = await photoService.getPhotoRelatedData(widget.photoId);
      
      setState(() {
        _photoData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载照片数据失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _addTag(String tag) async {
    if (tag.trim().isEmpty) return;
    
    setState(() => _isAddingTag = true);
    
    try {
      final photoService = PhotoDataService();
      await photoService.addUserTag(widget.photoId, tag.trim());
      
      // 重新加载数据以显示新标签
      await _loadPhotoData();
      
      _tagController.clear();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('标签添加成功'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('添加标签失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      setState(() => _isAddingTag = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: DSAppBar(title: '照片详情'),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_photoData == null) {
      return Scaffold(
        appBar: DSAppBar(title: '照片详情'),
        body: _buildErrorState(),
      );
    }

    final photo = _photoData!.photoIndex;

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSAppBar(
        title: '钓鱼回忆详情',
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              Icons.share,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () => _sharePhoto(),
          ),
          PopupMenuButton<String>(
            icon: Icon(
              Icons.more_vert,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editPhotoInfo();
                  break;
                case 'delete':
                  _deletePhoto();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Text('编辑信息'),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Text('删除照片'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          // 照片展示区域
          _buildPhotoSection(photo),
          // 标签页
          TabBar(
            controller: _tabController,
            isScrollable: true,
            indicatorColor: Theme.of(context).colorScheme.primary,
            labelColor: Theme.of(context).colorScheme.primary,
            unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
            tabs: [
              Tab(text: '概况'),
              Tab(text: '钓点 (${_photoData!.relatedSpots.length})'),
              Tab(text: '动态 (${_photoData!.relatedMoments.length})'),
              Tab(text: '活动 (${_photoData!.relatedActivities.length})'),
              Tab(text: '钓获 (${_photoData!.relatedCatches.length})'),
            ],
          ),
          // 标签页内容
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(photo),
                _buildSpotsTab(_photoData!.relatedSpots),
                _buildMomentsTab(_photoData!.relatedMoments),
                _buildActivitiesTab(_photoData!.relatedActivities),
                _buildCatchesTab(_photoData!.relatedCatches),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoSection(PhotoDataIndex photo) {
    return Container(
      height: 300,
      width: double.infinity,
      margin: Spacing.md,
      child: DSCard(
        elevation: ElevationTokens.level2,
        child: ClipRRect(
          borderRadius: ShapeTokens.cardShape,
          child: Image.network(
            photo.photoUrl,
            fit: BoxFit.cover,
            errorBuilder: (context, error, stackTrace) {
              return Container(
                color: Theme.of(context).colorScheme.surfaceVariant,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.image_not_supported,
                      size: 64,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(height: SpacingTokens.space2),
                    Text(
                      '图片加载失败',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildOverviewTab(PhotoDataIndex photo) {
    return SingleChildScrollView(
      padding: Spacing.screen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 基本信息
          _buildInfoCard(
            title: '基本信息',
            children: [
              _buildInfoRow('拍摄时间', _formatDateTime(photo.takenAt)),
              _buildInfoRow('照片来源', photo.source.displayName),
              if (photo.location != null) ...[
                _buildInfoRow('拍摄位置', photo.location!.address ?? '未知位置'),
                if (photo.location!.spotName != null)
                  _buildInfoRow('钓点名称', photo.location!.spotName!),
              ],
              _buildInfoRow('关联数据', '${photo.totalRelatedItems} 项'),
            ],
          ),
          
          SizedBox(height: SpacingTokens.space4),
          
          // 标签管理
          _buildTagsCard(photo),
          
          SizedBox(height: SpacingTokens.space4),
          
          // 数据关联统计
          _buildDataStatsCard(),
        ],
      ),
    );
  }

  Widget _buildInfoCard({
    required String title,
    required List<Widget> children,
  }) {
    return DSCard(
      child: Padding(
        padding: Spacing.md,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: SpacingTokens.space3),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: SpacingTokens.space2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTagsCard(PhotoDataIndex photo) {
    return DSCard(
      child: Padding(
        padding: Spacing.md,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '标签管理',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                DSButton.text(
                  label: '添加标签',
                  onPressed: () => _showAddTagDialog(),
                ),
              ],
            ),
            SizedBox(height: SpacingTokens.space3),
            
            if (photo.autoTags.isNotEmpty) ...[
              Text(
                '自动标签',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              SizedBox(height: SpacingTokens.space2),
              Wrap(
                spacing: SpacingTokens.space2,
                runSpacing: SpacingTokens.space1,
                children: photo.autoTags.map((tag) {
                  return DSChip(
                    label: tag,
                    backgroundColor: Theme.of(context).colorScheme.secondaryContainer,
                    textColor: Theme.of(context).colorScheme.onSecondaryContainer,
                    size: DSChipSize.small,
                  );
                }).toList(),
              ),
              SizedBox(height: SpacingTokens.space3),
            ],
            
            if (photo.userTags.isNotEmpty) ...[
              Text(
                '用户标签',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              SizedBox(height: SpacingTokens.space2),
              Wrap(
                spacing: SpacingTokens.space2,
                runSpacing: SpacingTokens.space1,
                children: photo.userTags.map((tag) {
                  return DSChip(
                    label: tag,
                    backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                    textColor: Theme.of(context).colorScheme.onPrimaryContainer,
                    size: DSChipSize.small,
                    onTap: () => _removeTag(tag),
                  );
                }).toList(),
              ),
            ] else ...[
              Container(
                padding: Spacing.md,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceVariant,
                  borderRadius: ShapeTokens.cardShape,
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.tag,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: SpacingTokens.space2),
                    Text(
                      '还没有用户标签，添加一些吧',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDataStatsCard() {
    return DSCard(
      child: Padding(
        padding: Spacing.md,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '关联数据统计',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: SpacingTokens.space3),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    Icons.location_on,
                    '钓点',
                    _photoData!.relatedSpots.length,
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    Icons.dynamic_feed,
                    '动态',
                    _photoData!.relatedMoments.length,
                    ColorTokens.success,
                  ),
                ),
              ],
            ),
            SizedBox(height: SpacingTokens.space3),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    Icons.event,
                    '活动',
                    _photoData!.relatedActivities.length,
                    ColorTokens.warning,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    Icons.phishing,
                    '钓获',
                    _photoData!.relatedCatches.length,
                    ColorTokens.info,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String label, int count, Color color) {
    return Container(
      padding: Spacing.md,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: ShapeTokens.cardShape,
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          SizedBox(height: SpacingTokens.space2),
          Text(
            count.toString(),
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpotsTab(List<FishingSpot> spots) {
    if (spots.isEmpty) {
      return _buildEmptyTab('暂无关联钓点', '这张照片还没有关联任何钓点');
    }

    return ListView.builder(
      padding: Spacing.screen,
      itemCount: spots.length,
      itemBuilder: (context, index) {
        final spot = spots[index];
        return _buildSpotCard(spot);
      },
    );
  }

  Widget _buildMomentsTab(List<Moment> moments) {
    if (moments.isEmpty) {
      return _buildEmptyTab('暂无关联动态', '这张照片还没有关联任何动态');
    }

    return ListView.builder(
      padding: Spacing.screen,
      itemCount: moments.length,
      itemBuilder: (context, index) {
        final moment = moments[index];
        return _buildMomentCard(moment);
      },
    );
  }

  Widget _buildActivitiesTab(List<Activity> activities) {
    if (activities.isEmpty) {
      return _buildEmptyTab('暂无关联活动', '这张照片还没有关联任何活动');
    }

    return ListView.builder(
      padding: Spacing.screen,
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return _buildActivityCard(activity);
      },
    );
  }

  Widget _buildCatchesTab(List<CatchRecord> catches) {
    if (catches.isEmpty) {
      return _buildEmptyTab('暂无关联钓获', '这张照片还没有关联任何钓获记录');
    }

    return ListView.builder(
      padding: Spacing.screen,
      itemCount: catches.length,
      itemBuilder: (context, index) {
        final catchRecord = catches[index];
        return _buildCatchCard(catchRecord);
      },
    );
  }

  Widget _buildEmptyTab(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.inbox_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: SpacingTokens.space4),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: SpacingTokens.space2),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSpotCard(FishingSpot spot) {
    // TODO: 实现钓点卡片显示
    return DSCard(
      margin: EdgeInsets.only(bottom: SpacingTokens.space3),
      child: ListTile(
        leading: Icon(Icons.location_on, color: Theme.of(context).colorScheme.primary),
        title: Text('钓点: ${spot.name ?? "未命名钓点"}'),
        subtitle: Text('点击查看详情'),
        trailing: Icon(Icons.chevron_right),
        onTap: () {
          // TODO: 导航到钓点详情页
        },
      ),
    );
  }

  Widget _buildMomentCard(Moment moment) {
    // TODO: 实现动态卡片显示
    return DSCard(
      margin: EdgeInsets.only(bottom: SpacingTokens.space3),
      child: ListTile(
        leading: Icon(Icons.dynamic_feed, color: ColorTokens.success),
        title: Text('动态: ${moment.content ?? "无内容"}'),
        subtitle: Text('点击查看详情'),
        trailing: Icon(Icons.chevron_right),
        onTap: () {
          // TODO: 导航到动态详情页
        },
      ),
    );
  }

  Widget _buildActivityCard(Activity activity) {
    // TODO: 实现活动卡片显示
    return DSCard(
      margin: EdgeInsets.only(bottom: SpacingTokens.space3),
      child: ListTile(
        leading: Icon(Icons.event, color: ColorTokens.warning),
        title: Text('活动: ${activity.title ?? "未命名活动"}'),
        subtitle: Text('点击查看详情'),
        trailing: Icon(Icons.chevron_right),
        onTap: () {
          // TODO: 导航到活动详情页
        },
      ),
    );
  }

  Widget _buildCatchCard(CatchRecord catchRecord) {
    // TODO: 实现钓获卡片显示
    return DSCard(
      margin: EdgeInsets.only(bottom: SpacingTokens.space3),
      child: ListTile(
        leading: Icon(Icons.phishing, color: ColorTokens.info),
        title: Text('钓获: ${catchRecord.fishSpecies ?? "未知鱼类"}'),
        subtitle: Text('点击查看详情'),
        trailing: Icon(Icons.chevron_right),
        onTap: () {
          // TODO: 导航到钓获详情页
        },
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: Theme.of(context).colorScheme.error,
          ),
          SizedBox(height: SpacingTokens.space4),
          Text(
            '照片数据加载失败',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: SpacingTokens.space2),
          Text(
            '请检查网络连接或稍后重试',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: SpacingTokens.space6),
          DSButton(
            label: '重新加载',
            onPressed: _loadPhotoData,
          ),
        ],
      ),
    );
  }

  void _showAddTagDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('添加标签'),
        content: DSTextField(
          controller: _tagController,
          labelText: '标签名称',
          hintText: '输入标签名称',
          autofocus: true,
        ),
        actions: [
          DSButton.text(
            label: '取消',
            onPressed: () => Navigator.of(context).pop(),
          ),
          DSButton(
            label: _isAddingTag ? '添加中...' : '添加',
            onPressed: _isAddingTag 
                ? null 
                : () {
                    _addTag(_tagController.text);
                    Navigator.of(context).pop();
                  },
          ),
        ],
      ),
    );
  }

  void _removeTag(String tag) {
    // TODO: 实现删除标签功能
  }

  void _sharePhoto() {
    // TODO: 实现分享功能
  }

  void _editPhotoInfo() {
    // TODO: 实现编辑照片信息功能
  }

  void _deletePhoto() {
    // TODO: 实现删除照片功能
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}年${dateTime.month}月${dateTime.day}日 '
           '${dateTime.hour.toString().padLeft(2, '0')}:'
           '${dateTime.minute.toString().padLeft(2, '0')}';
  }
}