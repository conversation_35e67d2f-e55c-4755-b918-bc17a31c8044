import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/user/view_models/personal_moment_list_view_model.dart';
import 'package:user_app/models/moment/moment_vo.dart';

class PersonalMomentListPage extends StatefulWidget {
 const PersonalMomentListPage({super.key});

  @override
  State<PersonalMomentListPage> createState() => _PersonalMomentListPageState();
}

class _PersonalMomentListPageState extends State<PersonalMomentListPage>
    with TickerProviderStateMixin {
  late PersonalMomentListViewModel _viewModel;
  final ScrollController _scrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  final List<AnimationController> _itemAnimationControllers = [];

  // 点赞状态管理
  final Map<int, bool> _likingStates = {}; // 防止多次点击
  final Map<int, AnimationController> _likeAnimationControllers = {};

  // 评论状态管理
  final Map<int, bool> _commentingStates = {}; // 防止多次点击评论

  @override
  void initState() {
    super.initState();
    _viewModel = PersonalMomentListViewModel();
    _scrollController.addListener(_onScroll);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _pulseAnimation = Tween<double>(
      begin: 0.95,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewModel.loadMoments();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _pulseController.dispose();
    for (var controller in _itemAnimationControllers) {
      controller.dispose();
    }
    for (var controller in _likeAnimationControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      // Load more when within 200 pixels of the bottom
      if (!_viewModel.isLoading && _viewModel.hasMore) {
        _viewModel.loadMoreMoments();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: Consumer<PersonalMomentListViewModel>(
          builder: (context, viewModel, child) {
            return Stack(
              children: [
                RefreshIndicator(
                  onRefresh: () async {
                    await _viewModel.refreshMoments();
                  },
                  color: Theme.of(context).colorScheme.primary,
                  child: CustomScrollView(
                    controller: _scrollController,
                    slivers: [
                      // 现代化的 AppBar
                      SliverAppBar(
                        expandedHeight: 200,
                        floating: false,
                        pinned: true,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        elevation: 0,
                        flexibleSpace: FlexibleSpaceBar(
                          background: Container(
                            decoration: const BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Theme.of(context).colorScheme.primary,
                                  Theme.of(context).colorScheme.secondary,
                                ],
                              ),
                            ),
                            child: SafeArea(
                              child: Padding(
                                padding: SpacingTokens.paddingLg,
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SlideTransition(
                                      position: _slideAnimation,
                                      child: FadeTransition(
                                        opacity: _fadeAnimation,
                                        child: AnimatedBuilder(
                                          animation: _pulseAnimation,
                                          builder: (context, child) {
                                            return Transform.scale(
                                              scale: _pulseAnimation.value,
                                              child: Container(
                                                width: 80,
                                                height: 80,
                                                decoration: BoxDecoration(
                                                  color: Theme.of(context).colorScheme.onPrimary
                                                      .withValues(alpha: 0.2),
                                                  shape: BoxShape.circle,
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: Theme.of(context).colorScheme.onPrimary
                                                          .withValues(alpha: 0.3),
                                                      blurRadius: 20,
                                                      spreadRadius: 5,
                                                    ),
                                                  ],
                                                ),
                                                child: Icon(
                                                  Icons.article,
                                                  size: 40,
                                                  color: Theme.of(context).colorScheme.onPrimary,
                                                ),
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    ),
                                   SizedBox(height: SpacingTokens.space2),
                                    FadeTransition(
                                      opacity: _fadeAnimation,
                                      child: const Text(
                                        '我的动态',
                                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                          color: Theme.of(context).colorScheme.onPrimary,
                                          fontWeight: FontWeight.bold,
                                          letterSpacing: 1.2,
                                        ),
                                      ),
                                    ),
                                   SizedBox(height: SpacingTokens.space1),
                                    FadeTransition(
                                      opacity: _fadeAnimation,
                                      child:
                                          Consumer<PersonalMomentListViewModel>(
                                        builder: (context, viewModel, child) {
                                          return Text(
                                            '共 ${viewModel.moments.length} 条动态',
                                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                              color:
                                                  Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                   SizedBox(height: SpacingTokens.space2),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        leading: IconButton(
                          icon: Container(
                            padding: SpacingTokens.paddingSm,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                              borderRadius: ShapeTokens.small,
                            ),
                            child: Icon(Icons.arrow_back_ios,
                                color: Theme.of(context).colorScheme.onPrimary),
                          ),
                          onPressed: () {
                            HapticFeedback.lightImpact();
                            context.pop();
                          },
                        ),
                        actions: [
                          IconButton(
                            icon: Container(
                              padding: SpacingTokens.paddingSm,
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.24), Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.12)],
                                ),
                                borderRadius: ShapeTokens.small,
                                boxShadow: [
                                  BoxShadow(
                                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.2),
                                    blurRadius: 10,
                                    spreadRadius: 1,
                                  ),
                                ],
                              ),
                              child: Icon(Icons.add, color: Theme.of(context).colorScheme.onPrimary),
                            ),
                            onPressed: () {
                              HapticFeedback.lightImpact();
                              context.navigateTo('/publish_moment');
                            },
                          ),
                        ],
                        bottom: PreferredSize(
                          preferredSize: const Size.fromHeight(30),
                          child: Container(
                            height: 30,
                            decoration: const BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(SpacingTokens.space8),
                                topRight: Radius.circular(SpacingTokens.space8),
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.12),
                                  blurRadius: 10,
                                  offset: Offset(0, -5),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                      // 内容区域
                      Consumer<PersonalMomentListViewModel>(
                        builder: (context, viewModel, child) {
                          if (viewModel.isLoading &&
                              viewModel.moments.isEmpty) {
                            return SliverFillRemaining(
                              child: _buildLoadingState(),
                            );
                          }

                          if (viewModel.hasError && viewModel.moments.isEmpty) {
                            return SliverFillRemaining(
                              child: _buildErrorState(),
                            );
                          }

                          if (viewModel.moments.isEmpty &&
                              !viewModel.isLoading) {
                            return SliverFillRemaining(
                              child: _buildEmptyState(),
                            );
                          }

                          return SliverPadding(
                            padding: SpacingTokens.paddingLg,
                            sliver: SliverList(
                              delegate: SliverChildBuilderDelegate(
                                (context, index) {
                                  if (index == viewModel.moments.length) {
                                    return _buildLoadingMoreIndicator();
                                  }

                                  final moment = viewModel.moments[index];
                                  return _buildModernMomentCard(moment, index);
                                },
                                childCount: viewModel.moments.length +
                                    (viewModel.hasMore ? 1 : 0),
                              ),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),

                // Network loading indicator
                if (viewModel.isLoading && viewModel.moments.isNotEmpty)
                  Positioned(
                    top: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      height: 3,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                        ),
                      ),
                      child: const LinearProgressIndicator(
                        backgroundColor: Colors.transparent,
                        valueColor:
                            AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.primary.withValues(alpha: 0.5)),
                      ),
                    ),
                  ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildModernMomentCard(MomentVo moment, int index) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 50)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final slideAnimation = Tween<Offset>(
          begin: Offset(0.3, 0),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOutBack,
        ));

        final scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.elasticOut,
        ));

        return SlideTransition(
          position: slideAnimation,
          child: ScaleTransition(
            scale: scaleAnimation,
            child: child,
          ),
        );
      },
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            HapticFeedback.lightImpact();
            if (moment.id != null) {
              context.navigateTo('/momentDetail', extra: {
                'momentId': moment.id,
              });
            }
          },
          onLongPress: () {
            HapticFeedback.mediumImpact();
            _showMomentOptions(moment);
          },
          borderRadius: ShapeTokens.large,
          child: Container(
            margin: EdgeInsets.only(bottom: SpacingTokens.space5),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.surface,
                  Theme.of(context).colorScheme.background,
                ],
              ),
              borderRadius: ShapeTokens.large,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.08),
                  blurRadius: 20,
                  offset: Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 顶部信息栏 - 简化版
                Container(
                  padding: EdgeInsets.only(left: SpacingTokens.space5, top: SpacingTokens.space1, bottom: SpacingTokens.space1),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
                        Theme.of(context).colorScheme.secondary.withValues(alpha: 0.05),
                      ],
                    ),
                    borderRadius: BorderRadius.only(
                      topLeft: ShapeTokens.large.topLeft,
                      topRight: ShapeTokens.large.topRight,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // 时间信息
                      Text(
                        moment.createdAt ?? '',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),

                      // 更多按钮
                      InkWell(
                        onTap: () => _showMomentOptions(moment),
                        borderRadius: ShapeTokens.small,
                        child: Padding(
                          padding: EdgeInsets.all(SpacingTokens.space2),
                          child: Icon(
                            Icons.more_horiz,
                            size: 20,
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 内容区域
                Padding(
                  padding: SpacingTokens.paddingLg,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 文字内容
                      if (moment.content != null &&
                          moment.content!.isNotEmpty) ...[
                        Text(
                          moment.content!,
                          style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            height: 1.6,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                       SizedBox(height: SpacingTokens.space3),
                      ],

                      // 图片展示
                      if (moment.images != null && moment.images!.isNotEmpty)
                        _buildModernImageGrid(
                            moment.images!.map((img) => img.imageUrl).toList()),

                      // 位置信息
                      if (moment.fishingSpotName != null &&
                          moment.fishingSpotName!.isNotEmpty) ...[
                       SizedBox(height: SpacingTokens.space3),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: SpacingTokens.space3, vertical: SpacingTokens.space2),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                              ],
                            ),
                            borderRadius: ShapeTokens.small,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                             Icon(Icons.location_on,
                                  size: 16, color: Theme.of(context).colorScheme.primary),
                             SizedBox(width: SpacingTokens.space2),
                              Text(
                                moment.fishingSpotName!,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // 互动栏
                Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: SpacingTokens.space5, vertical: SpacingTokens.space4),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.surfaceVariant,
                        Theme.of(context).colorScheme.surface,
                      ],
                    ),
                    borderRadius: BorderRadius.only(
                      bottomLeft: ShapeTokens.large.bottomLeft,
                      bottomRight: ShapeTokens.large.bottomRight,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildInteractionButton(
                        icon: moment.isLiked ?? false
                            ? Icons.thumb_up
                            : Icons.thumb_up_outlined,
                        label: '${moment.likeCount ?? 0}',
                        isActive: moment.isLiked ?? false,
                        isLoading: _likingStates[moment.id] ?? false,
                        onTap: () => _handleLike(moment),
                      ),
                      _buildInteractionButton(
                        icon: Icons.chat_bubble_outline,
                        label: '${moment.commentCount?.toInt() ?? 0}',
                        isActive: false,
                        onTap: () => _handleComment(moment),
                      ),
                      _buildInteractionButton(
                        icon: Icons.share_outlined,
                        label: '分享',
                        isActive: false,
                        onTap: () {
                          HapticFeedback.lightImpact();
                          _shareMoment(moment);
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInteractionButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
    final bool isLoading = false,
    Color? loadingColor,
  }) {
    return GestureDetector(
      onTap: isLoading
          ? null
          : () {
              HapticFeedback.lightImpact();
              onTap();
            },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space4, vertical: SpacingTokens.space2),
        decoration: BoxDecoration(
          gradient: isActive
              ? LinearGradient(
                  colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                )
              : null,
          color: isActive ? null : Colors.transparent,
          borderRadius: ShapeTokens.small,
          boxShadow: isActive
              ? [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            if (isLoading) ...[
              SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    loadingColor ??
                        (isActive ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.onSurfaceVariant),
                  ),
                ),
              ),
            ] else ...[
              Icon(
                icon,
                size: 20,
                color: isActive ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ],
            if (label.isNotEmpty) ...[
             SizedBox(width: SpacingTokens.space2),
              Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  color: isActive ? Theme.of(context).colorScheme.onPrimary : Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildModernImageGrid(List<String> images) {
    if (images.length == 1) {
      return ClipRRect(
        borderRadius: ShapeTokens.medium,
        child: CachedNetworkImage(
          imageUrl: images[0],
          width: double.infinity,
          height: 250,
          fit: BoxFit.cover,
        ),
      );
    }

    if (images.length == 2) {
      return Row(
        children: images
            .map((url) => Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(right: SpacingTokens.space2),
                    child: ClipRRect(
                      borderRadius: ShapeTokens.small,
                      child: CachedNetworkImage(
                        imageUrl: url,
                        height: 200,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ))
            .toList(),
      );
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: SpacingTokens.space2,
        mainAxisSpacing: SpacingTokens.space2,
        childAspectRatio: 1,
      ),
      itemCount: images.length > 9 ? 9 : images.length,
      itemBuilder: (context, index) {
        return Stack(
          children: [
            ClipRRect(
              borderRadius: ShapeTokens.small,
              child: CachedNetworkImage(
                imageUrl: images[index],
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            if (index == 8 && images.length > 9)
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: ShapeTokens.small,
                  child: Container(
                    color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.54),
                    child: Center(
                      child: Text(
                        '+${images.length - 9}',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const Padding(
              padding: EdgeInsets.all(12),
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Theme.of(context).colorScheme.onPrimary),
                strokeWidth: 3,
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space5),
         const Text(
            '加载中...',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Consumer<PersonalMomentListViewModel>(
      builder: (context, viewModel, child) {
        if (!viewModel.isLoading && !viewModel.hasMore) {
          return Container(
            padding: SpacingTokens.paddingLg,
            child: Center(
              child: Column(
                children: [
                  Icon(
                    Icons.check_circle_outline,
                    size: 40,
                    color: Theme.of(context).colorScheme.outline,
                  ),
                 const SizedBox(height: SpacingTokens.space2),
                  Text(
                    '已经到底了',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          );
        }

        return Container(
          padding: SpacingTokens.paddingLg,
          child: Center(
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Column(
                    children: [
                      Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: const Padding(
                          padding: SpacingTokens.paddingSm,
                          child: CircularProgressIndicator(
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                            strokeWidth: 2,
                          ),
                        ),
                      ),
                     SizedBox(height: SpacingTokens.space3),
                      Text(
                        '加载更多...',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                      ],
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.error_outline,
                    size: 60,
                    color: Theme.of(context).colorScheme.error,
                  ),
                ),
              );
            },
          ),
         const SizedBox(height: SpacingTokens.space6),
         const Text(
            '加载失败',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
         SizedBox(height: SpacingTokens.space1),
          Consumer<PersonalMomentListViewModel>(
            builder: (context, viewModel, child) {
              return Text(
                viewModel.errorMessage ?? '网络连接异常，请检查网络设置',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              );
            },
          ),
         SizedBox(height: SpacingTokens.space8),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _viewModel.loadMoments();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space10, vertical: SpacingTokens.space4),
              shape: RoundedRectangleBorder(
                borderRadius: ShapeTokens.large,
              ),
              elevation: 5,
              shadowColor: Color(0xFF667EEA).withValues(alpha: 0.5),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Icon(Icons.refresh, color: Theme.of(context).colorScheme.onPrimary),
               SizedBox(width: SpacingTokens.space2),
                Text(
                  '重新加载',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _pulseAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _pulseAnimation.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                      ],
                    ),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.dynamic_feed_outlined,
                    size: 60,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              );
            },
          ),
         const SizedBox(height: SpacingTokens.space6),
         const Text(
            '暂无动态',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
         SizedBox(height: SpacingTokens.space1),
          Text(
            '快去发布你的第一条钓鱼动态吧！',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
         SizedBox(height: SpacingTokens.space8),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              context.navigateTo('/publish_moment');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: EdgeInsets.symmetric(horizontal: SpacingTokens.space10, vertical: SpacingTokens.space4),
              shape: RoundedRectangleBorder(
                borderRadius: ShapeTokens.large,
              ),
              elevation: 5,
              shadowColor: Color(0xFF667EEA).withValues(alpha: 0.5),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Icon(Icons.add_circle_outline, color: Theme.of(context).colorScheme.onPrimary),
               SizedBox(width: SpacingTokens.space2),
                Text(
                  '发布动态',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 处理点赞功能，包含防抖和动画
  Future<void> _handleLike(MomentVo moment) async {
    if (moment.id == null) return;

    // 防止多次点击
    if (_likingStates[moment.id] == true) return;
    
    // 立即更新UI状态（乐观更新）
    final isCurrentlyLiked = moment.isLiked ?? false;
    final newLikeState = !isCurrentlyLiked;
    final currentLikeCount = moment.likeCount ?? 0;
    final newLikeCount = newLikeState ? currentLikeCount + 1 : currentLikeCount - 1;
    
    // 立即更新本地状态
    _viewModel.updateMomentLike(moment.id!, newLikeState, newLikeCount);

    setState(() {
      _likingStates[moment.id!] = true;
    });

    try {
      await _viewModel.likeMoment(moment.id!, newLikeState);

      // 触发成功反馈
      HapticFeedback.lightImpact();
      
      // 可以添加成功动画效果
      if (newLikeState) {
        _animateLikeSuccess(moment.id!);
      }
      
    } catch (error) {
      debugPrint('Like error: $error');
      
      // 网络失败时回滚状态
      _viewModel.updateMomentLike(moment.id!, isCurrentlyLiked, currentLikeCount);
      
      if (mounted) {
        HapticFeedback.heavyImpact(); // 失败时使用不同的震动
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
               Icon(Icons.error_outline, color: Theme.of(context).colorScheme.onError, size: 20),
               SizedBox(width: SpacingTokens.space2),
               const Expanded(child: Text('点赞失败，请检查网络连接')),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _handleLike(moment); // 重试
                  },
                  child: Text('重试', style: TextStyle(color: Theme.of(context).colorScheme.onError)),
                ),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _likingStates[moment.id!] = false;
        });
      }
    }
  }

  // 点赞成功动画
  void _animateLikeSuccess(int momentId) {
    // 可以添加点赞成功的动画效果
    // 比如心形动画、粒子效果等
  }

  // 处理评论功能，防止多次点击
  void _handleComment(MomentVo moment) {
    if (moment.id == null) return;

    // 防止多次点击
    if (_commentingStates[moment.id] == true) return;

    setState(() {
      _commentingStates[moment.id!] = true;
    });

    // 触发触觉反馈
    HapticFeedback.lightImpact();

    // 立即跳转，不需要延迟
    context.navigateTo('/momentDetail', extra: {
      'momentId': moment.id,
    });

    // 短暂延迟后重置状态，防止快速连击
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _commentingStates[moment.id!] = false;
        });
      }
    });
  }

  void _shareMoment(MomentVo moment) {
    final content = moment.content ?? '';
    final location =
        moment.fishingSpotName != null && moment.fishingSpotName!.isNotEmpty
            ? ' - 📍 ${moment.fishingSpotName}'
            : '';
    final shareText = '$content$location\n\n来自钓鱼社区的分享 🎣';

    Share.share(shareText, subject: '分享钓鱼动态');
  }

  void _showMomentOptions(MomentVo moment) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: ShapeTokens.large.topLeft,
            topRight: ShapeTokens.large.topRight,
          ),
        ),
        padding: EdgeInsets.symmetric(vertical: SpacingTokens.space5),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: EdgeInsets.only(bottom: SpacingTokens.space5),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline,
                borderRadius: BorderRadius.circular(SpacingTokens.space1 / 2),
              ),
            ),
            ListTile(
              leading: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                  borderRadius: ShapeTokens.small,
                ),
                child: Icon(Icons.share, color: Theme.of(context).colorScheme.secondary),
              ),
              title: const Text(
                '分享动态',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                _shareMoment(moment);
              },
            ),
            ListTile(
              leading: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.1),
                  borderRadius: ShapeTokens.small,
                ),
                child: Icon(
                  moment.visibility == 'private'
                      ? Icons.visibility
                      : Icons.visibility_off,
                  color: Theme.of(context).colorScheme.tertiary,
                ),
              ),
              title: Text(
                moment.visibility == 'private' ? '设为公开' : '设为私密',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              onTap: () async {
                Navigator.pop(context);
                if (moment.id != null) {
                  try {
                    final newVisibility =
                        moment.visibility == 'private' ? 'public' : 'private';
                    debugPrint(
                        'Updating visibility for moment ${moment.id} from ${moment.visibility} to $newVisibility');

                    await _viewModel.updateMomentVisibility(
                        moment.id!, newVisibility);

                    if (mounted) {
                      final message =
                          newVisibility == 'private' ? '已设为私密' : '已设为公开';
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(message),
                          backgroundColor: Theme.of(context).colorScheme.primary,
                        ),
                      );
                    }
                  } catch (error, stackTrace) {
                    debugPrint('Privacy update error: $error');
                    debugPrint('Stack trace: $stackTrace');

                    if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('设置失败: ${error.toString()}'),
                          backgroundColor: Theme.of(context).colorScheme.error,
                          duration: const Duration(seconds: 3),
                        ),
                      );
                    }
                  }
                } else {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                     const SnackBar(
                        content: Text('动态ID不可用'),
                        backgroundColor: Theme.of(context).colorScheme.tertiary,
                      ),
                    );
                  }
                }
              },
            ),
           const Divider(height: 1),
            ListTile(
              leading: Container(
                padding: SpacingTokens.paddingSm,
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.error.withValues(alpha: 0.1),
                  borderRadius: ShapeTokens.small,
                ),
                child: Icon(Icons.delete_outline, color: Theme.of(context).colorScheme.error),
              ),
              title: const Text(
                '删除动态',
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.error,
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                HapticFeedback.mediumImpact();
                _showDeleteConfirmation(moment);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteConfirmation(MomentVo moment) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: ShapeTokens.medium,
        ),
        title: const Text(
          '删除动态',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: const Text('确定要删除这条动态吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              if (moment.id != null) {
                try {
                  await _viewModel.deleteMoment(moment.id!);
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                     const SnackBar(
                        content: Text('动态已删除'),
                        backgroundColor: Theme.of(context).colorScheme.primary,
                      ),
                    );
                  }
                } catch (error) {
                  if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                     const SnackBar(
                        content: Text('删除失败，请重试'),
                        backgroundColor: Theme.of(context).colorScheme.error,
                      ),
                    );
                  }
                }
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}
