import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/features/user/screens/enhanced_photo_viewer.dart';
import 'package:user_app/features/user/view_models/album_view_model.dart';
import 'package:user_app/models/photo/photo.dart';

class AlbumPage extends StatefulWidget {
  const AlbumPage({super.key});

  @override
  State<AlbumPage> createState() => _AlbumPageState();
}

class _AlbumPageState extends State<AlbumPage> with TickerProviderStateMixin {
  late AlbumViewModel _viewModel;
  final ScrollController _scrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _floatingController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;
  late Animation<double> _floatingAnimation;

  final List<AnimationController> _itemAnimationControllers = [];

  // 网格类型
  int _gridCrossAxisCount = 3;

  // 增强的状态管理
  bool _isRefreshing = false;
  bool _isLoadingMore = false;
  String? _errorMessage;
  final Map<String, bool> _deletingItems = {}; // 正在删除的图片

  // 分类筛选
  String _selectedCategory = 'all'; // all, fish, spot, moment

  // 搜索相关
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;
  String _searchQuery = '';

  // 交互状态管理

  @override
  void initState() {
    super.initState();
    _viewModel = AlbumViewModel();
    _scrollController.addListener(_onScroll);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, -0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    );
    _floatingAnimation = Tween<double>(
      begin: -10,
      end: 10,
    ).animate(CurvedAnimation(
      parent: _floatingController,
      curve: Curves.easeInOut,
    ));

    // 开始动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadAlbumPhotos();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _floatingController.dispose();
    for (final controller in _itemAnimationControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (!_isLoadingMore && _viewModel.hasMore) {
        _loadMorePhotos();
      }
    }
  }

  // 增强的加载方法
  Future<void> _loadAlbumPhotos() async {
    try {
      setState(() {
        _errorMessage = null;
      });
      await _viewModel.loadAlbumPhotos();
    } catch (error) {
      debugPrint('Load album photos error: $error');
      if (mounted) {
        setState(() {
          _errorMessage = error.toString();
        });
        _showErrorSnackBar('加载失败，请检查网络连接', retry: _loadAlbumPhotos);
      }
    }
  }

  // 加载更多照片
  Future<void> _loadMorePhotos() async {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    try {
      await _viewModel.loadMorePhotos();
    } catch (error) {
      debugPrint('Load more photos error: $error');
      if (mounted) {
        _showErrorSnackBar('加载更多失败', retry: _loadMorePhotos);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
      }
    }
  }

  // 刷新相册
  Future<void> _refreshAlbum() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
      _errorMessage = null;
    });

    try {
      await _viewModel.refreshPhotos();
      if (mounted) {
        HapticFeedback.lightImpact();
      }
    } catch (error) {
      debugPrint('Refresh album error: $error');
      if (mounted) {
        _showErrorSnackBar('刷新失败，请检查网络连接', retry: _refreshAlbum);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: RefreshIndicator(
          onRefresh: _refreshAlbum,
          color: Theme.of(context).colorScheme.primary,
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              // 现代化的 AppBar
              SliverAppBar(
                expandedHeight: 240,
                floating: false,
                pinned: true,
                backgroundColor: ColorTokens.transparent,
                elevation: 0,
                flexibleSpace: FlexibleSpaceBar(
                  background: Stack(
                    children: [
                      // 渐变背景
                      Container(
                        decoration: const BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                            colors: [
                              Theme.of(context).colorScheme.primary,
                              Theme.of(context).colorScheme.secondary,
                            ],
                          ),
                        ),
                      ),
                      // 装饰元素
                      Positioned(
                        top: -50,
                        right: -50,
                        child: AnimatedBuilder(
                          animation: _floatingAnimation,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(0, _floatingAnimation.value),
                              child: Container(
                                width: 200,
                                height: 200,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: ColorTokens.onPrimary
                                      .withValues(alpha: 0.1),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      Positioned(
                        bottom: -30,
                        left: -30,
                        child: AnimatedBuilder(
                          animation: _floatingAnimation,
                          builder: (context, child) {
                            return Transform.translate(
                              offset: Offset(0, -_floatingAnimation.value),
                              child: Container(
                                width: 150,
                                height: 150,
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  color: ColorTokens.onPrimary
                                      .withValues(alpha: 0.05),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                      // 内容
                      SafeArea(
                        child: Padding(
                          padding: SpacingTokens.paddingLg,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SlideTransition(
                                position: _slideAnimation,
                                child: FadeTransition(
                                  opacity: _fadeAnimation,
                                  child: Container(
                                    width: 80,
                                    height: 80,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: ColorTokens.onPrimary
                                          .withValues(alpha: 0.2),
                                      boxShadow: [
                                        BoxShadow(
                                          color: ColorTokens.onPrimary
                                              .withValues(alpha: 0.3),
                                          blurRadius: 20,
                                          spreadRadius: 5,
                                        ),
                                      ],
                                    ),
                                    child: const Icon(
                                      Icons.photo_library,
                                      size: 40,
                                      color: ColorTokens.onPrimary,
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(height: SpacingTokens.space5),
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: const Text(
                                  '我的相册',
                                  style: TextStyle(
                                    color: ColorTokens.onPrimary,
                                    fontSize: 28,
                                    fontWeight: FontWeight.bold,
                                    letterSpacing: 1.2,
                                  ),
                                ),
                              ),
                              const SizedBox(height: SpacingTokens.space4),
                              FadeTransition(
                                opacity: _fadeAnimation,
                                child: _buildAlbumStats(),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                leading: IconButton(
                  icon: Container(
                    padding: SpacingTokens.paddingSm,
                    decoration: BoxDecoration(
                      color: ColorTokens.onPrimary.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(Icons.arrow_back_ios,
                        color: ColorTokens.onPrimary),
                  ),
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    context.pop();
                  },
                ),
                actions: [
                  Consumer<AlbumViewModel>(
                    builder: (context, viewModel, child) {
                      if (viewModel.isSelectionMode) {
                        return Row(
                          children: [
                            if (viewModel.selectedPhotos.isNotEmpty) ...[
                              IconButton(
                                icon: Container(
                                  padding: SpacingTokens.paddingSm,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.onPrimary
                                        .withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Icon(Icons.download,
                                      color: ColorTokens.onPrimary),
                                ),
                                onPressed: () {
                                  HapticFeedback.mediumImpact();
                                  _batchDownloadPhotos();
                                },
                              ),
                              IconButton(
                                icon: Container(
                                  padding: SpacingTokens.paddingSm,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.onPrimary
                                        .withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Icon(Icons.share,
                                      color: ColorTokens.onPrimary),
                                ),
                                onPressed: () {
                                  HapticFeedback.mediumImpact();
                                  _batchSharePhotos();
                                },
                              ),
                              IconButton(
                                icon: Container(
                                  padding: SpacingTokens.paddingSm,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.onPrimary
                                        .withValues(alpha: 0.2),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: const Icon(Icons.delete,
                                      color: ColorTokens.onPrimary),
                                ),
                                onPressed: () {
                                  HapticFeedback.mediumImpact();
                                  _showDeleteConfirmation();
                                },
                              ),
                            ],
                            IconButton(
                              icon: Container(
                                padding: SpacingTokens.paddingSm,
                                decoration: BoxDecoration(
                                  color: ColorTokens.onPrimary
                                      .withValues(alpha: 0.2),
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: const Icon(Icons.close,
                                    color: ColorTokens.onPrimary),
                              ),
                              onPressed: () {
                                HapticFeedback.lightImpact();
                                viewModel.toggleSelectionMode();
                              },
                            ),
                          ],
                        );
                      }
                      return PopupMenuButton<String>(
                        icon: Container(
                          padding: SpacingTokens.paddingSm,
                          decoration: BoxDecoration(
                            color: ColorTokens.onPrimary.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: const Icon(Icons.more_vert,
                              color: ColorTokens.onPrimary),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(15),
                        ),
                        onSelected: _handleMenuAction,
                        itemBuilder: (context) => [
                          PopupMenuItem(
                            value: 'upload',
                            child: Row(
                              children: [
                                Container(
                                  padding: SpacingTokens.paddingSm,
                                  decoration: BoxDecoration(
                                    color: ColorTokens.success
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Icon(
                                    Icons.add_photo_alternate,
                                    size: 20,
                                    color: ColorTokens.success,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text('上传照片'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'search',
                            child: Row(
                              children: [
                                Container(
                                  padding: SpacingTokens.paddingSm,
                                  decoration: BoxDecoration(
                                    color:
                                        ColorTokens.info.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Icon(
                                    Icons.search,
                                    size: 20,
                                    color: ColorTokens.info,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text('搜索照片'),
                              ],
                            ),
                          ),
                          const PopupMenuDivider(),
                          PopupMenuItem(
                            value: 'select',
                            child: Row(
                              children: [
                                Container(
                                  padding: SpacingTokens.paddingSm,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .primary
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Icon(
                                    Icons.check_circle_outline,
                                    size: 20,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                const Text('选择照片'),
                              ],
                            ),
                          ),
                          PopupMenuItem(
                            value: 'grid',
                            child: Row(
                              children: [
                                Container(
                                  padding: SpacingTokens.paddingSm,
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .secondary
                                        .withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(10),
                                  ),
                                  child: const Icon(
                                    Icons.grid_view,
                                    size: 20,
                                    color:
                                        Theme.of(context).colorScheme.secondary,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Text('切换布局 ($_gridCrossAxisCount列)'),
                              ],
                            ),
                          ),
                        ],
                      );
                    },
                  ),
                ],
                bottom: PreferredSize(
                  preferredSize: const Size.fromHeight(30),
                  child: Container(
                    height: 30,
                    decoration: const BoxDecoration(
                      color: ColorTokens.onPrimary,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(30),
                        topRight: Radius.circular(30),
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: ColorTokens.scrim.withValues(alpha: 0.12),
                          blurRadius: 10,
                          offset: Offset(0, -5),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // 分类筛选标签
              SliverToBoxAdapter(
                child: Container(
                  color: ColorTokens.onPrimary,
                  padding: EdgeInsets.symmetric(vertical: 8),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.symmetric(horizontal: 20),
                    child: Row(
                      children: [
                        _buildCategoryChip('all', '全部', Icons.photo_library),
                        const SizedBox(width: 10),
                        _buildCategoryChip('fish', '渔获', Icons.set_meal),
                        const SizedBox(width: 10),
                        _buildCategoryChip('spot', '钓点', Icons.location_on),
                        const SizedBox(width: 10),
                        _buildCategoryChip('moment', '动态', Icons.article),
                      ],
                    ),
                  ),
                ),
              ),

              // 搜索栏和筛选
              SliverToBoxAdapter(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 300),
                  height: _isSearching ? 70 : 0,
                  child: _isSearching
                      ? Container(
                          color: ColorTokens.onPrimary,
                          padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
                          child: Row(
                            children: [
                              Expanded(
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: Theme.of(context)
                                        .colorScheme
                                        .surfaceContainerHighest,
                                    borderRadius: BorderRadius.circular(30),
                                    border: Border.all(
                                      color: _searchController.text.isNotEmpty
                                          ? Theme.of(context)
                                              .colorScheme
                                              .primary
                                          : ColorTokens.transparent,
                                      width: 2,
                                    ),
                                  ),
                                  child: Row(
                                    children: [
                                      const SizedBox(width: 16),
                                      Icon(
                                        Icons.search,
                                        color: ColorTokens.onSurfaceVariant,
                                        size: 22,
                                      ),
                                      const SizedBox(width: 12),
                                      Expanded(
                                        child: TextField(
                                          controller: _searchController,
                                          autofocus: true,
                                          decoration: InputDecoration(
                                            hintText: '搜索照片描述、日期...',
                                            hintStyle: TextStyle(
                                              color: ColorTokens.outline,
                                              fontSize: 16,
                                            ),
                                            border: InputBorder.none,
                                            contentPadding: EdgeInsets.zero,
                                          ),
                                          onChanged: (value) {
                                            setState(() {
                                              _searchQuery = value;
                                            });
                                            // 延迟搜索，避免频繁请求
                                            Future.delayed(
                                                const Duration(
                                                    milliseconds: 500), () {
                                              if (_searchQuery == value) {
                                                _performSearch();
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                      if (_searchController.text.isNotEmpty)
                                        IconButton(
                                          icon: const Icon(Icons.clear),
                                          onPressed: () {
                                            _searchController.clear();
                                            setState(() {
                                              _searchQuery = '';
                                            });
                                            _performSearch();
                                          },
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 10),
                              // 筛选按钮
                              Container(
                                decoration: BoxDecoration(
                                  gradient: const LinearGradient(
                                    colors: [
                                      Theme.of(context).colorScheme.primary,
                                      Theme.of(context).colorScheme.secondary
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                  boxShadow: [
                                    BoxShadow(
                                      color: Theme.of(context)
                                          .colorScheme
                                          .primary
                                          .withValues(alpha: 0.3),
                                      blurRadius: 8,
                                      offset: Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Material(
                                  color: ColorTokens.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(20),
                                    onTap: _showAdvancedFilters,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                        horizontal: 16,
                                        vertical: 12,
                                      ),
                                      child: Row(
                                        children: const [
                                          Icon(
                                            Icons.tune,
                                            color: ColorTokens.onPrimary,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 6),
                                          Text(
                                            '筛选',
                                            style: TextStyle(
                                              color: ColorTokens.onPrimary,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : null,
                ),
              ),

              // 内容区域
              Consumer<AlbumViewModel>(
                builder: (context, viewModel, child) {
                  if (viewModel.isLoading && viewModel.photos.isEmpty) {
                    return SliverFillRemaining(
                      child: _buildLoadingState(),
                    );
                  }

                  if (_errorMessage != null && viewModel.photos.isEmpty) {
                    return SliverFillRemaining(
                      child: _buildErrorState(),
                    );
                  }

                  if (viewModel.photos.isEmpty && !viewModel.isLoading) {
                    return SliverFillRemaining(
                      child: _buildEmptyState(),
                    );
                  }

                  return SliverPadding(
                    padding: SpacingTokens.paddingLg,
                    sliver: SliverGrid(
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: _gridCrossAxisCount,
                        crossAxisSpacing: 12,
                        mainAxisSpacing: 12,
                        childAspectRatio: 1,
                      ),
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          if (index == viewModel.photos.length) {
                            return _buildLoadingMoreIndicator();
                          }
                          return _buildEnhancedPhotoItem(
                              viewModel.photos[index], index);
                        },
                        childCount: viewModel.photos.length +
                            (viewModel.hasMore && !_isLoadingMore ? 1 : 0),
                      ),
                    ),
                  );
                },
              ),
            ],
          ), // CustomScrollView结束
        ), // RefreshIndicator结束

        // 选择模式的底部操作栏
        bottomNavigationBar: Consumer<AlbumViewModel>(
          builder: (context, viewModel, child) {
            if (!viewModel.isSelectionMode) return const SizedBox.shrink();

            return Container(
              decoration: BoxDecoration(
                color: ColorTokens.onPrimary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: ColorTokens.scrim.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: SafeArea(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                  child: Row(
                    children: [
                      // 选择信息
                      Expanded(
                        child: Text(
                          viewModel.selectedPhotos.isEmpty
                              ? '请选择照片'
                              : '已选择 ${viewModel.selectedPhotos.length} 张',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      // 全选按钮
                      TextButton(
                        onPressed: () {
                          if (viewModel.selectedPhotos.length ==
                              viewModel.photos.length) {
                            // 取消全选
                            viewModel.clearSelection();
                          } else {
                            // 全选
                            for (final photo in viewModel.photos) {
                              if (!viewModel.selectedPhotos
                                  .contains(photo.id)) {
                                viewModel.togglePhotoSelection(photo.id);
                              }
                            }
                          }
                        },
                        child: Text(
                          viewModel.selectedPhotos.length ==
                                  viewModel.photos.length
                              ? '取消全选'
                              : '全选',
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ), // Scaffold结束
    ); // ChangeNotifierProvider.value结束
  }

  Widget _buildAlbumStats() {
    return Consumer<AlbumViewModel>(
      builder: (context, viewModel, child) {
        final photoCount = viewModel.photos.length;
        final totalSize = viewModel.photos
            .fold<int>(0, (sum, photo) => sum + (photo.fileSize ?? 0));

        return Container(
          padding: EdgeInsets.symmetric(horizontal: 24, vertical: 12),
          decoration: BoxDecoration(
            color: ColorTokens.onPrimary.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: ColorTokens.onPrimary.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatItem(Icons.photo, '$photoCount', '照片'),
              Container(
                width: 1,
                height: 20,
                margin: EdgeInsets.symmetric(horizontal: 16),
                color: ColorTokens.onPrimary.withValues(alpha: 0.3),
              ),
              _buildStatItem(Icons.folder, '3', '分类'),
              Container(
                width: 1,
                height: 20,
                margin: EdgeInsets.symmetric(horizontal: 16),
                color: ColorTokens.onPrimary.withValues(alpha: 0.3),
              ),
              _buildStatItem(
                  Icons.storage, _formatStorageSize(totalSize), '占用'),
            ],
          ),
        );
      },
    );
  }

  Widget _buildStatItem(IconData icon, String value, String label) {
    return Row(
      children: [
        Icon(icon,
            size: 16, color: ColorTokens.onPrimary.withValues(alpha: 0.8)),
        const SizedBox(width: 6),
        Flexible(
          // 添加 Flexible 包装
          child: Column(
            mainAxisSize: MainAxisSize.min, // 添加这行，确保 Column 只占用必要的空间
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                value,
                style: const TextStyle(
                  color: ColorTokens.onPrimary,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                overflow: TextOverflow.ellipsis, // 添加文本溢出处理
              ),
              Text(
                label,
                style: TextStyle(
                  color: ColorTokens.onPrimary.withValues(alpha: 0.8),
                  fontSize: 10,
                ),
                overflow: TextOverflow.ellipsis, // 添加文本溢出处理
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryChip(String category, String label, IconData icon) {
    final isSelected = _selectedCategory == category;

    return GestureDetector(
      onTap: () {
        if (_selectedCategory != category) {
          HapticFeedback.lightImpact();
          setState(() {
            _selectedCategory = category;
          });
          _loadAlbumPhotos();
        }
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected
              ? const LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.secondary
                  ],
                )
              : null,
          color: isSelected
              ? null
              : Theme.of(context).colorScheme.surfaceContainerHighest,
          borderRadius: BorderRadius.circular(20),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: Theme.of(context)
                        .colorScheme
                        .primary
                        .withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          children: [
            Icon(
              icon,
              size: 18,
              color: isSelected
                  ? ColorTokens.onPrimary
                  : ColorTokens.onSurfaceVariant,
            ),
            const SizedBox(width: 6),
            Text(
              label,
              style: TextStyle(
                color: isSelected
                    ? ColorTokens.onPrimary
                    : ColorTokens.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 14,
              ),
            ),
            if (category != 'all')
              Container(
                margin: EdgeInsets.only(left: 6),
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: isSelected
                      ? ColorTokens.onPrimary.withValues(alpha: 0.2)
                      : ColorTokens.outline,
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  _getCategoryCount(category, _viewModel),
                  style: TextStyle(
                    fontSize: 11,
                    color: isSelected
                        ? ColorTokens.onPrimary
                        : ColorTokens.onSurfaceVariant,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getCategoryCount(String category, AlbumViewModel viewModel) {
    // 这里应该从 viewModel 获取各分类的数量
    // 暂时返回模拟数据
    switch (category) {
      case 'fish':
        return '12';
      case 'spot':
        return '8';
      case 'moment':
        return '15';
      default:
        return '0';
    }
  }

  Widget _buildEnhancedPhotoItem(Photo photo, int index) {
    // 创建动画控制器
    if (_itemAnimationControllers.length <= index) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 600 + (index * 30)),
        vsync: this,
      );
      _itemAnimationControllers.add(controller);
      controller.forward();
    }

    return AnimatedBuilder(
      animation: _itemAnimationControllers[index],
      builder: (context, child) {
        final scaleAnimation = Tween<double>(
          begin: 0.8,
          end: 1.0,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.elasticOut,
        ));

        final fadeAnimation = Tween<double>(
          begin: 0,
          end: 1,
        ).animate(CurvedAnimation(
          parent: _itemAnimationControllers[index],
          curve: Curves.easeOut,
        ));

        return ScaleTransition(
          scale: scaleAnimation,
          child: FadeTransition(
            opacity: fadeAnimation,
            child: child,
          ),
        );
      },
      child: Consumer<AlbumViewModel>(
        builder: (context, viewModel, child) {
          final isSelected = viewModel.selectedPhotos.contains(photo.id);
          final isDeleting = _deletingItems[photo.id.toString()] ?? false;

          return GestureDetector(
            onTap: () {
              if (isDeleting) return; // 防止删除过程中的交互

              HapticFeedback.lightImpact();
              if (viewModel.isSelectionMode) {
                viewModel.togglePhotoSelection(photo.id);
              } else {
                _openPhotoViewer(index);
              }
            },
            onLongPress: () {
              if (isDeleting) return; // 防止删除过程中的交互

              HapticFeedback.mediumImpact();
              if (!viewModel.isSelectionMode) {
                viewModel.toggleSelectionMode();
                viewModel.togglePhotoSelection(photo.id);
              }
            },
            child: Hero(
              tag: 'photo_${photo.id}',
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: ColorTokens.scrim.withValues(alpha: 0.1),
                      blurRadius: 15,
                      offset: Offset(0, 5),
                    ),
                  ],
                ),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    // 图片
                    ClipRRect(
                      borderRadius: BorderRadius.circular(20),
                      child: CachedNetworkImage(
                        imageUrl: photo.originalUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                ColorTokens.outline!,
                                Theme.of(context)
                                    .colorScheme
                                    .surfaceContainerHigh!,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Theme.of(context).colorScheme.primary,
                              ),
                              strokeWidth: 2,
                            ),
                          ),
                        ),
                        errorWidget: (context, url, error) => Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context)
                                .colorScheme
                                .surfaceContainerHigh,
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Icon(
                            Icons.broken_image,
                            color: ColorTokens.outline,
                          ),
                        ),
                      ),
                    ),

                    // 删除中的遮罩
                    if (isDeleting)
                      Positioned.fill(
                        child: Container(
                          decoration: BoxDecoration(
                            color: ColorTokens.scrim.withValues(alpha: 0.54),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: const Center(
                            child: CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation<Color>(
                                  ColorTokens.onPrimary),
                              strokeWidth: 3,
                            ),
                          ),
                        ),
                      ),

                    // 渐变遮罩（仅在选择模式下）
                    if (viewModel.isSelectionMode)
                      ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                ColorTokens.transparent,
                                ColorTokens.scrim.withValues(alpha: 0.3),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // 选择指示器
                    if (viewModel.isSelectionMode)
                      Positioned(
                        top: 12,
                        right: 12,
                        child: Container(
                          width: 28,
                          height: 28,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : ColorTokens.surface.withValues(alpha: 0.9),
                            border: Border.all(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : ColorTokens.outline!,
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: ColorTokens.scrim.withValues(alpha: 0.2),
                                blurRadius: 8,
                              ),
                            ],
                          ),
                          child: isSelected
                              ? const Icon(
                                  Icons.check,
                                  size: 18,
                                  color: ColorTokens.onPrimary,
                                )
                              : null,
                        ),
                      ),

                    // 类型标签
                    if (photo.fileType != null)
                      Positioned(
                        bottom: 8,
                        left: 8,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 10,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [
                                Theme.of(context).colorScheme.primary,
                                Theme.of(context).colorScheme.secondary
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: ColorTokens.scrim.withValues(alpha: 0.3),
                                blurRadius: 8,
                              ),
                            ],
                          ),
                          child: Text(
                            _getPhotoTypeLabel(photo.fileType!),
                            style: const TextStyle(
                              fontSize: 11,
                              color: ColorTokens.onPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  String _getPhotoTypeLabel(String type) {
    switch (type) {
      case 'fish':
        return '渔获';
      case 'spot':
        return '钓点';
      case 'moment':
        return '动态';
      default:
        return '其他';
    }
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [
                  Theme.of(context).colorScheme.primary,
                  Theme.of(context).colorScheme.secondary
                ],
              ),
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: const Padding(
              padding: SpacingTokens.paddingLg,
              child: CircularProgressIndicator(
                valueColor:
                    AlwaysStoppedAnimation<Color>(ColorTokens.onPrimary),
                strokeWidth: 3,
              ),
            ),
          ),
          const SizedBox(height: SpacingTokens.space6),
          const Text(
            '加载中...',
            style: TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingMoreIndicator() {
    return Container(
      decoration: BoxDecoration(
        color: ColorTokens.onPrimary,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: ColorTokens.scrim.withValues(alpha: 0.05),
            blurRadius: 10,
          ),
        ],
      ),
      child: Center(
        child: Padding(
          padding: SpacingTokens.paddingLg,
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary),
            strokeWidth: 2,
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ScaleTransition(
            scale: _scaleAnimation,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context)
                        .colorScheme
                        .primary
                        .withValues(alpha: 0.1),
                    Theme.of(context)
                        .colorScheme
                        .secondary
                        .withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.photo_library_outlined,
                size: 60,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          const SizedBox(height: SpacingTokens.space6),
          const Text(
            '相册空空如也',
            style: TextStyle(
              fontSize: 20,
              color: ColorTokens.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            '快去记录美好的钓鱼时光吧',
            style: TextStyle(
              fontSize: 14,
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _showPhotoUploadOptions();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 5,
              shadowColor:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Icon(Icons.add_a_photo, color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                Text(
                  '上传照片',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColorTokens.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleMenuAction(String action) {
    HapticFeedback.lightImpact();
    switch (action) {
      case 'upload':
        _showPhotoUploadOptions();
        break;
      case 'search':
        setState(() {
          _isSearching = !_isSearching;
          if (!_isSearching) {
            _searchController.clear();
            _searchQuery = '';
            _loadAlbumPhotos(); // 清除搜索结果
          }
        });
        break;
      case 'select':
        _viewModel.toggleSelectionMode();
        break;
      case 'grid':
        setState(() {
          _gridCrossAxisCount = _gridCrossAxisCount == 3 ? 2 : 3;
        });
        _showGridChangeSnackBar();
        break;
    }
  }

  void _showGridChangeSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('已切换为 $_gridCrossAxisCount 列布局'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showPhotoUploadOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: ColorTokens.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: ColorTokens.onPrimary,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 40,
                height: 4,
                margin: EdgeInsets.symmetric(vertical: 12),
                decoration: BoxDecoration(
                  color: ColorTokens.outline,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const Padding(
                padding: SpacingTokens.paddingMd,
                child: Text(
                  '选择照片来源',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ListTile(
                leading: Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .primary
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.camera_alt,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                title: const Text('拍照'),
                subtitle: const Text('使用相机拍摄新照片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromCamera();
                },
              ),
              ListTile(
                leading: Container(
                  padding: EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: Theme.of(context)
                        .colorScheme
                        .secondary
                        .withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.photo_library,
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
                title: const Text('从相册选择'),
                subtitle: const Text('选择已有的照片'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImageFromGallery();
                },
              ),
              const SizedBox(height: SpacingTokens.space4),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _pickImageFromCamera() async {
    try {
      // TODO: 实现相机拍照功能
      // final ImagePicker picker = ImagePicker();
      // final XFile? photo = await picker.pickImage(source: ImageSource.camera);
      // if (photo != null) {
      //   await _uploadPhoto(photo);
      // }

      _showFeatureComingSoon('拍照功能');
    } catch (e) {
      debugPrint('Camera error: $e');
      _showErrorSnackBar('打开相机失败');
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      // TODO: 实现相册选择功能
      // final ImagePicker picker = ImagePicker();
      // final List<XFile> images = await picker.pickMultiImage();
      // if (images.isNotEmpty) {
      //   for (final image in images) {
      //     await _uploadPhoto(image);
      //   }
      // }

      _showFeatureComingSoon('相册选择功能');
    } catch (e) {
      debugPrint('Gallery error: $e');
      _showErrorSnackBar('打开相册失败');
    }
  }

  Future<void> _uploadPhoto(dynamic imageFile) async {
    // TODO: 实现照片上传功能
    try {
      // 显示上传进度
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: const [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary),
              ),
              const SizedBox(height: SpacingTokens.space4),
              const Text('正在上传照片...'),
            ],
          ),
        ),
      );

      // 模拟上传
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        Navigator.pop(context); // 关闭进度对话框

        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: ColorTokens.onPrimary, size: 20),
                SizedBox(width: 8),
                Text('照片上传成功'),
              ],
            ),
            backgroundColor: ColorTokens.success,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );

        // 刷新相册
        _loadAlbumPhotos();
      }
    } catch (e) {
      debugPrint('Upload error: $e');
      if (mounted) {
        Navigator.pop(context); // 关闭进度对话框
        _showErrorSnackBar('上传失败，请重试');
      }
    }
  }

  void _showFeatureComingSoon(String feature) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline,
                color: ColorTokens.onPrimary, size: 20),
            const SizedBox(width: 8),
            Text('$feature即将上线'),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _performSearch() {
    if (_searchQuery.isEmpty) {
      _loadAlbumPhotos();
    } else {
      // TODO: 实现搜索功能
      // 这里应该调用 viewModel 的搜索方法
      // await _viewModel.searchPhotos(_searchQuery, category: _selectedCategory);

      // 暂时显示提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('搜索 "$_searchQuery" 的结果'),
          backgroundColor: Theme.of(context).colorScheme.primary,
          duration: const Duration(seconds: 1),
          behavior: SnackBarBehavior.floating,
        ),
      );

      // 模拟搜索效果 - 实际应该根据搜索结果过滤
      setState(() {
        // 这里可以临时过滤显示的照片
      });
    }
  }

  void _showDeleteConfirmation() {
    final selectedCount = _viewModel.selectedPhotos.length;
    final selectedPhotos = List<String>.from(_viewModel.selectedPhotos);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '删除照片',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text('确定要删除选中的 $selectedCount 张照片吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteSelectedPhotos(selectedPhotos, selectedCount);
            },
            style: TextButton.styleFrom(
              foregroundColor: ColorTokens.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSelectedPhotos(List<String> photoIds, int count) async {
    // 设置删除状态
    setState(() {
      for (String photoId in photoIds) {
        _deletingItems[photoId] = true;
      }
    });

    try {
      await _viewModel.deleteSelectedPhotos();

      if (mounted) {
        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.check_circle,
                    color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                Text('已删除 $count 张照片'),
              ],
            ),
            backgroundColor: ColorTokens.success,
            duration: const Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (error) {
      debugPrint('Delete photos error: $error');
      if (mounted) {
        HapticFeedback.heavyImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline,
                    color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                const Expanded(child: Text('删除失败，请检查网络连接')),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _deleteSelectedPhotos(photoIds, count); // 重试
                  },
                  child: const Text('重试',
                      style: TextStyle(color: ColorTokens.onPrimary)),
                ),
              ],
            ),
            backgroundColor: ColorTokens.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          for (String photoId in photoIds) {
            _deletingItems.remove(photoId);
          }
        });
      }
    }
  }

  void _openPhotoViewer(int initialIndex) {
    Navigator.of(context).push(
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) =>
            EnhancedPhotoViewer(
          photos: _viewModel.photos,
          initialIndex: initialIndex,
          onPhotoDeleted: (photo) {
            // 照片删除后的回调
            _viewModel.photos.removeWhere((p) => p.id == photo.id);
            setState(() {});
          },
        ),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 300),
      ),
    );
  }

  // 错误状态页面
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  ColorTokens.error.withValues(alpha: 0.1),
                  ColorTokens.warning.withValues(alpha: 0.1),
                ],
              ),
              shape: BoxShape.circle,
            ),
            child: const Icon(
              Icons.error_outline,
              size: 60,
              color: ColorTokens.error,
            ),
          ),
          const SizedBox(height: SpacingTokens.space6),
          const Text(
            '加载失败',
            style: TextStyle(
              fontSize: 20,
              color: ColorTokens.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: SpacingTokens.space2),
          Text(
            _errorMessage?.contains('网络') == true
                ? '网络连接异常，请检查网络设置'
                : '加载相册时出现问题，请稍后重试',
            style: TextStyle(
              fontSize: 14,
              color: ColorTokens.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 32),
          ElevatedButton(
            onPressed: () {
              HapticFeedback.lightImpact();
              _loadAlbumPhotos();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: EdgeInsets.symmetric(horizontal: 32, vertical: 14),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
              elevation: 5,
              shadowColor:
                  Theme.of(context).colorScheme.primary.withValues(alpha: 0.5),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: const [
                Icon(Icons.refresh, color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                Text(
                  '重新加载',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: ColorTokens.onPrimary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 显示错误提示
  void _showErrorSnackBar(String message, {VoidCallback? retry}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline,
                color: ColorTokens.onPrimary, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
            if (retry != null)
              TextButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  retry();
                },
                child: const Text('重试',
                    style: TextStyle(color: ColorTokens.onPrimary)),
              ),
          ],
        ),
        backgroundColor: ColorTokens.error,
        duration: Duration(seconds: retry != null ? 4 : 3),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _batchDownloadPhotos() {
    final selectedCount = _viewModel.selectedPhotos.length;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '批量下载',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text('确定要下载选中的 $selectedCount 张照片吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performBatchDownload();
            },
            child: const Text('下载'),
          ),
        ],
      ),
    );
  }

  void _performBatchDownload() {
    _showFeatureComingSoon('批量下载功能');
    // TODO: 实现批量下载
    // 1. 获取选中的照片
    // 2. 逐个下载并保存到相册
    // 3. 显示下载进度
    // 4. 完成后清除选择状态
    _viewModel.toggleSelectionMode();
  }

  void _batchSharePhotos() {
    final selectedCount = _viewModel.selectedPhotos.length;

    if (selectedCount > 9) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('最多只能分享9张照片'),
          backgroundColor: ColorTokens.warning,
          duration: Duration(seconds: 2),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    _showFeatureComingSoon('批量分享功能');
    // TODO: 实现批量分享
    // 1. 获取选中的照片
    // 2. 准备分享内容
    // 3. 调用系统分享
    _viewModel.toggleSelectionMode();
  }

  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: ColorTokens.transparent,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              height: MediaQuery.of(context).size.height * 0.7,
              decoration: const BoxDecoration(
                color: ColorTokens.onPrimary,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    margin: EdgeInsets.symmetric(vertical: 12),
                    decoration: BoxDecoration(
                      color: ColorTokens.outline,
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const Padding(
                    padding: SpacingTokens.paddingMd,
                    child: Text(
                      '高级筛选',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // 日期范围
                          const Text(
                            '日期范围',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: _buildDateSelector('开始日期', true),
                              ),
                              const Padding(
                                padding: EdgeInsets.symmetric(horizontal: 8),
                                child: Text('至'),
                              ),
                              Expanded(
                                child: _buildDateSelector('结束日期', false),
                              ),
                            ],
                          ),
                          const SizedBox(height: SpacingTokens.space6),

                          // 照片大小
                          const Text(
                            '照片大小',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildFilterChip('小于 1MB', false),
                              _buildFilterChip('1MB - 5MB', false),
                              _buildFilterChip('5MB - 10MB', false),
                              _buildFilterChip('大于 10MB', false),
                            ],
                          ),
                          const SizedBox(height: SpacingTokens.space6),

                          // 排序方式
                          const Text(
                            '排序方式',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildFilterChip('时间最新', true),
                              _buildFilterChip('时间最早', false),
                              _buildFilterChip('大小最大', false),
                              _buildFilterChip('大小最小', false),
                            ],
                          ),
                          const SizedBox(height: SpacingTokens.space6),

                          // 其他筛选
                          const Text(
                            '其他条件',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildFilterChip('有描述', false),
                              _buildFilterChip('无描述', false),
                              _buildFilterChip('已分享', false),
                              _buildFilterChip('未分享', false),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  // 底部按钮
                  Container(
                    padding: SpacingTokens.paddingLg,
                    decoration: BoxDecoration(
                      color: ColorTokens.onPrimary,
                      boxShadow: [
                        BoxShadow(
                          color: ColorTokens.scrim.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () {
                              // 重置筛选
                              Navigator.pop(context);
                              _showFeatureComingSoon('重置筛选功能');
                            },
                            child: const Text(
                              '重置',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          flex: 2,
                          child: ElevatedButton(
                            onPressed: () {
                              Navigator.pop(context);
                              _showFeatureComingSoon('高级筛选功能');
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor:
                                  Theme.of(context).colorScheme.primary,
                              padding: EdgeInsets.symmetric(vertical: 14),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(25),
                              ),
                            ),
                            child: const Text(
                              '应用筛选',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildDateSelector(String hint, bool isStart) {
    return InkWell(
      onTap: () async {
        final date = await showDatePicker(
          context: context,
          initialDate: DateTime.now(),
          firstDate: DateTime(2020),
          lastDate: DateTime.now(),
          builder: (context, child) {
            return Theme(
              data: Theme.of(context).copyWith(
                colorScheme: const ColorScheme.light(
                  primary: Theme.of(context).colorScheme.primary,
                ),
              ),
              child: child!,
            );
          },
        );
        if (date != null) {
          // TODO: 处理日期选择
          _showFeatureComingSoon('日期筛选功能');
        }
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 14),
        decoration: BoxDecoration(
          border: Border.all(color: ColorTokens.outline!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(Icons.calendar_today,
                size: 18, color: ColorTokens.onSurfaceVariant),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                hint,
                style: TextStyle(
                  color: ColorTokens.onSurfaceVariant,
                  fontSize: 14,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatStorageSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / 1024 / 1024).toStringAsFixed(1)} MB';
    }
    return '${(bytes / 1024 / 1024 / 1024).toStringAsFixed(1)} GB';
  }

  Widget _buildFilterChip(String label, bool isSelected) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        // TODO: 处理筛选选择
      },
      selectedColor:
          Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
      checkmarkColor: Theme.of(context).colorScheme.primary,
      labelStyle: TextStyle(
        color: isSelected
            ? Theme.of(context).colorScheme.primary
            : ColorTokens.onSurfaceVariant,
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected
            ? Theme.of(context).colorScheme.primary
            : ColorTokens.outline!,
      ),
    );
  }
}

// 照片查看器页面
class PhotoViewerPage extends StatefulWidget {
  final List<Photo> photos;
  final int initialIndex;

  const PhotoViewerPage({
    super.key,
    required this.photos,
    required this.initialIndex,
  });

  @override
  State<PhotoViewerPage> createState() => _PhotoViewerPageState();
}

class _PhotoViewerPageState extends State<PhotoViewerPage> {
  late PageController _pageController;
  late int _currentIndex;
  bool _showOverlay = true;

  // 添加交互状态管理
  final Map<String, bool> _savingStates = {}; // 正在保存的图片
  final Map<String, bool> _sharingStates = {}; // 正在分享的图片
  final Map<String, bool> _deletingItems = {}; // 正在删除的图片

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.initialIndex;
    _pageController = PageController(initialPage: widget.initialIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorTokens.scrim,
      body: GestureDetector(
        onTap: () {
          setState(() {
            _showOverlay = !_showOverlay;
          });
        },
        child: Stack(
          children: [
            // 图片浏览器
            PhotoViewGallery.builder(
              pageController: _pageController,
              itemCount: widget.photos.length,
              onPageChanged: (index) {
                setState(() {
                  _currentIndex = index;
                });
              },
              builder: (context, index) {
                final photo = widget.photos[index];
                return PhotoViewGalleryPageOptions(
                  imageProvider: CachedNetworkImageProvider(photo.originalUrl),
                  heroAttributes:
                      PhotoViewHeroAttributes(tag: 'photo_${photo.id}'),
                  minScale: PhotoViewComputedScale.contained,
                  maxScale: PhotoViewComputedScale.covered * 3,
                );
              },
              scrollPhysics: const BouncingScrollPhysics(),
              backgroundDecoration:
                  const BoxDecoration(color: ColorTokens.scrim),
              loadingBuilder: (context, event) => Center(
                child: CircularProgressIndicator(
                  value: event == null
                      ? null
                      : event.cumulativeBytesLoaded /
                          (event.expectedTotalBytes ?? 1),
                  valueColor: const AlwaysStoppedAnimation<Color>(
                      ColorTokens.onPrimary),
                ),
              ),
            ),

            // 顶部栏
            AnimatedOpacity(
              opacity: _showOverlay ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      ColorTokens.scrim.withValues(alpha: 0.7),
                      ColorTokens.transparent,
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: SpacingTokens.paddingMd,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IconButton(
                          icon: Container(
                            padding: SpacingTokens.paddingSm,
                            decoration: BoxDecoration(
                              color:
                                  ColorTokens.onPrimary.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(Icons.close,
                                color: ColorTokens.onPrimary),
                          ),
                          onPressed: () {
                            HapticFeedback.lightImpact();
                            Navigator.pop(context);
                          },
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: ColorTokens.onPrimary.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '${_currentIndex + 1} / ${widget.photos.length}',
                            style: const TextStyle(
                              color: ColorTokens.onPrimary,
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        PopupMenuButton<String>(
                          icon: Container(
                            padding: SpacingTokens.paddingSm,
                            decoration: BoxDecoration(
                              color:
                                  ColorTokens.onPrimary.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: const Icon(Icons.more_vert,
                                color: ColorTokens.onPrimary),
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(15),
                          ),
                          onSelected: (value) => _handlePhotoAction(value),
                          itemBuilder: (context) => [
                            const PopupMenuItem(
                              value: 'save',
                              child: Row(
                                children: [
                                  Icon(Icons.download),
                                  const SizedBox(width: 8),
                                  Text('保存到相册'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'share',
                              child: Row(
                                children: [
                                  Icon(Icons.share),
                                  const SizedBox(width: 8),
                                  Text('分享'),
                                ],
                              ),
                            ),
                            const PopupMenuItem(
                              value: 'delete',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, color: ColorTokens.error),
                                  const SizedBox(width: 8),
                                  Text('删除',
                                      style:
                                          TextStyle(color: ColorTokens.error)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),

            // 底部信息栏
            AnimatedOpacity(
              opacity: _showOverlay ? 1.0 : 0.0,
              duration: const Duration(milliseconds: 300),
              child: Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.bottomCenter,
                      end: Alignment.topCenter,
                      colors: [
                        ColorTokens.scrim.withValues(alpha: 0.7),
                        ColorTokens.transparent,
                      ],
                    ),
                  ),
                  child: SafeArea(
                    child: Padding(
                      padding: SpacingTokens.paddingLg,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (widget.photos[_currentIndex].description != null)
                            Text(
                              widget.photos[_currentIndex].description!,
                              style: const TextStyle(
                                color: ColorTokens.onPrimary,
                                fontSize: 14,
                              ),
                            ),
                          const SizedBox(height: SpacingTokens.space2),
                          Row(
                            children: [
                              Text(
                                _formatDate(widget
                                    .photos[_currentIndex].createdAt
                                    .toIso8601String()),
                                style: TextStyle(
                                  color: ColorTokens.onPrimary
                                      .withValues(alpha: 0.7),
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handlePhotoAction(String action) {
    final currentPhoto = widget.photos[_currentIndex];

    switch (action) {
      case 'save':
        _savePhotoToGallery(currentPhoto.id.toString());
        break;
      case 'share':
        _sharePhoto(currentPhoto);
        break;
      case 'delete':
        _showPhotoDeleteConfirmation(currentPhoto);
        break;
    }
  }

  Future<void> _savePhotoToGallery(String photoId) async {
    if (_savingStates[photoId] == true) return;

    setState(() {
      _savingStates[photoId] = true;
    });

    try {
      // TODO: 实现保存到相册功能
      await Future.delayed(const Duration(seconds: 1)); // 模拟保存过程

      if (mounted) {
        HapticFeedback.lightImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                Text('已保存到相册'),
              ],
            ),
            backgroundColor: ColorTokens.success,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (error) {
      debugPrint('Save photo error: $error');
      if (mounted) {
        HapticFeedback.heavyImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline,
                    color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                const Expanded(child: Text('保存失败，请检查权限设置')),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _savePhotoToGallery(photoId); // 重试
                  },
                  child: const Text('重试',
                      style: TextStyle(color: ColorTokens.onPrimary)),
                ),
              ],
            ),
            backgroundColor: ColorTokens.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _savingStates.remove(photoId);
        });
      }
    }
  }

  Future<void> _sharePhoto(Photo photo) async {
    if (_sharingStates[photo.id.toString()] == true) return;

    setState(() {
      _sharingStates[photo.id.toString()] = true;
    });

    try {
      HapticFeedback.lightImpact();

      // TODO: 实现分享功能
      // 可以使用 share_plus 包分享图片URL或下载后分享
      // TODO: 实现分享功能
      // final shareText = '分享我的钓鱼照片\\n\\n来自钓鱼社区 🎣';
      // await Share.share(shareText);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('分享功能开发中...'),
            backgroundColor: Theme.of(context).colorScheme.primary,
            duration: Duration(seconds: 1),
          ),
        );
      }
    } catch (error) {
      debugPrint('Share photo error: $error');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('分享失败'),
            backgroundColor: ColorTokens.warning,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _sharingStates.remove(photo.id.toString());
        });
      }
    }
  }

  void _showPhotoDeleteConfirmation(Photo photo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        title: const Text(
          '删除照片',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        content: const Text('确定要删除这张照片吗？此操作不可恢复。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _deleteSinglePhoto(photo);
            },
            style: TextButton.styleFrom(
              foregroundColor: ColorTokens.error,
            ),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteSinglePhoto(Photo photo) async {
    setState(() {
      _deletingItems[photo.id.toString()] = true;
    });

    try {
      // TODO: 实现单张照片删除
      await Future.delayed(const Duration(seconds: 1)); // 模拟删除过程

      if (mounted) {
        HapticFeedback.lightImpact();
        Navigator.pop(context); // 关闭照片查看器
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle,
                    color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                Text('照片已删除'),
              ],
            ),
            backgroundColor: ColorTokens.success,
            duration: Duration(seconds: 2),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } catch (error) {
      debugPrint('Delete single photo error: $error');
      if (mounted) {
        HapticFeedback.heavyImpact();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline,
                    color: ColorTokens.onPrimary, size: 20),
                const SizedBox(width: 8),
                const Expanded(child: Text('删除失败，请检查网络连接')),
                TextButton(
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                    _deleteSinglePhoto(photo); // 重试
                  },
                  child: const Text('重试',
                      style: TextStyle(color: ColorTokens.onPrimary)),
                ),
              ],
            ),
            backgroundColor: ColorTokens.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _deletingItems.remove(photo.id.toString());
        });
      }
    }
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return '${date.year}年${date.month}月${date.day}日';
    } catch (e) {
      return dateStr;
    }
  }
}
