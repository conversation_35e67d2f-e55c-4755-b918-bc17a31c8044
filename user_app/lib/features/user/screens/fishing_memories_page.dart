import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/services/photo/photo_data_service.dart';
import 'package:user_app/models/photo/photo_data_index.dart';
import 'package:user_app/shared/widgets/design_system/ds_app_bar.dart';
import 'package:user_app/shared/widgets/design_system/ds_card.dart';
import 'package:user_app/shared/widgets/design_system/ds_button.dart';
import 'package:user_app/shared/widgets/design_system/ds_chip.dart';
import 'package:user_app/shared/widgets/design_system/ds_text_field.dart';

/// 钓鱼回忆页面 - 通过照片作为数据关联中心展示钓鱼经历
class FishingMemoriesPage extends StatefulWidget {
  const FishingMemoriesPage({super.key});

  @override
  State<FishingMemoriesPage> createState() => _FishingMemoriesPageState();
}

class _FishingMemoriesPageState extends State<FishingMemoriesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  
  List<PhotoDataIndex> _photos = [];
  List<String> _popularTags = [];
  PhotoStatistics? _statistics;
  bool _isLoading = true;
  String _searchQuery = '';
  List<String> _selectedTags = [];
  PhotoSource? _selectedSource;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);
    
    try {
      final photoService = PhotoDataService();
      
      // 并行加载数据
      final results = await Future.wait([
        photoService.searchPhotos(),
        Future.value(photoService.getPopularTags()),
        Future.value(photoService.getStatistics()),
      ]);
      
      setState(() {
        _photos = results[0] as List<PhotoDataIndex>;
        _popularTags = results[1] as List<String>;
        _statistics = results[2] as PhotoStatistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('加载数据失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _performSearch() async {
    setState(() => _isLoading = true);
    
    try {
      final photoService = PhotoDataService();
      final results = await photoService.searchPhotos(
        query: _searchQuery.isEmpty ? null : _searchQuery,
        tags: _selectedTags.isEmpty ? null : _selectedTags,
        source: _selectedSource,
      );
      
      setState(() {
        _photos = results;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('搜索失败: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _selectedTags.clear();
      _selectedSource = null;
      _searchController.clear();
    });
    _loadData();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      appBar: DSAppBar(
        title: '钓鱼回忆',
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              Icons.search,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () => _showSearchDialog(),
          ),
          IconButton(
            icon: Icon(
              Icons.analytics_outlined,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            onPressed: () => _showStatistics(),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Theme.of(context).colorScheme.primary,
          labelColor: Theme.of(context).colorScheme.primary,
          unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
          tabs: const [
            Tab(text: '全部照片'),
            Tab(text: '热门标签'),
            Tab(text: '时光轴'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildPhotosView(),
          _buildTagsView(),
          _buildTimelineView(),
        ],
      ),
    );
  }

  Widget _buildPhotosView() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_photos.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        if (_hasActiveFilters()) _buildActiveFilters(),
        Expanded(
          child: GridView.builder(
            padding: Spacing.screen,
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: SpacingTokens.space3,
              mainAxisSpacing: SpacingTokens.space3,
              childAspectRatio: 1.0,
            ),
            itemCount: _photos.length,
            itemBuilder: (context, index) {
              return _buildPhotoCard(_photos[index]);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildPhotoCard(PhotoDataIndex photo) {
    return DSCard(
      elevation: ElevationTokens.level1,
      child: InkWell(
        borderRadius: ShapeTokens.cardShape,
        onTap: () => _openPhotoDetail(photo),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 照片预览
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(SpacingTokens.space3),
                  ),
                  color: Theme.of(context).colorScheme.surfaceVariant,
                ),
                child: ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(SpacingTokens.space3),
                  ),
                  child: Image.network(
                    photo.thumbnailUrl ?? photo.photoUrl,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Icon(
                        Icons.image_not_supported,
                        size: 48,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      );
                    },
                  ),
                ),
              ),
            ),
            // 信息区域
            Expanded(
              flex: 2,
              child: Padding(
                padding: Spacing.xs,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 数据关联指示器
                    Row(
                      children: [
                        _buildDataIndicator(
                          Icons.location_on,
                          photo.relatedSpotIds.length,
                          Theme.of(context).colorScheme.primary,
                        ),
                        SizedBox(width: SpacingTokens.space1),
                        _buildDataIndicator(
                          Icons.dynamic_feed,
                          photo.relatedMomentIds.length,
                          ColorTokens.success,
                        ),
                        SizedBox(width: SpacingTokens.space1),
                        _buildDataIndicator(
                          Icons.event,
                          photo.relatedActivityIds.length,
                          ColorTokens.warning,
                        ),
                      ],
                    ),
                    SizedBox(height: SpacingTokens.space2),
                    // 来源标签
                    DSChip(
                      label: photo.source.displayName,
                      size: DSChipSize.small,
                      backgroundColor: Theme.of(context).colorScheme.primaryContainer,
                      textColor: Theme.of(context).colorScheme.onPrimaryContainer,
                    ),
                    const Spacer(),
                    // 时间
                    Text(
                      _formatDate(photo.takenAt),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataIndicator(IconData icon, int count, Color color) {
    if (count == 0) return const SizedBox.shrink();
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 12, color: color),
        SizedBox(width: SpacingTokens.space1 / 2),
        Text(
          count.toString(),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildTagsView() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return SingleChildScrollView(
      padding: Spacing.screen,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '热门标签',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: SpacingTokens.space4),
          Wrap(
            spacing: SpacingTokens.space2,
            runSpacing: SpacingTokens.space2,
            children: _popularTags.map((tag) {
              final isSelected = _selectedTags.contains(tag);
              return DSChip(
                label: tag,
                isSelected: isSelected,
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      _selectedTags.remove(tag);
                    } else {
                      _selectedTags.add(tag);
                    }
                  });
                  _performSearch();
                },
              );
            }).toList(),
          ),
          if (_selectedTags.isNotEmpty) ...[
            SizedBox(height: SpacingTokens.space6),
            Row(
              children: [
                Text(
                  '已选择的标签',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
                const Spacer(),
                DSButton.text(
                  label: '清除全部',
                  onPressed: _clearFilters,
                ),
              ],
            ),
            SizedBox(height: SpacingTokens.space3),
            Wrap(
              spacing: SpacingTokens.space2,
              runSpacing: SpacingTokens.space2,
              children: _selectedTags.map((tag) {
                return DSChip(
                  label: tag,
                  isSelected: true,
                  onTap: () {
                    setState(() => _selectedTags.remove(tag));
                    _performSearch();
                  },
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildTimelineView() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // 按日期分组照片
    final groupedPhotos = <String, List<PhotoDataIndex>>{};
    for (final photo in _photos) {
      final dateKey = _formatDateKey(photo.takenAt);
      groupedPhotos[dateKey] = (groupedPhotos[dateKey] ?? [])..add(photo);
    }

    final sortedDates = groupedPhotos.keys.toList()
      ..sort((a, b) => b.compareTo(a)); // 按日期倒序

    return ListView.builder(
      padding: Spacing.screen,
      itemCount: sortedDates.length,
      itemBuilder: (context, index) {
        final dateKey = sortedDates[index];
        final photos = groupedPhotos[dateKey]!;
        
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(vertical: SpacingTokens.space3),
              child: Text(
                _formatDateHeader(dateKey),
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: SpacingTokens.space2,
                mainAxisSpacing: SpacingTokens.space2,
                childAspectRatio: 1.0,
              ),
              itemCount: photos.length,
              itemBuilder: (context, photoIndex) {
                return _buildTimelinePhotoCard(photos[photoIndex]);
              },
            ),
            SizedBox(height: SpacingTokens.space4),
          ],
        );
      },
    );
  }

  Widget _buildTimelinePhotoCard(PhotoDataIndex photo) {
    return DSCard(
      elevation: ElevationTokens.level1,
      child: InkWell(
        borderRadius: ShapeTokens.cardShape,
        onTap: () => _openPhotoDetail(photo),
        child: Stack(
          fit: StackFit.expand,
          children: [
            ClipRRect(
              borderRadius: ShapeTokens.cardShape,
              child: Image.network(
                photo.thumbnailUrl ?? photo.photoUrl,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    child: Icon(
                      Icons.image_not_supported,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  );
                },
              ),
            ),
            if (photo.totalRelatedItems > 0)
              Positioned(
                top: SpacingTokens.space1,
                right: SpacingTokens.space1,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: SpacingTokens.space1,
                    vertical: SpacingTokens.space1 / 2,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary,
                    borderRadius: BorderRadius.circular(SpacingTokens.space2),
                  ),
                  child: Text(
                    photo.totalRelatedItems.toString(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: SpacingTokens.space4),
          Text(
            '还没有钓鱼照片',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: SpacingTokens.space2),
          Text(
            '开始记录你的钓鱼经历吧',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: SpacingTokens.space6),
          DSButton(
            label: '去发布动态',
            onPressed: () {
              // TODO: 导航到发布动态页面
            },
          ),
        ],
      ),
    );
  }

  Widget _buildActiveFilters() {
    return Container(
      padding: Spacing.md,
      color: Theme.of(context).colorScheme.surfaceVariant,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '当前筛选',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              const Spacer(),
              DSButton.text(
                label: '清除',
                onPressed: _clearFilters,
              ),
            ],
          ),
          SizedBox(height: SpacingTokens.space2),
          Wrap(
            spacing: SpacingTokens.space2,
            runSpacing: SpacingTokens.space1,
            children: [
              if (_searchQuery.isNotEmpty)
                DSChip(
                  label: '搜索: $_searchQuery',
                  isSelected: true,
                  onTap: () {
                    setState(() => _searchQuery = '');
                    _searchController.clear();
                    _performSearch();
                  },
                ),
              if (_selectedSource != null)
                DSChip(
                  label: '来源: ${_selectedSource!.displayName}',
                  isSelected: true,
                  onTap: () {
                    setState(() => _selectedSource = null);
                    _performSearch();
                  },
                ),
              ..._selectedTags.map((tag) => DSChip(
                    label: tag,
                    isSelected: true,
                    onTap: () {
                      setState(() => _selectedTags.remove(tag));
                      _performSearch();
                    },
                  )),
            ],
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return _searchQuery.isNotEmpty || 
           _selectedTags.isNotEmpty || 
           _selectedSource != null;
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => _SearchDialog(
        initialQuery: _searchQuery,
        selectedTags: _selectedTags,
        selectedSource: _selectedSource,
        availableTags: _popularTags,
        onSearch: (query, tags, source) {
          setState(() {
            _searchQuery = query;
            _selectedTags = tags;
            _selectedSource = source;
          });
          _performSearch();
        },
      ),
    );
  }

  void _showStatistics() {
    if (_statistics == null) return;
    
    showModalBottomSheet(
      context: context,
      builder: (context) => _StatisticsSheet(statistics: _statistics!),
    );
  }

  void _openPhotoDetail(PhotoDataIndex photo) {
    // TODO: 导航到照片详情页面
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final photoDate = DateTime(date.year, date.month, date.day);
    
    if (photoDate == today) {
      return '今天 ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else if (photoDate == today.subtract(const Duration(days: 1))) {
      return '昨天 ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } else {
      return '${date.month}/${date.day}';
    }
  }

  String _formatDateKey(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  String _formatDateHeader(String dateKey) {
    final parts = dateKey.split('-');
    final date = DateTime(int.parse(parts[0]), int.parse(parts[1]), int.parse(parts[2]));
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    
    if (date == today) {
      return '今天';
    } else if (date == today.subtract(const Duration(days: 1))) {
      return '昨天';
    } else {
      return '${date.month}月${date.day}日';
    }
  }
}

/// 搜索对话框
class _SearchDialog extends StatefulWidget {
  final String initialQuery;
  final List<String> selectedTags;
  final PhotoSource? selectedSource;
  final List<String> availableTags;
  final Function(String, List<String>, PhotoSource?) onSearch;

  const _SearchDialog({
    required this.initialQuery,
    required this.selectedTags,
    required this.selectedSource,
    required this.availableTags,
    required this.onSearch,
  });

  @override
  State<_SearchDialog> createState() => _SearchDialogState();
}

class _SearchDialogState extends State<_SearchDialog> {
  late TextEditingController _searchController;
  late List<String> _selectedTags;
  PhotoSource? _selectedSource;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController(text: widget.initialQuery);
    _selectedTags = List.from(widget.selectedTags);
    _selectedSource = widget.selectedSource;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('搜索钓鱼照片'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            DSTextField(
              controller: _searchController,
              labelText: '搜索关键词',
              hintText: '输入标签、地点等关键词',
              prefixIcon: Icons.search,
            ),
            SizedBox(height: SpacingTokens.space4),
            Text(
              '照片来源',
              style: Theme.of(context).textTheme.titleSmall,
            ),
            SizedBox(height: SpacingTokens.space2),
            Wrap(
              spacing: SpacingTokens.space2,
              children: PhotoSource.values.map((source) {
                return DSChip(
                  label: source.displayName,
                  isSelected: _selectedSource == source,
                  onTap: () {
                    setState(() {
                      _selectedSource = _selectedSource == source ? null : source;
                    });
                  },
                );
              }).toList(),
            ),
            if (widget.availableTags.isNotEmpty) ...[
              SizedBox(height: SpacingTokens.space4),
              Text(
                '热门标签',
                style: Theme.of(context).textTheme.titleSmall,
              ),
              SizedBox(height: SpacingTokens.space2),
              Wrap(
                spacing: SpacingTokens.space2,
                runSpacing: SpacingTokens.space1,
                children: widget.availableTags.take(10).map((tag) {
                  return DSChip(
                    label: tag,
                    isSelected: _selectedTags.contains(tag),
                    onTap: () {
                      setState(() {
                        if (_selectedTags.contains(tag)) {
                          _selectedTags.remove(tag);
                        } else {
                          _selectedTags.add(tag);
                        }
                      });
                    },
                  );
                }).toList(),
              ),
            ],
          ],
        ),
      ),
      actions: [
        DSButton.text(
          label: '重置',
          onPressed: () {
            setState(() {
              _searchController.clear();
              _selectedTags.clear();
              _selectedSource = null;
            });
          },
        ),
        DSButton.text(
          label: '取消',
          onPressed: () => Navigator.of(context).pop(),
        ),
        DSButton(
          label: '搜索',
          onPressed: () {
            widget.onSearch(
              _searchController.text,
              _selectedTags,
              _selectedSource,
            );
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }
}

/// 统计信息底部表单
class _StatisticsSheet extends StatelessWidget {
  final PhotoStatistics statistics;

  const _StatisticsSheet({required this.statistics});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: Spacing.lg,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '钓鱼回忆统计',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          SizedBox(height: SpacingTokens.space4),
          _buildStatItem(
            context,
            '总照片数',
            statistics.totalPhotos.toString(),
            Icons.photo_library,
          ),
          _buildStatItem(
            context,
            '总标签数',
            statistics.totalTags.toString(),
            Icons.tag,
          ),
          SizedBox(height: SpacingTokens.space4),
          Text(
            '照片来源分布',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          SizedBox(height: SpacingTokens.space2),
          ...statistics.sourceDistribution.entries.map((entry) {
            return _buildStatItem(
              context,
              entry.key.displayName,
              entry.value.toString(),
              _getSourceIcon(entry.key),
            );
          }),
          SizedBox(height: SpacingTokens.space6),
          DSButton(
            label: '关闭',
            onPressed: () => Navigator.of(context).pop(),
            type: DSButtonType.outlined,
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: SpacingTokens.space2),
      child: Row(
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(width: SpacingTokens.space3),
          Expanded(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getSourceIcon(PhotoSource source) {
    switch (source) {
      case PhotoSource.fishingSpot:
        return Icons.location_on;
      case PhotoSource.moment:
        return Icons.dynamic_feed;
      case PhotoSource.activity:
        return Icons.event;
      case PhotoSource.catchRecord:
        return Icons.phishing;
      case PhotoSource.manual:
        return Icons.upload;
      case PhotoSource.unknown:
        return Icons.help_outline;
    }
  }
}