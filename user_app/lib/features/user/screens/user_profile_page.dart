import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:provider/provider.dart';
import 'package:user_app/features/user/view_models/user_profile_view_model.dart';
import 'package:user_app/features/fishing_spots/widgets/moments/moment_card.dart';
import 'package:user_app/widgets/user_avatar.dart';
import 'package:user_app/utils/browsing_record_manager.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';
import 'package:user_app/shared/widgets/design_system/design_system.dart';

class UserProfilePage extends StatefulWidget {
  final int userId;

 const UserProfilePage({
    super.key,
    required this.userId,
  });

  @override
  State<UserProfilePage> createState() => _UserProfilePageState();
}

class _UserProfilePageState extends State<UserProfilePage>
    with SingleTickerProviderStateMixin, BrowsingRecordMixin {
  late TabController _tabController;
  late UserProfileViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _viewModel = UserProfileViewModel();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewModel.loadUserProfile(widget.userId);
      
      // 监听用户资料加载完成，然后记录浏览
      _viewModel.addListener(_onUserProfileLoaded);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _viewModel.removeListener(_onUserProfileLoaded);
    // 结束浏览记录
    endBrowsingRecord('user', widget.userId);
    super.dispose();
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  /// 监听用户资料加载完成
  void _onUserProfileLoaded() {
    if (_viewModel.user != null && !_viewModel.isLoading) {
      _recordUserBrowsing();
    }
  }

  /// 记录用户浏览
  void _recordUserBrowsing() {
    final user = _viewModel.user;
    if (user != null) {
      recordUserBrowsing(
        userId: user.id,
        userName: user.name,
        description: '${user.title} · ${user.introduce ?? ''}',
        avatarUrl: user.avatarUrl,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Consumer<UserProfileViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            body: _buildBody(viewModel),
          );
        },
      ),
    );
  }

  Widget _buildBody(UserProfileViewModel viewModel) {
    if (viewModel.isLoading) {
      return const Scaffold(
        appBar: null,
        body: Center(
          child: CircularProgressIndicator(color: ColorTokens.primary),
        ),
      );
    }

    if (viewModel.error != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('用户主页'),
          backgroundColor: ColorTokens.surface,
          foregroundColor: ColorTokens.onSurface,
          elevation: 0,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
             const Icon(
                Icons.error_outline,
                size: 64,
                color: ColorTokens.onSurfaceVariant,
              ),
              SpacingTokens.verticalSpaceMd,
              Text(
                viewModel.error!,
                style: TypographyTokens.bodyLarge.copyWith(
                  color: ColorTokens.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
              SpacingTokens.verticalSpaceMd,
              ElevatedButton(
                onPressed: () => viewModel.loadUserProfile(widget.userId),
                child: const Text('重试'),
              ),
            ],
          ),
        ),
      );
    }

    if (viewModel.user == null) {
      return const Scaffold(
        body: Center(
          child: Text('用户不存在'),
        ),
      );
    }

    return NestedScrollView(
      headerSliverBuilder: (context, innerBoxIsScrolled) {
        return [
          SliverAppBar(
            expandedHeight: 300,
            floating: false,
            pinned: true,
            backgroundColor: ColorTokens.surface,
            foregroundColor: ColorTokens.onSurface,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: _buildProfileHeader(viewModel),
            ),
            actions: [
              IconButton(
                icon: const Icon(Icons.more_vert),
                onPressed: _showMoreOptions,
              ),
            ],
          ),
          SliverPersistentHeader(
            delegate: _SliverAppBarDelegate(
              TabBar(
                controller: _tabController,
                labelColor: ColorTokens.onSurface,
                unselectedLabelColor: ColorTokens.onSurfaceVariant,
                indicatorColor: ColorTokens.primary,
                tabs: const [
                  Tab(text: '动态'),
                  Tab(text: '钓点'),
                  Tab(text: '收藏'),
                ],
              ),
            ),
            pinned: true,
          ),
        ];
      },
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMomentsTab(viewModel),
          _buildSpotsTab(viewModel),
          _buildFavoritesTab(viewModel),
        ],
      ),
    );
  }

  Widget _buildProfileHeader(UserProfileViewModel viewModel) {
    final user = viewModel.user!;
    
    return Container(
      padding: SpacingTokens.paddingLg,
      child: Column(
        children: [
         const SizedBox(height: SpacingTokens.space10), // 为AppBar留空间
          UserAvatar(
            avatarUrl: user.avatarUrl,
            radius: 50,
            placeholderText: user.name.isNotEmpty ? user.name[0] : '?',
          ),
          SpacingTokens.verticalSpaceMd,
          Text(
            user.name,
            style: TypographyTokens.headlineMedium,
          ),
          SpacingTokens.verticalSpaceXs,
          if (user.introduce != null && user.introduce!.isNotEmpty)
            Text(
              user.introduce!,
              style: TypographyTokens.bodyLarge.copyWith(
                color: ColorTokens.onSurfaceVariant,
              ),
            ),
          if (user.province != null || user.city != null)
            SpacingTokens.verticalSpaceXs,
          if (user.province != null || user.city != null)
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
               const Icon(Icons.location_on, size: 16, color: ColorTokens.onSurfaceVariant),
                Text(
                  '${user.province ?? ''}${user.city ?? ''}${user.county ?? ''}',
                  style: TypographyTokens.bodyMedium.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          SpacingTokens.verticalSpaceMd,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              _buildStatItem('动态', user.momentCount ?? 0),
              _buildStatItem('关注', user.attentionCount ?? 0),
              _buildStatItem('粉丝', user.followCount ?? 0),
            ],
          ),
          SpacingTokens.verticalSpaceMd,
          Row(
            children: [
              Expanded(
                child: viewModel.isFollowing
                    ? DSButton.secondary(
                        text: '已关注',
                        onPressed: viewModel.isFollowLoading ? null : () => viewModel.toggleFollow(widget.userId),
                        isLoading: viewModel.isFollowLoading,
                      )
                    : DSButton.primary(
                        text: '关注',
                        onPressed: viewModel.isFollowLoading ? null : () => viewModel.toggleFollow(widget.userId),
                        isLoading: viewModel.isFollowLoading,
                      ),
              ),
              SpacingTokens.horizontalSpaceSm,
              Expanded(
                child: DSButton.primary(
                  text: '发消息',
                  onPressed: () => _startChat(user),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, int count) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: TypographyTokens.headlineSmall,
        ),
        Text(
          label,
          style: TypographyTokens.bodyMedium.copyWith(
            color: ColorTokens.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildMomentsTab(UserProfileViewModel viewModel) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        if (!viewModel.momentsLoading &&
            viewModel.hasMomentsMore &&
            scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent) {
          viewModel.loadUserMoments(widget.userId);
        }
        return false;
      },
      child: RefreshIndicator(
        onRefresh: () => viewModel.loadUserMoments(widget.userId, refresh: true),
        child: Builder(
          builder: (context) {
            // Initialize loading if not loaded yet
            if (viewModel.userMoments.isEmpty && !viewModel.momentsLoading) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                viewModel.loadUserMoments(widget.userId);
              });
            }

            if (viewModel.userMoments.isEmpty && viewModel.momentsLoading) {
              return const Center(
                child: CircularProgressIndicator(color: ColorTokens.primary),
              );
            }

            if (viewModel.userMoments.isEmpty) {
              return Center(
                child: Text(
                  '暂无动态',
                  style: TypographyTokens.bodyLarge.copyWith(
                    color: ColorTokens.onSurfaceVariant,
                  ),
                ),
              );
            }

            return ListView.builder(
              padding: SpacingTokens.paddingMd,
              itemCount: viewModel.userMoments.length + (viewModel.hasMomentsMore ? 1 : 0),
              itemBuilder: (context, index) {
                if (index == viewModel.userMoments.length) {
                  return Center(
                    child: const Padding(
                      padding: SpacingTokens.paddingMd,
                      child: const CircularProgressIndicator(color: ColorTokens.primary),
                    ),
                  );
                }

                final moment = viewModel.userMoments[index];
                return Padding(
                  padding: EdgeInsets.only(bottom: SpacingTokens.space4),
                  child: MomentCard(
                    moment: moment,
                    onLikeChanged: (isLiked) {
                      viewModel.updateMomentLike(
                        moment.id!,
                        isLiked,
                        isLiked ? (moment.likeCount ?? 0) + 1 : (moment.likeCount ?? 0) - 1,
                      );
                    },
                    onCommentAdded: () {
                      viewModel.updateMomentComment(
                        moment.id!,
                        (moment.commentCount ?? 0) + 1,
                      );
                    },
                    onDeleted: () => viewModel.removeMoment(moment.id!),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }

  Widget _buildSpotsTab(UserProfileViewModel viewModel) {
    return Builder(
      builder: (context) {
        if (viewModel.userSpots.isEmpty && !viewModel.spotsLoading) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            viewModel.loadUserSpots(widget.userId);
          });
        }

        if (viewModel.userSpots.isEmpty && viewModel.spotsLoading) {
          return const Center(
            child: CircularProgressIndicator(color: ColorTokens.primary),
          );
        }

        return Center(
          child: Text(
            '暂无钓点',
            style: TypographyTokens.bodyLarge.copyWith(
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        );
      },
    );
  }

  Widget _buildFavoritesTab(UserProfileViewModel viewModel) {
    return Builder(
      builder: (context) {
        if (viewModel.userBookmarks.isEmpty && !viewModel.bookmarksLoading) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            viewModel.loadUserBookmarks(widget.userId);
          });
        }

        if (viewModel.userBookmarks.isEmpty && viewModel.bookmarksLoading) {
          return const Center(
            child: CircularProgressIndicator(color: ColorTokens.primary),
          );
        }

        return Center(
          child: Text(
            '暂无收藏',
            style: TypographyTokens.bodyLarge.copyWith(
              color: ColorTokens.onSurfaceVariant,
            ),
          ),
        );
      },
    );
  }

  void _showMoreOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.report),
                title: const Text('举报'),
                onTap: () {
                  Navigator.pop(context);
                  _showSnackBar('举报功能待开发');
                },
              ),
              ListTile(
                leading: const Icon(Icons.block),
                title: const Text('拉黑'),
                onTap: () {
                  Navigator.pop(context);
                  _showSnackBar('拉黑功能待开发');
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _startChat(user) {
    context.navigateTo('/chat_detail', extra: {
      'conversationID': 'c2c_${user.id}',
      'conversationType': 1, // C2C
      'showName': user.name,
      'userID': user.id.toString(),
    });
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverAppBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      color: ColorTokens.surface,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return false;
  }
}