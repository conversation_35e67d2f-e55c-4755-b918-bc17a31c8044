import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:user_app/core/navigation/navigation_extensions.dart';
import 'package:provider/provider.dart';
import 'package:user_app/config/app_routes.dart';
import 'package:user_app/features/user/view_models/fans_view_model.dart';
import 'package:user_app/models/user.dart';
import 'package:user_app/core/design_tokens/design_tokens.dart';

class FansPage extends StatefulWidget {
  final String userId;

 const FansPage({
    super.key,
    required this.userId,
  });

  @override
  State<FansPage> createState() => _FansPageState();
}

class _FansPageState extends State<FansPage> with TickerProviderStateMixin {
  late FansViewModel _viewModel;
  final ScrollController _scrollController = ScrollController();

  // 动画控制器
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  // 列表项动画控制器
  final List<AnimationController> _itemControllers = [];
  final Map<int, AnimationController> _followAnimationControllers = {};

  @override
  void initState() {
    super.initState();
    _viewModel = FansViewModel();
    _scrollController.addListener(_onScroll);

    // 初始化动画
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeOut,
    );
    _slideAnimation = Tween<Offset>(
      begin: Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutBack,
    ));
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOutBack,
    ));

    // 启动动画
    _fadeController.forward();
    _slideController.forward();
    _scaleController.forward();

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewModel.loadFans(int.parse(widget.userId));
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    for (var controller in _itemControllers) {
      controller.dispose();
    }
    for (var controller in _followAnimationControllers.values) {
      controller.dispose();
    }
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      _viewModel.loadMoreFans();
    }
  }

  // 创建列表项动画控制器
  AnimationController _createItemController(int index) {
    if (index >= _itemControllers.length) {
      final controller = AnimationController(
        duration: Duration(milliseconds: 400 + (index * 50)),
        vsync: this,
      );
      _itemControllers.add(controller);
      controller.forward();
    }
    return _itemControllers[index];
  }

  // 处理关注按钮动画
  void _handleFollowAnimation(int userId) {
    if (!_followAnimationControllers.containsKey(userId)) {
      _followAnimationControllers[userId] = AnimationController(
        duration: const Duration(milliseconds: 300),
        vsync: this,
      );
    }
    final controller = _followAnimationControllers[userId]!;
    controller.forward().then((_) {
      controller.reverse();
    });
  }

  // 下拉刷新
  Future<void> _onRefresh() async {
    HapticFeedback.mediumImpact();
    await _viewModel.refreshFans(int.parse(widget.userId));
  }

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _viewModel,
      child: Scaffold(
        backgroundColor: Theme.of(context).colorScheme.surface,
        body: Stack(
          children: [
            // 渐变背景
            Container(
              height: 280,
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.secondary,
                  ],
                ),
              ),
            ),
            // 主内容
            SafeArea(
              child: Column(
                children: [
                  // 自定义AppBar
                  _buildCustomAppBar(),
                  // 内容区域
                  Expanded(
                    child: SlideTransition(
                      position: _slideAnimation,
                      child: FadeTransition(
                        opacity: _fadeAnimation,
                        child: _buildContent(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomAppBar() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // 返回按钮
          IconButton(
            icon: Container(
              padding: SpacingTokens.paddingSm,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(Icons.arrow_back_ios,
                  color: Theme.of(context).colorScheme.surface, size: 20),
            ),
            onPressed: () {
              HapticFeedback.lightImpact();
              context.pop();
            },
          ),
          // 标题
          Expanded(
            child: ScaleTransition(
              scale: _scaleAnimation,
              child: Column(
                children: [
                 const Text(
                    '粉丝列表',
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.surface,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      letterSpacing: 0.8,
                    ),
                  ),
                 const SizedBox(height: 4),
                  Consumer<FansViewModel>(
                    builder: (context, viewModel, child) {
                      return Text(
                        '共 ${viewModel.fans.length} 位粉丝',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
                          fontSize: 14,
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
         const SizedBox(width: 48), // 平衡布局
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Consumer<FansViewModel>(
      builder: (context, viewModel, child) {
        if (viewModel.isLoading && viewModel.fans.isEmpty) {
          return _buildLoadingState();
        }

        if (viewModel.fans.isEmpty) {
          return _buildEmptyState();
        }

        return RefreshIndicator(
          onRefresh: _onRefresh,
          color: Theme.of(context).colorScheme.primary,
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.only(top: 20, bottom: 20),
            itemCount: viewModel.fans.length + (viewModel.hasMore ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == viewModel.fans.length) {
                return _buildLoadMoreIndicator();
              }
              return _buildFansItem(viewModel.fans[index], index);
            },
          ),
        );
      },
    );
  }

  Widget _buildFansItem(User user, int index) {
    final controller = _createItemController(index);

    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
            0,
            30 * (1 - controller.value),
          ),
          child: Opacity(
            opacity: controller.value,
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 16, vertical: 6),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Theme.of(context).colorScheme.surface, Theme.of(context).colorScheme.surfaceContainer],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.scrim.withValues(alpha: 0.05),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
              child: InkWell(
                onTap: () {
                  HapticFeedback.lightImpact();
                  context.navigateTo(
                    AppRoutes.profile
                        .replaceFirst(':userId', user.id.toString()),
                  );
                },
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: SpacingTokens.paddingMd,
                  child: Row(
                    children: [
                      // 用户头像
                      _buildAvatar(user),
                     const SizedBox(width: 16),
                      // 用户信息
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              user.name ?? '未知用户',
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: Color(0xFF1A1E25),
                              ),
                            ),
                           const SizedBox(height: 4),
                            Text(
                              user.introduce ?? '这个人很懒，什么都没留下~',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                           const SizedBox(height: 6),
                            Row(
                              children: [
                                _buildUserStat(
                                    Icons.favorite, user.followCount ?? 0),
                               const SizedBox(width: 16),
                                _buildUserStat(
                                    Icons.photo_library, user.momentCount ?? 0),
                              ],
                            ),
                          ],
                        ),
                      ),
                      // 操作按钮组
                      Row(
                        children: [
                          // 私信按钮
                          _buildMessageButton(user),
                         const SizedBox(width: 8),
                          // 关注按钮
                          _buildFollowButton(user),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildAvatar(User user) {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: const LinearGradient(
          colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      padding: EdgeInsets.all(2),
      child: CircleAvatar(
        radius: 28,
        backgroundColor: Theme.of(context).colorScheme.surface,
        child: CircleAvatar(
          radius: 26,
          backgroundImage: user.avatarUrl != null
              ? CachedNetworkImageProvider(user.avatarUrl!)
              : null,
          child: user.avatarUrl == null
              ? Text(
                  (user.name ?? 'U').substring(0, 1).toUpperCase(),
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                )
              : null,
        ),
      ),
    );
  }

  Widget _buildUserStat(IconData icon, int count) {
    return Row(
      children: [
        Icon(icon, size: 14, color: Colors.grey[500]),
       const SizedBox(width: 4),
        Text(
          count.toString(),
          style: TextStyle(
            fontSize: 13,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildFollowButton(User user) {
    final isFollowing = user.isFollowing ?? false;

    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        _handleFollowAnimation(user.id!);
        // TODO: 实现关注/取消关注逻辑
      },
      child: AnimatedBuilder(
        animation:
            _followAnimationControllers[user.id] ?? AlwaysStoppedAnimation(0.0),
        builder: (context, child) {
          final scale =
              1.0 + (_followAnimationControllers[user.id]?.value ?? 0) * 0.1;

          return Transform.scale(
            scale: scale,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20, vertical: 8),
              decoration: BoxDecoration(
                gradient: isFollowing
                    ? LinearGradient(
                        colors: [
                          Colors.grey[300]!,
                          Colors.grey[400]!,
                        ],
                      )
                    : const LinearGradient(
                        colors: [
                          Theme.of(context).colorScheme.primary,
                          Theme.of(context).colorScheme.secondary,
                        ],
                      ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: (isFollowing ? Colors.grey : Theme.of(context).colorScheme.primary)
                        .withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                isFollowing ? '已关注' : '关注',
                style: TextStyle(
                  color: isFollowing ? Colors.grey[700] : Theme.of(context).colorScheme.surface,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMessageButton(User user) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        // 导航到聊天页面 - 使用正确的路由格式
        final conversationID = 'c2c_${user.id}';
        context.navigateTo(
          '/chat/simple/$conversationID',
          extra: {
            'conversationID': conversationID,
            'conversationType': 1, // C2C私聊
            'showName': user.name ?? '用户${user.id}',
            'userID': user.id.toString(),
            'userAvatar': user.avatarUrl,
          },
        );
      },
      child: Container(
        width: 42,
        height: 42,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: const Center(
          child: Icon(
            Icons.chat_bubble_outline,
            size: 20,
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
              ),
              borderRadius: BorderRadius.circular(15),
            ),
            child: const Center(
              child: CircularProgressIndicator(
                color: Theme.of(context).colorScheme.surface,
                strokeWidth: 3,
              ),
            ),
          ),
         const SizedBox(height: SpacingTokens.space4),
         const Text(
            '加载中...',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                  ],
                ),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.people_outline,
                size: 60,
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
           const SizedBox(height: SpacingTokens.space5),
           const Text(
              '暂无粉丝',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF1A1E25),
              ),
            ),
           const SizedBox(height: SpacingTokens.space2),
            Text(
              '发布精彩内容，吸引更多粉丝关注',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLoadMoreIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20),
      child: const Center(
        child: CircularProgressIndicator(
          color: Theme.of(context).colorScheme.primary,
          strokeWidth: 2,
        ),
      ),
    );
  }
}
