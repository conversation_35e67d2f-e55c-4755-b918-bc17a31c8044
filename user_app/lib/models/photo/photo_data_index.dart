import 'package:user_app/models/fishing_spot/fishing_spot.dart';
import 'package:user_app/models/moment/moment.dart';
import 'package:user_app/models/activity/activity.dart';
import 'package:user_app/models/catch_record/catch_record.dart';

/// 照片数据索引模型 - 将照片作为钓鱼数据的关联中心
class PhotoDataIndex {
  final String photoId;
  final String photoUrl;
  final String? thumbnailUrl;
  final DateTime takenAt;
  final PhotoLocation? location;
  
  // 关联的数据实体
  final List<String> relatedSpotIds;      // 关联的钓点ID
  final List<String> relatedMomentIds;    // 关联的动态ID
  final List<String> relatedActivityIds;  // 关联的活动ID
  final List<String> relatedCatchIds;     // 关联的钓获记录ID
  
  // 智能标签和元数据
  final List<String> autoTags;            // AI自动识别的标签
  final List<String> userTags;            // 用户手动添加的标签
  final Map<String, dynamic> metadata;    // 其他元数据
  
  // 来源信息
  final PhotoSource source;               // 照片来源（钓点、动态、活动等）
  final String sourceId;                  // 来源实体的ID
  
  const PhotoDataIndex({
    required this.photoId,
    required this.photoUrl,
    this.thumbnailUrl,
    required this.takenAt,
    this.location,
    this.relatedSpotIds = const [],
    this.relatedMomentIds = const [],
    this.relatedActivityIds = const [],
    this.relatedCatchIds = const [],
    this.autoTags = const [],
    this.userTags = const [],
    this.metadata = const {},
    required this.source,
    required this.sourceId,
  });

  /// 获取所有关联的实体数量
  int get totalRelatedItems {
    return relatedSpotIds.length + 
           relatedMomentIds.length + 
           relatedActivityIds.length + 
           relatedCatchIds.length;
  }

  /// 获取所有标签（自动+手动）
  List<String> get allTags {
    return [...autoTags, ...userTags];
  }

  /// 判断是否包含指定标签
  bool hasTag(String tag) {
    return allTags.any((t) => t.toLowerCase().contains(tag.toLowerCase()));
  }

  /// 复制并更新关联数据
  PhotoDataIndex copyWith({
    String? photoId,
    String? photoUrl,
    String? thumbnailUrl,
    DateTime? takenAt,
    PhotoLocation? location,
    List<String>? relatedSpotIds,
    List<String>? relatedMomentIds,
    List<String>? relatedActivityIds,
    List<String>? relatedCatchIds,
    List<String>? autoTags,
    List<String>? userTags,
    Map<String, dynamic>? metadata,
    PhotoSource? source,
    String? sourceId,
  }) {
    return PhotoDataIndex(
      photoId: photoId ?? this.photoId,
      photoUrl: photoUrl ?? this.photoUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      takenAt: takenAt ?? this.takenAt,
      location: location ?? this.location,
      relatedSpotIds: relatedSpotIds ?? this.relatedSpotIds,
      relatedMomentIds: relatedMomentIds ?? this.relatedMomentIds,
      relatedActivityIds: relatedActivityIds ?? this.relatedActivityIds,
      relatedCatchIds: relatedCatchIds ?? this.relatedCatchIds,
      autoTags: autoTags ?? this.autoTags,
      userTags: userTags ?? this.userTags,
      metadata: metadata ?? this.metadata,
      source: source ?? this.source,
      sourceId: sourceId ?? this.sourceId,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'photoId': photoId,
      'photoUrl': photoUrl,
      'thumbnailUrl': thumbnailUrl,
      'takenAt': takenAt.toIso8601String(),
      'location': location?.toJson(),
      'relatedSpotIds': relatedSpotIds,
      'relatedMomentIds': relatedMomentIds,
      'relatedActivityIds': relatedActivityIds,
      'relatedCatchIds': relatedCatchIds,
      'autoTags': autoTags,
      'userTags': userTags,
      'metadata': metadata,
      'source': source.name,
      'sourceId': sourceId,
    };
  }

  factory PhotoDataIndex.fromJson(Map<String, dynamic> json) {
    return PhotoDataIndex(
      photoId: json['photoId'] as String,
      photoUrl: json['photoUrl'] as String,
      thumbnailUrl: json['thumbnailUrl'] as String?,
      takenAt: DateTime.parse(json['takenAt'] as String),
      location: json['location'] != null 
          ? PhotoLocation.fromJson(json['location']) 
          : null,
      relatedSpotIds: List<String>.from(json['relatedSpotIds'] ?? []),
      relatedMomentIds: List<String>.from(json['relatedMomentIds'] ?? []),
      relatedActivityIds: List<String>.from(json['relatedActivityIds'] ?? []),
      relatedCatchIds: List<String>.from(json['relatedCatchIds'] ?? []),
      autoTags: List<String>.from(json['autoTags'] ?? []),
      userTags: List<String>.from(json['userTags'] ?? []),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      source: PhotoSource.values.firstWhere(
        (e) => e.name == json['source'],
        orElse: () => PhotoSource.unknown,
      ),
      sourceId: json['sourceId'] as String,
    );
  }
}

/// 照片位置信息
class PhotoLocation {
  final double latitude;
  final double longitude;
  final String? address;
  final String? spotName;

  const PhotoLocation({
    required this.latitude,
    required this.longitude,
    this.address,
    this.spotName,
  });

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'spotName': spotName,
    };
  }

  factory PhotoLocation.fromJson(Map<String, dynamic> json) {
    return PhotoLocation(
      latitude: json['latitude'] as double,
      longitude: json['longitude'] as double,
      address: json['address'] as String?,
      spotName: json['spotName'] as String?,
    );
  }
}

/// 照片来源类型
enum PhotoSource {
  fishingSpot,  // 来自钓点创建
  moment,       // 来自动态发布
  activity,     // 来自活动记录
  catchRecord,  // 来自钓获记录
  manual,       // 用户手动上传
  unknown,      // 未知来源
}

extension PhotoSourceExtension on PhotoSource {
  String get displayName {
    switch (this) {
      case PhotoSource.fishingSpot:
        return '钓点';
      case PhotoSource.moment:
        return '动态';
      case PhotoSource.activity:
        return '活动';
      case PhotoSource.catchRecord:
        return '钓获记录';
      case PhotoSource.manual:
        return '手动上传';
      case PhotoSource.unknown:
        return '未知';
    }
  }

  String get description {
    switch (this) {
      case PhotoSource.fishingSpot:
        return '创建钓点时上传的照片';
      case PhotoSource.moment:
        return '发布动态时上传的照片';
      case PhotoSource.activity:
        return '参与活动时上传的照片';
      case PhotoSource.catchRecord:
        return '记录钓获时上传的照片';
      case PhotoSource.manual:
        return '用户主动上传的照片';
      case PhotoSource.unknown:
        return '来源不明的照片';
    }
  }
}