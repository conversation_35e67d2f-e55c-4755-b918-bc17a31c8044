# 结构化动态系统 API 文档

## 概述

本文档详细描述了结构化动态系统的所有 REST API 接口。该系统将原有的 JSON 数据结构替换为明确的关系型数据库表结构，提供更好的数据完整性和查询性能。

## 基础信息

- **Base URL**: `http://localhost:9010/api/v1`
- **Content-Type**: `application/json`
- **认证方式**: JWT Token (Header: `Authorization: Bearer {token}`)

## 数据类型映射

### 动态类型 (moment_type)

| 数值 | 枚举值 | 中文名称 | 描述 |
|------|--------|----------|------|
| 1    | fishing_catch | 钓获分享 | 分享钓鱼收获和经历 |
| 2    | equipment | 装备展示 | 展示和评价钓鱼装备 |
| 3    | technique | 技巧分享 | 分享钓鱼技巧和经验 |
| 4    | question | 求助问答 | 提问和寻求帮助 |

### 可见性 (visibility)

| 数值 | 枚举值 | 中文名称 | 描述 |
|------|--------|----------|------|
| 1    | public | 公开 | 所有人可见 |
| 2    | followers | 关注者 | 仅关注者可见 |
| 3    | private | 私密 | 仅自己可见 |

### 难度等级 (difficulty)

| 数值 | 枚举值 | 中文名称 |
|------|--------|----------|
| 1    | beginner | 初级 |
| 2    | intermediate | 中级 |
| 3    | advanced | 高级 |

### 紧急程度 (urgency)

| 数值 | 枚举值 | 中文名称 |
|------|--------|----------|
| 1    | low | 低 |
| 2    | medium | 中 |
| 3    | high | 高 |

### 问题状态 (status)

| 数值 | 枚举值 | 中文名称 |
|------|--------|----------|
| 1    | unsolved | 未解决 |
| 2    | solved | 已解决 |
| 3    | closed | 已关闭 |

## API 接口

### 1. 创建结构化动态

**POST** `/structured-moments`

创建一个新的结构化动态，根据动态类型自动处理相关的子数据。

#### 请求体

```json
{
  "momentType": 1,
  "content": "今天在湖边钓到了几条不错的鲫鱼",
  "visibility": 1,
  "fishingSpotId": 123,
  "imageUrls": [
    "https://example.com/image1.jpg",
    "https://example.com/image2.jpg"
  ],
  "fishingCatch": {
    "totalWeight": 2.5,
    "fishingMethod": "台钓",
    "baitUsed": "蚯蚓",
    "weatherConditions": "晴天，微风",
    "waterTemperature": 18.5,
    "waterDepth": 3.2,
    "fishingDuration": 240,
    "caughtFishes": [
      {
        "fishTypeId": 1,
        "fishTypeName": "鲫鱼",
        "count": 3,
        "weight": 1.2,
        "size": 18.5,
        "remark": "体型不错"
      }
    ]
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 12345,
    "momentType": "fishing_catch",
    "content": "今天在湖边钓到了几条不错的鲫鱼",
    "visibility": "public",
    "userId": 1001,
    "username": "钓鱼爱好者",
    "userAvatar": "https://example.com/avatar.jpg",
    "fishingSpotId": 123,
    "fishingSpotName": "西湖",
    "imageUrls": [
      "https://example.com/image1.jpg",
      "https://example.com/image2.jpg"
    ],
    "createTime": "2024-01-15T10:30:00Z",
    "updateTime": "2024-01-15T10:30:00Z",
    "fishingCatch": {
      "totalWeight": 2.5,
      "fishingMethod": "台钓",
      "baitUsed": "蚯蚓",
      "weatherConditions": "晴天，微风",
      "waterTemperature": 18.5,
      "waterDepth": 3.2,
      "fishingDuration": 240,
      "caughtFishes": [
        {
          "id": 1,
          "fishTypeId": 1,
          "fishTypeName": "鲫鱼",
          "count": 3,
          "weight": 1.2,
          "size": 18.5,
          "remark": "体型不错"
        }
      ],
      "images": [
        {
          "id": 1,
          "imageUrl": "https://example.com/image1.jpg",
          "displayOrder": 1
        }
      ]
    }
  }
}
```

### 2. 获取结构化动态列表

**GET** `/structured-moments`

获取结构化动态列表，支持分页、筛选和排序。

#### 查询参数

| 参数 | 类型 | 必填 | 说明 | 示例 |
|------|------|------|------|------|
| page | int | 否 | 页码，从0开始 | 0 |
| size | int | 否 | 每页数量 | 10 |
| momentType | int | 否 | 动态类型筛选 | 1 |
| userId | long | 否 | 用户ID筛选 | 1001 |
| fishingSpotId | long | 否 | 钓点ID筛选 | 123 |
| sortBy | string | 否 | 排序字段 | createTime |
| sortDirection | string | 否 | 排序方向 | desc |

#### 响应

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "content": [
      {
        "id": 12345,
        "momentType": "fishing_catch",
        "content": "今天在湖边钓到了几条不错的鲫鱼",
        "visibility": "public",
        "userId": 1001,
        "username": "钓鱼爱好者",
        "userAvatar": "https://example.com/avatar.jpg",
        "fishingSpotId": 123,
        "fishingSpotName": "西湖",
        "imageUrls": [
          "https://example.com/image1.jpg"
        ],
        "createTime": "2024-01-15T10:30:00Z",
        "likeCount": 15,
        "commentCount": 3,
        "isLiked": false,
        "typeSpecificSummary": {
          "fishCount": 3,
          "totalWeight": 2.5,
          "mainFishType": "鲫鱼"
        }
      }
    ],
    "pageable": {
      "page": 0,
      "size": 10,
      "totalElements": 100,
      "totalPages": 10,
      "hasNext": true,
      "hasPrevious": false
    }
  }
}
```

### 3. 获取结构化动态详情

**GET** `/structured-moments/{id}`

获取指定动态的完整详情信息，包括所有类型特定的数据。

#### 路径参数

| 参数 | 类型 | 说明 |
|------|------|------|
| id | long | 动态ID |

#### 响应

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 12345,
    "momentType": "fishing_catch",
    "content": "今天在湖边钓到了几条不错的鲫鱼",
    "visibility": "public",
    "userId": 1001,
    "username": "钓鱼爱好者",
    "userAvatar": "https://example.com/avatar.jpg",
    "fishingSpotId": 123,
    "fishingSpotName": "西湖",
    "imageUrls": [
      "https://example.com/image1.jpg",
      "https://example.com/image2.jpg"
    ],
    "createTime": "2024-01-15T10:30:00Z",
    "updateTime": "2024-01-15T10:30:00Z",
    "likeCount": 15,
    "commentCount": 3,
    "isLiked": false,
    "fishingCatch": {
      "totalWeight": 2.5,
      "fishingMethod": "台钓",
      "baitUsed": "蚯蚓",
      "weatherConditions": "晴天，微风",
      "waterTemperature": 18.5,
      "waterDepth": 3.2,
      "fishingDuration": 240,
      "caughtFishes": [
        {
          "id": 1,
          "fishTypeId": 1,
          "fishTypeName": "鲫鱼",
          "count": 3,
          "weight": 1.2,
          "size": 18.5,
          "remark": "体型不错"
        }
      ],
      "images": [
        {
          "id": 1,
          "imageUrl": "https://example.com/image1.jpg",
          "displayOrder": 1
        }
      ]
    }
  }
}
```

### 4. 更新结构化动态

**PUT** `/structured-moments/{id}`

更新指定动态的信息，支持部分更新。

#### 路径参数

| 参数 | 类型 | 说明 |
|------|------|------|
| id | long | 动态ID |

#### 请求体

```json
{
  "content": "更新后的内容描述",
  "visibility": 2,
  "fishingCatch": {
    "totalWeight": 3.0,
    "fishingMethod": "路亚",
    "weatherConditions": "阴天"
  }
}
```

#### 响应

```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 12345,
    "content": "更新后的内容描述",
    "visibility": "followers",
    "updateTime": "2024-01-15T12:00:00Z"
  }
}
```

### 5. 删除结构化动态

**DELETE** `/structured-moments/{id}`

删除指定的动态及其所有相关数据。

#### 路径参数

| 参数 | 类型 | 说明 |
|------|------|------|
| id | long | 动态ID |

#### 响应

```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 6. 获取用户动态列表

**GET** `/structured-moments/user/{userId}`

获取指定用户的动态列表。

#### 路径参数

| 参数 | 类型 | 说明 |
|------|------|------|
| userId | long | 用户ID |

#### 查询参数

同获取动态列表接口，但不包含 userId 参数。

#### 响应

格式同获取动态列表接口。

### 7. 获取钓点动态列表

**GET** `/structured-moments/fishing-spot/{spotId}`

获取指定钓点的动态列表。

#### 路径参数

| 参数 | 类型 | 说明 |
|------|------|------|
| spotId | long | 钓点ID |

## 类型特定的数据结构

### 钓获分享 (fishing_catch)

```json
{
  "fishingCatch": {
    "totalWeight": 2.5,
    "fishingMethod": "台钓",
    "baitUsed": "蚯蚓",
    "weatherConditions": "晴天，微风",
    "waterTemperature": 18.5,
    "waterDepth": 3.2,
    "fishingDuration": 240,
    "caughtFishes": [
      {
        "fishTypeId": 1,
        "fishTypeName": "鲫鱼",
        "count": 3,
        "weight": 1.2,
        "size": 18.5,
        "remark": "体型不错"
      }
    ],
    "images": [
      {
        "imageUrl": "https://example.com/image1.jpg",
        "displayOrder": 1
      }
    ]
  }
}
```

### 装备展示 (equipment)

```json
{
  "equipment": {
    "equipmentName": "达亿瓦一击鱼竿",
    "equipmentType": "鱼竿",
    "brand": "达亿瓦",
    "model": "一击2.7m",
    "price": "299.00",
    "purchaseLink": "https://example.com/product",
    "rating": 4,
    "pros": "轻便，手感好",
    "cons": "价格偏高",
    "usageTips": "适合钓鲫鱼和小鲤鱼",
    "targetFishTypes": [
      {
        "fishTypeId": 1,
        "fishTypeName": "鲫鱼"
      }
    ]
  }
}
```

### 技巧分享 (technique)

```json
{
  "technique": {
    "techniqueName": "台钓调漂技巧",
    "difficulty": "intermediate",
    "toolsRequired": "浮漂、铅皮、剪刀",
    "bestTime": "春秋季节",
    "successRate": 85,
    "detailedSteps": "1. 确定水深...",
    "commonMistakes": "调漂过钝或过灵",
    "videoUrl": "https://example.com/video.mp4",
    "suitableEnvironments": ["湖泊", "水库"],
    "targetFishTypes": [
      {
        "fishTypeId": 1,
        "fishTypeName": "鲫鱼"
      }
    ]
  }
}
```

### 求助问答 (question)

```json
{
  "question": {
    "questionTitle": "如何在冬天钓鲫鱼？",
    "urgency": "medium",
    "status": "unsolved",
    "bestAnswerId": null,
    "tags": [
      {
        "id": 1,
        "name": "冬钓",
        "category": "question"
      }
    ]
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证 |
| 403 | 无权限 |
| 404 | 资源不存在 |
| 409 | 数据冲突 |
| 500 | 服务器内部错误 |

## 错误响应格式

```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "errors": [
    {
      "field": "momentType",
      "message": "动态类型不能为空"
    }
  ]
}
```

## 分页响应格式

所有分页接口都使用统一的响应格式：

```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "content": [], // 数据数组
    "pageable": {
      "page": 0,      // 当前页码
      "size": 10,     // 每页大小
      "totalElements": 100,  // 总记录数
      "totalPages": 10,      // 总页数
      "hasNext": true,       // 是否有下一页
      "hasPrevious": false   // 是否有上一页
    }
  }
}
```

## 使用示例

### 创建钓获分享动态

```bash
curl -X POST http://localhost:9010/api/v1/structured-moments \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "momentType": 1,
    "content": "今天钓获不错",
    "visibility": 1,
    "fishingCatch": {
      "totalWeight": 2.5,
      "fishingMethod": "台钓",
      "caughtFishes": [
        {
          "fishTypeId": 1,
          "fishTypeName": "鲫鱼",
          "count": 3
        }
      ]
    }
  }'
```

### 获取动态列表

```bash
curl -X GET "http://localhost:9010/api/v1/structured-moments?page=0&size=10&momentType=1" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

### 获取动态详情

```bash
curl -X GET http://localhost:9010/api/v1/structured-moments/12345 \
  -H "Authorization: Bearer YOUR_TOKEN"
```

## 注意事项

1. 所有时间字段均采用 ISO 8601 格式 (UTC)
2. 图片 URL 应该是已上传到 OSS 的完整路径
3. 创建动态时，根据 momentType 只需要提供对应的类型特定数据
4. 更新操作支持部分更新，只需提供需要修改的字段
5. 删除动态会级联删除所有相关的子数据
6. 分页从第 0 页开始计数
7. 所有枚举值在响应中会转换为字符串形式以提高可读性

## 版本历史

- **v1.0.0** (2024-01-15): 初始版本，支持四种动态类型的完整 CRUD 操作