# 动态结构化数据 API 文档

## 概述

动态结构化数据API提供了对钓鱼动态中结构化数据的完整管理功能，包括数据查询、验证、导出等功能。

## 基础信息

- **Base URL**: `/admin-api/moments`
- **认证**: 需要管理员权限
- **数据格式**: JSON
- **字符编码**: UTF-8

## API 端点

### 1. 动态统计信息

#### GET `/stats`

获取动态的统计信息，包括总数、类型分布、结构化数据统计等。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalMoments": 1250,
    "totalLikes": 8500,
    "totalComments": 3200,
    "todayNewMoments": 25,
    "todayNewLikes": 150,
    "todayNewComments": 85,
    "activeMoments": 890,
    "typeDistribution": {
      "fishing_catch": 450,
      "equipment": 320,
      "technique": 280,
      "question": 200
    },
    "structuredDataStats": {
      "fishingCatch": {
        "total": 450,
        "averageWeight": 2.5,
        "totalCaughtFish": 1200
      },
      "equipment": {
        "total": 320,
        "topBrands": {
          "Daiwa": 80,
          "Shimano": 75,
          "Abu Garcia": 45
        },
        "averageRating": 4.2
      },
      "technique": {
        "total": 280,
        "difficultyDistribution": {
          "1": 120,
          "2": 100,
          "3": 60
        }
      },
      "question": {
        "total": 200,
        "resolveRate": 75.5
      }
    },
    "weeklyTrend": {
      "dates": ["2025-01-08", "2025-01-09", "2025-01-10", "2025-01-11", "2025-01-12", "2025-01-13", "2025-01-14"],
      "momentCounts": [15, 18, 22, 19, 25, 28, 35],
      "likeCounts": [120, 145, 165, 155, 180, 195, 220],
      "commentCounts": [45, 52, 58, 48, 65, 70, 85],
      "momentGrowthRate": 25.0
    }
  }
}
```

### 2. 动态列表查询

#### GET `/list`

查询动态列表，支持多种筛选条件。

**请求参数**:
- `userId` (Long, 可选): 用户ID
- `momentType` (String, 可选): 动态类型 (`fishing_catch`, `equipment`, `technique`, `question`)
- `visibility` (String, 可选): 可见性 (`public`, `followers`, `private`)
- `fishingSpotId` (Long, 可选): 钓点ID
- `content` (String, 可选): 内容关键词
- `startTime` (String, 可选): 开始时间 (格式: `yyyy-MM-dd HH:mm:ss`)
- `endTime` (String, 可选): 结束时间 (格式: `yyyy-MM-dd HH:mm:ss`)
- `page` (Integer, 默认0): 页码
- `size` (Integer, 默认10): 每页大小

**响应示例**:
```json
{
  "code": 200,
  "msg": null,
  "total": 150,
  "rows": [
    {
      "momentId": 1001,
      "momentType": "fishing_catch",
      "content": "今天在西湖钓到了一条大鲤鱼！",
      "userName": "钓鱼达人",
      "createTime": "2025-01-15 14:30:00",
      "fishingSpotName": "西湖",
      "visibility": "public",
      "likeCount": 25,
      "commentCount": 8,
      "viewCount": 150,
      "isViolation": false,
      "auditStatus": "NORMAL",
      "images": [
        {
          "id": 2001,
          "imageUrl": "https://example.com/images/fish1.jpg",
          "displayOrder": 1
        }
      ]
    }
  ]
}
```

### 3. 动态详情

#### GET `/{id}`

获取指定动态的详细信息。

**路径参数**:
- `id` (Long): 动态ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "momentId": 1001,
    "momentType": "fishing_catch",
    "content": "今天在西湖钓到了一条大鲤鱼！重量达到了3.5公斤。",
    "userName": "钓鱼达人",
    "userAvatar": "https://example.com/avatars/user1.jpg",
    "createTime": "2025-01-15 14:30:00",
    "fishingSpotName": "西湖",
    "visibility": "public",
    "likeCount": 25,
    "commentCount": 8,
    "viewCount": 150,
    "isLiked": false,
    "images": [...],
    "comments": [...],
    "location": {
      "latitude": 30.2741,
      "longitude": 120.1551
    }
  }
}
```

### 4. 结构化数据查询

#### GET `/{id}/structured-data`

获取指定动态的结构化数据详情。

**路径参数**:
- `id` (Long): 动态ID

**响应示例 (钓获分享)**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "momentId": 1001,
    "momentType": "fishing_catch",
    "fishingCatchData": {
      "totalWeight": "3.5",
      "fishingMethod": "台钓",
      "weatherConditions": "晴朗，微风",
      "attributes": "{\"水深\":\"2.5米\",\"水温\":\"15°C\"}",
      "caughtFish": [
        {
          "fishTypeId": 101,
          "fishTypeName": "鲤鱼",
          "count": 1,
          "weight": "3.5"
        },
        {
          "fishTypeId": 102,
          "fishTypeName": "鲫鱼",
          "count": 3,
          "weight": "0.8"
        }
      ]
    }
  }
}
```

**响应示例 (装备展示)**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "momentId": 1002,
    "momentType": "equipment",
    "equipmentData": {
      "equipmentName": "Daiwa银河系列鱼竿",
      "brand": "Daiwa",
      "model": "Galaxy-540",
      "priceRange": "1200",
      "purchaseChannel": null,
      "experience": null,
      "recommendationIndex": 5,
      "attributes": "{\"长度\":\"5.4米\",\"重量\":\"180克\"}"
    }
  }
}
```

#### POST `/structured-data/batch`

批量获取多个动态的结构化数据。

**请求体**:
```json
[1001, 1002, 1003, 1004, 1005]
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "momentId": 1001,
      "momentType": "fishing_catch",
      "fishingCatchData": {...}
    },
    {
      "momentId": 1002,
      "momentType": "equipment",
      "equipmentData": {...}
    }
  ]
}
```

### 5. 数据验证

#### GET `/validate`

验证所有动态的数据完整性。

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalValidated": 1250,
    "validCount": 1240,
    "invalidCount": 10,
    "orphanedDataCount": 5,
    "validationErrors": [
      {
        "momentId": 1001,
        "errorType": "MISSING_STRUCTURED_DATA",
        "errorMessage": "动态类型为fishing_catch但缺少结构化数据"
      }
    ],
    "orphanedData": [
      {
        "dataType": "fishing_catch",
        "dataId": 2001,
        "momentId": 9999,
        "message": "结构化数据存在但对应的动态不存在"
      }
    ],
    "validationTime": "2025-01-15 16:30:00"
  }
}
```

#### GET `/validate/{id}`

验证指定动态的数据完整性。

**路径参数**:
- `id` (Long): 动态ID

#### GET `/validate/orphaned`

检查孤立的结构化数据。

### 6. 数据导出

#### GET `/export/preview`

预览导出数据。

**请求参数**:
- `momentType` (String, 可选): 动态类型
- `limit` (Integer, 默认10): 预览数量限制

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "momentId": 1001,
      "momentType": "fishing_catch",
      "content": "今天钓到了大鲤鱼",
      "userName": "钓鱼达人",
      "createTime": "2025-01-15 14:30:00",
      "fishingSpotName": "西湖",
      "visibility": "public",
      "likeCount": 25,
      "commentCount": 8,
      "viewCount": 150,
      "totalWeight": "3.5",
      "fishingMethod": "台钓",
      "weatherConditions": "晴朗，微风",
      "caughtFishCount": 4
    }
  ]
}
```

#### GET `/export/excel/all`

导出所有结构化数据为Excel文件。

**响应**: Excel文件下载

#### GET `/export/excel/type`

导出指定类型的结构化数据为Excel文件。

**请求参数**:
- `momentType` (String): 动态类型
- `startTime` (String, 可选): 开始时间
- `endTime` (String, 可选): 结束时间

**响应**: Excel文件下载

#### GET `/export/csv/all`

导出所有结构化数据为CSV文件。

**响应**: CSV文件下载

#### GET `/export/csv/type`

导出指定类型的结构化数据为CSV文件。

**请求参数**:
- `momentType` (String): 动态类型
- `startTime` (String, 可选): 开始时间
- `endTime` (String, 可选): 结束时间

**响应**: CSV文件下载

#### GET `/export/report/fishing-catch`

导出钓获分享统计报告。

**请求参数**:
- `startTime` (String, 可选): 开始时间
- `endTime` (String, 可选): 结束时间

**响应**: 包含数据和统计分析的Excel报告

#### GET `/export/report/equipment`

导出装备使用统计报告。

**请求参数**:
- `startTime` (String, 可选): 开始时间
- `endTime` (String, 可选): 结束时间

**响应**: 包含装备数据和统计分析的Excel报告

## 数据结构说明

### 动态类型 (momentType)

- `fishing_catch`: 钓获分享
- `equipment`: 装备展示
- `technique`: 技巧分享
- `question`: 问答求助

### 结构化数据类型

#### 1. 钓获分享数据 (FishingCatchData)

```json
{
  "totalWeight": "3.5",           // 总重量(kg)
  "fishingMethod": "台钓",        // 钓法
  "weatherConditions": "晴朗",    // 天气条件
  "attributes": "{}",             // 扩展属性(JSON)
  "caughtFish": [                // 捕获鱼类列表
    {
      "fishTypeId": 101,          // 鱼类ID
      "fishTypeName": "鲤鱼",     // 鱼类名称
      "count": 1,                 // 数量
      "weight": "3.5"             // 重量(kg)
    }
  ]
}
```

#### 2. 装备展示数据 (EquipmentData)

```json
{
  "equipmentName": "Daiwa银河系列", // 装备名称
  "brand": "Daiwa",               // 品牌
  "model": "Galaxy-540",          // 型号
  "priceRange": "1200",           // 价格
  "purchaseChannel": null,        // 购买渠道
  "experience": null,             // 使用体验
  "recommendationIndex": 5,       // 推荐指数(1-5)
  "attributes": "{}"              // 扩展属性(JSON)
}
```

#### 3. 技巧分享数据 (TechniqueData)

```json
{
  "techniqueTitle": "调漂技巧",       // 技巧标题
  "difficultyLevel": "2",           // 难度等级(1-3)
  "applicableEnvironment": null,     // 适用环境
  "requiredEquipment": null,         // 所需装备
  "detailedSteps": "详细步骤...",    // 详细步骤
  "precautions": null,              // 注意事项
  "attributes": "{}",               // 扩展属性(JSON)
  "applicableFishTypes": [],        // 适用鱼类
  "environmentConditions": []       // 环境条件
}
```

#### 4. 问答求助数据 (QuestionData)

```json
{
  "questionTitle": "如何选择鱼线？",  // 问题标题
  "questionType": null,            // 问题类型
  "urgencyLevel": null,            // 紧急程度
  "attributes": "{}",              // 扩展属性(JSON)
  "questionTags": []               // 问题标签
}
```

## 错误码说明

### 通用错误码

- `200`: 成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 业务错误码

- `1001`: 动态不存在
- `1002`: 结构化数据不存在
- `1003`: 动态类型不支持
- `1004`: 数据验证失败
- `1005`: 导出失败

## 使用示例

### 1. 获取钓获分享类型的动态列表

```bash
curl -X GET "http://localhost:8080/admin-api/moments/list?momentType=fishing_catch&page=0&size=10" \
  -H "Authorization: Bearer your-token"
```

### 2. 获取动态的结构化数据

```bash
curl -X GET "http://localhost:8080/admin-api/moments/1001/structured-data" \
  -H "Authorization: Bearer your-token"
```

### 3. 批量获取结构化数据

```bash
curl -X POST "http://localhost:8080/admin-api/moments/structured-data/batch" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d "[1001, 1002, 1003]"
```

### 4. 导出钓获分享数据为Excel

```bash
curl -X GET "http://localhost:8080/admin-api/moments/export/excel/type?momentType=fishing_catch" \
  -H "Authorization: Bearer your-token" \
  --output fishing_catch_data.xlsx
```

### 5. 验证数据完整性

```bash
curl -X GET "http://localhost:8080/admin-api/moments/validate" \
  -H "Authorization: Bearer your-token"
```

## 注意事项

1. **权限要求**: 所有API都需要管理员权限
2. **数据量限制**: 单次导出建议不超过10000条记录
3. **时间格式**: 统一使用 `yyyy-MM-dd HH:mm:ss` 格式
4. **字符编码**: 导出文件使用UTF-8编码，支持中文
5. **缓存策略**: 统计数据有5分钟缓存，实时性要求高的场景请注意
6. **性能优化**: 批量操作使用了优化的SQL查询，建议使用批量接口而不是循环调用单个接口

## 版本历史

- **v1.0** (2025-01-15): 初始版本，包含基础的CRUD和导出功能
- **v1.1** (计划中): 增加数据分析功能和更多导出格式支持