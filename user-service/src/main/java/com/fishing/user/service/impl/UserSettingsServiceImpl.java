package com.fishing.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fishing.domain.User;
import com.fishing.domain.UserSettings;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.moment.MomentComment;
import com.fishing.domain.moment.MomentLike;
import com.fishing.domain.moment.MomentImage;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.domain.user.UserFollow;
import com.fishing.dto.user.UserStatsDto;
import com.fishing.mapper.*;
import com.fishing.user.service.UserSettingsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 用户设置服务实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class UserSettingsServiceImpl implements UserSettingsService {

    private final UserSettingsMapper userSettingsMapper;
    private final UserMapper userMapper;
    private final MomentMapper momentMapper;
    private final MomentCommentMapper momentCommentMapper;
    private final MomentLikeMapper momentLikeMapper;
    private final MomentImageMapper momentImageMapper;
    private final FishingSpotMapper fishingSpotMapper;
    private final UserFollowMapper followMapper;

    @Override
    public UserSettings getUserSettings(Long userId) {
        UserSettings settings = userSettingsMapper.selectByUserId(userId);
        if (settings == null) {
            // 如果不存在设置，创建默认设置
            initDefaultSettings(userId);
            settings = userSettingsMapper.selectByUserId(userId);
        }
        return settings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserSettings(Long userId, UserSettings settings) {
        settings.setUpdateTime(LocalDateTime.now());
        return userSettingsMapper.updateByUserId(userId, settings) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean initDefaultSettings(Long userId) {
        UserSettings existingSettings = userSettingsMapper.selectByUserId(userId);
        if (existingSettings != null) {
            return true; // 已存在，不需要重复创建
        }

        UserSettings defaultSettings = createDefaultSettings(userId);
        return userSettingsMapper.insert(defaultSettings) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePrivacySettings(Long userId, String profileVisibility, String momentVisibility,
                                         Boolean allowFollow, Boolean allowMessage) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        if (profileVisibility != null) settings.setProfileVisibility(profileVisibility);
        if (momentVisibility != null) settings.setMomentVisibility(momentVisibility);
        if (allowFollow != null) settings.setAllowFollow(allowFollow ? 1 : 0);
        if (allowMessage != null) settings.setAllowMessage(allowMessage ? 1 : 0);

        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateNotificationSettings(Long userId, Boolean momentNotification, Boolean commentNotification,
                                              Boolean followNotification, Boolean systemNotification) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        if (momentNotification != null) settings.setMomentNotification(momentNotification ? 1 : 0);
        if (commentNotification != null) settings.setCommentNotification(commentNotification ? 1 : 0);
        if (followNotification != null) settings.setFollowNotification(followNotification ? 1 : 0);
        if (systemNotification != null) settings.setSystemNotification(systemNotification ? 1 : 0);

        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLanguage(Long userId, String language) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        settings.setLanguage(language);
        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePrivacyLevel(Long userId, String privacyLevel) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null) {
            return false;
        }

        settings.setPrivacyLevel(privacyLevel);
        return updateUserSettings(userId, settings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchUpdateNotificationEnabled(Long[] userIds, Boolean enabled) {
        return userSettingsMapper.batchUpdateNotificationSettings(userIds, enabled ? 1 : 0) > 0;
    }

    @Override
    public Map<String, Boolean> getUserNotificationPreferences(Long userId) {
        UserSettings settings = getUserSettings(userId);
        Map<String, Boolean> preferences = new HashMap<>();

        if (settings != null) {
            preferences.put("notification_enabled", settings.getNotificationEnabled() == 1);
            preferences.put("moment_notification", settings.getMomentNotification() == 1);
            preferences.put("comment_notification", settings.getCommentNotification() == 1);
            preferences.put("follow_notification", settings.getFollowNotification() == 1);
            preferences.put("system_notification", settings.getSystemNotification() == 1);
        } else {
            // 默认值
            preferences.put("notification_enabled", true);
            preferences.put("moment_notification", true);
            preferences.put("comment_notification", true);
            preferences.put("follow_notification", true);
            preferences.put("system_notification", true);
        }

        return preferences;
    }

    @Override
    public boolean isNotificationEnabled(Long userId, String notificationType) {
        UserSettings settings = getUserSettings(userId);
        if (settings == null || settings.getNotificationEnabled() != 1) {
            return false;
        }

        return switch (notificationType.toLowerCase()) {
            case "moment" -> settings.getMomentNotification() == 1;
            case "comment" -> settings.getCommentNotification() == 1;
            case "follow" -> settings.getFollowNotification() == 1;
            case "system" -> settings.getSystemNotification() == 1;
            default -> false;
        };
    }

    @Override
    public Long[] getNotificationEnabledUserIds() {
        return userSettingsMapper.selectNotificationEnabledUserIds();
    }

    @Override
    public Map<String, Long> getPrivacyLevelStats() {
        Map<String, Long> stats = new HashMap<>();
        stats.put("public", userSettingsMapper.countByPrivacyLevel("public"));
        stats.put("friends", userSettingsMapper.countByPrivacyLevel("friends"));
        stats.put("private", userSettingsMapper.countByPrivacyLevel("private"));
        return stats;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetToDefaultSettings(Long userId) {
        // 先删除现有设置
        UserSettings existingSettings = userSettingsMapper.selectByUserId(userId);
        if (existingSettings != null) {
            userSettingsMapper.deleteById(existingSettings.getId());
        }

        // 重新创建默认设置
        return initDefaultSettings(userId);
    }

    /**
     * 创建默认用户设置
     */
    private UserSettings createDefaultSettings(Long userId) {
        UserSettings settings = new UserSettings();
        settings.setUserId(userId);
        settings.setNotificationEnabled(1);
        settings.setPrivacyLevel("public");
        settings.setLanguage("zh_CN");
        settings.setAutoSaveLocation(1);
        settings.setShowOnlineStatus(1);
        settings.setProfileVisibility("public");
        settings.setMomentVisibility("public");
        settings.setAllowFollow(1);
        settings.setAllowMessage(1);
        settings.setMomentNotification(1);
        settings.setCommentNotification(1);
        settings.setFollowNotification(1);
        settings.setSystemNotification(1);
        settings.setCreateTime(LocalDateTime.now());
        settings.setUpdateTime(LocalDateTime.now());
        return settings;
    }

    @Override
    public UserStatsDto getUserStats(Long userId) {
        try {
            log.info("开始获取用户统计数据: userId={}", userId);

            // 关注数量（我关注的人数）
            Long followingCount = 0L;
            try {
                LambdaQueryWrapper<UserFollow> followingWrapper = new LambdaQueryWrapper<>();
                followingWrapper.eq(UserFollow::getUserId, userId);
                followingCount = followMapper.selectCount(followingWrapper);
                log.debug("关注数量查询完成: userId={}, count={}", userId, followingCount);
            } catch (Exception e) {
                log.error("查询关注数量失败: userId={}", userId, e);
                followingCount = 0L;
            }

            // 粉丝数量（关注我的人数）
            Long followersCount = 0L;
            try {
                LambdaQueryWrapper<UserFollow> followersWrapper = new LambdaQueryWrapper<>();
                followersWrapper.eq(UserFollow::getFollowUserId, userId);
                followersCount = followMapper.selectCount(followersWrapper);
                log.debug("粉丝数量查询完成: userId={}, count={}", userId, followersCount);
            } catch (Exception e) {
                log.error("查询粉丝数量失败: userId={}", userId, e);
                followersCount = 0L;
            }

            // 动态数量
            Long momentsCount = 0L;
            try {
                LambdaQueryWrapper<Moment> momentWrapper = new LambdaQueryWrapper<>();
                momentWrapper.eq(Moment::getUserId, userId);
                momentsCount = momentMapper.selectCount(momentWrapper);
                log.debug("动态数量查询完成: userId={}, count={}", userId, momentsCount);
            } catch (Exception e) {
                log.error("查询动态数量失败: userId={}", userId, e);
                momentsCount = 0L;
            }

            // 钓点数量
            Long spotsCount = 0L;
            try {
                LambdaQueryWrapper<FishingSpot> spotWrapper = new LambdaQueryWrapper<>();
                spotWrapper.eq(FishingSpot::getCreateBy, userId);
                spotsCount = fishingSpotMapper.selectCount(spotWrapper);
                log.debug("钓点数量查询完成: userId={}, count={}", userId, spotsCount);
            } catch (Exception e) {
                log.error("查询钓点数量失败: userId={}", userId, e);
                spotsCount = 0L;
            }

            // 获得的赞数（我的动态被点赞的总数）
            Long likesCount = 0L;
            try {
                LambdaQueryWrapper<Moment> userMomentsWrapper = new LambdaQueryWrapper<>();
                userMomentsWrapper.eq(Moment::getUserId, userId).select(Moment::getId);
                var userMoments = momentMapper.selectList(userMomentsWrapper);

                if (!userMoments.isEmpty()) {
                    var momentIds = userMoments.stream().map(Moment::getId).toList();
                    LambdaQueryWrapper<MomentLike> likesWrapper = new LambdaQueryWrapper<>();
                    likesWrapper.in(MomentLike::getMomentId, momentIds);
                    likesCount = momentLikeMapper.selectCount(likesWrapper);
                }
                log.debug("点赞数量查询完成: userId={}, count={}", userId, likesCount);
            } catch (Exception e) {
                log.error("查询点赞数量失败: userId={}", userId, e);
                likesCount = 0L;
            }

            // 评论数量（我发表的评论数）
            Long commentsCount = 0L;
            try {
                LambdaQueryWrapper<MomentComment> commentsWrapper = new LambdaQueryWrapper<>();
                commentsWrapper.eq(MomentComment::getUserId, userId);
                commentsCount = momentCommentMapper.selectCount(commentsWrapper);
                log.debug("评论数量查询完成: userId={}, count={}", userId, commentsCount);
            } catch (Exception e) {
                log.error("查询评论数量失败: userId={}", userId, e);
                commentsCount = 0L;
            }

            // 收藏数量（暂时设为0，需要有收藏功能才能实现）
            Long bookmarksCount = 0L;

            // 相册数量（基于用户动态中包含图片的数量估算）
            Long albumCount = 0L;
            try {
                // 查询用户的动态
                LambdaQueryWrapper<Moment> momentWrapper = new LambdaQueryWrapper<>();
                momentWrapper.eq(Moment::getUserId, userId);
                var userMoments = momentMapper.selectList(momentWrapper);

                // 统计包含图片的动态数量
                albumCount = userMoments.stream()
                        .filter(moment -> {
                            // 查询该动态是否有关联图片
                            LambdaQueryWrapper<MomentImage> imageWrapper = new LambdaQueryWrapper<>();
                            imageWrapper.eq(MomentImage::getMomentId, moment.getId());
                            return momentImageMapper.selectCount(imageWrapper) > 0;
                        })
                        .count();
                log.debug("相册数量查询完成: userId={}, count={}", userId, albumCount);
            } catch (Exception e) {
                log.error("查询相册数量失败: userId={}", userId, e);
                albumCount = 0L;
            }

            // 确保所有值都是非负数
            followingCount = Math.max(0L, followingCount != null ? followingCount : 0L);
            followersCount = Math.max(0L, followersCount != null ? followersCount : 0L);
            momentsCount = Math.max(0L, momentsCount != null ? momentsCount : 0L);
            spotsCount = Math.max(0L, spotsCount != null ? spotsCount : 0L);
            likesCount = Math.max(0L, likesCount != null ? likesCount : 0L);
            commentsCount = Math.max(0L, commentsCount != null ? commentsCount : 0L);
            albumCount = Math.max(0L, albumCount != null ? albumCount : 0L);

            log.info("用户统计数据查询完成: userId={}, following={}, followers={}, moments={}, spots={}, likes={}, comments={}, albums={}",
                    userId, followingCount, followersCount, momentsCount, spotsCount, likesCount, commentsCount, albumCount);

            return UserStatsDto.builder()
                    .userId(userId)
                    .followingCount(followingCount.intValue())
                    .followersCount(followersCount.intValue())
                    .momentsCount(momentsCount.intValue())
                    .spotsCount(spotsCount)
                    .likesCount(likesCount.intValue())
                    .commentsCount(commentsCount.intValue())
                    .bookmarksCount(bookmarksCount.intValue())
                    .albumCount(albumCount.intValue())
                    .lastUpdated(System.currentTimeMillis())
                    .build();
        } catch (Exception e) {
            log.error("获取用户统计失败: userId={}", userId, e);
            return UserStatsDto.builder()
                    .userId(userId)
                    .followingCount(0)
                    .followersCount(0)
                    .momentsCount(0)
                    .spotsCount(0L)
                    .likesCount(0)
                    .commentsCount(0)
                    .bookmarksCount(0)
                    .albumCount(0)
                    .lastUpdated(System.currentTimeMillis())
                    .build();
        }
    }

    @Override
    public boolean validatePassword(Long userId, String password) {
        try {
            if (password == null || password.trim().isEmpty()) {
                return false;
            }

            // 从数据库获取用户信息
            User user = userMapper.selectById(userId);
            if (user == null) {
                log.warn("用户不存在: userId={}", userId);
                return false;
            }

            // 实际项目中应该使用加密后的密码进行比较
            // 这里假设数据库中存储的是加密后的密码
            String storedPassword = user.getPassword();
            if (storedPassword == null) {
                log.warn("用户密码为空: userId={}", userId);
                return false;
            }

            // 这里应该使用密码加密工具进行验证，例如 BCrypt
            // return BCrypt.checkpw(password, storedPassword);

            // 临时实现：简单的长度和非空验证
            // 实际使用时应该替换为真正的密码加密验证
            boolean isValid = password.length() >= 6 && password.equals(storedPassword);

            log.info("验证用户密码: userId={}, 验证结果={}", userId, isValid);
            return isValid;

        } catch (Exception e) {
            log.error("密码验证失败: userId={}", userId, e);
            return false;
        }
    }
}