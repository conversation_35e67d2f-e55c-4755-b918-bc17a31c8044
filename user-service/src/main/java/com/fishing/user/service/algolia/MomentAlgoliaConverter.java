package com.fishing.user.service.algolia;

import com.algolia.api.SearchClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fishing.domain.User;
import com.fishing.domain.moment.Moment;
import com.fishing.domain.moment.MomentImage;
import com.fishing.domain.spot.FishingSpot;
import com.fishing.dto.algolia.MomentIndexData;
import com.fishing.mapper.FishingSpotMapper;
import com.fishing.mapper.MomentImageMapper;
import com.fishing.mapper.UserMapper;
import com.fishing.mapper.MomentFishingCatchMapper;
import com.fishing.mapper.MomentEquipmentMapper;
import com.fishing.mapper.MomentTechniqueMapper;
import com.fishing.mapper.MomentQuestionMapper;
import com.fishing.domain.moment.MomentFishingCatch;
import com.fishing.domain.moment.MomentEquipment;
import com.fishing.domain.moment.MomentTechnique;
import com.fishing.domain.moment.MomentQuestion;
import com.fishing.user.service.counter.IRedisCounterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnBean(SearchClient.class)
public class MomentAlgoliaConverter implements AlgoliaDataConverter<Moment> {

    private final UserMapper userMapper;
    private final FishingSpotMapper fishingSpotMapper;
    private final MomentImageMapper momentImageMapper;
    private final MomentFishingCatchMapper momentFishingCatchMapper;
    private final MomentEquipmentMapper momentEquipmentMapper;
    private final MomentTechniqueMapper momentTechniqueMapper;
    private final MomentQuestionMapper momentQuestionMapper;
    private final IRedisCounterService redisCounterService;
    private final ObjectMapper objectMapper;
    private final MomentTypeDataProcessor typeDataProcessor;

    @Override
    public Map<String, Object> convert(Moment moment) {
        try {
            MomentIndexData indexData = buildMomentIndexData(moment);
            Map<String, Object> data = objectMapper.convertValue(indexData, new TypeReference<>() {
            });

            // 额外处理，确保符合 Algolia 格式要求
            data.put("objectID", "moment_" + moment.getId());
            data.put("type", "moment");

            if (indexData.getCreateTime() != null) {
                data.put("createdAt", indexData.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toEpochSecond());
            }
            if (indexData.getUpdateTime() != null) {
                data.put("updatedAt", indexData.getUpdateTime().atZone(java.time.ZoneId.systemDefault()).toEpochSecond());
            }

            return data;
        } catch (Exception e) {
            log.error("转换动态到 Algolia 对象失败: momentId={}", moment.getId(), e);
            return buildFallbackMomentData(moment);
        }
    }

    private MomentIndexData buildMomentIndexData(Moment moment) {
        MomentIndexData.MomentIndexDataBuilder builder = MomentIndexData.builder()
                .id(moment.getId())
                .content(moment.getContent())
                .momentType(moment.getMomentType())
                .visibility(moment.getVisibility())
                .fishingSpotId(moment.getFishingSpotId())
                .createTime(moment.getCreateTime())
                .updateTime(moment.getUpdateTime());

        populateAuthor(builder, moment.getUserId());
        populateFishingSpot(builder, moment.getFishingSpotId());
        populateImages(builder, moment.getId());
        populateStats(builder, moment.getId());
        populateTagsAndTypeData(builder, moment);

        MomentIndexData indexData = builder.build();

        // 后续处理
        indexData.extractTitle();
        indexData.buildLocationString();
        indexData.calculateHotScore();

        return indexData;
    }

    private void populateAuthor(MomentIndexData.MomentIndexDataBuilder builder, Long userId) {
        if (userId == null) return;
        User user = userMapper.selectById(userId);
        if (user != null) {
            builder.authorId(user.getId())
                    .authorName(user.getName())
                    .authorAvatar(user.getAvatarUrl())
                    .province(user.getProvince())
                    .city(user.getCity())
                    .county(user.getCounty());
        }
    }

    private void populateFishingSpot(MomentIndexData.MomentIndexDataBuilder builder, Long spotId) {
        if (spotId == null) return;
        FishingSpot spot = fishingSpotMapper.selectById(spotId);
        if (spot != null) {
            builder.fishingSpotName(spot.getName());
            MomentIndexData partialData = builder.build(); // 获取当前已构建的数据
            if (partialData.getProvince() == null || partialData.getProvince().isEmpty()) {
                builder.province(spot.getProvince())
                        .city(spot.getCity())
                        .county(spot.getCounty())
                        .addressDetail(spot.getAddress());
            }
        }
    }

    private void populateImages(MomentIndexData.MomentIndexDataBuilder builder, Long momentId) {
        try {
            List<MomentImage> images = momentImageMapper.selectList(
                    new LambdaQueryWrapper<MomentImage>()
                            .eq(MomentImage::getMomentId, momentId)
                            .orderByAsc(MomentImage::getDisplayOrder)
            );
            if (!images.isEmpty()) {
                List<String> imageUrls = images.stream().map(MomentImage::getImageUrl).collect(Collectors.toList());
                builder.images(imageUrls).coverImage(imageUrls.get(0));
            } else {
                builder.images(Collections.emptyList());
            }
        } catch (Exception e) {
            log.warn("获取动态图片失败: momentId={}, error={}", momentId, e.getMessage());
            builder.images(Collections.emptyList());
        }
    }

    private void populateStats(MomentIndexData.MomentIndexDataBuilder builder, Long momentId) {
        try {
            int likeCount = redisCounterService.getLikeCount(momentId);
            int commentCount = redisCounterService.getCommentCount(momentId);
            Long viewCountLong = redisCounterService.getViewCount(momentId);
            builder.likeCount(likeCount)
                    .commentCount(commentCount)
                    .viewCount(viewCountLong != null ? viewCountLong.intValue() : 0);
        } catch (Exception e) {
            log.warn("获取动态统计数据失败: momentId={}, error={}", momentId, e.getMessage());
            builder.likeCount(0).commentCount(0).viewCount(0);
        }
    }

    private void populateTagsAndTypeData(MomentIndexData.MomentIndexDataBuilder builder, Moment moment) {
        // 从结构化数据表获取类型特定数据
        String typeSpecificDataJson = getStructuredDataAsJson(moment);
        
        if (typeSpecificDataJson == null || typeSpecificDataJson.trim().isEmpty()) {
            builder.tags(Collections.emptyList());
            return;
        }

        try {
            // 使用增强的类型数据处理器
            MomentTypeDataProcessor.ProcessedTypeData processedData = 
                typeDataProcessor.processTypeData(moment.getMomentType(), typeSpecificDataJson);
            
            builder.typeSpecificData(processedData.getOriginalData());
            builder.tags(processedData.getCleanTags());
            
            // 将搜索字段添加到索引数据中（如果MomentIndexData支持的话）
            // 这些字段可以用于更精确的搜索
            log.debug("处理动态类型数据: momentId={}, type={}, tags={}, searchableFields={}", 
                moment.getId(), moment.getMomentType(), processedData.getCleanTags().size(), 
                processedData.getSearchableFields().size());
                
        } catch (Exception e) {
            log.warn("解析动态特定数据或提取标签失败: momentId={}", moment.getId(), e);
            builder.tags(Collections.emptyList());
        }
    }

    /**
     * 从结构化数据表获取类型特定数据并转换为JSON字符串
     */
    private String getStructuredDataAsJson(Moment moment) {
        try {
            switch (moment.getMomentType()) {
                case "fishing_catch":
                    LambdaQueryWrapper<MomentFishingCatch> fishingCatchWrapper = new LambdaQueryWrapper<>();
                    fishingCatchWrapper.eq(MomentFishingCatch::getMomentId, moment.getId());
                    MomentFishingCatch fishingCatch = momentFishingCatchMapper.selectOne(fishingCatchWrapper);
                    return fishingCatch != null ? objectMapper.writeValueAsString(fishingCatch) : null;
                    
                case "equipment":
                    LambdaQueryWrapper<MomentEquipment> equipmentWrapper = new LambdaQueryWrapper<>();
                    equipmentWrapper.eq(MomentEquipment::getMomentId, moment.getId());
                    MomentEquipment equipment = momentEquipmentMapper.selectOne(equipmentWrapper);
                    return equipment != null ? objectMapper.writeValueAsString(equipment) : null;
                    
                case "technique":
                    LambdaQueryWrapper<MomentTechnique> techniqueWrapper = new LambdaQueryWrapper<>();
                    techniqueWrapper.eq(MomentTechnique::getMomentId, moment.getId());
                    MomentTechnique technique = momentTechniqueMapper.selectOne(techniqueWrapper);
                    return technique != null ? objectMapper.writeValueAsString(technique) : null;
                    
                case "question":
                    LambdaQueryWrapper<MomentQuestion> questionWrapper = new LambdaQueryWrapper<>();
                    questionWrapper.eq(MomentQuestion::getMomentId, moment.getId());
                    MomentQuestion question = momentQuestionMapper.selectOne(questionWrapper);
                    return question != null ? objectMapper.writeValueAsString(question) : null;
                    
                default:
                    return null;
            }
        } catch (Exception e) {
            log.warn("获取动态结构化数据失败: momentId={}, type={}", moment.getId(), moment.getMomentType(), e);
            return null;
        }
    }

    private Map<String, Object> buildFallbackMomentData(Moment moment) {
        Map<String, Object> data = new HashMap<>();
        data.put("objectID", "moment_" + moment.getId());
        data.put("id", moment.getId());
        data.put("type", "moment");
        data.put("content", moment.getContent() != null ? moment.getContent() : "");
        data.put("momentType", moment.getMomentType() != null ? moment.getMomentType() : "");
        data.put("visibility", moment.getVisibility() != null ? moment.getVisibility() : "");
        data.put("createdAt", moment.getCreateTime() != null ? moment.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toEpochSecond() : 0);
        data.put("updatedAt", moment.getUpdateTime() != null ? moment.getUpdateTime().atZone(java.time.ZoneId.systemDefault()).toEpochSecond() : 0);
        data.put("title", "动态内容(降级)");
        data.put("likeCount", 0);
        data.put("commentCount", 0);
        data.put("viewCount", 0);
        data.put("hotScore", 0.0);
        data.put("images", Collections.emptyList());
        return data;
    }
}