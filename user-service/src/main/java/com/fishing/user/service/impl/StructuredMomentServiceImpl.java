package com.fishing.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fishing.domain.Tag;
import com.fishing.domain.moment.*;
import com.fishing.dto.moment.structured.*;
import com.fishing.mapper.*;
import com.fishing.user.service.StructuredMomentService;
import com.fishing.vo.moment.MomentImageVO;
import com.fishing.vo.moment.structured.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 结构化动态服务实现类
 */
@Service
public class StructuredMomentServiceImpl implements StructuredMomentService {

    private final MomentMapper momentMapper;
    private final MomentImageMapper momentImageMapper;
    private final MomentFishingCatchMapper fishingCatchMapper;
    private final MomentCaughtFishMapper caughtFishMapper;
    private final MomentCatchImageMapper catchImageMapper;
    private final MomentEquipmentMapper equipmentMapper;
    private final MomentEquipmentFishTypeMapper equipmentFishTypeMapper;
    private final MomentTechniqueMapper techniqueMapper;
    private final MomentTechniqueEnvironmentMapper techniqueEnvironmentMapper;
    private final MomentTechniqueFishTypeMapper techniqueFishTypeMapper;
    private final MomentQuestionMapper questionMapper;
    private final MomentQuestionTagMapper questionTagMapper;
    private final TagMapper tagMapper;

    public StructuredMomentServiceImpl(
            MomentMapper momentMapper,
            MomentImageMapper momentImageMapper,
            MomentFishingCatchMapper fishingCatchMapper,
            MomentCaughtFishMapper caughtFishMapper,
            MomentCatchImageMapper catchImageMapper,
            MomentEquipmentMapper equipmentMapper,
            MomentEquipmentFishTypeMapper equipmentFishTypeMapper,
            MomentTechniqueMapper techniqueMapper,
            MomentTechniqueEnvironmentMapper techniqueEnvironmentMapper,
            MomentTechniqueFishTypeMapper techniqueFishTypeMapper,
            MomentQuestionMapper questionMapper,
            MomentQuestionTagMapper questionTagMapper,
            TagMapper tagMapper) {
        this.momentMapper = momentMapper;
        this.momentImageMapper = momentImageMapper;
        this.fishingCatchMapper = fishingCatchMapper;
        this.caughtFishMapper = caughtFishMapper;
        this.catchImageMapper = catchImageMapper;
        this.equipmentMapper = equipmentMapper;
        this.equipmentFishTypeMapper = equipmentFishTypeMapper;
        this.techniqueMapper = techniqueMapper;
        this.techniqueEnvironmentMapper = techniqueEnvironmentMapper;
        this.techniqueFishTypeMapper = techniqueFishTypeMapper;
        this.questionMapper = questionMapper;
        this.questionTagMapper = questionTagMapper;
        this.tagMapper = tagMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StructuredMomentResponse createStructuredMoment(Long userId, CreateStructuredMomentRequest request) {
        // 1. 创建主动态记录
        Moment moment = new Moment();
        moment.setUserId(userId);
        moment.setContent(request.getContent());
        moment.setMomentType(convertMomentTypeToString(request.getMomentType()));
        moment.setVisibility(convertVisibilityToString(request.getVisibility()));
        moment.setFishingSpotId(request.getFishingSpotId());
        moment.setCreateTime(LocalDateTime.now());
        moment.setUpdateTime(LocalDateTime.now());
        
        momentMapper.insert(moment);

        // 2. 保存主图片
        if (!CollectionUtils.isEmpty(request.getImageUrls())) {
            saveMomentImages(moment.getId(), request.getImageUrls());
        }

        // 3. 根据动态类型保存特定数据
        switch (request.getMomentType()) {
            case 1: // 钓获分享
                if (request.getFishingCatchData() != null) {
                    saveFishingCatchData(moment.getId(), request.getFishingCatchData());
                }
                break;
            case 2: // 装备展示
                if (request.getEquipmentData() != null) {
                    saveEquipmentData(moment.getId(), request.getEquipmentData());
                }
                break;
            case 3: // 技巧分享
                if (request.getTechniqueData() != null) {
                    saveTechniqueData(moment.getId(), request.getTechniqueData());
                }
                break;
            case 4: // 问答求助
                if (request.getQuestionData() != null) {
                    saveQuestionData(moment.getId(), request.getQuestionData());
                }
                break;
        }

        return getStructuredMoment(moment.getId());
    }

    @Override
    public StructuredMomentResponse getStructuredMoment(Long momentId) {
        // 获取主动态数据
        Moment moment = momentMapper.selectById(momentId);
        if (moment == null) {
            return null;
        }

        StructuredMomentResponse response = new StructuredMomentResponse();
        BeanUtils.copyProperties(moment, response);
        
        // 转换类型值
        response.setMomentType(convertStringToMomentType(moment.getMomentType()));
        response.setMomentTypeName(getMomentTypeName(response.getMomentType()));
        response.setVisibility(convertStringToVisibility(moment.getVisibility()));
        response.setVisibilityName(getVisibilityName(response.getVisibility()));

        // 获取主图片
        response.setImages(getMomentImages(momentId));

        // 根据类型获取特定数据
        switch (response.getMomentType()) {
            case 1: // 钓获分享
                response.setFishingCatchData(getFishingCatchData(momentId));
                break;
            case 2: // 装备展示
                response.setEquipmentData(getEquipmentData(momentId));
                break;
            case 3: // 技巧分享
                response.setTechniqueData(getTechniqueData(momentId));
                break;
            case 4: // 问答求助
                response.setQuestionData(getQuestionData(momentId));
                break;
        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StructuredMomentResponse updateStructuredMoment(Long userId, Long momentId, CreateStructuredMomentRequest request) {
        // 检查权限
        Moment existingMoment = momentMapper.selectById(momentId);
        if (existingMoment == null || !existingMoment.getUserId().equals(userId)) {
            throw new RuntimeException("无权限修改该动态");
        }

        // 更新主动态
        Moment moment = new Moment();
        moment.setId(momentId);
        moment.setContent(request.getContent());
        moment.setVisibility(convertVisibilityToString(request.getVisibility()));
        moment.setFishingSpotId(request.getFishingSpotId());
        moment.setUpdateTime(LocalDateTime.now());
        
        momentMapper.updateById(moment);

        // 更新主图片（先删除再插入）
        deleteMomentImages(momentId);
        if (!CollectionUtils.isEmpty(request.getImageUrls())) {
            saveMomentImages(momentId, request.getImageUrls());
        }

        // 根据动态类型更新特定数据
        Integer momentType = convertStringToMomentType(existingMoment.getMomentType());
        switch (momentType) {
            case 1: // 钓获分享
                deleteFishingCatchData(momentId);
                if (request.getFishingCatchData() != null) {
                    saveFishingCatchData(momentId, request.getFishingCatchData());
                }
                break;
            case 2: // 装备展示
                deleteEquipmentData(momentId);
                if (request.getEquipmentData() != null) {
                    saveEquipmentData(momentId, request.getEquipmentData());
                }
                break;
            case 3: // 技巧分享
                deleteTechniqueData(momentId);
                if (request.getTechniqueData() != null) {
                    saveTechniqueData(momentId, request.getTechniqueData());
                }
                break;
            case 4: // 问答求助
                deleteQuestionData(momentId);
                if (request.getQuestionData() != null) {
                    saveQuestionData(momentId, request.getQuestionData());
                }
                break;
        }

        return getStructuredMoment(momentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteStructuredMoment(Long userId, Long momentId) {
        // 检查权限
        Moment existingMoment = momentMapper.selectById(momentId);
        if (existingMoment == null || !existingMoment.getUserId().equals(userId)) {
            throw new RuntimeException("无权限删除该动态");
        }

        // 删除关联数据（CASCADE 删除会自动处理）
        momentMapper.deleteById(momentId);
    }

    @Override
    public IPage<StructuredMomentResponse> getStructuredMomentList(
            Page<StructuredMomentResponse> page,
            Integer momentType,
            Long userId,
            Long fishingSpotId,
            Integer visibility) {
        
        LambdaQueryWrapper<Moment> queryWrapper = new LambdaQueryWrapper<>();
        
        if (momentType != null) {
            queryWrapper.eq(Moment::getMomentType, convertMomentTypeToString(momentType));
        }
        if (userId != null) {
            queryWrapper.eq(Moment::getUserId, userId);
        }
        if (fishingSpotId != null) {
            queryWrapper.eq(Moment::getFishingSpotId, fishingSpotId);
        }
        if (visibility != null) {
            queryWrapper.eq(Moment::getVisibility, convertVisibilityToString(visibility));
        }
        
        queryWrapper.orderByDesc(Moment::getCreateTime);

        // 创建用于数据库查询的分页对象
        IPage<Moment> momentPageQuery = new Page<>(page.getCurrent(), page.getSize());
        
        // 执行分页查询
        IPage<Moment> momentPage = momentMapper.selectPage(momentPageQuery, queryWrapper);
        
        // 转换为响应对象
        List<StructuredMomentResponse> responseList = momentPage.getRecords().stream()
                .map(moment -> getStructuredMoment(moment.getId()))
                .collect(Collectors.toList());

        // 构建返回的分页对象
        IPage<StructuredMomentResponse> responsePage = new Page<>();
        BeanUtils.copyProperties(momentPage, responsePage);
        responsePage.setRecords(responseList);
        
        return responsePage;
    }

    // =========================== 私有辅助方法 ===========================

    private void saveMomentImages(Long momentId, List<String> imageUrls) {
        for (int i = 0; i < imageUrls.size(); i++) {
            MomentImage image = new MomentImage();
            image.setMomentId(momentId);
            image.setImageUrl(imageUrls.get(i));
            image.setDisplayOrder(i);
            image.setCreateTime(LocalDateTime.now());
            momentImageMapper.insert(image);
        }
    }

    private void deleteMomentImages(Long momentId) {
        LambdaQueryWrapper<MomentImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentImage::getMomentId, momentId);
        momentImageMapper.delete(queryWrapper);
    }

    private List<MomentImageVO> getMomentImages(Long momentId) {
        LambdaQueryWrapper<MomentImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentImage::getMomentId, momentId)
                .orderByAsc(MomentImage::getDisplayOrder);
        
        List<MomentImage> images = momentImageMapper.selectList(queryWrapper);
        return images.stream().map(image -> {
            MomentImageVO vo = new MomentImageVO();
            BeanUtils.copyProperties(image, vo);
            return vo;
        }).collect(Collectors.toList());
    }

    // 钓获分享相关方法
    private void saveFishingCatchData(Long momentId, FishingCatchDataRequest request) {
        // 保存主记录
        MomentFishingCatch fishingCatch = new MomentFishingCatch();
        fishingCatch.setMomentId(momentId);
        fishingCatch.setTotalWeight(request.getTotalWeight());
        fishingCatch.setFishingMethod(request.getFishingMethod());
        fishingCatch.setWeatherConditions(request.getWeatherConditions());
        fishingCatch.setCreateTime(LocalDateTime.now());
        
        fishingCatchMapper.insert(fishingCatch);

        // 保存捕获鱼类
        if (!CollectionUtils.isEmpty(request.getCaughtFishes())) {
            for (CaughtFishRequest caughtFishRequest : request.getCaughtFishes()) {
                MomentCaughtFish caughtFish = new MomentCaughtFish();
                caughtFish.setFishingCatchId(fishingCatch.getId());
                caughtFish.setFishTypeId(caughtFishRequest.getFishTypeId());
                caughtFish.setFishTypeName(caughtFishRequest.getFishTypeName());
                caughtFish.setCount(caughtFishRequest.getCount());
                caughtFish.setWeight(caughtFishRequest.getWeight());
                caughtFish.setCreateTime(LocalDateTime.now());
                caughtFishMapper.insert(caughtFish);
            }
        }

        // 保存钓获图片
        if (!CollectionUtils.isEmpty(request.getCatchImageUrls())) {
            for (int i = 0; i < request.getCatchImageUrls().size(); i++) {
                MomentCatchImage catchImage = new MomentCatchImage();
                catchImage.setFishingCatchId(fishingCatch.getId());
                catchImage.setImageUrl(request.getCatchImageUrls().get(i));
                catchImage.setImageOrder(i);
                catchImage.setCreateTime(LocalDateTime.now());
                catchImageMapper.insert(catchImage);
            }
        }
    }

    private void deleteFishingCatchData(Long momentId) {
        // 级联删除会自动处理子表数据
        LambdaQueryWrapper<MomentFishingCatch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentFishingCatch::getMomentId, momentId);
        fishingCatchMapper.delete(queryWrapper);
    }

    private FishingCatchDataResponse getFishingCatchData(Long momentId) {
        LambdaQueryWrapper<MomentFishingCatch> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentFishingCatch::getMomentId, momentId);
        MomentFishingCatch fishingCatch = fishingCatchMapper.selectOne(queryWrapper);
        
        if (fishingCatch == null) {
            return null;
        }

        FishingCatchDataResponse response = new FishingCatchDataResponse();
        BeanUtils.copyProperties(fishingCatch, response);

        // 获取捕获鱼类
        response.setCaughtFishes(getCaughtFishes(fishingCatch.getId()));

        // 获取钓获图片
        response.setCatchImages(getCatchImages(fishingCatch.getId()));

        return response;
    }

    private List<CaughtFishResponse> getCaughtFishes(Long fishingCatchId) {
        LambdaQueryWrapper<MomentCaughtFish> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentCaughtFish::getFishingCatchId, fishingCatchId);
        
        List<MomentCaughtFish> caughtFishes = caughtFishMapper.selectList(queryWrapper);
        return caughtFishes.stream().map(fish -> {
            CaughtFishResponse response = new CaughtFishResponse();
            BeanUtils.copyProperties(fish, response);
            return response;
        }).collect(Collectors.toList());
    }

    private List<CatchImageResponse> getCatchImages(Long fishingCatchId) {
        LambdaQueryWrapper<MomentCatchImage> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentCatchImage::getFishingCatchId, fishingCatchId)
                .orderByAsc(MomentCatchImage::getImageOrder);
        
        List<MomentCatchImage> catchImages = catchImageMapper.selectList(queryWrapper);
        return catchImages.stream().map(image -> {
            CatchImageResponse response = new CatchImageResponse();
            BeanUtils.copyProperties(image, response);
            return response;
        }).collect(Collectors.toList());
    }

    // 装备展示相关方法（类似实现）
    private void saveEquipmentData(Long momentId, EquipmentDataRequest request) {
        MomentEquipment equipment = new MomentEquipment();
        equipment.setMomentId(momentId);
        equipment.setEquipmentName(request.getEquipmentName());
        equipment.setCategory(request.getCategory());
        equipment.setBrand(request.getBrand());
        equipment.setModel(request.getModel());
        equipment.setPrice(request.getPrice());
        equipment.setRating(request.getRating());
        equipment.setTargetFish(request.getTargetFish());
        equipment.setCreateTime(LocalDateTime.now());
        
        equipmentMapper.insert(equipment);

        // 保存适用鱼类
        if (!CollectionUtils.isEmpty(request.getTargetFishTypes())) {
            for (EquipmentFishTypeRequest fishTypeRequest : request.getTargetFishTypes()) {
                MomentEquipmentFishType fishType = new MomentEquipmentFishType();
                fishType.setEquipmentId(equipment.getId());
                fishType.setFishTypeId(fishTypeRequest.getFishTypeId());
                fishType.setFishTypeName(fishTypeRequest.getFishTypeName());
                fishType.setCreateTime(LocalDateTime.now());
                equipmentFishTypeMapper.insert(fishType);
            }
        }
    }

    private void deleteEquipmentData(Long momentId) {
        LambdaQueryWrapper<MomentEquipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentEquipment::getMomentId, momentId);
        equipmentMapper.delete(queryWrapper);
    }

    private EquipmentDataResponse getEquipmentData(Long momentId) {
        LambdaQueryWrapper<MomentEquipment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentEquipment::getMomentId, momentId);
        MomentEquipment equipment = equipmentMapper.selectOne(queryWrapper);
        
        if (equipment == null) {
            return null;
        }

        EquipmentDataResponse response = new EquipmentDataResponse();
        BeanUtils.copyProperties(equipment, response);

        // 获取适用鱼类
        response.setTargetFishTypes(getEquipmentFishTypes(equipment.getId()));

        return response;
    }

    private List<EquipmentFishTypeResponse> getEquipmentFishTypes(Long equipmentId) {
        LambdaQueryWrapper<MomentEquipmentFishType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentEquipmentFishType::getEquipmentId, equipmentId);
        
        List<MomentEquipmentFishType> fishTypes = equipmentFishTypeMapper.selectList(queryWrapper);
        return fishTypes.stream().map(fishType -> {
            EquipmentFishTypeResponse response = new EquipmentFishTypeResponse();
            BeanUtils.copyProperties(fishType, response);
            return response;
        }).collect(Collectors.toList());
    }

    // 技巧分享相关方法
    private void saveTechniqueData(Long momentId, TechniqueDataRequest request) {
        // 保存主记录
        MomentTechnique technique = new MomentTechnique();
        technique.setMomentId(momentId);
        technique.setTechniqueName(request.getTechniqueName());
        technique.setDifficulty(request.getDifficulty());
        technique.setTargetFish(request.getTargetFish());
        technique.setDescription(request.getDescription());
        technique.setCreateTime(LocalDateTime.now());
        
        techniqueMapper.insert(technique);

        // 保存适用环境
        if (!CollectionUtils.isEmpty(request.getEnvironments())) {
            for (String environment : request.getEnvironments()) {
                MomentTechniqueEnvironment techEnv = new MomentTechniqueEnvironment();
                techEnv.setTechniqueId(technique.getId());
                techEnv.setEnvironment(environment);
                techEnv.setCreateTime(LocalDateTime.now());
                techniqueEnvironmentMapper.insert(techEnv);
            }
        }

        // 保存目标鱼类
        if (!CollectionUtils.isEmpty(request.getTargetFishTypes())) {
            for (TechniqueFishTypeRequest fishTypeRequest : request.getTargetFishTypes()) {
                MomentTechniqueFishType fishType = new MomentTechniqueFishType();
                fishType.setTechniqueId(technique.getId());
                fishType.setFishTypeId(fishTypeRequest.getFishTypeId());
                fishType.setFishTypeName(fishTypeRequest.getFishTypeName());
                fishType.setCreateTime(LocalDateTime.now());
                techniqueFishTypeMapper.insert(fishType);
            }
        }
    }

    private void deleteTechniqueData(Long momentId) {
        LambdaQueryWrapper<MomentTechnique> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentTechnique::getMomentId, momentId);
        techniqueMapper.delete(queryWrapper);
    }

    private TechniqueDataResponse getTechniqueData(Long momentId) {
        LambdaQueryWrapper<MomentTechnique> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentTechnique::getMomentId, momentId);
        MomentTechnique technique = techniqueMapper.selectOne(queryWrapper);
        
        if (technique == null) {
            return null;
        }

        TechniqueDataResponse response = new TechniqueDataResponse();
        BeanUtils.copyProperties(technique, response);

        // 获取适用环境
        response.setEnvironments(getTechniqueEnvironments(technique.getId()));

        // 获取目标鱼类
        response.setTargetFishTypes(getTechniqueFishTypes(technique.getId()));

        return response;
    }

    private List<TechniqueEnvironmentResponse> getTechniqueEnvironments(Long techniqueId) {
        LambdaQueryWrapper<MomentTechniqueEnvironment> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentTechniqueEnvironment::getTechniqueId, techniqueId);
        
        List<MomentTechniqueEnvironment> environments = techniqueEnvironmentMapper.selectList(queryWrapper);
        return environments.stream().map(env -> {
            TechniqueEnvironmentResponse response = new TechniqueEnvironmentResponse();
            BeanUtils.copyProperties(env, response);
            return response;
        }).collect(Collectors.toList());
    }

    private List<TechniqueFishTypeResponse> getTechniqueFishTypes(Long techniqueId) {
        LambdaQueryWrapper<MomentTechniqueFishType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentTechniqueFishType::getTechniqueId, techniqueId);
        
        List<MomentTechniqueFishType> fishTypes = techniqueFishTypeMapper.selectList(queryWrapper);
        return fishTypes.stream().map(fishType -> {
            TechniqueFishTypeResponse response = new TechniqueFishTypeResponse();
            BeanUtils.copyProperties(fishType, response);
            return response;
        }).collect(Collectors.toList());
    }

    // 问答求助相关方法
    private void saveQuestionData(Long momentId, QuestionDataRequest request) {
        // 保存主记录
        MomentQuestion question = new MomentQuestion();
        question.setMomentId(momentId);
        question.setQuestionTitle(request.getQuestionTitle());
        question.setDetailedProblem(request.getDetailedProblem());
        question.setIsResolved(0); // 默认状态：未解决
        question.setCreateTime(LocalDateTime.now());
        
        questionMapper.insert(question);

        // 保存已存在的标签关联
        if (!CollectionUtils.isEmpty(request.getTagIds())) {
            for (Long tagId : request.getTagIds()) {
                MomentQuestionTag questionTag = new MomentQuestionTag();
                questionTag.setQuestionId(question.getId());
                questionTag.setTagId(tagId);
                questionTag.setCreateTime(LocalDateTime.now());
                questionTagMapper.insert(questionTag);
            }
        }
        
        // 保存新创建的标签关联
        if (!CollectionUtils.isEmpty(request.getNewTags())) {
            for (String tagName : request.getNewTags()) {
                // 确保标签存在，如果不存在则创建
                Tag tag = findOrCreateTag(tagName);
                
                // 创建问题标签关联
                MomentQuestionTag questionTag = new MomentQuestionTag();
                questionTag.setQuestionId(question.getId());
                questionTag.setTagId(tag.getId());
                questionTag.setCreateTime(LocalDateTime.now());
                questionTagMapper.insert(questionTag);
            }
        }
    }

    private void deleteQuestionData(Long momentId) {
        LambdaQueryWrapper<MomentQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentQuestion::getMomentId, momentId);
        questionMapper.delete(queryWrapper);
    }

    private QuestionDataResponse getQuestionData(Long momentId) {
        LambdaQueryWrapper<MomentQuestion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentQuestion::getMomentId, momentId);
        MomentQuestion question = questionMapper.selectOne(queryWrapper);
        
        if (question == null) {
            return null;
        }

        QuestionDataResponse response = new QuestionDataResponse();
        BeanUtils.copyProperties(question, response);

        // 获取标签
        response.setTags(getQuestionTags(question.getId()));

        return response;
    }

    private List<QuestionTagResponse> getQuestionTags(Long questionId) {
        LambdaQueryWrapper<MomentQuestionTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MomentQuestionTag::getQuestionId, questionId);
        
        List<MomentQuestionTag> questionTags = questionTagMapper.selectList(queryWrapper);
        List<Long> tagIds = questionTags.stream()
                .map(MomentQuestionTag::getTagId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(tagIds)) {
            return new ArrayList<>();
        }

        List<Tag> tags = tagMapper.selectBatchIds(tagIds);
        return tags.stream().map(tag -> {
            QuestionTagResponse response = new QuestionTagResponse();
            response.setId(tag.getId());
            response.setTagId(tag.getId());
            response.setTagName(tag.getTag()); // Tag entity uses 'tag' field, response uses 'tagName'
            response.setQuestionId(questionId);
            response.setCreateTime(tag.getCreateTime());
            return response;
        }).collect(Collectors.toList());
    }

    private Tag findOrCreateTag(String tagName) {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getTag, tagName);
        Tag existingTag = tagMapper.selectOne(queryWrapper);
        
        if (existingTag != null) {
            return existingTag;
        }

        // 创建新标签
        Tag newTag = new Tag();
        newTag.setTag(tagName);
        newTag.setUsageCount(1);
        newTag.setIsActive(1);
        newTag.setCreateTime(LocalDateTime.now());
        newTag.setUpdateTime(LocalDateTime.now());
        tagMapper.insert(newTag);
        
        return newTag;
    }

    // =========================== 类型转换辅助方法 ===========================

    private String convertMomentTypeToString(Integer momentType) {
        switch (momentType) {
            case 1: return "fishing_catch";
            case 2: return "equipment";
            case 3: return "technique";
            case 4: return "question";
            default: throw new IllegalArgumentException("Invalid moment type: " + momentType);
        }
    }

    private Integer convertStringToMomentType(String momentType) {
        switch (momentType) {
            case "fishing_catch": return 1;
            case "equipment": return 2;
            case "technique": return 3;
            case "question": return 4;
            default: throw new IllegalArgumentException("Invalid moment type: " + momentType);
        }
    }

    private String convertVisibilityToString(Integer visibility) {
        switch (visibility) {
            case 1: return "public";
            case 2: return "followers";
            case 3: return "private";
            default: throw new IllegalArgumentException("Invalid visibility: " + visibility);
        }
    }

    private Integer convertStringToVisibility(String visibility) {
        switch (visibility) {
            case "public": return 1;
            case "followers": return 2;
            case "private": return 3;
            default: throw new IllegalArgumentException("Invalid visibility: " + visibility);
        }
    }

    private String getMomentTypeName(Integer momentType) {
        switch (momentType) {
            case 1: return "钓获分享";
            case 2: return "装备展示";
            case 3: return "技巧分享";
            case 4: return "问答求助";
            default: return "未知类型";
        }
    }

    // 难度等级转换
    private Integer convertDifficultyToInt(String difficulty) {
        switch (difficulty) {
            case "beginner": return 1;
            case "intermediate": return 2;
            case "advanced": return 3;
            default: return 1;
        }
    }

    private String convertIntToDifficulty(Integer difficulty) {
        switch (difficulty) {
            case 1: return "beginner";
            case 2: return "intermediate";
            case 3: return "advanced";
            default: return "beginner";
        }
    }

    // 紧急程度转换
    private Integer convertUrgencyToInt(String urgency) {
        switch (urgency) {
            case "low": return 1;
            case "medium": return 2;
            case "high": return 3;
            default: return 2;
        }
    }

    private String convertIntToUrgency(Integer urgency) {
        switch (urgency) {
            case 1: return "low";
            case 2: return "medium";
            case 3: return "high";
            default: return "medium";
        }
    }

    // 问题状态转换
    private String convertIntToQuestionStatus(Integer status) {
        switch (status) {
            case 1: return "unsolved";
            case 2: return "solved";
            case 3: return "closed";
            default: return "unsolved";
        }
    }

    private String getVisibilityName(Integer visibility) {
        switch (visibility) {
            case 1: return "公开";
            case 2: return "关注者可见";
            case 3: return "仅自己可见";
            default: return "未知";
        }
    }
}