package com.fishing.domain.moment;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户动态表
 */
@Data
public class Moment implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 发布用户ID
     */
    private Long userId;

    /**
     * 动态类型：钓获分享/装备展示/技巧分享/问答求助
     */
    private String momentType;

    /**
     * 动态内容
     */
    private String content;

    /**
     * 可见性：公开/仅关注者/私密
     */
    private String visibility;

    /**
     * 钓点ID，可为空
     */
    private Long fishingSpotId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

}