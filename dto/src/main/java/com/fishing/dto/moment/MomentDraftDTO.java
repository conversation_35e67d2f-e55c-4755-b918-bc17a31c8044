package com.fishing.dto.moment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 动态草稿的数据传输对象
 */
@Data
@Schema(description = "动态草稿请求参数")
public class MomentDraftDTO {
    
    /**
     * 草稿ID，为null时创建新草稿
     */
    @Schema(description = "草稿ID，为null时创建新草稿")
    private Long draftId;
    
    /**
     * 动态内容
     */
    @Schema(description = "动态内容")
    private String content;
    
    /**
     * 动态类型：钓获分享/装备展示/技巧分享/问答求助
     */
    @Schema(description = "动态类型（fishing_catch/equipment/technique/question）")
    private String momentType;
    
    /**
     * 可见性：公开/仅关注者/私密
     */
    @Schema(description = "可见性（public/followers/private）")
    private String visibility;
    
    /**
     * 钓点ID
     */
    @Schema(description = "钓点ID")
    private Long fishingSpotId;
    
    
    /**
     * 图片URL列表
     */
    @Schema(description = "图片URL列表")
    private List<String> imageUrls;
} 