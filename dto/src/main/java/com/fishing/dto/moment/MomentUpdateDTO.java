package com.fishing.dto.moment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 更新动态的数据传输对象
 */
@Data
@Schema(description = "更新动态请求参数")
public class MomentUpdateDTO {
    
    /**
     * 动态ID
     */
    @NotNull(message = "动态ID不能为空")
    @Schema(description = "动态ID")
    private Long id;
    
    /**
     * 动态内容
     */
    @NotBlank(message = "动态内容不能为空")
    @Schema(description = "动态内容")
    private String content;
    
    /**
     * 动态类型：钓获分享/装备展示/技巧分享/问答求助
     */
    @NotBlank(message = "动态类型不能为空")
    @Schema(description = "动态类型（fishing_catch/equipment/technique/question）")
    private String momentType;
    
    /**
     * 可见性：公开/仅关注者/私密
     */
    @NotBlank(message = "可见性不能为空")
    @Schema(description = "可见性（public/followers/private）")
    private String visibility;
    
    /**
     * 钓点ID
     */
    @Schema(description = "钓点ID")
    private Long fishingSpotId;
    
    
    /**
     * 图片URL列表
     */
    @Schema(description = "图片URL列表")
    private List<String> imageUrls;
} 