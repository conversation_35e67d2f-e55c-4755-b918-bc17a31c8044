package com.fishing.vo.moment;

import com.fishing.vo.user.UserVo;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 动态信息展示对象
 */
@Data
public class MomentVO {
    /**
     * 动态ID
     */
    private Long id;

    /**
     * 发布用户信息
     */
    private UserVo publisher;

    /**
     * 发布用户ID
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 动态类型：钓获分享/装备展示/技巧分享/问答求助
     */
    private String momentType;

    /**
     * 动态内容
     */
    private String content;

    /**
     * 可见性：公开/仅关注者/私密
     */
    private String visibility;

    /**
     * 钓点ID
     */
    private Long fishingSpotId;

    /**
     * 钓点名称
     */
    private String fishingSpotName;


    /**
     * 动态图片列表
     */
    private List<MomentImageVO> images;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 评论数
     */
    private Integer commentCount;

    /**
     * 当前用户是否点赞
     */
    private Boolean isLiked;

    /**
     * 当前用户是否收藏
     */
    private Boolean isBookmarked;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
} 