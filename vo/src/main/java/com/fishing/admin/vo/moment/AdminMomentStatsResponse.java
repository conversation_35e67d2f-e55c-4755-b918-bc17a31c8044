package com.fishing.admin.vo.moment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Admin动态统计响应VO
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Schema(description = "Admin动态统计响应VO")
public class AdminMomentStatsResponse {

    /**
     * 总动态数
     */
    @Schema(description = "总动态数")
    private Long totalCount;

    /**
     * 今日新增
     */
    @Schema(description = "今日新增")
    private Long todayCount;

    /**
     * 待审核数量
     */
    @Schema(description = "待审核数量")
    private Long pendingAuditCount;

    /**
     * 已审核数量
     */
    @Schema(description = "已审核数量")
    private Long approvedCount;

    /**
     * 已拒绝数量
     */
    @Schema(description = "已拒绝数量")
    private Long rejectedCount;

    /**
     * 违规动态数量
     */
    @Schema(description = "违规动态数量")
    private Long violationCount;

    /**
     * 总点赞数
     */
    @Schema(description = "总点赞数")
    private Long totalLikes;

    /**
     * 总评论数
     */
    @Schema(description = "总评论数")
    private Long totalComments;

    /**
     * 总收藏数
     */
    @Schema(description = "总收藏数")
    private Long totalBookmarks;

    /**
     * 活跃动态数（最近30天有互动）
     */
    @Schema(description = "活跃动态数")
    private Long activeMoments;

    /**
     * 今日新增点赞数
     */
    @Schema(description = "今日新增点赞数")
    private Long todayNewLikes;

    /**
     * 今日新增评论数
     */
    @Schema(description = "今日新增评论数")
    private Long todayNewComments;

    /**
     * 动态类型分布统计
     */
    @Schema(description = "动态类型分布统计")
    private List<Map<String, Object>> typeStats;

    /**
     * 可见性分布统计
     */
    @Schema(description = "可见性分布统计")
    private List<Map<String, Object>> visibilityStats;

    /**
     * 审核状态分布统计
     */
    @Schema(description = "审核状态分布统计")
    private List<Map<String, Object>> auditStats;

    /**
     * 每日新增动态统计（最近7天）
     */
    @Schema(description = "每日新增动态统计")
    private List<Map<String, Object>> dailyStats;

    /**
     * 热门钓点排行（按动态数量）
     */
    @Schema(description = "热门钓点排行")
    private List<Map<String, Object>> hotSpotRanking;

    /**
     * 活跃用户排行（按动态数量）
     */
    @Schema(description = "活跃用户排行")
    private List<Map<String, Object>> activeUserRanking;
    
    /**
     * 动态类型分布统计
     */
    @Schema(description = "动态类型分布统计")
    private Map<String, Long> typeDistribution;
    
    /**
     * 结构化数据统计
     */
    @Schema(description = "结构化数据统计")
    private Map<String, Object> structuredDataStats;
    
    /**
     * 最近7天趋势分析
     */
    @Schema(description = "最近7天趋势分析")
    private Map<String, Object> weeklyTrend;
}