package com.fishing.admin.vo.moment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 动态数据导出VO
 *
 * <AUTHOR>
 * @since 2025-01-15
 */
@Data
@Schema(description = "动态数据导出响应")
public class MomentDataExportVO {

    @Schema(description = "动态ID")
    private Long momentId;

    @Schema(description = "动态类型")
    private String momentType;

    @Schema(description = "动态内容")
    private String content;

    @Schema(description = "用户ID")
    private Long userId;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "钓点ID")
    private Long fishingSpotId;

    @Schema(description = "钓点名称")
    private String fishingSpotName;

    @Schema(description = "可见性")
    private String visibility;

    @Schema(description = "点赞数")
    private Integer likeCount;

    @Schema(description = "评论数")
    private Integer commentCount;

    @Schema(description = "查看数")
    private Integer viewCount;

    @Schema(description = "结构化数据")
    private Map<String, Object> structuredData;

    // 钓获分享专用字段
    @Schema(description = "总重量(仅钓获分享)")
    private String totalWeight;

    @Schema(description = "钓法(仅钓获分享)")
    private String fishingMethod;

    @Schema(description = "天气条件(仅钓获分享)")
    private String weatherConditions;

    @Schema(description = "捕获鱼类数量(仅钓获分享)")
    private Integer caughtFishCount;

    // 装备展示专用字段
    @Schema(description = "装备名称(仅装备展示)")
    private String equipmentName;

    @Schema(description = "品牌(仅装备展示)")
    private String brand;

    @Schema(description = "型号(仅装备展示)")
    private String model;

    @Schema(description = "价格(仅装备展示)")
    private String price;

    @Schema(description = "评分(仅装备展示)")
    private Integer rating;

    // 技巧分享专用字段
    @Schema(description = "技巧名称(仅技巧分享)")
    private String techniqueName;

    @Schema(description = "难度等级(仅技巧分享)")
    private Integer difficulty;

    @Schema(description = "目标鱼种(仅技巧分享)")
    private String targetFish;

    // 问答求助专用字段
    @Schema(description = "问题标题(仅问答求助)")
    private String questionTitle;

    @Schema(description = "是否已解决(仅问答求助)")
    private Boolean isResolved;

    @Schema(description = "解决时间(仅问答求助)")
    private LocalDateTime resolvedAt;
}