package com.fishing.admin.vo.moment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Admin动态详情VO
 * 
 * <AUTHOR>
 * @since 2025-07-01
 */
@Data
@Schema(description = "Admin动态详情VO")
public class AdminMomentVO {

    /**
     * 动态ID
     */
    @Schema(description = "动态ID")
    private Long id;

    /**
     * 动态内容
     */
    @Schema(description = "动态内容")
    private String content;

    /**
     * 动态类型
     */
    @Schema(description = "动态类型")
    private String momentType;

    /**
     * 可见性
     */
    @Schema(description = "可见性")
    private String visibility;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 发布者ID
     */
    @Schema(description = "发布者ID")
    private Long publisherId;

    /**
     * 发布者名称
     */
    @Schema(description = "发布者名称")
    private String publisherName;

    /**
     * 用户名（前端兼容字段）
     */
    @Schema(description = "用户名（前端兼容字段）")
    private String userName;

    /**
     * 发布者头像
     */
    @Schema(description = "发布者头像")
    private String publisherAvatar;

    /**
     * 发布者电话
     */
    @Schema(description = "发布者电话")
    private String publisherPhone;

    /**
     * 钓点ID
     */
    @Schema(description = "钓点ID")
    private Long fishingSpotId;

    /**
     * 钓点名称
     */
    @Schema(description = "钓点名称")
    private String fishingSpotName;

    /**
     * 钓点地址
     */
    @Schema(description = "钓点地址")
    private String fishingSpotAddress;

    /**
     * 省份
     */
    @Schema(description = "省份")
    private String province;

    /**
     * 城市
     */
    @Schema(description = "城市")
    private String city;

    /**
     * 纬度
     */
    @Schema(description = "纬度")
    private Double latitude;

    /**
     * 经度
     */
    @Schema(description = "经度")
    private Double longitude;

    /**
     * 点赞数
     */
    @Schema(description = "点赞数")
    private Integer likeCount;

    /**
     * 评论数
     */
    @Schema(description = "评论数")
    private Integer commentCount;

    /**
     * 查看数
     */
    @Schema(description = "查看数")
    private Integer viewCount;

    /**
     * 图片数量
     */
    @Schema(description = "图片数量")
    private Integer imageCount;

    /**
     * 图片列表
     */
    @Schema(description = "图片列表")
    private List<MomentImageVO> images;


    /**
     * 是否点赞
     */
    @Schema(description = "是否点赞")
    private Boolean isLiked;

    /**
     * 是否收藏
     */
    @Schema(description = "是否收藏")
    private Boolean isBookmarked;

    /**
     * 审核状态
     */
    @Schema(description = "审核状态")
    private String auditStatus;

    /**
     * 审核备注
     */
    @Schema(description = "审核备注")
    private String auditRemark;

    /**
     * 是否违规
     */
    @Schema(description = "是否违规")
    private Boolean isViolation;

    /**
     * 举报次数
     */
    @Schema(description = "举报次数")
    private Integer reportCount;

    /**
     * 管理员标记
     */
    @Schema(description = "管理员标记")
    private String adminNote;

    /**
     * 处理状态
     */
    @Schema(description = "处理状态")
    private String processStatus;

    /**
     * 动态图片VO
     */
    @Data
    @Schema(description = "动态图片VO")
    public static class MomentImageVO {
        @Schema(description = "图片ID")
        private Long id;

        @Schema(description = "图片URL")
        private String imageUrl;

        @Schema(description = "图片顺序")
        private Integer displayOrder;
    }
}