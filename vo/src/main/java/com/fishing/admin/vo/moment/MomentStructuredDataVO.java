package com.fishing.admin.vo.moment;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 动态结构化数据VO
 * 
 * <AUTHOR>
 * @since 2025-01-18
 */
@Data
@Schema(description = "动态结构化数据")
public class MomentStructuredDataVO {

    @Schema(description = "动态ID")
    private Long momentId;

    @Schema(description = "动态类型")
    private String momentType;

    @Schema(description = "钓获分享数据")
    private FishingCatchDataVO fishingCatchData;

    @Schema(description = "装备展示数据")
    private EquipmentDataVO equipmentData;

    @Schema(description = "技巧分享数据")
    private TechniqueDataVO techniqueData;

    @Schema(description = "问答求助数据")
    private QuestionDataVO questionData;

    /**
     * 钓获分享数据VO
     */
    @Data
    @Schema(description = "钓获分享数据")
    public static class FishingCatchDataVO {
        @Schema(description = "总重量(kg)")
        private String totalWeight;

        @Schema(description = "钓法")
        private String fishingMethod;

        @Schema(description = "天气条件")
        private String weatherConditions;

        @Schema(description = "扩展属性")
        private String attributes;

        @Schema(description = "捕获鱼类列表")
        private List<CaughtFishVO> caughtFish;
    }

    /**
     * 装备展示数据VO
     */
    @Data
    @Schema(description = "装备展示数据")
    public static class EquipmentDataVO {
        @Schema(description = "装备名称")
        private String equipmentName;

        @Schema(description = "品牌")
        private String brand;

        @Schema(description = "型号")
        private String model;

        @Schema(description = "价格区间")
        private String priceRange;

        @Schema(description = "购买渠道")
        private String purchaseChannel;

        @Schema(description = "使用体验")
        private String experience;

        @Schema(description = "推荐指数(1-5)")
        private Integer recommendationIndex;

        @Schema(description = "扩展属性")
        private String attributes;
    }

    /**
     * 技巧分享数据VO
     */
    @Data
    @Schema(description = "技巧分享数据")
    public static class TechniqueDataVO {
        @Schema(description = "技巧标题")
        private String techniqueTitle;

        @Schema(description = "难度级别")
        private String difficultyLevel;

        @Schema(description = "适用环境")
        private String applicableEnvironment;

        @Schema(description = "所需装备")
        private String requiredEquipment;

        @Schema(description = "详细步骤")
        private String detailedSteps;

        @Schema(description = "注意事项")
        private String precautions;

        @Schema(description = "扩展属性")
        private String attributes;

        @Schema(description = "适用鱼类列表")
        private List<FishTypeReferenceVO> applicableFishTypes;

        @Schema(description = "环境条件列表")
        private List<EnvironmentConditionVO> environmentConditions;
    }

    /**
     * 问答求助数据VO
     */
    @Data
    @Schema(description = "问答求助数据")
    public static class QuestionDataVO {
        @Schema(description = "问题标题")
        private String questionTitle;

        @Schema(description = "问题类型")
        private String questionType;

        @Schema(description = "紧急程度")
        private String urgencyLevel;

        @Schema(description = "扩展属性")
        private String attributes;

        @Schema(description = "问题标签列表")
        private List<QuestionTagVO> questionTags;
    }

    /**
     * 捕获鱼类VO
     */
    @Data
    @Schema(description = "捕获鱼类")
    public static class CaughtFishVO {
        @Schema(description = "鱼类类型ID")
        private Long fishTypeId;

        @Schema(description = "鱼类类型名称")
        private String fishTypeName;

        @Schema(description = "捕获数量")
        private Integer count;

        @Schema(description = "重量(kg)")
        private String weight;
    }

    /**
     * 鱼类引用VO
     */
    @Data
    @Schema(description = "鱼类引用")
    public static class FishTypeReferenceVO {
        @Schema(description = "鱼类类型ID")
        private Long fishTypeId;

        @Schema(description = "鱼类类型名称")
        private String fishTypeName;
    }

    /**
     * 环境条件VO
     */
    @Data
    @Schema(description = "环境条件")
    public static class EnvironmentConditionVO {
        @Schema(description = "环境类型")
        private String environmentType;

        @Schema(description = "环境描述")
        private String environmentDescription;
    }

    /**
     * 问题标签VO
     */
    @Data
    @Schema(description = "问题标签")
    public static class QuestionTagVO {
        @Schema(description = "标签名称")
        private String tagName;

        @Schema(description = "标签描述")
        private String tagDescription;
    }
}