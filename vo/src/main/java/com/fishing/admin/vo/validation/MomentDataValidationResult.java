package com.fishing.admin.vo.validation;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 动态数据验证结果VO
 * 
 * <AUTHOR>
 * @since 2025-01-18
 */
@Data
@Schema(description = "动态数据验证结果")
public class MomentDataValidationResult {

    @Schema(description = "验证是否通过")
    private Boolean isValid;

    @Schema(description = "验证时间")
    private LocalDateTime validationTime;

    @Schema(description = "检查的动态总数")
    private Integer totalMoments;

    @Schema(description = "有效的动态数量")
    private Integer validMoments;

    @Schema(description = "无效的动态数量")
    private Integer invalidMoments;

    @Schema(description = "数据完整性问题列表")
    private List<DataIntegrityIssue> issues;

    @Schema(description = "按类型统计的问题数量")
    private Map<String, Integer> issuesByType;

    @Schema(description = "孤立数据统计")
    private OrphanedDataStats orphanedData;

    @Schema(description = "修复建议")
    private List<RepairSuggestion> repairSuggestions;

    /**
     * 数据完整性问题
     */
    @Data
    @Schema(description = "数据完整性问题")
    public static class DataIntegrityIssue {
        @Schema(description = "动态ID")
        private Long momentId;

        @Schema(description = "问题类型")
        private String issueType;

        @Schema(description = "问题描述")
        private String description;

        @Schema(description = "严重程度: LOW, MEDIUM, HIGH, CRITICAL")
        private String severity;

        @Schema(description = "影响的字段")
        private List<String> affectedFields;

        @Schema(description = "建议的修复操作")
        private String suggestedAction;
    }

    /**
     * 孤立数据统计
     */
    @Data
    @Schema(description = "孤立数据统计")
    public static class OrphanedDataStats {
        @Schema(description = "孤立的钓获分享数据")
        private Integer orphanedFishingCatch;

        @Schema(description = "孤立的装备展示数据")
        private Integer orphanedEquipment;

        @Schema(description = "孤立的技巧分享数据")
        private Integer orphanedTechnique;

        @Schema(description = "孤立的问答求助数据")
        private Integer orphanedQuestion;

        @Schema(description = "孤立的捕获鱼类数据")
        private Integer orphanedCaughtFish;

        @Schema(description = "孤立的动态图片数据")
        private Integer orphanedImages;
    }

    /**
     * 修复建议
     */
    @Data
    @Schema(description = "修复建议")
    public static class RepairSuggestion {
        @Schema(description = "建议类型")
        private String suggestionType;

        @Schema(description = "建议描述")
        private String description;

        @Schema(description = "影响的动态ID列表")
        private List<Long> affectedMomentIds;

        @Schema(description = "是否可自动修复")
        private Boolean canAutoRepair;

        @Schema(description = "修复的SQL语句或操作步骤")
        private String repairAction;
    }
}