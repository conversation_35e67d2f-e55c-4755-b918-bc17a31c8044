package com.fishing.admin.vo.moment;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 动态管理列表VO - Admin专用
 * 专为B端管理后台动态管理设计，包含审核、统计、违规等信息
 */
@Data
@Schema(description = "动态管理列表VO")
public class AdminMomentListVO {

    @Schema(description = "动态ID")
    private Long id;

    @Schema(description = "动态内容")
    private String content;

    @Schema(description = "动态类型: FISHING-钓鱼, SHARING-分享, QUESTION-提问")
    private String momentType;

    // 发布者信息
    @Schema(description = "发布者ID")
    private Long publisherId;

    @Schema(description = "发布者姓名")
    private String publisherName;

    @Schema(description = "用户名（前端兼容字段）")
    private String userName;

    @Schema(description = "发布者手机号")
    private String publisherPhone;

    @Schema(description = "发布者头像")
    private String publisherAvatar;

    // 钓点信息
    @Schema(description = "钓点ID")
    private Long fishingSpotId;

    @Schema(description = "钓点名称")
    private String fishingSpotName;

    @Schema(description = "钓点地址")
    private String fishingSpotAddress;

    // 审核信息
    @Schema(description = "审核状态: APPROVED-已通过, REJECTED-已拒绝, PENDING-待审核")
    private String auditStatus;

    @Schema(description = "审核原因")
    private String auditReason;

    @Schema(description = "审核人")
    private String auditBy;

    @Schema(description = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    // 可见性控制
    @Schema(description = "可见性: PUBLIC-公开, PRIVATE-私有, FRIENDS-好友可见")
    private String visibility;

    @Schema(description = "是否推荐")
    private Boolean isRecommended;

    // 统计信息
    @Schema(description = "点赞数")
    private Integer likeCount;

    @Schema(description = "评论数")
    private Integer commentCount;

    @Schema(description = "分享数")
    private Integer shareCount;

    @Schema(description = "查看数")
    private Integer viewCount;

    // 违规信息（暂未实现完整功能）
    @Schema(description = "是否违规")
    private Boolean isViolation;

    // 时间信息
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    // 图片信息
    @Schema(description = "动态图片列表")
    private List<MomentImageVO> images;


    /**
     * 动态图片VO
     */
    @Data
    @Schema(description = "动态图片VO")
    public static class MomentImageVO {
        @Schema(description = "图片ID")
        private Long id;

        @Schema(description = "图片URL")
        private String imageUrl;

        @Schema(description = "图片顺序")
        private Integer displayOrder;
    }
}